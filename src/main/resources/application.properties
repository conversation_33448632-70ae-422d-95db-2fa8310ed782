# --- Common Application Settings ---
spring.application.name=parabella-csrd-db
server.port=${PORT:8080}
spring.main.allow-bean-definition-overriding=true

# --- Common JPA Settings (can be overridden) ---
spring.jpa.show-sql=false
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.PostgreSQLDialect
spring.datasource.driver-class-name=org.postgresql.Driver



# --- App Specific Properties (Use Env Vars for Secrets!) ---
parabella_csrd_db.jwtSecret=${JWT_SECRET}
parabella_csrd_db.jwtExpirationMs=86400000

spring.jpa.properties.hibernate.envers.store_data_at_delete=true
spring.jpa.properties.hibernate.envers.global_with_modified_flag=true
spring.jpa.properties.hibernate.envers.modified_flag_suffix=_MOD

# --- OpenAI (Use Env Var) ---
OPEN_AI_API_KEY = ${OPEN_AI_API_KEY}
spring.datasource.username=postgres

# --- CSRD Database Configuration (Primary) ---
spring.datasource.csrd.url=${SPRING_DATASOURCE_CSRD_URL}
spring.datasource.csrd.username=${SPRING_DATASOURCE_CSRD_USERNAME}
spring.datasource.csrd.password=${SPRING_DATASOURCE_CSRD_PASSWORD}
# Add other properties specific to this datasource if needed, e.g., driver class name, connection pool settings
# spring.datasource.csrd.driver-class-name=org.postgresql.Driver

# --- VectorDB Database Configuration (Secondary) ---
spring.datasource.vectordb.url=${SPRING_DATASOURCE_VECTORDB_URL}
spring.datasource.vectordb.username=${SPRING_DATASOURCE_VECTORDB_USERNAME}
spring.datasource.vectordb.password=${SPRING_DATASOURCE_VECTORDB_PASSWORD}

spring.jpa.hibernate.ddl-auto=update


app.frontend.url=https://parabella-elessar-1091242934000.europe-west3.run.app/

# --- Mail (Use Env Vars for Secrets!) ---
spring.mail.host=smtp.gmail.com
spring.mail.port=587
spring.mail.username=${SPRING_MAIL_USERNAME}
spring.mail.password=${SPRING_MAIL_PASSWORD}
spring.mail.properties.mail.smtp.auth=true
spring.mail.properties.mail.smtp.starttls.enable=true

# --- Default Active Profile if none is set (optional, 'local' is a good default) ---
spring.profiles.default=staging