# --- Common Application Settings ---
spring.application.name=parabella-csrd-db
server.port=${PORT:8080}
spring.main.allow-bean-definition-overriding=true

# --- Common JPA Settings (can be overridden) ---
spring.jpa.show-sql=false
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.PostgreSQLDialect
spring.datasource.driver-class-name=org.postgresql.Driver



# --- App Specific Properties (Use Env Vars for Secrets!) ---
parabella_csrd_db.jwtSecret=${JWT_SECRET}
parabella_csrd_db.jwtExpirationMs=86400000

spring.jpa.properties.hibernate.envers.store_data_at_delete=true
spring.jpa.properties.hibernate.envers.global_with_modified_flag=true
spring.jpa.properties.hibernate.envers.modified_flag_suffix=_MOD

# --- OpenAI (Use Env Var) ---
OPEN_AI_API_KEY = ${OPEN_AI_API_KEY}


# --- Mail (Use Env Vars for Secrets!) ---
spring.mail.host=smtp.gmail.com
spring.mail.port=587
spring.mail.username=${SPRING_MAIL_USERNAME}
spring.mail.password=${SPRING_MAIL_PASSWORD}
spring.mail.properties.mail.smtp.auth=true
spring.mail.properties.mail.smtp.starttls.enable=true

# --- Default Active Profile if none is set (optional, 'local' is a good default) ---
spring.profiles.default=staging