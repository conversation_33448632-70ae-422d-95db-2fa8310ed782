spring.application.name=parabella_csrd_db


# --- CSRD Database Configuration (Primary) ---
spring.datasource.csrd.url=${SPRING_DATASOURCE_CSRD_URL}
spring.datasource.csrd.username=${SPRING_DATASOURCE_CSRD_USERNAME}
spring.datasource.csrd.password=${SPRING_DATASOURCE_CSRD_PASSWORD}
# Add other properties specific to this datasource if needed, e.g., driver class name, connection pool settings
# spring.datasource.csrd.driver-class-name=org.postgresql.Driver

# --- VectorDB Database Configuration (Secondary) ---
spring.datasource.vectordb.url=${SPRING_DATASOURCE_VECTORDB_URL}
spring.datasource.vectordb.username=${SPRING_DATASOURCE_VECTORDB_USERNAME}
spring.datasource.vectordb.password=${SPRING_DATASOURCE_VECTORDB_PASSWORD}


server.port=${PORT:8080}

app.frontend.url=https://parabella.app/

