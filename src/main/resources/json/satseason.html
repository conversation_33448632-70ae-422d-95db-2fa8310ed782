<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <title>sat.season @ radtanke - Social Media Video</title>
    <!-- Include p5.js library -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/p5.js/1.4.0/p5.js"></script>
    <!-- Include CCapture.js for recording -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/ccapture.js/1.1.0/CCapture.all.min.js"></script>
</head>
<body>
<script>
    let eventDate;
    let capturer;
    let recording = false;
    const maxFrames = 600; // 20 seconds at 30 fps

    function setup() {
        // Create a 1080x1080 canvas (ideal for social media)
        createCanvas(1080, 1080);
        frameRate(30);
        angleMode(RADIANS);
        textAlign(CENTER, CENTER);
        // Set your event date (adjust as needed)
        eventDate = new Date("June 15, 2025 22:00:00").getTime();

        // Initialize CCapture to record as a WebM video at 30 fps
        capturer = new CCapture({ format: 'webm', framerate: 30, verbose: true });
        capturer.start();
        recording = true;
    }

    function draw() {
        background(0);
        drawBackgroundStars();
        drawCountdown();
        drawDishAndRays();
        drawTextOverlay();

        // Capture the current canvas frame if recording
        if (recording) {
            capturer.capture(document.getElementById('defaultCanvas0'));
            if (frameCount >= maxFrames) {
                noLoop();
                capturer.stop();
                capturer.save();
                recording = false;
                console.log("Recording stopped and saved.");
            }
        }
    }

    // Draw a static starry background
    function drawBackgroundStars() {
        randomSeed(99); // Ensure stars stay the same each frame
        noStroke();
        fill(255);
        for (let i = 0; i < 100; i++) {
            let x = random(width);
            let y = random(height);
            let r = random(1, 3);
            circle(x, y, r);
        }
    }

    // Draw a countdown timer in the upper-right corner
    function drawCountdown() {
        let now = new Date().getTime();
        let diff = eventDate - now;
        let countdownText = "";
        if (diff > 0) {
            let days = floor(diff / (1000 * 60 * 60 * 24));
            let hours = floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
            let minutes = floor((diff % (1000 * 60 * 60)) / (1000 * 60));
            let seconds = floor((diff % (1000 * 60)) / 1000);
            countdownText = nf(days, 2) + "d " + nf(hours, 2) + "h " + nf(minutes, 2) + "m " + nf(seconds, 2) + "s";
        } else {
            countdownText = "Event Started";
        }
        push();
        textSize(24);
        fill(255);
        textAlign(RIGHT, TOP);
        text(countdownText, width - 20, 20);
        pop();
    }

    // Draw the satellite dish and the emanating rays
    function drawDishAndRays() {
        // Dish center
        let dishX = width / 2;
        let dishY = height / 2;
        // Compute rotation angle from dish center to mouse position
        let angleToMouse = atan2(mouseY - dishY, mouseX - dishX);

        // Draw the dish with rotation
        push();
        translate(dishX, dishY);
        rotate(angleToMouse);
        drawDish();
        pop();

        // Compute feed horn (antenna endpoint) position in global coordinates.
        // In the dish drawing, the feed horn is at (40, -30) in local dish coordinates.
        let feedHornLocal = createVector(40, -30);
        let feedHornGlobal = feedHornLocal.copy();
        feedHornGlobal.rotate(angleToMouse);
        feedHornGlobal.add(createVector(dishX, dishY));

        // Draw the feed horn as a bright circle
        push();
        noStroke();
        fill(255);
        circle(feedHornGlobal.x, feedHornGlobal.y, 12);
        pop();

        // Fixed target position for rays (simulate the countdown timer location)
        let targetX = width - 50;
        let targetY = 50;
        // Compute the directional vector from the feed horn to the target
        let dir = createVector(targetX - feedHornGlobal.x, targetY - feedHornGlobal.y);
        // Compute a perpendicular vector for offsetting multiple rays
        let perp = createVector(-dir.y, dir.x).normalize();

        // Set dashed line style for the rays
        drawingContext.setLineDash([5, 5]);
        stroke(255);
        strokeWeight(2);
        // Draw five rays with perpendicular offsets (-2, -1, 0, 1, 2)
        let offsets = [-2, -1, 0, 1, 2];
        let spacing = 5;
        for (let i = 0; i < offsets.length; i++) {
            let offset = offsets[i] * spacing;
            let start = createVector(feedHornGlobal.x, feedHornGlobal.y).add(p5.Vector.mult(perp, offset));
            let end = createVector(targetX, targetY).add(p5.Vector.mult(perp, offset));
            line(start.x, start.y, end.x, end.y);
        }
        // Reset dash style
        drawingContext.setLineDash([]);
    }

    // Draw the satellite dish using p5.js shapes in its local coordinate system
    function drawDish() {
        // Draw the dish surface (an ellipse)
        noFill();
        stroke(0, 255, 255);
        strokeWeight(2);
        ellipse(0, 20, 160, 100);
        // Draw the dish stand (a rectangle)
        fill(0, 255, 255);
        noStroke();
        rect(-5, 70, 10, 40);
        // Draw the feed arm (a line from dish center to feed horn)
        stroke(0, 255, 255);
        strokeWeight(2);
        line(0, 20, 40, -30);
        // Draw a decorative dashed line across the dish
        stroke(0, 255, 255);
        strokeWeight(1);
        drawingContext.setLineDash([4, 3]);
        line(-70, 20, 70, 20);
        drawingContext.setLineDash([]);
    }

    // Draw text overlays with event name and details
    function drawTextOverlay() {
        push();
        textSize(48);
        fill(0, 255, 255);
        textAlign(CENTER, CENTER);
        text("sat.season @ radtanke", width / 2, height * 0.8);
        pop();

        push();
        textSize(32);
        fill(255);
        textAlign(CENTER, CENTER);
        text("15.06.25 | 22:00 - 04:00", width / 2, height * 0.85);
        pop();
    }
</script>
</body>
</html>
