<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
    <title>sat.season @ radtanke</title>
    <style>
        /* Basic Reset */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        html, body {
            height: 100%;
            background: #000;
            color: #fff;
            font-family: 'Courier New', Courier, monospace;
            overflow-x: hidden;
        }

        /* Fixed Countdown Timer (Upper Right) */
        #countdown {
            position: fixed;
            top: 10px;
            right: 10px;
            font-size: 1em;
            z-index: 10;
            background: rgba(0, 0, 0, 0.5);
            padding: 5px 10px;
            border-radius: 5px;
        }

        /* Fullscreen SVG Overlay for Rays */
        #beamOverlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100vw;
            height: 100vh;
            pointer-events: none;
            z-index: 5;
        }
        .beam-line {
            stroke: #fff;
            stroke-width: 2;
            stroke-dasharray: 5,5;
            animation: dashAnim 1s linear infinite;
        }
        @keyframes dashAnim {
            to { stroke-dashoffset: 20; }
        }

        /* Hero Section */
        #hero {
            position: relative;
            height: 100vh;
            text-align: center;
            background: radial-gradient(circle, #222 0%, #000 70%);
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
        }

        /* Hero Text */
        .hero-text {
            position: relative;
            z-index: 7;
            font-size: 2.5em;
            margin-bottom: 20px;
        }

        /* Interactive Satellite Dish */
        .dish-container {
            position: absolute;
            width: 250px;
            height: 250px;
            top: 50%;
            left: 50%;
            /* Center the container */
            transform: translate(-50%, -50%);
            transform-origin: center center;
            z-index: 6;
            opacity: 0.25;
            transition: opacity 0.3s;
        }
        .dish-container:hover {
            opacity: 0.5;
        }
        .dish-svg {
            width: 100%;
            height: 100%;
            transform-origin: 50% 50%;
        }

        /* Details Section */
        #details {
            padding: 40px 20px;
            background: #111;
            text-align: center;
        }
        #details h3 {
            font-size: 2em;
            margin-bottom: 0.5em;
        }
        #details p {
            font-size: 1.1em;
            margin: 1em 0;
            line-height: 1.5em;
        }
        .cta {
            display: inline-block;
            margin-top: 1em;
            padding: 12px 24px;
            border: 2px solid #fff;
            background: transparent;
            color: #fff;
            text-decoration: none;
            transition: background 0.3s, color 0.3s;
            cursor: pointer;
        }
        .cta:hover {
            background: #fff;
            color: #000;
        }

        /* Footer */
        footer {
            background: #000;
            padding: 20px;
            text-align: center;
        }
        footer a {
            color: #0ff;
            text-decoration: none;
            margin: 0 10px;
        }
    </style>
</head>
<body>
<!-- Countdown Timer -->
<div id="countdown"></div>

<!-- SVG Overlay for Rays -->
<svg id="beamOverlay">
    <!-- Five rays: indices -2, -1, 0, 1, 2 -->
    <line class="beam-line" id="beamLine-2"></line>
    <line class="beam-line" id="beamLine-1"></line>
    <line class="beam-line" id="beamLine0"></line>
    <line class="beam-line" id="beamLine1"></line>
    <line class="beam-line" id="beamLine2"></line>
</svg>

<!-- Hero Section -->
<section id="hero">
    <div class="hero-text">
        sat.season @ radtanke
    </div>
    <!-- Interactive Satellite Dish -->
    <div class="dish-container" id="dishContainer">
        <svg class="dish-svg" id="satDish" viewBox="0 0 200 200">
            <!-- Main Dish -->
            <ellipse cx="100" cy="110" rx="80" ry="50" fill="none" stroke="#0ff" stroke-width="2"/>
            <!-- Dish Stand -->
            <rect x="95" y="110" width="10" height="60" fill="#0ff" />
            <!-- Feed Arm -->
            <line x1="100" y1="80" x2="140" y2="40" stroke="#0ff" stroke-width="2"/>
            <!-- Feed Horn (Antenna Endpoint) -->
            <circle id="feedHorn" cx="140" cy="40" r="6" fill="#fff" />
            <!-- Decorative Line -->
            <line x1="20" y1="110" x2="180" y2="110" stroke="#0ff" stroke-width="1" stroke-dasharray="4,3"/>
        </svg>
    </div>
</section>

<!-- Details Section -->
<section id="details">
    <h3>About the Event</h3>
    <p>
        Experience a night where art, music, and technology converge.
        <em>sat.season</em> at radtanke offers experimental techno, captivating visuals,
        and interactive art that blurs the boundaries of creativity.
    </p>
    <p>
        Let the minimal design and playful interactivity transport you into a futuristic realm.
    </p>
    <a href="#" class="cta">Get Tickets</a>
</section>

<!-- Footer -->
<footer>
    <p>Connect with us:</p>
    <a href="#" target="_blank">Instagram</a>
    <a href="#" target="_blank">Facebook</a>
    <a href="#" target="_blank">Twitter</a>
</footer>

<script>
    // -------------------------
    // Countdown Timer Setup
    // -------------------------
    const countdownEl = document.getElementById("countdown");
    const eventDate = new Date("June 15, 2025 22:00:00").getTime();
    setInterval(() => {
        const now = new Date().getTime();
        const distance = eventDate - now;
        const days = Math.floor(distance / (1000 * 60 * 60 * 24));
        const hours = Math.floor((distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
        const minutes = Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60));
        const seconds = Math.floor((distance % (1000 * 60)) / 1000);
        if (distance >= 0) {
            countdownEl.innerHTML = `${days}d ${hours}h ${minutes}m ${seconds}s`;
        } else {
            countdownEl.innerHTML = "Event Started";
        }
    }, 1000);

    // -------------------------
    // Interactive Dish & Rays
    // -------------------------
    const dishContainer = document.getElementById("dishContainer");
    const feedHornEl = document.getElementById("feedHorn");
    const beamLines = [
        document.getElementById("beamLine-2"),
        document.getElementById("beamLine-1"),
        document.getElementById("beamLine0"),
        document.getElementById("beamLine1"),
        document.getElementById("beamLine2")
    ];
    const offsetSpacing = 5; // pixel offset for each beam

    // Update rays so they originate at the feed horn endpoint and extend to the timer.
    function updateRays() {
        // Get feed horn's center (global coordinates)
        const hornRect = feedHornEl.getBoundingClientRect();
        const hornX = hornRect.left + hornRect.width / 2;
        const hornY = hornRect.top + hornRect.height / 2;

        // Get countdown timer's center (global coordinates)
        const timerRect = countdownEl.getBoundingClientRect();
        const timerX = timerRect.left + timerRect.width / 2;
        const timerY = timerRect.top + timerRect.height / 2;

        // Compute the main beam vector from horn to timer
        const dx = timerX - hornX;
        const dy = timerY - hornY;
        const angle = Math.atan2(dy, dx);
        // Compute perpendicular vector (normalized)
        const length = Math.sqrt(dx*dx + dy*dy);
        const perpX = ( -dy / length );
        const perpY = ( dx / length );

        // For each beam, compute an offset multiplier (-2, -1, 0, 1, 2)
        const offsets = [-2, -1, 0, 1, 2];
        offsets.forEach((i, index) => {
            const offsetX = i * offsetSpacing * perpX;
            const offsetY = i * offsetSpacing * perpY;
            const startX = hornX + offsetX;
            const startY = hornY + offsetY;
            const endX = timerX + offsetX;
            const endY = timerY + offsetY;
            beamLines[index].setAttribute("x1", startX);
            beamLines[index].setAttribute("y1", startY);
            beamLines[index].setAttribute("x2", endX);
            beamLines[index].setAttribute("y2", endY);
        });
    }

    // Rotate the dish to follow the mouse and update rays accordingly.
    document.addEventListener("mousemove", (e) => {
        // Get dish container center (for rotation)
        const rect = dishContainer.getBoundingClientRect();
        const centerX = rect.left + rect.width / 2;
        const centerY = rect.top + rect.height / 2;
        const angle = Math.atan2(e.clientY - centerY, e.clientX - centerX);
        const degrees = angle * (180 / Math.PI);
        dishContainer.style.transform = `translate(-50%, -50%) rotate(${degrees}deg)`;
        updateRays();
    });

    // Update rays on window resize and initial load.
    window.addEventListener("resize", updateRays);
    window.addEventListener("load", updateRays);
</script>
</body>
</html>
