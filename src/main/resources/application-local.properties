spring.datasource.csrd.url=*****************************************
spring.datasource.csrd.username=postgres
spring.datasource.csrd.password=admin


# === Secondary Database Configuration (e.g., 'vectordb' DB) ===

spring.datasource.vectordb.url=*****************************************
spring.datasource.vectordb.username=postgres
spring.datasource.vectordb.password=admin


spring.jpa.hibernate.ddl-auto=update
spring.jpa.show-sql=true
# --- Local Frontend URL ---
app.frontend.url=http://localhost:5173

# App Properties
JWT_SECRET= ======================Parabella=Spring===========================


OPEN_AI_API_KEY=********************************************************************************************************************************************************************
SPRING_MAIL_USERNAME=<EMAIL>
SPRING_MAIL_PASSWORD=lwoqmydulqsxxjpg
