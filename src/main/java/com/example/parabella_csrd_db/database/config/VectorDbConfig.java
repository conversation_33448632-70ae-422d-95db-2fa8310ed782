package com.example.parabella_csrd_db.database.config;

import jakarta.persistence.EntityManagerFactory; // For Spring Boot 3+
// import javax.persistence.EntityManagerFactory; // For Spring Boot 2.x
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.jdbc.DataSourceProperties;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.orm.jpa.EntityManagerFactoryBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.orm.jpa.JpaTransactionManager;
import org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import javax.sql.DataSource;

@Configuration
@EnableTransactionManagement
@EnableJpaRepositories(
        entityManagerFactoryRef = "vectordbEntityManagerFactory",
        transactionManagerRef = "vectordbTransactionManager",
        basePackages = {"com.example.parabella_csrd_db.database.vectordatabase.repository"} // Path to VectorDB repositories
)
public class VectorDbConfig {

    @Bean(name = "vectordbDataSourceProperties")
    @ConfigurationProperties("spring.datasource.vectordb")
    public DataSourceProperties vectordbDataSourceProperties() {
        return new DataSourceProperties();
    }

    @Bean(name = "vectordbDataSource")
    @ConfigurationProperties("spring.datasource.vectordb.hikari")
    public DataSource vectordbDataSource(@Qualifier("vectordbDataSourceProperties") DataSourceProperties properties) {
        return properties.initializeDataSourceBuilder().build();
    }

    @Bean(name = "vectordbEntityManagerFactory")
    public LocalContainerEntityManagerFactoryBean vectordbEntityManagerFactory(
            EntityManagerFactoryBuilder builder,
            @Qualifier("vectordbDataSource") DataSource dataSource) {
        return builder
                .dataSource(dataSource)
                .packages("com.example.parabella_csrd_db.database.vectordatabase.model") // Path to VectorDB entities
                .persistenceUnit("vectorPU") // Persistence Unit name
                .build();
    }

    @Bean(name = "vectordbTransactionManager")
    public PlatformTransactionManager vectordbTransactionManager(
            @Qualifier("vectordbEntityManagerFactory") EntityManagerFactory entityManagerFactory) {
        return new JpaTransactionManager(entityManagerFactory);
    }
}