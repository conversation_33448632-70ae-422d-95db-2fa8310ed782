package com.example.parabella_csrd_db.database.config;

import jakarta.persistence.EntityManagerFactory;
import org.hibernate.cfg.AvailableSettings; // Import Hibernate AvailableSettings
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.jdbc.DataSourceProperties;
import org.springframework.boot.autoconfigure.orm.jpa.JpaProperties;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.orm.jpa.EntityManagerFactoryBuilder;
// Import the naming strategy classes
import org.springframework.boot.orm.jpa.hibernate.SpringImplicitNamingStrategy;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.orm.jpa.JpaTransactionManager;
import org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import javax.sql.DataSource;
import java.util.HashMap;
import java.util.Map;

@Configuration
@EnableTransactionManagement
@EnableJpaRepositories(
        entityManagerFactoryRef = "csrdEntityManagerFactory",
        transactionManagerRef = "csrdTransactionManager",
        basePackages = {"com.example.parabella_csrd_db.database.maindatabase.repository"}
)
public class MainDbConfig {

    @Primary
    @Bean(name = "csrdDataSourceProperties")
    @ConfigurationProperties("spring.datasource.csrd")
    public DataSourceProperties csrdDataSourceProperties() {
        return new DataSourceProperties();
    }

    @Primary
    @Bean(name = "csrdDataSource")
    @ConfigurationProperties("spring.datasource.csrd.hikari")
    public DataSource csrdDataSource(@Qualifier("csrdDataSourceProperties") DataSourceProperties properties) {
        return properties.initializeDataSourceBuilder().build();
    }

    // In MainDbConfig.java
// Bean needed to bind spring.jpa.* properties
    @Primary
    @Bean(name = "jpaProperties")
    @ConfigurationProperties("spring.jpa")
    public JpaProperties jpaProperties() {
        return new JpaProperties();
    }

    @Primary
    @Bean(name = "csrdEntityManagerFactory")
    public LocalContainerEntityManagerFactoryBean csrdEntityManagerFactory(
            EntityManagerFactoryBuilder builder,
            @Qualifier("csrdDataSource") DataSource dataSource,
            @Qualifier("jpaProperties") JpaProperties jpaProperties) { // Inject JpaProperties

        // Get the map of all hibernate properties from application.properties
        Map<String, String> hibernateProps = jpaProperties.getProperties();

        return builder
                .dataSource(dataSource)
                .packages("com.example.parabella_csrd_db.database.csrd.model")
                .persistenceUnit("csrdPU")
                // Pass all the Hibernate properties at once. This now includes hbm2ddl.auto.
                .properties(hibernateProps)
                .build();
    }

    @Primary
    @Bean(name = "csrdTransactionManager")
    public PlatformTransactionManager csrdTransactionManager(
            @Qualifier("csrdEntityManagerFactory") EntityManagerFactory entityManagerFactory) {
        return new JpaTransactionManager(entityManagerFactory);
    }
}