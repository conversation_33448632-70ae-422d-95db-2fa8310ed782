package com.example.parabella_csrd_db.database.config;

import jakarta.persistence.EntityManagerFactory;
import org.hibernate.cfg.AvailableSettings; // Import Hibernate AvailableSettings
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.jdbc.DataSourceProperties;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.orm.jpa.EntityManagerFactoryBuilder;
// Import the naming strategy classes
import org.springframework.boot.orm.jpa.hibernate.SpringImplicitNamingStrategy;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.orm.jpa.JpaTransactionManager;
import org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import javax.sql.DataSource;
import java.util.HashMap;
import java.util.Map;

@Configuration
@EnableTransactionManagement
@EnableJpaRepositories(
        entityManagerFactoryRef = "csrdEntityManagerFactory",
        transactionManagerRef = "csrdTransactionManager",
        basePackages = {"com.example.parabella_csrd_db.database.maindatabase.repository"}
)
public class MainDbConfig {

    @Primary
    @Bean(name = "csrdDataSourceProperties")
    @ConfigurationProperties("spring.datasource.csrd")
    public DataSourceProperties csrdDataSourceProperties() {
        return new DataSourceProperties();
    }

    @Primary
    @Bean(name = "csrdDataSource")
    @ConfigurationProperties("spring.datasource.csrd.hikari")
    public DataSource csrdDataSource(@Qualifier("csrdDataSourceProperties") DataSourceProperties properties) {
        return properties.initializeDataSourceBuilder().build();
    }

    @Primary
    @Bean(name = "csrdEntityManagerFactory")
    public LocalContainerEntityManagerFactoryBean csrdEntityManagerFactory(
            EntityManagerFactoryBuilder builder,
            @Qualifier("csrdDataSource") DataSource dataSource) {

        Map<String, Object> jpaProperties = new HashMap<>();
        // Provide instances instead of class names

        jpaProperties.put(AvailableSettings.IMPLICIT_NAMING_STRATEGY, new SpringImplicitNamingStrategy());
        // You might also want to set the dialect if auto-detection is an issue, though usually not necessary for this problem
        // jpaProperties.put(AvailableSettings.DIALECT, "org.hibernate.dialect.PostgreSQLDialect");

        return builder
                .dataSource(dataSource)
                .packages("com.example.parabella_csrd_db.database.maindatabase.model")
                .persistenceUnit("csrdPU")
                .properties(jpaProperties) // Pass the map with instances
                .build();
    }

    @Primary
    @Bean(name = "csrdTransactionManager")
    public PlatformTransactionManager csrdTransactionManager(
            @Qualifier("csrdEntityManagerFactory") EntityManagerFactory entityManagerFactory) {
        return new JpaTransactionManager(entityManagerFactory);
    }
}