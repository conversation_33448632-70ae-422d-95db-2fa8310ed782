package com.example.parabella_csrd_db.database.config;

import jakarta.persistence.EntityManagerFactory; // For Spring Boot 3+
// import javax.persistence.EntityManagerFactory; // For Spring Boot 2.x
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.jdbc.DataSourceProperties;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.orm.jpa.EntityManagerFactoryBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.orm.jpa.JpaTransactionManager;
import org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import javax.sql.DataSource;

@Configuration
@EnableTransactionManagement
@EnableJpaRepositories(
        entityManagerFactoryRef = "csrdEntityManagerFactory",
        transactionManagerRef = "csrdTransactionManager",
        basePackages = {"com.example.parabella_csrd_db.database.maindatabase.repository"} // Path to CSRD repositories
)
public class MainDbConfig {

    @Primary
    @Bean(name = "csrdDataSourceProperties")
    @ConfigurationProperties("spring.datasource.csrd")
    public DataSourceProperties csrdDataSourceProperties() {
        return new DataSourceProperties();
    }

    @Primary
    @Bean(name = "csrdDataSource")
    @ConfigurationProperties("spring.datasource.csrd.hikari")
    public DataSource csrdDataSource(@Qualifier("csrdDataSourceProperties") DataSourceProperties properties) {
        return properties.initializeDataSourceBuilder().build();
    }

    @Primary
    @Bean(name = "csrdEntityManagerFactory")
    public LocalContainerEntityManagerFactoryBean csrdEntityManagerFactory(
            EntityManagerFactoryBuilder builder,
            @Qualifier("csrdDataSource") DataSource dataSource) {
        return builder
                .dataSource(dataSource)
                .packages("com.example.parabella_csrd_db.database.maindatabase.model") // Path to CSRD entities
                .persistenceUnit("csrdPU") // Persistence Unit name
                .build();
    }

    @Primary
    @Bean(name = "csrdTransactionManager")
    public PlatformTransactionManager csrdTransactionManager(
            @Qualifier("csrdEntityManagerFactory") EntityManagerFactory entityManagerFactory) {
        return new JpaTransactionManager(entityManagerFactory);
    }
}