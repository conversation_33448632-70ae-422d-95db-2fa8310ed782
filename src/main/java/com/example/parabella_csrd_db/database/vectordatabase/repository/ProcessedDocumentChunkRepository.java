package com.example.parabella_csrd_db.database.vectordatabase.repository;


import com.example.parabella_csrd_db.database.vectordatabase.model.ProcessedDocumentChunk;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Set;

@Repository
public interface ProcessedDocumentChunkRepository extends JpaRepository<ProcessedDocumentChunk, Long> {

    // --- NEW Query Method ---
    /**
     * Finds chunks matching the disclosure requirement, source ID, AND project ID.
     * Uses JPA derived query naming convention.
     *
     * @param disclosureRequirement The ESRS disclosure requirement code (e.g., "E1-1 DR")
     * @param sourceId The specific ESRS source ID (e.g., "E1-1")
     * @param projectId The ID of the current project context.
     * @return A list of matching document chunks.
     */
    List<ProcessedDocumentChunk> findByDisclosureRequirementAndSourceIdAndProjectId(
            String disclosureRequirement,
            String sourceId,
            Long projectId
    );

    // More specific query example if needed:
    @Query("SELECT c FROM ProcessedDocumentChunk c WHERE c.projectId = :projectId " +
            "AND c.disclosureRequirement = :disclosureRequirement " +
            "AND c.sourceId = :sourceId " +
            "ORDER BY c.matchScore DESC NULLS LAST, c.createdAt DESC") // Example ordering
    List<ProcessedDocumentChunk> findRelevantChunks(
            @Param("projectId") Long projectId,
            @Param("disclosureRequirement") String disclosureRequirement,
            @Param("sourceId") String sourceId
    );

    // Find by Project ID only (useful for general project context)
    List<ProcessedDocumentChunk> findByProjectId(Long projectId);
    // -----------------------
    // Efficiently find all chunks for a project with non-null source_id
    List<ProcessedDocumentChunk> findByProjectIdAndSourceIdIsNotNull(Long projectId);

    // More Optimized: Find only distinct source_ids present for a project
    @Query("SELECT DISTINCT p.sourceId FROM ProcessedDocumentChunk p WHERE p.projectId = :projectId AND p.sourceId IS NOT NULL")
    Set<String> findDistinctSourceIdsByProjectId(@Param("projectId") Long projectId);

    // Find chunks by project and a specific source_id (if needed for details)
    List<ProcessedDocumentChunk> findByProjectIdAndSourceId(Long projectId, String sourceId);

    // Keep the vector search method if it's used elsewhere
    // @Query(...)
    // List<ProcessedDocumentChunk> findNearestChunksBySummaryEmbedding(...);
}