package com.example.parabella_csrd_db.database.vectordatabase.model;


import jakarta.persistence.*;
import lombok.*;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.type.SqlTypes;
import java.time.LocalDateTime;
import java.util.Map;


/**
 * Maps to the existing table  public.esrs_data
 */

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Entity
@Table(name = "esrs_data")
public class EsrsDatapoint {

    /* ---------- primary key ---------- */

    @Id
    @SequenceGenerator(
            name = "esrs_data_id_seq",
            sequenceName = "esrs_data_id_seq",
            allocationSize = 1                 // match your DB sequence
    )
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "esrs_data_id_seq")
    private Long id;

    /* ---------- simple varchar / text columns ---------- */

    @Column(name = "source_id", length = 255)
    private String sourceId;

    @Column(name = "sheet_name", length = 255)
    private String sheetName;

    @Column(name = "esrs_standard", length = 50)
    private String esrsStandard;

    @Column(name = "disclosure_requirement", length = 255)
    private String disclosureRequirement;

    @Column(columnDefinition = "text")
    private String paragraph;

    @Column(name = "related_ar", columnDefinition = "text")
    private String relatedAr;

    @Column(name = "data_point_name", columnDefinition = "text")
    private String dataPointName;

    @Column(name = "data_type", length = 50)
    private String dataType;

    @Column(name = "data_response", columnDefinition = "text")
    private String dataResponse;

    @Column(name = "data_unit", length = 50)
    private String dataUnit;

    /* ---------- booleans ---------- */

    @Column(name = "conditional_dp")
    private Boolean conditionalDp;

    /* ---------- advanced / “non-standard” postgres types ---------- */

    /**
     * pgvector column.
     *  - Option 1 (quick): store as a raw String (`'{0.1,0.2, … }'`)
     *  - Option 2 (clean): write a custom Hibernate UserType or use the
     *    <a href="https://github.com/pgvector/pgvector-java">pgvector-hibernate</a> module
     *    and expose it as `Float[]` or `Vector` here.
     */
    @Column(columnDefinition = "vector")
    private String embedding;

    /**
     * JSONB column – requires the hibernate-types library.
     * Gradle:  implementation("com.vladmihalcea:hibernate-types-60:2.21.1")
     */
    @JdbcTypeCode(SqlTypes.JSON)
    private Map<String, Object> metadata;

    /* ---------- audit ---------- */

    @CreationTimestamp
    @Column(name = "created_at", updatable = false)
    private LocalDateTime createdAt;
}
