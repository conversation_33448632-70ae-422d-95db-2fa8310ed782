package com.example.parabella_csrd_db.database.vectordatabase.repository;

import com.example.parabella_csrd_db.database.vectordatabase.model.EsrsDatapoint;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;
import java.util.List;

@Repository
public interface EsrsDatapointRepository extends JpaRepository<EsrsDatapoint, Long> {
    // Find datapoints by disclosure requirement to link them to subtopics
    List<EsrsDatapoint> findByDisclosureRequirement(String disclosureRequirement);

    // Or more flexibly if disclosureRequirement in EsrsDatapoint might be more specific
    // e.g., EsrsDatapoint.disclosureRequirement = "E2-1a" and CsrdSubtopic.csrdSubtopicId = "E2-1"
    List<EsrsDatapoint> findByDisclosureRequirementStartingWith(String disclosureRequirementPrefix);

    // Optional: Find by standard if needed for filtering later
    List<EsrsDatapoint> findByEsrsStandard(String standard);

    // We usually fetch all ESRS datapoints as the baseline
    List<EsrsDatapoint> findAll();
}