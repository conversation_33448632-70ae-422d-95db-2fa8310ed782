package com.example.parabella_csrd_db.database.vectordatabase.model;

import jakarta.persistence.*;
import lombok.*;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.type.SqlTypes;
// import com.pgvector.PGvector; // Keep if you still use vector elsewhere

import java.time.OffsetDateTime;
// import java.util.Map; // No longer needed for Map
import java.util.List; // <<<--- IMPORT THIS

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Entity
@Table(name = "processed_document_chunks", indexes = { // Add indexes for filtering
        @Index(name = "idx_chunk_project_id", columnList = "project_id"),
        @Index(name = "idx_chunk_disc_req_src_id", columnList = "disclosure_requirement, source_id"),
        // Add the combined index if your DB creation script includes it
        @Index(name = "idx_chunk_project_disc_req_src_id", columnList = "project_id, disclosure_requirement, source_id")
})
public class ProcessedDocumentChunk {

    @Id
    @SequenceGenerator(
            name = "processed_document_chunks_id_seq",
            sequenceName = "processed_document_chunks_id_seq",
            allocationSize = 1
    )
    @GeneratedValue(
            strategy = GenerationType.SEQUENCE,
            generator = "processed_document_chunks_id_seq"
    )
    private Long id;

    // --- Association Columns ---
    @Column(name = "project_id", nullable = false)
    private Long projectId;

    @Column(name = "uploaded_by_user_id")
    private String uploadedByUserId;
    // -----------------------------

    @Column(name = "document_name", length = 255)
    private String documentName;

    @Column(name = "chunk_index")
    private Integer chunkIndex;

    @Column(name = "chunk_text", columnDefinition = "text")
    private String chunkText;

    @Column(name = "chunk_summary", columnDefinition = "text")
    private String chunkSummary;

    @Column(columnDefinition = "vector")
    private String embedding;

    @Column(name = "summary_embedding", columnDefinition = "vector")
    private String summaryEmbedding;

    @Column(name = "disclosure_requirement", length = 255)
    private String disclosureRequirement;

    @Column(name = "source_id", length = 255)
    private String sourceId;

    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "data_source_ids", columnDefinition = "jsonb")
    // private Map<String, Object> dataSourceIds; // <<<--- CHANGE FROM THIS
    private List<Object> dataSourceIds; // <<<--- TO THIS (Use List<String> if they are always strings)

    @Column(name = "match_score")
    private Float matchScore;

    @JdbcTypeCode(SqlTypes.JSON)
    @Column(columnDefinition = "jsonb")
    private Object metadata; // Keep as Object if metadata structure can vary

    @CreationTimestamp
    @Column(name = "created_at", updatable = false, columnDefinition = "timestamp with time zone")
    private OffsetDateTime createdAt;
}