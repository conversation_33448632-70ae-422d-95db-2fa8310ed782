// src/main/java/com/example/parabella_csrd_db/model/mithril/Iro.java
package com.example.parabella_csrd_db.database.maindatabase.model.mithril;

import com.example.parabella_csrd_db.database.maindatabase.model.audit_trail.BaseAuditedEntity;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.envers.Audited;

@Audited
@Entity
@Table(name = "iro")
@Getter
@Setter
@NoArgsConstructor
public class Iro extends BaseAuditedEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    private String name;

    private String iroType; // negative or positive

    @ManyToOne
    @JoinColumn(name = "esrs_topic_selection_id")
    private EsrsTopicSelection esrsTopicSelection;

    @ManyToOne
    @JoinColumn(name = "stakeholder_id")
    private Stakeholder stakeholder;

    @ManyToOne
    @JoinColumn(name = "company_id", nullable = false)
    private Company company;

    // Cascade deletion so that when an IRO is deleted its evaluation is removed too
    @OneToOne(mappedBy = "iro", cascade = CascadeType.ALL, orphanRemoval = true)
    private IroEvaluation iroEvaluation;
}
