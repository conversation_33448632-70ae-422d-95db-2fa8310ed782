package com.example.parabella_csrd_db.database.maindatabase.repository.mithril;

import com.example.parabella_csrd_db.database.maindatabase.model.mithril.Iro;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface IroRepository extends JpaRepository<Iro, Long> {
    List<Iro> findAllByCompanyId(Long companyId);
    List<Iro> findByStakeholderId(Long stakeholderId);
}
