// src/main/java/com/example/parabella_csrd_db/model/notifications/Notification.java

package com.example.parabella_csrd_db.database.maindatabase.model.notification;

import com.example.parabella_csrd_db.database.maindatabase.model.audit_trail.BaseAuditedEntity;
import com.example.parabella_csrd_db.database.maindatabase.model.mithril.Project;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.envers.Audited;

import java.time.LocalDateTime;

@Getter
@Setter
@Audited
@Entity
@Table(name = "notifications")
public class Notification extends BaseAuditedEntity {

    @Id
    @GeneratedValue(strategy=GenerationType.IDENTITY)
    private Long id;

    private String message;

    private boolean read;

    @Column(name = "created_at")
    private LocalDateTime createdAt;

    @Column(name = "read_at")
    private LocalDateTime readAt;

    // Associate with Project instead of User
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "project_id")
    private Project project;

    // Constructors
    public Notification() {}

    public Notification(String message, Project project) {
        this.message = message;
        this.project = project;
        this.read = false;
        this.createdAt = LocalDateTime.now();
    }


}
