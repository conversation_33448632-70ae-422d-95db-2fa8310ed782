// src/main/java/com/yourpackage/repository/IndustryRepository.java

package com.example.parabella_csrd_db.database.maindatabase.repository.mithril;

import com.example.parabella_csrd_db.database.maindatabase.model.mithril.Industry;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface IndustryRepository extends JpaRepository<Industry, Long> {
    Optional<Industry> findByName(String name);
}
