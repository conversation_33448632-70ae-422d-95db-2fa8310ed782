package com.example.parabella_csrd_db.database.maindatabase.model.taxonomy;



import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Entity
public class TaxonomyActivity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    private String code;        // e.g., "4.2"
    private String title;       // e.g., "Electricity generation from solar"
    private String criteria;    // e.g., "Must meet certain thresholds..."

    @ManyToOne
    @JoinColumn(name = "category_id")
    private TaxonomyCategory category;

    public TaxonomyActivity() {}

    public TaxonomyActivity(String code, String title, String criteria, TaxonomyCategory category) {
        this.code = code;
        this.title = title;
        this.criteria = criteria;
        this.category = category;
    }


}


