
package com.example.parabella_csrd_db.database.maindatabase.repository.mithril;


import com.example.parabella_csrd_db.database.maindatabase.model.mithril.ValueChain;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface ValueChainRepository extends JpaRepository<ValueChain, Long> {
    Optional<ValueChain> findByValueChainKeyAndIndustry_Name(String valueChainKey, String industryName);
    List<ValueChain> findByIndustry_Name(String industryName);
}
