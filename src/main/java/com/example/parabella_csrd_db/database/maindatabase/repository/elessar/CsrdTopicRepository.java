package com.example.parabella_csrd_db.database.maindatabase.repository.elessar;

import com.example.parabella_csrd_db.database.maindatabase.model.elessar.dashboard.CsrdTopic;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.List;
import java.util.Optional;

public interface CsrdTopicRepository extends JpaRepository<CsrdTopic, Long> {

    Optional<CsrdTopic> findByCode(String code);
    // Fetch topics with their subtopics eagerly to avoid N+1 queries later
    // This assumes subtopics are fetched lazily by default in CsrdTopic entity
    @Query("SELECT DISTINCT t FROM CsrdTopic t LEFT JOIN FETCH t.subtopics")
    List<CsrdTopic> findAllWithSubtopics();
}
