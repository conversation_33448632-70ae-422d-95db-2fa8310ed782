// src/main/java/com/yourpackage/entity/Industry.java

package com.example.parabella_csrd_db.database.maindatabase.model.mithril;

import com.example.parabella_csrd_db.database.maindatabase.model.audit_trail.BaseAuditedEntity;
import jakarta.persistence.*;
import org.hibernate.envers.Audited;

import java.util.HashSet;
import java.util.Set;

@Audited
@Entity
@Table(name = "industries", uniqueConstraints = {
        @UniqueConstraint(columnNames = {"name"})
})
public class Industry extends BaseAuditedEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(nullable = false, unique = true)
    private String name;

    // One-to-Many relationship with ValueChain
    @OneToMany(mappedBy = "industry", cascade = CascadeType.ALL, orphanRemoval = true)
    private Set<ValueChain> valueChains = new HashSet<>();

    // Constructors
    public Industry() {}

    public Industry(String name) {
        this.name = name;
    }

    // Getters and Setters

    public Long getId() {
        return id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Set<ValueChain> getValueChains() {
        return valueChains;
    }

    public void setValueChains(Set<ValueChain> valueChains) {
        this.valueChains = valueChains;
    }

    // Utility methods to manage bi-directional relationship
    public void addValueChain(ValueChain valueChain) {
        valueChains.add(valueChain);
        valueChain.setIndustry(this);
    }

    public void removeValueChain(ValueChain valueChain) {
        valueChains.remove(valueChain);
        valueChain.setIndustry(null);
    }

    @Override
    public String toString() {
        return "Industry{" +
                "id=" + id +
                ", name='" + name + '\'' +
                '}';
    }
}
