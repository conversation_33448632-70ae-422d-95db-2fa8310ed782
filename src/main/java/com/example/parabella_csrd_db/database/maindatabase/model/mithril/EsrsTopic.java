
package com.example.parabella_csrd_db.database.maindatabase.model.mithril;

import com.example.parabella_csrd_db.database.maindatabase.model.audit_trail.BaseAuditedEntity;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.envers.Audited;

@Getter
@Setter
@Audited
@Entity
public class EsrsTopic extends BaseAuditedEntity {

    @Id
    private Long id;

    private String area;

    @Column(name = "esrs_code")
    private String esrsCode;

    private String topic;

    private String subtopic;

    @Column(name = "sub_sub_topic")
    private String subSubTopic;



}
