package com.example.parabella_csrd_db.database.maindatabase.model.elessar.dashboard;


import com.fasterxml.jackson.annotation.JsonIgnore;
import jakarta.persistence.*;

@Entity
@Table(name = "csrd_field_option")
public class CsrdFieldOption {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @ManyToOne
    @JoinColumn(name = "field_id", nullable = false)
    @JsonIgnore
    private CsrdField field;

    private String optionValue;

    public CsrdFieldOption() {}

    public Long getId() {
        return id;
    }

    public void setId(Long id) { this.id = id; }

    public CsrdField getField() {
        return field;
    }

    public void setField(CsrdField field) {
        this.field = field;
    }

    public String getOptionValue() {
        return optionValue;
    }

    public void setOptionValue(String optionValue) {
        this.optionValue = optionValue;
    }
}
