package com.example.parabella_csrd_db.database.maindatabase.repository.mithril;


import com.example.parabella_csrd_db.database.maindatabase.model.mithril.CompanyGroup;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface CompanyGroupRepository extends JpaRepository<CompanyGroup, Long> {
    // Custom query methods (if any) can be added here

    // Example of finding a CompanyGroup by its name
    Optional<CompanyGroup> findByCompanyGroupName(String companyGroupName);

    // Example of finding a CompanyGroup by its VAT number
    Optional<CompanyGroup> findByCompanyGroupVAT(String companyGroupVAT);
}