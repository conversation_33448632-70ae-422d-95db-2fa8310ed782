package com.example.parabella_csrd_db.database.maindatabase.model.elessar.dashboard;


import jakarta.persistence.*;

import java.util.ArrayList;
import java.util.List;

@Entity
@Table(name = "csrd_field")
public class CsrdField {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @ManyToOne
    @JoinColumn(name = "section_id", nullable = false)
    private CsrdSubtopicSection section;

    private String fieldType; // e.g. "radioButtonGroup", "checkbox", "subSection", etc.

    @Column(columnDefinition = "TEXT")
    private String label;

    @OneToMany(mappedBy = "field", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<CsrdFieldOption> options = new ArrayList<>();

    @OneToMany(mappedBy = "parentField", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<CsrdConditionalField> conditionalChildren = new ArrayList<>();

    @OneToMany(mappedBy = "childField", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<CsrdConditionalField> conditionalParents = new ArrayList<>();

    public CsrdField() {}

    public Long getId() {
        return id;
    }

    public void setId(Long id) { this.id = id; }

    public CsrdSubtopicSection getSection() {
        return section;
    }

    public void setSection(CsrdSubtopicSection section) {
        this.section = section;
    }

    public String getFieldType() {
        return fieldType;
    }

    public void setFieldType(String fieldType) {
        this.fieldType = fieldType;
    }

    public String getLabel() {
        return label;
    }

    public void setLabel(String label) {
        this.label = label;
    }

    public List<CsrdFieldOption> getOptions() {
        return options;
    }

    public void setOptions(List<CsrdFieldOption> options) {
        this.options = options;
    }

    public List<CsrdConditionalField> getConditionalChildren() {
        return conditionalChildren;
    }

    public void setConditionalChildren(List<CsrdConditionalField> conditionalChildren) {
        this.conditionalChildren = conditionalChildren;
    }

    public List<CsrdConditionalField> getConditionalParents() {
        return conditionalParents;
    }

    public void setConditionalParents(List<CsrdConditionalField> conditionalParents) {
        this.conditionalParents = conditionalParents;
    }
}
