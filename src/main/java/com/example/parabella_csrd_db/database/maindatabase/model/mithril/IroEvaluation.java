// src/main/java/com/example/parabella_csrd_db/model/mithril/IroEvaluation.java
package com.example.parabella_csrd_db.database.maindatabase.model.mithril;

import com.example.parabella_csrd_db.database.maindatabase.model.audit_trail.BaseAuditedEntity;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.envers.Audited;

import java.time.LocalDateTime;

@Entity
@Table(name = "iro_evaluation")
@Getter
@Setter
@NoArgsConstructor
@Audited
public class IroEvaluation extends BaseAuditedEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    // --- Common Fields for both IRO types ---
    @Column(columnDefinition = "TEXT")
    private String description;

    @Column(name = "direct_indirect_impact")
    private String directIndirectImpact; // "direct", "indirect"

    @Column(name = "affected_area")
    private String affectedArea; // "own operation", "upstream value chain", "downstream value chain"

    @Column(name = "time_horizon")
    private String timeHorizon; // "short-term", "medium-term", "long-term"

    // --- Impact IRO Fields ---
    @Column(name = "positive_negative_impact")
    private String positiveNegativeImpact; // "positive", "negative"

    @Column(name = "actual_potential_impact")
    private String actualPotentialImpact; // "actual", "potential" (Previously named risk_chance)

    @Column(name = "human_rights_impact")
    private String humanRightsImpact; // "yes", "no"

    @Column(name = "scale")
    private Integer scale; // 1 to 5

    @Column(name = "scope")
    private Integer scope; // 1 to 5

    @Column(name = "irreversibility")
    private Integer irreversibility; // 1 to 5, only for negative impacts

    // --- Financial IRO Fields ---
    @Column(name = "risk_opportunity")
    private String riskOpportunity; // "risk", "opportunity"

    @Column(name = "financial_materiality_actual_impact")
    private Integer financialMaterialityActualImpact; // 1 to 5, Repurposed for "Financial Effect"

    @Column(name = "probability")
    private Double probability; // 0.2 to 1.0, Repurposed for "Probability" slider in Financial IRO

    // --- Calculated Result Fields ---
    @Column(name = "impact_materiality_potential_impact")
    private String impactMaterialityPotentialImpact; // Final calculated score for Impact IROs

    @Column(name = "financial_materiality_potential_impact")
    private String financialMaterialityPotentialImpact; // Final calculated score for Financial IROs

    @Column(name = "result_materiality_assessment")
    private String resultMaterialityAssessment; // "Relevant", "Not Relevant"

    // --- Metadata ---
    @Column(name = "last_modified_by")
    private String lastModifiedBy;

    @Column(name = "last_modified_at")
    private LocalDateTime lastModifiedAt;

    @Column(name = "is_locked")
    private Boolean isLocked;

    // --- Relationships ---
    @OneToOne
    @JoinColumn(name = "iro_id")
    private Iro iro;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "company_id")
    private Company company;

    /*
     * Obsolete fields that have been removed based on the new UI:
     * - effect (String)
     * - connection (String)
     * - basisOfAssessmentOfFinancialImpact (String)
     * - timeSpan (Double) - Replaced by timeHorizon (String)
     * - impactMaterialityActualImpact (String) - This is now a calculation result, not a stored field.
     */
}