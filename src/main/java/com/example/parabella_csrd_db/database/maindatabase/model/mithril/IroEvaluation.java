// src/main/java/com/example/parabella_csrd_db/model/mithril/IroEvaluation.java
package com.example.parabella_csrd_db.database.maindatabase.model.mithril;

import com.example.parabella_csrd_db.database.maindatabase.model.audit_trail.BaseAuditedEntity;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.envers.Audited;

import java.time.LocalDateTime;

@Entity
@Table(name = "iro_evaluation")
@Getter
@Setter
@NoArgsConstructor
@Audited
public class IroEvaluation extends BaseAuditedEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    // General Information
    @Column(name = "risk_chance")
    private String actualPotentialImpact; // "risk" or "chance"

    @Column(name = "affected_area")
    private String affectedArea; // "own operation", "upstream value chain", "downstream value chain"

    @Column(columnDefinition = "TEXT")
    private String description;

    @Column(columnDefinition = "TEXT")
    private String effect;

    @Column(name = "connection")
    private String connection; // "caused", "linked", "contributed"

    // Impact Materiality
    @Column(name = "scale")
    private Integer scale; // 1 to 5

    @Column(name = "scope")
    private Integer scope; // 1 to 5

    @Column(name = "irreversibility")
    private Integer irreversibility; // 1 to 5, optional

    @Column(name = "probability")
    private Double probability; // 0.2 to 1.0

    @Column(name = "impact_materiality_actual_impact")
    private String impactMaterialityActualImpact;

    @Column(name = "impact_materiality_potential_impact")
    private String impactMaterialityPotentialImpact;

    // Financial Materiality
    @Column(name = "basis_of_assessment_of_financial_impact", columnDefinition = "TEXT")
    private String basisOfAssessmentOfFinancialImpact;

    @Column(name = "financial_materiality_actual_impact")
    private Integer financialMaterialityActualImpact; // 1 to 5

    @Setter
    @Column(name = "financial_materiality_potential_impact")
    private String financialMaterialityPotentialImpact;

    @Column(name = "time_span")
    private Double timeSpan; // 0.2 to 1.0

    @Column(name = "last_modified_by")
    private String lastModifiedBy;

    @Column(name = "last_modified_at")
    private LocalDateTime lastModifiedAt;

    @Column(name = "result_materiality_assessment")
    private String resultMaterialityAssessment;

    @Column(name = "is_locked")
    private Boolean isLocked;

    @OneToOne
    @JoinColumn(name = "iro_id")
    private Iro iro;

    @ManyToOne
    @JoinColumn(name = "company_id")
    private Company company;
}
