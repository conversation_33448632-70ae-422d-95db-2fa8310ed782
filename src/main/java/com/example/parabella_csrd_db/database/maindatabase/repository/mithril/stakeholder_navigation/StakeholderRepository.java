package com.example.parabella_csrd_db.database.maindatabase.repository.mithril.stakeholder_navigation;


import com.example.parabella_csrd_db.database.maindatabase.model.mithril.Stakeholder;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface StakeholderRepository extends JpaRepository<Stakeholder, Long> {
    Optional<Stakeholder> findByToken(String token);

    List<Stakeholder> findByProjectId(Long projectId);

    List<Stakeholder> findByCompany_Id(Long companyId);
}