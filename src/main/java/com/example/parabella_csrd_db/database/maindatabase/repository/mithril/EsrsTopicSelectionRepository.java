package com.example.parabella_csrd_db.database.maindatabase.repository.mithril;

import com.example.parabella_csrd_db.database.maindatabase.model.mithril.EsrsTopicSelection;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface EsrsTopicSelectionRepository extends JpaRepository<EsrsTopicSelection, Long> {
    List<EsrsTopicSelection> findAllByCompanyId(Long companyId);
    // Find all selections for a specific company
    List<EsrsTopicSelection> findByCompanyId(Long companyId);

    // Find all selections for a specific stakeholder
    List<EsrsTopicSelection> findByStakeholderId(Long stakeholderId);

    // Find all selections for a specific ESRS topic
    EsrsTopicSelection findByEsrsTopicId(Long esrsTopicId);

    // Find all relevant selections for a company
    List<EsrsTopicSelection> findByCompanyIdAndRelevantTrue(Long companyId);

    // Find all irrelevant selections for a company
    List<EsrsTopicSelection> findByCompanyIdAndRelevantFalse(Long companyId);

    Optional<EsrsTopicSelection> findByCompanyIdAndEsrsTopicId(Long companyId, Long esrsTopicId);



}