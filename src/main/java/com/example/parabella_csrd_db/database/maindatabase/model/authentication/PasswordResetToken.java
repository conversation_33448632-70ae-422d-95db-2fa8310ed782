package com.example.parabella_csrd_db.database.maindatabase.model.authentication;

import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

import java.time.Instant;


@Getter
@Setter
@Entity
@Table(name = "password_reset_tokens")
public class PasswordResetToken {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(nullable = false, unique = true)
    private String token;  // a secure random string

    @Column(nullable = false)
    private Instant expiryDate;  // when this token becomes invalid

    // One-to-one or many-to-one to User
    // Typically Many-to-one: a user can have multiple tokens (old ones, though you may choose to delete them).
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "user_id", nullable = false)
    private User user;

    public PasswordResetToken() {}

    public PasswordResetToken(String token, Instant expiryDate, User user) {
        this.token = token;
        this.expiryDate = expiryDate;
        this.user = user;
    }


}
