package com.example.parabella_csrd_db.database.maindatabase.model.mithril;

import com.example.parabella_csrd_db.database.maindatabase.model.audit_trail.BaseAuditedEntity;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.envers.Audited;

import java.util.List;

@Audited

@Getter
@Setter
@Entity
@Table(name = "stakeholders")
public class Stakeholder extends BaseAuditedEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @ManyToOne
    private Project project;

    @Column(name = "stakeholder_name")
    private String name;

    @Column(name = "stakeholder_email")
    private String email;

    @ManyToMany
    @JoinTable(
            name = "stakeholder_value_chain_objects",
            joinColumns = @JoinColumn(name = "stakeholder_id"),
            inverseJoinColumns = @JoinColumn(name = "value_chain_object_id")
    )
    private List<ValueChainObject> valueChainObjects;

    @ManyToMany
    @JoinTable(
            name = "stakeholder_topics",
            joinColumns = @JoinColumn(name = "stakeholder_id"),
            inverseJoinColumns = @JoinColumn(name = "topic_id")
    )
    private List<EsrsTopic> esrsTopics;

    @Column(name = "role")
    private String role;

    @Column(name = "stakeholder_type")
    private String stakeholderType;

    @ManyToOne
    private Company company;

    @Column(name = "token")
    private String token;

    @Enumerated(EnumType.STRING)
    @Column(name = "status")
    private StakeholderStatus status = StakeholderStatus.INVITED; // Default status

    @Column(name = "completed_datapoints")
    private Integer completedDatapoints;

    @Column(name = "total_datapoints")
    private Integer totalDatapoints;

    @Column(name = "is_responsible")
    private Boolean is_responsible;

}

