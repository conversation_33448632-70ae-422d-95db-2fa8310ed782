package com.example.parabella_csrd_db.database.maindatabase.model.mithril;

import com.example.parabella_csrd_db.database.maindatabase.model.audit_trail.BaseAuditedEntity;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.envers.Audited;

import java.math.BigDecimal;

@Getter
@Setter
@Audited
@Table(name = "esrs_topic_selection")
@Entity
public class EsrsTopicSelection extends BaseAuditedEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;


    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "esrs_topic_id")
    private EsrsTopic esrsTopic;

    @ManyToOne
    private Stakeholder stakeholder;

    @Column(name = "relevant")
    private Boolean relevant;

    @Column(columnDefinition="TEXT", name = "reason_irrelevance")
    private String reasonIrrelevance;

    @ManyToOne
    private Company company;

    // === NEW FIELDS ===
    /**
     * Final aggregated actual impact (impact materiality),
     * stored so we can use it later in a heatmap without recalculating.
     */
    @Column(name = "final_impact_materiality_actual_impact")
    private BigDecimal finalImpactMaterialityActualImpact;

    /**
     * Final aggregated financial actual impact (financial materiality).
     */
    @Column(name = "final_financial_materiality_actual_impact")
    private BigDecimal finalFinancialMaterialityActualImpact;

    /**
     * Final decision if the topic is relevant based on the aggregated analysis.
     */
    @Column(name = "final_relevance")
    private String finalRelevance;
}
