package com.example.parabella_csrd_db.database.maindatabase.model.mithril;

import com.example.parabella_csrd_db.database.maindatabase.model.audit_trail.BaseAuditedEntity;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.envers.Audited;

@Entity
@Table(name = "company_group_esrs_selection")
@Getter
@Setter
@NoArgsConstructor
@Audited
public class CompanyGroupEsrsSelection extends BaseAuditedEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    // Relationship to CompanyGroup
    @ManyToOne(optional = false)
    @JoinColumn(name = "company_group_id", nullable = false)
    private CompanyGroup companyGroup;

    // Relationship to EsrsTopic
    @ManyToOne(optional = false)
    @JoinColumn(name = "esrs_topic_id", nullable = false)
    private EsrsTopic esrsTopic;


    @Column(name = "reason_irrelevance", columnDefinition = "TEXT")
    private String reasonIrrelevance;


    @Column(name = "is_relevant_company_group")
    private Boolean isRelevantCompanyGroup;
}
