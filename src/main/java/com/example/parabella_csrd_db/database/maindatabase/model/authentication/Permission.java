package com.example.parabella_csrd_db.database.maindatabase.model.authentication;

import jakarta.persistence.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Table(name = "permissions")
@Getter
@Setter
@NoArgsConstructor
public class Permission {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    // A unique key for the permission, e.g., "user.manage"
    @Column(nullable = false, unique = true)
    private String functionKey;

    // A human-readable name, e.g., "Manage Users"
    @Column(nullable = false)
    private String functionName;

    // A category for grouping in the UI, e.g., "User Management"
    @Column(nullable = false)
    private String category;

    public Permission(String functionKey, String functionName, String category) {
        this.functionKey = functionKey;
        this.functionName = functionName;
        this.category = category;
    }
}