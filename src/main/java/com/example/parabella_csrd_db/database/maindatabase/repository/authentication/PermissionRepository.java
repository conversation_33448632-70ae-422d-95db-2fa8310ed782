package com.example.parabella_csrd_db.database.maindatabase.repository.authentication;


import com.example.parabella_csrd_db.database.maindatabase.model.authentication.Permission;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional; // Make sure to import Optional
import java.util.Set;

@Repository
public interface PermissionRepository extends JpaRepository<Permission, Integer> {

    /**
     * Finds a permission by its unique function key (e.g., "user.manage").
     * This method was missing and is required by the DataSeeder.
     *
     * @param functionKey The unique key of the permission.
     * @return An Optional containing the Permission if found, otherwise empty.
     */
    Optional<Permission> findByFunctionKey(String functionKey);

    /**
     * Finds all permissions whose IDs are in the provided set.
     * Used for updating a role's permissions.
     *
     * @param ids A set of permission IDs.
     * @return A set of matching Permission entities.
     */
    Set<Permission> findByIdIn(Set<Integer> ids);
}