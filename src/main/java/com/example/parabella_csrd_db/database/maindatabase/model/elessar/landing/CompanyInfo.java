package com.example.parabella_csrd_db.database.maindatabase.model.elessar.landing;

import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "csrd_company_info")
public class CompanyInfo {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column( length = 255, name ="company_name")
    private String companyName;

    @Column(length = 100) // Example length
    private String revenue; // Keep as String as per frontend, validation can be added

    @Column(columnDefinition = "TEXT") // Use TEXT for potentially long descriptions
    private String industry;

    @Column(length = 50)
    private String size; // Could be an Enum if values are fixed

    @Column(length = 50,name="number_of_employees") // Example length
    private String numberOfEmployees; // Keep as String as per frontend

    // Optional: Link back to the project if bidirectional access is needed,
    // but often not required if you always navigate Project -> CompanyInfo.
    // @OneToOne(mappedBy = "companyInfo", fetch = FetchType.LAZY)
    // private CsrdProject csrdProject;
}
