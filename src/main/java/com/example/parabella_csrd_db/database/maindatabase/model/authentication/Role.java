package com.example.parabella_csrd_db.database.maindatabase.model.authentication;


import com.fasterxml.jackson.annotation.JsonIgnore;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import java.util.HashSet;
import java.util.Set;

@Setter
@Getter
@Entity
@NoArgsConstructor
@Table(name = "roles")
public class Role {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    // The Enum is replaced by a simple String name for flexibility
    @Column(length = 50, nullable = false, unique = true)
    private String name;

    @ManyToMany(fetch = FetchType.EAGER) // Eager fetch to easily load permissions with the role
    @JoinTable(name = "role_permissions",
            joinColumns = @JoinColumn(name = "role_id"),
            inverseJoinColumns = @JoinColumn(name = "permission_id"))
    private Set<Permission> permissions = new HashSet<>();

    public Role(String name) {
        this.name = name;
    }

    @OneToMany(mappedBy = "role")
    @JsonIgnore // <-- ADD THIS ANNOTATION
    private Set<User> users;
}