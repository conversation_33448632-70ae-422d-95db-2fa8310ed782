// src/main/java/com/example/parabella_csrd_db/repository/NotificationRepository.java

package com.example.parabella_csrd_db.database.maindatabase.repository.notification;


import com.example.parabella_csrd_db.database.maindatabase.model.notification.Notification;
import com.example.parabella_csrd_db.database.maindatabase.model.mithril.Project;
import org.springframework.data.jpa.repository.JpaRepository;
import java.util.List;

public interface NotificationRepository extends JpaRepository<Notification, Long> {

    List<Notification> findByProjectOrderByCreatedAtDesc(Project project);

}
