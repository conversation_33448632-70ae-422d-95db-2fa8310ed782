package com.example.parabella_csrd_db.database.maindatabase.repository.mithril;

import com.example.parabella_csrd_db.database.maindatabase.model.mithril.CompanyGroup;
import com.example.parabella_csrd_db.database.maindatabase.model.mithril.CompanyGroupEsrsSelection;
import com.example.parabella_csrd_db.database.maindatabase.model.mithril.EsrsTopic;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.List;
import java.util.Optional;

public interface CompanyGroupEsrsSelectionRepository extends JpaRepository<CompanyGroupEsrsSelection, Long> {
    Optional<CompanyGroupEsrsSelection> findByCompanyGroupAndEsrsTopic(CompanyGroup companyGroup, EsrsTopic esrsTopic);

    List<CompanyGroupEsrsSelection> findByCompanyGroupId(Long companyGroupId);
}
