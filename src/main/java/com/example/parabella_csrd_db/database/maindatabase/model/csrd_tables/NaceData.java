package com.example.parabella_csrd_db.database.maindatabase.model.csrd_tables;

import jakarta.persistence.*;




@Entity
@Table(name = "nace_data")  // Customize table name if you like
public class NaceData {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    // Columns corresponding to your Excel headers
    @Column(columnDefinition="TEXT")
    private String nace21Code;
    @Column(columnDefinition="TEXT")
    private String level;
    @Column(columnDefinition="TEXT")
    private String nace21Heading;
    @Column(columnDefinition="TEXT")
    private String nace2Code;
    @Column(columnDefinition="TEXT")
    private String nace2Heading;
    @Column(columnDefinition="TEXT")
    private String commonContent;
    @Column(columnDefinition="TEXT")
    private String typeOfMapping;
    @Column(columnDefinition="TEXT")
    private String typeOfCorrespondence;
    @Column(columnDefinition="TEXT")
    private String numberOfCorrespondingNace2;

    // Constructors
    public NaceData() {}

    public NaceData(
            String nace21Code,
            String level,
            String nace21Heading,
            String nace2Code,
            String nace2Heading,
            String commonContent,
            String typeOfMapping,
            String typeOfCorrespondence,
            String numberOfCorrespondingNace2
    ) {
        this.nace21Code = nace21Code;
        this.level = level;
        this.nace21Heading = nace21Heading;
        this.nace2Code = nace2Code;
        this.nace2Heading = nace2Heading;
        this.commonContent = commonContent;
        this.typeOfMapping = typeOfMapping;
        this.typeOfCorrespondence = typeOfCorrespondence;
        this.numberOfCorrespondingNace2 = numberOfCorrespondingNace2;
    }

    // Getters and Setters
    public Long getId() {
        return id;
    }

    public String getNace21Code() {
        return nace21Code;
    }

    public void setNace21Code(String nace21Code) {
        this.nace21Code = nace21Code;
    }

    public String getLevel() {
        return level;
    }

    public void setLevel(String level) {
        this.level = level;
    }

    public String getNace21Heading() {
        return nace21Heading;
    }

    public void setNace21Heading(String nace21Heading) {
        this.nace21Heading = nace21Heading;
    }

    public String getNace2Code() {
        return nace2Code;
    }

    public void setNace2Code(String nace2Code) {
        this.nace2Code = nace2Code;
    }

    public String getNace2Heading() {
        return nace2Heading;
    }

    public void setNace2Heading(String nace2Heading) {
        this.nace2Heading = nace2Heading;
    }

    public String getCommonContent() {
        return commonContent;
    }

    public void setCommonContent(String commonContent) {
        this.commonContent = commonContent;
    }

    public String getTypeOfMapping() {
        return typeOfMapping;
    }

    public void setTypeOfMapping(String typeOfMapping) {
        this.typeOfMapping = typeOfMapping;
    }

    public String getTypeOfCorrespondence() {
        return typeOfCorrespondence;
    }

    public void setTypeOfCorrespondence(String typeOfCorrespondence) {
        this.typeOfCorrespondence = typeOfCorrespondence;
    }

    public String getNumberOfCorrespondingNace2() {
        return numberOfCorrespondingNace2;
    }

    public void setNumberOfCorrespondingNace2(String numberOfCorrespondingNace2) {
        this.numberOfCorrespondingNace2 = numberOfCorrespondingNace2;
    }
}
