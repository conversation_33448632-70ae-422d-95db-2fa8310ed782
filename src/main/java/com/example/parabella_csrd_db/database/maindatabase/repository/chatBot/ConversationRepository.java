// Package: com.example.parabella_csrd_db.repository
package com.example.parabella_csrd_db.database.maindatabase.repository.chatBot;


import com.example.parabella_csrd_db.database.maindatabase.model.authentication.User;
import com.example.parabella_csrd_db.database.maindatabase.model.chatBot.Conversation;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.Optional;

public interface ConversationRepository extends JpaRepository<Conversation, Long> {
    Optional<Conversation> findByUser(User user);
}
