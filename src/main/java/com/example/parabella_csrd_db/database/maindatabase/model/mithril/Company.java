// Company.java
package com.example.parabella_csrd_db.database.maindatabase.model.mithril;



import com.example.parabella_csrd_db.database.maindatabase.model.audit_trail.BaseAuditedEntity;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.envers.Audited;

import java.util.List;

@Setter
@Getter
@Entity
@Table(name = "company")
@Audited
@Access(AccessType.FIELD)
@NoArgsConstructor
public class Company extends BaseAuditedEntity {


    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "company_name")
    private String companyName;

    @Column(name = "address")
    private String address;

    private String vat;

    @Column(name = "num_employees")
    private String numEmployees;

    @Column(name = "revenues")
    private Double revenues;

    @Column(name = "industry")
    private String industry;

    @Column(name = "is_sub_company")
    private Boolean isSubCompany;

    @OneToMany(mappedBy = "company")
    private List<ValueChainObject> valueChainObjects;

    @OneToMany(mappedBy = "company")
    private List<Stakeholder> stakeholders;

    @ManyToOne
    @JoinColumn(name = "company_group_id")
    private CompanyGroup companyGroup;

    @ManyToOne
    @JoinColumn(name = "project_id")
    private Project project;


}
