package com.example.parabella_csrd_db.database.maindatabase.model.elessar.dashboard;


import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
@Entity
@Table(name = "csrd_topic")
public class CsrdTopic {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    private String code; // e.g. "ESRS_E2"
    private String name; // e.g. "Pollution"

    @OneToMany(mappedBy = "topic", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JsonIgnoreProperties({"topic", "sections"})
    private List<CsrdSubtopic> subtopics = new ArrayList<>();


}
