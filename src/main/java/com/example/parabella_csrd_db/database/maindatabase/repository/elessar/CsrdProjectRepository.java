package com.example.parabella_csrd_db.database.maindatabase.repository.elessar;

import com.example.parabella_csrd_db.database.maindatabase.model.elessar.CsrdProject;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface CsrdProjectRepository extends JpaRepository<CsrdProject, Long> {
    List<CsrdProject> findByUserId(Long userId);

    @Query("SELECT p FROM CsrdProject p LEFT JOIN FETCH p.companyInfo WHERE p.id = :id")
    Optional<CsrdProject> findByIdWithCompanyInfo(Long id);

    Optional<CsrdProject> findByIdAndUserId(Long id, Long userId);
}