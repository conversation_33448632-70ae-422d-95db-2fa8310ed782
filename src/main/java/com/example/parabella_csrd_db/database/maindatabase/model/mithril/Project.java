package com.example.parabella_csrd_db.database.maindatabase.model.mithril;

import com.example.parabella_csrd_db.database.maindatabase.model.audit_trail.BaseAuditedEntity;
import com.example.parabella_csrd_db.database.maindatabase.model.authentication.User;
import com.example.parabella_csrd_db.database.maindatabase.model.notification.Notification;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import org.checkerframework.checker.units.qual.C;
import org.hibernate.envers.Audited;

import java.time.LocalDateTime;
import java.util.List;


@Setter
@Getter
@Entity
@Audited
@Table(name = "projects")
@Access(AccessType.FIELD)
public class Project extends BaseAuditedEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    // The user who owns or created the project
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id", nullable = false)
    private User user;

    @Column(name = "project_name")
    private String projectName;

    @Column(name = "project_description")
    private String projectDescription;

    @Column(name = "created_at")
    private LocalDateTime createdAt;

    // List of stakeholders associated with the project
    @OneToMany(mappedBy = "project", cascade = CascadeType.ALL, orphanRemoval = true)
    private List<Stakeholder> stakeholders;

    @Column(name = "project_type")
    private String projectType;

    @Column(name = "company_id")
    private Long companyId;

    @Column(name = "company_group_id")
    private Long companyGroupId;

    // List of notifications associated with the project
    @OneToMany(mappedBy = "project", cascade = CascadeType.ALL, orphanRemoval = true)
    private List<Notification> notifications;

    // Constructors
    public Project() {
    }


}
