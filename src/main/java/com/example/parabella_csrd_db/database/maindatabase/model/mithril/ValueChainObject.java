// ValueChainObject.java
package com.example.parabella_csrd_db.database.maindatabase.model.mithril;



import com.example.parabella_csrd_db.database.maindatabase.model.audit_trail.BaseAuditedEntity;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.envers.Audited;

@Setter
@Getter
@Entity
@Table(name = "value_chain_objects")

@NoArgsConstructor
@Audited
public class ValueChainObject extends BaseAuditedEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "name")
    private String name;

    @Column(name = "industry")
    private String industry;

    @ManyToOne
    @JoinColumn(name = "company_id")
    private Company company;

}
