package com.example.parabella_csrd_db.database.maindatabase.repository.mithril;

import com.example.parabella_csrd_db.database.maindatabase.model.mithril.Iro;
import com.example.parabella_csrd_db.database.maindatabase.model.mithril.IroEvaluation;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

//TODO: adapting to new IRO entity and optimze the Dashboard functionality
@Repository
public interface IroEvaluationRepository extends JpaRepository<IroEvaluation, Long> {

    Optional<IroEvaluation> findByIro(Iro iro);

    Optional<IroEvaluation> findByIro_EsrsTopicSelection_Id(Long topicId);

    /**
     * Methods depending on company ID
     */
    Long countByCompany_Id(Long companyId);

    Long countByCompany_IdAndIsLockedTrue(Long companyId);

    List<IroEvaluation> findByCompanyId(Long companyId);





//
//    // If you need to query by group and sub-subtopic, note that we now join via the IRO's ESRS topic:
//    @Query("""
//            SELECT d
//            FROM IroEvaluation d
//            JOIN d.company c
//            JOIN c.companyGroup g
//            JOIN d.iro i
//            JOIN i.esrsTopic t
//            WHERE g.id = :groupId
//              AND t.subSubTopic = :subSubTopic
//            """)
//    List<IroEvaluation> findByGroupIdAndSubSubTopic(Long groupId, String subSubTopic);
}
