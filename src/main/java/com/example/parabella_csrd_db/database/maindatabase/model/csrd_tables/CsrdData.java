package com.example.parabella_csrd_db.database.maindatabase.model.csrd_tables;


import jakarta.persistence.*;

@Entity
public class CsrdData {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "efragID")
    private String efragID;

    @Column(name = "esrs")
    private String esrs;

    @Column(name = "dr")
    private String dr;

    @Column(name = "paragraph")
    private String paragraph;

    @Column(name = "related_ar")
    private String relatedAr;

    @Column(name = "name")
    private String name;

    @Column(name = "hyperlink")
    private String hyperlink;

    @Column(name = "data_type")
    private String dataType;

    @Column(name = "data_response")
    private String dataResponse;

    @Column(name = "data_unit")
    private String dataUnit;

    @Column(name = "conditional_or_alternative_dp")
    private String conditionalOrAlternativeDp;

    @Column(name = "case_study")
    private String caseStudy;

    @Column(name = "additional_information")
    private String additionalInformation;

    @Column(name = "comment")
    private String comment;

    // Getters and Setters

    // Constructors (if needed)

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getEsrs() {
        return esrs;
    }

    public void setEsrs(String esrs) {
        this.esrs = esrs;
    }

    public String getDr() {
        return dr;
    }

    public void setDr(String dr) {
        this.dr = dr;
    }

    public String getParagraph() {
        return paragraph;
    }

    public void setParagraph(String paragraph) {
        this.paragraph = paragraph;
    }

    public String getRelatedAr() {
        return relatedAr;
    }

    public void setRelatedAr(String relatedAr) {
        this.relatedAr = relatedAr;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getHyperlink() {
        return hyperlink;
    }

    public void setHyperlink(String hyperlink) {
        this.hyperlink = hyperlink;
    }

    public String getDataType() {
        return dataType;
    }

    public void setDataType(String dataType) {
        this.dataType = dataType;
    }

    public String getDataResponse() {
        return dataResponse;
    }

    public void setDataResponse(String dataResponse) {
        this.dataResponse = dataResponse;
    }

    public String getDataUnit() {
        return dataUnit;
    }

    public void setDataUnit(String dataUnit) {
        this.dataUnit = dataUnit;
    }

    public String getConditionalOrAlternativeDp() {
        return conditionalOrAlternativeDp;
    }

    public void setConditionalOrAlternativeDp(String conditionalOrAlternativeDp) {
        this.conditionalOrAlternativeDp = conditionalOrAlternativeDp;
    }

    public String getCaseStudy() {
        return caseStudy;
    }

    public void setCaseStudy(String caseStudy) {
        this.caseStudy = caseStudy;
    }

    public String getAdditionalInformation() {
        return additionalInformation;
    }

    public void setAdditionalInformation(String additionalInformation) {
        this.additionalInformation = additionalInformation;
    }

    public String getComment() {
        return comment;
    }

    public void setComment(String comment) {
        this.comment = comment;
    }

    public String getEfragID() {
        return efragID;
    }

    public void setEfragID(String efragID) {
        this.efragID = efragID;
    }
}
