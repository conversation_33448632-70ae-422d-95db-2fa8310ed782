// CompanyGroup.java
package com.example.parabella_csrd_db.database.maindatabase.model.mithril;



import com.example.parabella_csrd_db.database.maindatabase.model.audit_trail.BaseAuditedEntity;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.envers.Audited;

import java.util.List;

@Setter
@Getter
@Entity
@Table(name = "company_groups")
@Audited
@NoArgsConstructor
public class CompanyGroup extends BaseAuditedEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "company_group_name")
    private String companyGroupName;
    @Column(name = "company_group_address")
    private String companyGroupAddress;
    @Column(name = "company_groupvat")
    private String companyGroupVAT;
    @Column(name = "num_employees")
    private String numEmployees;
    @Column(name = "revenues")
    private Double revenues;
    @Column(name = "industry")
    private String industry;

    @OneToMany(mappedBy = "companyGroup", cascade = CascadeType.ALL)
    private List<Company> subCompanies;

    @OneToOne
    @JoinColumn(name = "project_id")
    private Project project;


}
