// CompanyGroup.java
package com.example.parabella_csrd_db.database.maindatabase.model.mithril;



import com.example.parabella_csrd_db.database.maindatabase.model.audit_trail.BaseAuditedEntity;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.envers.Audited;

import java.util.List;

@Setter
@Getter
@Entity
@Table(name = "company_groups")
@Audited
@NoArgsConstructor
public class CompanyGroup extends BaseAuditedEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    private String companyGroupName;
    private String companyGroupAddress;
    private String companyGroupVAT;
    private String numEmployees;
    private Double revenues;
    private String industry;

    @OneToMany(mappedBy = "companyGroup", cascade = CascadeType.ALL)
    private List<Company> subCompanies;

    @OneToOne
    @JoinColumn(name = "project_id")
    private Project project;


}
