package com.example.parabella_csrd_db.database.maindatabase.model.elessar.dashboard;


import jakarta.persistence.*;

@Entity
@Table(name = "csrd_conditional_field")
public class CsrdConditionalField {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @ManyToOne
    @JoinColumn(name = "parent_field_id", nullable = false)
    private CsrdField parentField;

    private String parentOptionValue; // e.g. "Yes"

    @ManyToOne
    @JoinColumn(name = "child_field_id", nullable = false)
    private CsrdField childField;

    public CsrdConditionalField() {}

    public Long getId() {
        return id;
    }

    public void setId(Long id) { this.id = id; }

    public CsrdField getParentField() {
        return parentField;
    }

    public void setParentField(CsrdField parentField) {
        this.parentField = parentField;
    }

    public String getParentOptionValue() {
        return parentOptionValue;
    }

    public void setParentOptionValue(String parentOptionValue) {
        this.parentOptionValue = parentOptionValue;
    }

    public CsrdField getChildField() {
        return childField;
    }

    public void setChildField(CsrdField childField) {
        this.childField = childField;
    }
}
