package com.example.parabella_csrd_db.database.maindatabase.model.elessar.dashboard;


import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
@Entity
@Table(name = "csrd_subtopic")
public class CsrdSubtopic {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @ManyToOne
    @JoinColumn(name = "topic_id", nullable = false)
    @JsonIgnoreProperties({"subtopics"})
    private CsrdTopic topic;

    @Column(name="csrd_subtopic_id")
    private String csrdSubtopicId;
    @Column(name="csrd_subtopic_label")// e.g. "E2-1"
    private String csrdSubtopicLabel;  // e.g. "Policies related to pollution"

    @OneToMany(mappedBy = "subtopic", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JsonIgnoreProperties({"subtopic"}) // We'll load sections in a different endpoint to avoid huge nesting
    private List<CsrdSubtopicSection> sections = new ArrayList<>();


}
