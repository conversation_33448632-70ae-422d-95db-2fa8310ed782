package com.example.parabella_csrd_db.database.maindatabase.repository.authentication;


import com.example.parabella_csrd_db.database.maindatabase.model.authentication.PasswordResetToken;
import org.springframework.data.jpa.repository.JpaRepository;
import java.util.Optional;

public interface PasswordResetTokenRepository extends JpaRepository<PasswordResetToken, Long> {

    Optional<PasswordResetToken> findByToken(String token);

    // Optionally, you can delete all expired tokens
    // @Modifying
    // @Query("DELETE FROM PasswordResetToken t WHERE t.expiryDate < :now")
    // void deleteAllExpiredTokens(Instant now);
}
