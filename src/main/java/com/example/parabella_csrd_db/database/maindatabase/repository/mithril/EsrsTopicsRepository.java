package com.example.parabella_csrd_db.database.maindatabase.repository.mithril;

import com.example.parabella_csrd_db.database.maindatabase.model.mithril.EsrsTopic;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface EsrsTopicsRepository extends JpaRepository<EsrsTopic, Long> {

    List<EsrsTopic> findByArea(String area);
    List<EsrsTopic> findByEsrsCode(String esrsCode);
}


