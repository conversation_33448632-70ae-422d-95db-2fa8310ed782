package com.example.parabella_csrd_db.database.maindatabase.model.mithril;


import com.example.parabella_csrd_db.database.maindatabase.model.audit_trail.BaseAuditedEntity;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.envers.Audited;

@Getter
@Setter
@Audited
@Entity
@Table(name = "value_chains", uniqueConstraints = {
        @UniqueConstraint(columnNames = {"value_chain_key", "industry_id"})
})
public class ValueChain extends BaseAuditedEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @NotBlank(message = "Value Chain Key is mandatory")
    @Column(name = "value_chain_key", nullable = false)
    private String valueChainKey;

    // Many-to-One relationship with Industry
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "industry_id", nullable = false)
    private Industry industry;

    // Constructors
    public ValueChain() {}

    public ValueChain(String valueChainKey, Industry industry) {
        this.valueChainKey = valueChainKey;
        this.industry = industry;
    }




}
