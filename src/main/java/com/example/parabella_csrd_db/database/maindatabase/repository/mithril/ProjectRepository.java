package com.example.parabella_csrd_db.database.maindatabase.repository.mithril;

import com.example.parabella_csrd_db.database.maindatabase.model.authentication.User;
import com.example.parabella_csrd_db.database.maindatabase.model.mithril.Project;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.List;
import java.util.Optional;

public interface ProjectRepository extends JpaRepository<Project, Long> {
    List<Project> findByUserId(Long userId);


    Optional<Project> findByProjectNameAndUser(String projectName, User user);

}
