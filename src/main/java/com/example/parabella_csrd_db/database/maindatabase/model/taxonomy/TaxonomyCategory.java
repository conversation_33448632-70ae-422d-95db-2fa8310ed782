package com.example.parabella_csrd_db.database.maindatabase.model.taxonomy;

import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
@Entity
public class TaxonomyCategory {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    private String name;        // e.g., "Climate Change Mitigation"
    private String description; // e.g., "Activities that contribute..."

    @OneToMany(mappedBy = "category", cascade = CascadeType.ALL)
    private List<TaxonomyActivity> activities = new ArrayList<>();

    public TaxonomyCategory() {}

    public TaxonomyCategory(String name, String description) {
        this.name = name;
        this.description = description;
    }

    // getters, setters
    // ...
}
