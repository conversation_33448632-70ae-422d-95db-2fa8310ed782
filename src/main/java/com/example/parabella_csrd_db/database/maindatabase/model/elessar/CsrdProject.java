package com.example.parabella_csrd_db.database.maindatabase.model.elessar;

import com.example.parabella_csrd_db.database.maindatabase.model.authentication.User;
import com.example.parabella_csrd_db.database.maindatabase.model.elessar.landing.CompanyInfo;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.time.LocalDateTime;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "csrd_projects")
public class CsrdProject {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @ManyToOne(fetch = FetchType.LAZY) // Lazy fetch user details unless needed
    @JoinColumn(name = "user_id", nullable = false)
    private User user;

    @Column(nullable = false, length = 200, name="project_name")
    private String projectName;


    @Column(columnDefinition = "TEXT", name="project_description")
    private String projectDescription;


    @Enumerated(EnumType.STRING) // Store enum name as string in DB
    @Column(nullable = false, length = 50, name = "project_type")
    private ProjectType projectType;


    @CreationTimestamp // Automatically set on creation
    @Column(nullable = false, updatable = false, name = "created_at")
    private LocalDateTime createdAt;

    @UpdateTimestamp // Automatically set on update
    @Column(nullable = false,name="updated_at")
    private LocalDateTime updatedAt;

    // One-to-One relationship: Each CsrdProject has one CompanyInfo
    // Cascade ALL: Operations on CsrdProject propagate to CompanyInfo
    // orphanRemoval: If CompanyInfo is removed from CsrdProject, delete it from DB
    @OneToOne(cascade = CascadeType.ALL, fetch = FetchType.LAZY, orphanRemoval = true)
    @JoinColumn(name = "company_info_id", referencedColumnName = "id", unique = true) // Foreign key in csrd_projects table
    private CompanyInfo companyInfo;

    // Add other relationships if needed (e.g., links to specific ESRS data, reports)
    // @OneToMany(...)
    // private List<CsrdReportData> reportData;
}