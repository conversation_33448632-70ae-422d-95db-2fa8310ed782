package com.example.parabella_csrd_db.database.maindatabase.repository.authentication;

import com.example.parabella_csrd_db.database.maindatabase.model.authentication.ERole;
import com.example.parabella_csrd_db.database.maindatabase.model.authentication.Role;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;


@Repository
public interface RoleRepository extends JpaRepository<Role, Long> {
    Optional<Role> findByName(ERole name);
}

