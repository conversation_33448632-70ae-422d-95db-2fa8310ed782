package com.example.parabella_csrd_db.database.maindatabase.repository.mithril;

import com.example.parabella_csrd_db.database.maindatabase.model.mithril.ValueChainObject;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ValueChainObjectRepository extends JpaRepository<ValueChainObject, Long> {
    // Custom query methods (if any) can be added here
    List<ValueChainObject> findAllById(Long id);

    // Example of finding all ValueChainObjects by a company's id
    List<ValueChainObject> findByCompanyId(Long companyId);

    // Example of finding ValueChainObjects by their industry
    List<ValueChainObject> findByIndustry(String industry);
}