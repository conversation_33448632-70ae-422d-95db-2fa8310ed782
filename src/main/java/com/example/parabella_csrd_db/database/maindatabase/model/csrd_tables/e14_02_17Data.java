package com.example.parabella_csrd_db.database.maindatabase.model.csrd_tables;

import jakarta.persistence.*;

@Entity
@Table(name = "tables_E1_4_02_17")
public class e14_02_17Data {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    private String scope;
    private int baseYear;
    private int target2030;
    private int target2035;
    private int target2050;
    private String explanation;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getScope() {
        return scope;
    }

    public void setScope(String scope) {
        this.scope = scope;
    }

    public int getBaseYear() {
        return baseYear;
    }

    public void setBaseYear(int baseYear) {
        this.baseYear = baseYear;
    }

    public int getTarget2030() {
        return target2030;
    }

    public void setTarget2030(int target2030) {
        this.target2030 = target2030;
    }

    public int getTarget2035() {
        return target2035;
    }

    public void setTarget2035(int target2035) {
        this.target2035 = target2035;
    }

    public int getTarget2050() {
        return target2050;
    }

    public void setTarget2050(int target2050) {
        this.target2050 = target2050;
    }

    public String getExplanation() {
        return explanation;
    }

    public void setExplanation(String explanation) {
        this.explanation = explanation;
    }
}
