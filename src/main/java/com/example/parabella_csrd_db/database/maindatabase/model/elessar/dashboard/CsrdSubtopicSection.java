package com.example.parabella_csrd_db.database.maindatabase.model.elessar.dashboard;



import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.persistence.*;

import java.util.ArrayList;
import java.util.List;

@Entity
@Table(name = "csrd_subtopic_section")
public class CsrdSubtopicSection {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @ManyToOne
    @JoinColumn(name = "subtopic_id", nullable = false)
    @JsonIgnore
    private CsrdSubtopic subtopic;

    private String sectionId;    // e.g. "E2-1-1"
    private String sectionTitle; // e.g. "Addressing Negative Impacts..."

    @OneToMany(mappedBy = "section", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JsonIgnoreProperties({"section"})
    private List<CsrdField> fields = new ArrayList<>();

    public CsrdSubtopicSection() {}

    public Long getId() {
        return id;
    }

    public void setId(Long id) { this.id = id; }

    public CsrdSubtopic getSubtopic() {
        return subtopic;
    }

    public void setSubtopic(CsrdSubtopic subtopic) {
        this.subtopic = subtopic;
    }

    public String getSectionId() {
        return sectionId;
    }

    public void setSectionId(String sectionId) {
        this.sectionId = sectionId;
    }

    public String getSectionTitle() {
        return sectionTitle;
    }

    public void setSectionTitle(String sectionTitle) {
        this.sectionTitle = sectionTitle;
    }

    public List<CsrdField> getFields() {
        return fields;
    }

    public void setFields(List<CsrdField> fields) {
        this.fields = fields;
    }
}
