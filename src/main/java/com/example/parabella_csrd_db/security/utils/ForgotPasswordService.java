package com.example.parabella_csrd_db.security.utils;

import com.example.parabella_csrd_db.database.maindatabase.model.authentication.PasswordResetToken;
import com.example.parabella_csrd_db.database.maindatabase.model.authentication.User;

import com.example.parabella_csrd_db.database.maindatabase.repository.authentication.PasswordResetTokenRepository;
import com.example.parabella_csrd_db.database.maindatabase.repository.authentication.UserRepository;
import com.example.parabella_csrd_db.service.mail.EmailService;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.Optional;
import java.util.UUID;

@Service
public class ForgotPasswordService {

    @Value("${app.frontend.url}")
    private String frontendUrl;

    private final UserRepository userRepository;
    private final PasswordResetTokenRepository tokenRepository;
    private final PasswordEncoder passwordEncoder;
    private final EmailService emailService;

    public ForgotPasswordService(UserRepository userRepository,
                                 PasswordResetTokenRepository tokenRepository,
                                 PasswordEncoder passwordEncoder,
                                 EmailService emailService) {
        this.userRepository = userRepository;
        this.tokenRepository = tokenRepository;
        this.passwordEncoder = passwordEncoder;
        this.emailService = emailService;
    }

    /**
     * 1) Create a reset token for the user’s email and
     * 2) send them a password-reset link via email.
     */
    public void initiatePasswordReset(String email) {
        User user = userRepository.findByEmail(email);
        if (user == null) {
            // Best practice: don’t reveal if email is invalid or not.
            // But you can optionally handle "user not found".
            return;
        }

        // Generate a random token (UUID is quick approach; you can also use SecureRandom, etc.)
        String token = UUID.randomUUID().toString();

        // Calculate expiry date (e.g. 1 hour from now)
        Instant expiryDate = Instant.now().plus(1, ChronoUnit.HOURS);

        PasswordResetToken resetToken = new PasswordResetToken(token, expiryDate, user);
        tokenRepository.save(resetToken);

        // Build reset link for your frontend
        // e.g.: https://yourfrontend.com/reset-password?token=xxxx
        String resetLink = frontendUrl + "pages/authentication/resetpassword/" + token;

        // Send email
        String subject = "Parabella - Password Reset Request";
        String message = "Hello " + user.getUsername() + ",\n\n"
                + "We received a request to reset your password. "
                + "Click the link below to set a new password (valid for 1 hour):\n\n"
                + resetLink + "\n\n"
                + "If you did not request a password reset, you can ignore this email.\n\n"
                + "Best regards,\n"
                + "The Parabella Team";

        emailService.sendPlainOrHtmlEmail(user.getEmail(), subject, message);
    }

    /**
     * Validates the token, and if valid, updates the user’s password.
     */
    public boolean resetPassword(String token, String newPassword) {
        Optional<PasswordResetToken> maybeToken = tokenRepository.findByToken(token);
        if (maybeToken.isEmpty()) {
            return false; // token not found
        }
        PasswordResetToken resetToken = maybeToken.get();

        // Check if expired
        if (resetToken.getExpiryDate().isBefore(Instant.now())) {
            // Optionally, delete the expired token from DB
            // tokenRepository.delete(resetToken);
            return false; // token expired
        }

        // Token is valid, update the user’s password
        User user = resetToken.getUser();
        user.setPassword(passwordEncoder.encode(newPassword));
        userRepository.save(user);

        // Optionally, delete the token so it can’t be reused
        tokenRepository.delete(resetToken);

        return true;
    }
}
