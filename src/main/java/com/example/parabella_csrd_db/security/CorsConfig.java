package com.example.parabella_csrd_db.security;

import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.CorsRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

@Configuration
public class CorsConfig implements WebMvcConfigurer {

    @Override
    public void addCorsMappings(CorsRegistry registry) {
        registry.addMapping("/**")
                .allowedOrigins("https://storage.googleapis.com", "http://localhost:5173","https://parabella-elessar-1091242934000.europe-west3.run.app", "https://gcr-parabella-staging-frontend-1091242934000.europe-west10.run.app", "https://gcr-parabella-staging-frontend-1091242934000.europe-west4.run.app", "https://parabella.app" )  // or specific origins like "http://localhost:3000"
                .allowedMethods("GET", "POST", "PUT", "DELETE", "OPTIONS")
                .allowedHeaders("*")
                .allowCredentials(true)
                .maxAge(3600);
    }
}
