package com.example.parabella_csrd_db.security.services;

import com.example.parabella_csrd_db.database.maindatabase.model.authentication.User;
import com.example.parabella_csrd_db.database.maindatabase.repository.authentication.UserRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;


@Service
public class UserDetailsServiceImpl implements UserDetailsService {
    @Autowired
    UserRepository userRepository;

    /**
     * This method is called by Spring Security during the authentication process.
     * It loads the user from the database and converts it into a UserDetails object
     * that Spring Security can understand.
     */
    @Override
    @Transactional // Good practice: ensures all lazy-loaded collections (like permissions) are fetched.
    public UserDetails loadUserByUsername(String username) throws UsernameNotFoundException {
        // 1. Find the user in the database by their username.
        User user = userRepository.findByUsername(username)
                .orElseThrow(() -> new UsernameNotFoundException("User Not Found with username: " + username));

        // 2. Use the static factory method 'build' to create the UserDetails object.
        //    This method contains all the logic for extracting the role name and all permissions.
        return UserDetailsImpl.build(user);
    }


    /**
     * A helper method to find a user by username.
     * @param username The username to search for.
     * @return The User entity.
     */
    public User findByUsername(String username) {
        return userRepository.findByUsername(username)
                .orElseThrow(() -> new RuntimeException("User not found: " + username));
    }
}