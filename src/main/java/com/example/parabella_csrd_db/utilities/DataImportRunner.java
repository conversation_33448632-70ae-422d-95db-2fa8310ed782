//package com.example.parabella_csrd_db.utilities;
//
//import com.example.parabella_csrd_db.service.mithril.ExcelService;
//import com.example.parabella_csrd_db.service.utilities.excel_to_database.EsrsTopicService;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.boot.CommandLineRunner;
//import org.springframework.stereotype.Component;
//
//@Component
//public class DataImportRunner implements CommandLineRunner {
//
//    @Autowired
//    private EsrsTopicService esrsTopicService;
//    private ExcelService excelManager;
//
//    @Override
//    public void run(String... args) throws Exception {
//        String filePath = "src/main/resources/static/excel/impact_finance_esrs_topics_codes.csv"; // Update this with the actual path to your CSV file
//        excelManager.getExcelData(filePath);
//        System.out.println("Data import completed successfully.");
//    }
//}