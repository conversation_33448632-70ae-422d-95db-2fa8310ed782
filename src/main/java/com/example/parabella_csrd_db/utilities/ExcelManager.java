package com.example.parabella_csrd_db.utilities;

import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.io.InputStream;
import java.nio.file.*;
import java.util.*;

@Component
public class ExcelManager {

    private static final String EXCEL_FOLDER = "static/excel/";

    public List<Map<String, String>> readExcel(String fileName) throws IOException {
        List<Map<String, String>> data = new ArrayList<>();
        ClassPathResource classPathResource = new ClassPathResource(EXCEL_FOLDER + fileName);
        try (InputStream excelFile = classPathResource.getInputStream();
             Workbook workbook = new XSSFWorkbook(excelFile)) {
            Sheet sheet = workbook.getSheetAt(0);
            Row headerRow = sheet.getRow(0);
            int numColumns = headerRow.getPhysicalNumberOfCells();

            for (int i = 1; i <= sheet.getLastRowNum(); i++) {
                Row row = sheet.getRow(i);
                if (row == null) {
                    continue; // Skip empty rows
                }
                Map<String, String> rowData = new HashMap<>();
                for (int j = 0; j < numColumns; j++) {
                    Cell headerCell = headerRow.getCell(j);
                    if (headerCell == null) {
                        continue; // Skip columns without headers
                    }
                    String headerValue = headerCell.getStringCellValue();
                    Cell cell = row.getCell(j);
                    String cellValue = cell != null ? getCellValueAsString(cell) : "";
                    rowData.put(headerValue, cellValue);
                }
                data.add(rowData);
            }
        }
        return data;
    }

    public Map<String, List<Map<String, String>>> readAllExcelFiles() throws IOException {
        Map<String, List<Map<String, String>>> allData = new HashMap<>();
        ClassPathResource resource = new ClassPathResource(EXCEL_FOLDER);
        Path path = Paths.get(resource.getURI());

        try (DirectoryStream<Path> stream = Files.newDirectoryStream(path, "*.xlsx")) {
            for (Path entry : stream) {
                String fileName = entry.getFileName().toString();
                List<Map<String, String>> fileData = readExcel(fileName);
                allData.put(fileName, fileData);
            }
        }
        return allData;
    }

    private String getCellValueAsString(Cell cell) {
        switch (cell.getCellType()) {
            case STRING:
                return cell.getStringCellValue();
            case NUMERIC:
                if (DateUtil.isCellDateFormatted(cell)) {
                    return cell.getDateCellValue().toString();
                } else {
                    return Double.toString(cell.getNumericCellValue());
                }
            case BOOLEAN:
                return Boolean.toString(cell.getBooleanCellValue());
            case FORMULA:
                return cell.getCellFormula();
            case BLANK:
                return "";
            default:
                return "";
        }
    }
}
