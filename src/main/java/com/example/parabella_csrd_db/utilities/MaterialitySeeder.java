//package com.example.parabella_csrd_db.utilities;
//
//import com.example.parabella_csrd_db.model.*;
//import com.example.parabella_csrd_db.maindatabase.mithril.model.Stakeholder;
//import com.example.parabella_csrd_db.maindatabase.mithril.model.StakeholderStatus;
//import com.example.parabella_csrd_db.repository.*;
//import com.example.parabella_csrd_db.maindatabase.stakeholder_navigation.mithril.repository.StakeholderRepository;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.boot.CommandLineRunner;
//import org.springframework.core.io.ClassPathResource;
//import org.springframework.stereotype.Component;
//
//import java.io.BufferedReader;
//import java.io.InputStreamReader;
//import java.time.LocalDateTime;
//import java.util.*;
//
//@Component
//public class MaterialitySeeder implements CommandLineRunner {
//
//    @Autowired
//    private IroEvaluationRepository esrsTopicDetailsRepository;
//
//    @Autowired
//    private EsrsTopicsRepository esrsTopicRepository;
//
//    @Autowired
//    private CompanyRepository companyRepository;
//
//    @Autowired
//    private StakeholderRepository stakeholderRepository;
//
//    @Autowired
//    private ValueChainObjectRepository valueChainObjectRepository;
//
//    private final Random random = new Random();
//
//    @Override
//    public void run(String... args) throws Exception {
//        // First load ESRS topics from CSV
//        loadEsrsTopicsFromCsv();
//
//        // Get existing data
//        List<Company> companies = companyRepository.findAll();
//        List<EsrsTopic> topics = esrsTopicRepository.findAll();
//
//        if (companies.isEmpty() || topics.isEmpty()) {
//            return;
//        }
//
//        // Generate materiality details for each company and relevant topic
//        for (Company company : companies) {
//            // Create some value chain objects for the company
//            List<ValueChainObject> valueChainObjects = createValueChainObjects(company);
//
//            // Create stakeholders for the company
//            List<Stakeholder> stakeholders = createStakeholders(company, topics, valueChainObjects);
//
//            // Create materiality details for relevant topics
//            for (EsrsTopic topic : topics) {
//                if (isTopicRelevantForCompany(company, topic)) {
//                    createMaterialityDetails(company, topic, stakeholders);
//                }
//            }
//        }
//    }
//
//    private void loadEsrsTopicsFromCsv() throws Exception {
//        ClassPathResource resource = new ClassPathResource("impact_finance_esrs_topics_codes.csv");
//        BufferedReader reader = new BufferedReader(new InputStreamReader(resource.getInputStream()));
//
//        String line;
//        boolean firstLine = true;
//        while ((line = reader.readLine()) != null) {
//            if (firstLine) {
//                firstLine = false;
//                continue;
//            }
//
//            String[] parts = line.split(";");
//            EsrsTopic topic = new EsrsTopic();
//            topic.setId(Long.parseLong(parts[0]));
//            topic.setArea(parts[1]);
//            topic.setEsrsCode(parts[2]);
//            topic.setTopic(parts[3]);
//            topic.setSubtopic(parts[4]);
//            topic.setSubSubTopic(parts.length > 5 ? parts[5] : null);
//
//            esrsTopicRepository.save(topic);
//        }
//    }
//
//    private List<ValueChainObject> createValueChainObjects(Company company) {
//        List<ValueChainObject> objects = new ArrayList<>();
//        String[] industries = {
//                "Manufacturing", "Logistics", "Raw Materials", "Distribution", "Retail",
//                "Energy", "Agriculture", "Construction", "Technology", "Services"
//        };
//
//        int numObjects = random.nextInt(3) + 2; // 2-4 objects per company
//        for (int i = 0; i < numObjects; i++) {
//            ValueChainObject vco = new ValueChainObject();
//            vco.setName(generateValueChainName(company, i));
//            vco.setIndustry(industries[random.nextInt(industries.length)]);
//            vco.setCompany(company);
//            objects.add(valueChainObjectRepository.save(vco));
//        }
//
//        return objects;
//    }
//
//    private String generateValueChainName(Company company, int index) {
//        String[] prefixes = {"Supplier", "Partner", "Distributor", "Producer", "Contractor"};
//        return String.format("%s %d - %s Division",
//                prefixes[random.nextInt(prefixes.length)],
//                index + 1,
//                company.getCompanyName());
//    }
//
//    private List<Stakeholder> createStakeholders(Company company, List<EsrsTopic> topics, List<ValueChainObject> valueChainObjects) {
//        List<Stakeholder> stakeholders = new ArrayList<>();
//        String[] stakeholderTypes = {
//                "Employee", "Supplier", "Customer", "Investor", "Community Representative",
//                "NGO", "Government", "Trade Union", "Industry Expert", "Academic"
//        };
//        String[] roles = {
//                "Manager", "Director", "Coordinator", "Specialist", "Advisor",
//                "Consultant", "Analyst", "Representative", "Lead", "Expert"
//        };
//
//        int numStakeholders = random.nextInt(5) + 3; // 3-7 stakeholders per company
//        for (int i = 0; i < numStakeholders; i++) {
//            Stakeholder stakeholder = new Stakeholder();
//            String type = stakeholderTypes[random.nextInt(stakeholderTypes.length)];
//
//            stakeholder.setName(generateStakeholderName(type, i));
//            stakeholder.setEmail(generateStakeholderEmail(type, i, company));
//            stakeholder.setStakeholderType(type);
//            stakeholder.setRole(roles[random.nextInt(roles.length)]);
//            stakeholder.setCompany(company);
//            stakeholder.setStatus(StakeholderStatus.COMPLETED);
//
//            // Assign random value chain objects (1-3 objects per stakeholder)
//            int numVCO = random.nextInt(Math.min(3, valueChainObjects.size())) + 1;
//            stakeholder.setValueChainObjects(new ArrayList<>(valueChainObjects.subList(0, numVCO)));
//
//            // Assign relevant ESRS topics (3-7 topics per stakeholder)
//            int numTopics = random.nextInt(5) + 3;
//            List<EsrsTopic> relevantTopics = new ArrayList<>(topics);
//            Collections.shuffle(relevantTopics);
//            stakeholder.setEsrsTopics(new ArrayList<>(relevantTopics.subList(0, Math.min(numTopics, relevantTopics.size()))));
//
//            // Set progress indicators
//            int totalDatapoints = 20;
//            stakeholder.setTotalDatapoints(totalDatapoints);
//            stakeholder.setCompletedDatapoints(random.nextInt(totalDatapoints + 1));
//
//            stakeholders.add(stakeholderRepository.save(stakeholder));
//        }
//
//        return stakeholders;
//    }
//
//    private String generateStakeholderName(String type, int index) {
//        String[] firstNames = {"John", "Sarah", "Michael", "Emma", "David", "Lisa", "James", "Maria", "Robert", "Anna"};
//        String[] lastNames = {"Smith", "Johnson", "Brown", "Davis", "Wilson", "Moore", "Taylor", "Anderson", "Thomas", "Jackson"};
//        return String.format("%s %s (%s)",
//                firstNames[random.nextInt(firstNames.length)],
//                lastNames[random.nextInt(lastNames.length)],
//                type);
//    }
//
//    private String generateStakeholderEmail(String type, int index, Company company) {
//        return String.format("%s.stakeholder%d@%s.example.com",
//                type.toLowerCase().replace(" ", ""),
//                index + 1,
//                company.getCompanyName().toLowerCase().replace(" ", ""));
//    }
//
//    private boolean isTopicRelevantForCompany(Company company, EsrsTopic topic) {
//        // More sophisticated relevance logic based on company industry and topic area
//        if (company.getIndustry() == null) {
//            return random.nextDouble() > 0.3; // 70% chance if no industry specified
//        }
//
//        // Higher relevance for environmental topics in industrial sectors
//        if (topic.getArea().equals("Environmental") &&
//                (company.getIndustry().toLowerCase().contains("manufacturing") ||
//                        company.getIndustry().toLowerCase().contains("energy") ||
//                        company.getIndustry().toLowerCase().contains("transportation"))) {
//            return random.nextDouble() > 0.1; // 90% chance
//        }
//
//        // Higher relevance for social topics in service sectors
//        if (topic.getArea().equals("Social") &&
//                (company.getIndustry().toLowerCase().contains("service") ||
//                        company.getIndustry().toLowerCase().contains("retail") ||
//                        company.getIndustry().toLowerCase().contains("healthcare"))) {
//            return random.nextDouble() > 0.1; // 90% chance
//        }
//
//        // Higher relevance for governance topics in financial sector
//        if (topic.getArea().equals("Governance") &&
//                (company.getIndustry().toLowerCase().contains("financial") ||
//                        company.getIndustry().toLowerCase().contains("banking") ||
//                        company.getIndustry().toLowerCase().contains("insurance"))) {
//            return random.nextDouble() > 0.1; // 90% chance
//        }
//
//        return random.nextDouble() > 0.3; // 70% chance for other combinations
//    }
//
//    private void createMaterialityDetails(Company company, EsrsTopic topic, List<Stakeholder> stakeholders) {
//        IroEvaluation details = new IroEvaluation();
//
//        // Set relationships
//        details.setCompany(company);
//        details.setEsrsTopic(topic);
//
//        // General Information
//        details.setRiskChance(random.nextBoolean() ? "risk" : "chance");
//        details.setAffectedArea(getRandomAffectedArea());
//        details.setDescription(generateDescription(topic));
//        details.setEffect(generateEffect(topic));
//        details.setConnection(getRandomConnection());
//
//        // Impact Materiality
//        details.setScale(random.nextInt(4) + 1);
//        details.setScope(random.nextInt(4) + 1);
//        details.setIrreversibility(random.nextInt(4) + 1);
//        details.setProbability(0.2 + random.nextDouble() * 0.8);
//        details.setImpactMaterialityActualImpact(generateImpactDescription(topic, "actual"));
//        details.setImpactMaterialityPotentialImpact(generateImpactDescription(topic, "potential"));
//
//        // Financial Materiality
//        details.setBasisOfAssessmentOfFinancialImpact(generateFinancialAssessmentBasis(topic));
//        details.setFinancialMaterialityActualImpact(random.nextInt(4) + 1);
//        details.setFinancialMaterialityPotentialImpact(generateFinancialImpactDescription(topic));
//        details.setTimeSpan(0.2 + random.nextDouble() * 0.8);
//
//        // Metadata
//        details.setCreatedAt(LocalDateTime.now());
//        details.setLastModifiedAt(LocalDateTime.now());
//        details.setLastModifiedBy(stakeholders.get(random.nextInt(stakeholders.size())).getName());
//        details.setIs_locked(false);
//
//        // Calculate and set result
//        String result = calculateMaterialityResult(details);
//        details.setResultMaterialityAssessment(result);
//
//        esrsTopicDetailsRepository.save(details);
//    }
//
//    private String getRandomAffectedArea() {
//        List<String> areas = Arrays.asList(
//                "own operation",
//                "upstream value chain",
//                "downstream value chain"
//        );
//        return areas.get(random.nextInt(areas.size()));
//    }
//
//    private String getRandomConnection() {
//        List<String> connections = Arrays.asList("caused", "linked", "contributed");
//        return connections.get(random.nextInt(connections.size()));
//    }
//
//    private String generateDescription(EsrsTopic topic) {
//        String[] templates = {
//                "Analysis of %s (%s) impacts focusing on %s. This assessment evaluates both direct and indirect effects across the value chain.",
//                "Comprehensive evaluation of %s compliance and impact under %s, particularly regarding %s.",
//                "Strategic assessment of %s considerations within %s framework, emphasizing %s aspects."
//        };
//
//        String template = templates[random.nextInt(templates.length)];
//        return String.format(template,
//                topic.getTopic(),
//                topic.getEsrsCode(),
//                topic.getSubtopic() != null ? topic.getSubtopic() : "general aspects");
//    }
//
//    private String generateEffect(EsrsTopic topic) {
//        String[] templates = {
//                "The %s under %s has specific implications for %s, affecting %s across the organization.",
//                "Implementation of %s standards (%s) influences %s, particularly in relation to %s.",
//                "Compliance with %s requirements impacts %s operations, especially regarding %s initiatives."
//        };
//
//        String template = templates[random.nextInt(templates.length)];
//        return String.format(template,
//                topic.getSubtopic(),
//                topic.getEsrsCode(),
//                topic.getArea().toLowerCase(),
//                topic.getTopic().toLowerCase());
//    }
//
//    private String generateImpactDescription(EsrsTopic topic, String type) {
//        String[] actualImpacts = {
//                "reduced resource consumption",
//                "improved stakeholder engagement",
//                "enhanced compliance standards",
//                "operational efficiency gains",
//                "strengthened risk management"
//        };
//
//        String[] potentialImpacts = {
//                "market position enhancement",
//                "regulatory compliance risks",
//                "stakeholder relationship improvements",
//                "operational efficiency opportunities",
//                "innovation potential"
//        };
//
//        String[] impacts = type.equals("actual") ? actualImpacts : potentialImpacts;
//
//        return String.format("%s impact assessment for %s (%s) shows %s and %s, affecting %s",
//                type.substring(0, 1).toUpperCase() + type.substring(1),
//                topic.getTopic(),
//                topic.getEsrsCode(),
//                impacts[random.nextInt(impacts.length)],
//                impacts[random.nextInt(impacts.length)],
//                topic.getSubtopic());
//    }
//
//    private String generateFinancialAssessmentBasis(EsrsTopic topic) {
//        String[] factors = {
//                "operational costs",
//                "market demand",
//                "regulatory compliance",
//                "capital expenditure",
//                "insurance premiums",
//                "stakeholder relations",
//                "brand value",
//                "innovation potential",
//                "market access",
//                "resource efficiency"
//        };
//
//        // Select 3 random unique factors
//        List<String> selectedFactors = new ArrayList<>(Arrays.asList(factors));
//        Collections.shuffle(selectedFactors);
//        selectedFactors = selectedFactors.subList(0, 3);
//
//        return String.format("Financial assessment of %s (%s) based on %s, %s, and %s factors",
//                topic.getTopic(),
//                topic.getEsrsCode(),
//                selectedFactors.get(0),
//                selectedFactors.get(1),
//                selectedFactors.get(2));
//    }
//
//    private String generateFinancialImpactDescription(EsrsTopic topic) {
//        int impactPercentage = random.nextInt(20) + 1;
//        int timeframe = random.nextInt(5) + 1;
//
//        String[] impactTypes = {
//                "revenue", "operating costs","capital expenditure", "market share", "compliance costs",
//                "investment requirements", "operational efficiency", "resource costs"
//        };
//
//        String impactType = impactTypes[random.nextInt(impactTypes.length)];
//
//        return String.format("Estimated %s impact of %d%% related to %s (%s) within %d years, " +
//                        "considering current market conditions and regulatory trends",
//                impactType,
//                impactPercentage,
//                topic.getTopic(),
//                topic.getEsrsCode(),
//                timeframe);
//    }
//
//    private String calculateMaterialityResult(IroEvaluation details) {
//        // Calculate impact materiality score (0-5 scale)
//        double impactScore = calculateImpactScore(details);
//
//        // Calculate financial materiality score (0-5 scale)
//        double financialScore = calculateFinancialScore(details);
//
//        // Determine materiality classification
//        if (impactScore >= 3.5 && financialScore >= 3.5) {
//            return "Double Material";
//        } else if (impactScore >= 3.5) {
//            return "Impact Material";
//        } else if (financialScore >= 3.5) {
//            return "Financial Material";
//        } else {
//            return "Not Material";
//        }
//    }
//
//    private double calculateImpactScore(IroEvaluation details) {
//        // Impact score based on scale, scope, irreversibility, and probability
//        double baseScore = (details.getScale() + details.getScope() + details.getIrreversibility()) / 3.0;
//        return baseScore * details.getProbability();
//    }
//
//    private double calculateFinancialScore(IroEvaluation details) {
//        // Financial score based on actual impact and time span
//        return details.getFinancialMaterialityActualImpact() * details.getTimeSpan();
//    }
//
//    private String getRandomStakeholderImpact(String topicArea) {
//        Map<String, List<String>> areaSpecificImpacts = new HashMap<>();
//
//        // Environmental impacts
//        areaSpecificImpacts.put("Environmental", Arrays.asList(
//                "resource consumption patterns",
//                "emissions levels",
//                "waste management practices",
//                "biodiversity conservation",
//                "water usage efficiency",
//                "energy consumption patterns",
//                "environmental compliance costs"
//        ));
//
//        // Social impacts
//        areaSpecificImpacts.put("Social", Arrays.asList(
//                "working conditions",
//                "community relations",
//                "human rights practices",
//                "employee satisfaction",
//                "health and safety standards",
//                "diversity and inclusion metrics",
//                "stakeholder engagement levels"
//        ));
//
//        // Governance impacts
//        areaSpecificImpacts.put("Governance", Arrays.asList(
//                "compliance procedures",
//                "risk management practices",
//                "board oversight effectiveness",
//                "ethical business practices",
//                "transparency measures",
//                "anti-corruption controls",
//                "stakeholder communication"
//        ));
//
//        List<String> impacts = areaSpecificImpacts.getOrDefault(topicArea,
//                areaSpecificImpacts.get("Governance")); // Default to governance if area not found
//
//        return impacts.get(random.nextInt(impacts.size()));
//    }
//
//    private String generateTimeframeDescription(double timeSpan) {
//        if (timeSpan <= 0.3) {
//            return "Short-term (1-2 years)";
//        } else if (timeSpan <= 0.6) {
//            return "Medium-term (3-5 years)";
//        } else {
//            return "Long-term (5+ years)";
//        }
//    }
//
//    private String formatMonetaryImpact(int baseAmount) {
//        String[] magnitudes = {"thousand", "million", "billion"};
//        int magnitudeIndex = random.nextInt(magnitudes.length);
//        double amount = baseAmount * (random.nextDouble() * 0.5 + 0.5); // 50-100% of base amount
//        return String.format("%.1f %s EUR", amount, magnitudes[magnitudeIndex]);
//    }
//
//    private List<String> getStakeholderNames(List<Stakeholder> stakeholders, int count) {
//        List<String> names = new ArrayList<>();
//        for (int i = 0; i < Math.min(count, stakeholders.size()); i++) {
//            names.add(stakeholders.get(i).getName());
//        }
//        return names;
//    }
//
//    private String generateValueChainDescription(List<ValueChainObject> valueChainObjects) {
//        if (valueChainObjects.isEmpty()) {
//            return "No value chain objects affected";
//        }
//
//        StringBuilder description = new StringBuilder("Affects ");
//        for (int i = 0; i < Math.min(3, valueChainObjects.size()); i++) {
//            if (i > 0) {
//                description.append(i == valueChainObjects.size() - 1 ? " and " : ", ");
//            }
//            description.append(valueChainObjects.get(i).getName());
//        }
//
//        if (valueChainObjects.size() > 3) {
//            description.append(" and ").append(valueChainObjects.size() - 3).append(" more");
//        }
//
//        return description.toString();
//    }
//
//    private String generateIndustrySpecificContext(String industry, EsrsTopic topic) {
//        if (industry == null) {
//            return "General industry context";
//        }
//
//        return String.format("Specific considerations for %s industry regarding %s under %s framework",
//                industry,
//                topic.getTopic(),
//                topic.getEsrsCode());
//    }
//}