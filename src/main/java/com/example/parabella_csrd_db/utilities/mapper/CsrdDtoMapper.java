// src/main/java/com/example/parabella_csrd_db/mapper/CsrdDtoMapper.java
package com.example.parabella_csrd_db.utilities.mapper;


import com.example.parabella_csrd_db.dto.elessar.CompanyInfoDTO;
import com.example.parabella_csrd_db.dto.elessar.CsrdProjectDTO;
import com.example.parabella_csrd_db.database.maindatabase.model.authentication.User;

import com.example.parabella_csrd_db.database.maindatabase.model.elessar.CsrdProject;
import com.example.parabella_csrd_db.database.maindatabase.model.elessar.ProjectType;
import com.example.parabella_csrd_db.database.maindatabase.model.elessar.landing.CompanyInfo;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

@Component
public class CsrdDtoMapper {

    // --- CompanyInfo Mappers ---

    public CompanyInfoDTO toCompanyInfoDTO(CompanyInfo entity) {
        if (entity == null) return null;
        return new CompanyInfoDTO(
                entity.getId(),
                entity.getCompanyName(),
                entity.getRevenue(),
                entity.getIndustry(),
                entity.getSize(),
                entity.getNumberOfEmployees()
        );
    }

    // Map from DTO used in request body to a new entity
    public CompanyInfo toCompanyInfoEntity(CompanyInfoDTO dto) {
        if (dto == null) return null;
        CompanyInfo entity = new CompanyInfo();
        // ID is ignored from DTO when creating new entity
        entity.setCompanyName(dto.getCompanyName());
        entity.setRevenue(dto.getRevenue());
        entity.setIndustry(dto.getIndustry());
        entity.setSize(dto.getSize());
        entity.setNumberOfEmployees(dto.getNumberOfEmployees());
        return entity;
    }

    // Update an existing entity from DTO used in request body
    public void updateCompanyInfoFromDTO(CompanyInfoDTO dto, CompanyInfo entity) {
        if (dto == null || entity == null) return;
        // ID is not updated from DTO
        entity.setCompanyName(dto.getCompanyName());
        entity.setRevenue(dto.getRevenue());
        entity.setIndustry(dto.getIndustry());
        entity.setSize(dto.getSize());
        entity.setNumberOfEmployees(dto.getNumberOfEmployees());
    }

    // --- CsrdProject Mappers ---

    public CsrdProjectDTO toCsrdProjectDTO(CsrdProject entity) {
        if (entity == null) return null;
        return new CsrdProjectDTO(
                entity.getId(),
                entity.getUser() != null ? entity.getUser().getId() : null,
                entity.getProjectName(),
                entity.getProjectDescription(),
                entity.getProjectType() != null ? entity.getProjectType().name() : null,
                entity.getCreatedAt(),
                entity.getUpdatedAt(),
                toCompanyInfoDTO(entity.getCompanyInfo()) // Map nested CompanyInfo
        );
    }

    public List<CsrdProjectDTO> toCsrdProjectDTOList(List<CsrdProject> entities) {
        return entities.stream()
                .map(this::toCsrdProjectDTO)
                .collect(Collectors.toList());
    }

    public CsrdProject projectDtoToEntityForCreation(CsrdProjectDTO dto, User user) {
        if (dto == null || user == null) return null;
        CsrdProject entity = new CsrdProject();
        // Ignore id, createdAt, updatedAt from DTO
        entity.setUser(user);
        entity.setProjectName(dto.getProjectName());
        entity.setProjectDescription(dto.getProjectDescription());

        if ("companyGroup".equalsIgnoreCase(dto.getProjectType())) {
            entity.setProjectType(ProjectType.COMPANY_GROUP);
        } else {
            entity.setProjectType(ProjectType.COMPANY);
        }

        // --- *** REMOVED: Do not map companyInfo from DTO during creation *** ---
        // The service layer now handles creating the initial empty CompanyInfo.
        // if (dto.getCompanyInfo() != null) {
        //     entity.setCompanyInfo(toCompanyInfoEntity(dto.getCompanyInfo()));
        // }
        // --- *** END REMOVAL *** ---

        return entity;
    }
}