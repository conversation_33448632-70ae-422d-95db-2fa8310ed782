package com.example.parabella_csrd_db.utilities;

import com.example.parabella_csrd_db.service.utilities.excel_to_database.EsrsTopicService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

import java.io.InputStream;


public class EsrsTopicDataLoader {



//    @Override
//    public void run(String... args) throws Exception {
//        InputStream csvInputStream = getClass().getResourceAsStream("/impact_finance_esrs_topics_codes.csv");
//        if (csvInputStream != null) {
//            esrsTopicService.loadCSVData(csvInputStream);
//            System.out.println("CSV data loaded successfully.");
//        } else {
//            System.err.println("CSV file not found!");
//        }
//    }
}
