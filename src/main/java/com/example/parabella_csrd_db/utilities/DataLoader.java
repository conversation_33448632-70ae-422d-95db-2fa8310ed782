
package com.example.parabella_csrd_db.utilities;


import com.example.parabella_csrd_db.database.maindatabase.model.mithril.Industry;
import com.example.parabella_csrd_db.database.maindatabase.model.mithril.ValueChain;
import com.example.parabella_csrd_db.database.maindatabase.repository.mithril.IndustryRepository;
import com.example.parabella_csrd_db.database.maindatabase.repository.mithril.ValueChainRepository;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.boot.CommandLineRunner;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.io.InputStream;


@Component
public class DataLoader implements CommandLineRunner {

    private final IndustryRepository industryRepository;
    private final ValueChainRepository valueChainRepository;

    public DataLoader(IndustryRepository industryRepository, ValueChainRepository valueChainRepository) {
        this.industryRepository = industryRepository;
        this.valueChainRepository = valueChainRepository;
    }

    @Override
    @Transactional
    public void run(String... args) throws Exception {
        if (industryRepository.count() == 0) {
            loadValueChainsFromExcel();
        } else {
            System.out.println("Industries already populated. Skipping data loading.");
        }
    }

    private void loadValueChainsFromExcel() {
        try {
            // Load the Excel file from resources
            ClassPathResource resource = new ClassPathResource("static/excel/value_chains.xlsx");
            InputStream inputStream = resource.getInputStream();

            Workbook workbook = new XSSFWorkbook(inputStream);
            Sheet sheet = workbook.getSheetAt(0); // Assuming data is in the first sheet

            // Iterate over rows, skipping header
            boolean firstRow = true;
            for (Row row : sheet) {
                if (firstRow) {
                    firstRow = false;
                    continue; // Skip header
                }

                Cell industryCell = row.getCell(0, Row.MissingCellPolicy.RETURN_BLANK_AS_NULL);
                Cell objectsCell = row.getCell(1, Row.MissingCellPolicy.RETURN_BLANK_AS_NULL);

                if (industryCell == null || objectsCell == null) {
                    System.out.println("Skipping row " + row.getRowNum() + " due to missing data.");
                    continue; // Skip rows with missing data
                }

                String industryName = industryCell.getStringCellValue().trim();
                String valueChainObjects = objectsCell.getStringCellValue().trim();

                if (!industryName.isEmpty() && !valueChainObjects.isEmpty()) {
                    // Fetch or create the Industry
                    Industry industry = industryRepository.findByName(industryName)
                            .orElseGet(() -> {
                                Industry newIndustry = new Industry(industryName);
                                industryRepository.save(newIndustry);
                                return newIndustry;
                            });

                    // Split the Value Chain Objects and create ValueChain entries
                    String[] objectsArray = valueChainObjects.split(",");
                    for (String obj : objectsArray) {
                        String valueChainKey = obj.trim();
                        if (!valueChainKey.isEmpty()) {
                            // Check for existing ValueChain to prevent duplicates
                            boolean exists = valueChainRepository.findByValueChainKeyAndIndustry_Name(valueChainKey, industryName).isPresent();
                            if (!exists) {
                                ValueChain valueChain = new ValueChain(valueChainKey, industry);
                                valueChainRepository.save(valueChain);
                                System.out.println("Added ValueChain: " + valueChainKey + " under Industry: " + industryName);
                            } else {
                                System.out.println("ValueChain already exists: " + valueChainKey + " under Industry: " + industryName);
                            }
                        }
                    }
                }
            }

            workbook.close();
            inputStream.close();

            System.out.println("Data loading completed successfully!");

        } catch (Exception e) {
            System.err.println("Error loading data from Excel:");
            e.printStackTrace();
        }
    }
}
