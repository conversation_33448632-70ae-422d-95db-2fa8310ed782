package com.example.parabella_csrd_db.utilities.json;

import com.example.parabella_csrd_db.dto.elessar.CsrdTopicsRootDTO;
import com.example.parabella_csrd_db.service.elessar.CsrdLoaderService;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

import java.io.File;
import java.io.FilenameFilter;

@Component
public class CsrdDataLoaderRunner implements CommandLineRunner {

    private final CsrdLoaderService loaderService;

    public CsrdDataLoaderRunner(CsrdLoaderService loaderService) {
        this.loaderService = loaderService;
    }

    @Override
    public void run(String... args) throws Exception {
        // Adjust the directory path as needed:
        // e.g., "src/main/java/com/example/parabella_csrd_db/utilities/excel_to_database/json"
        // or "src/main/resources/json" if you prefer storing your files under resources.
        File folder = new File("src/main/java/com/example/parabella_csrd_db/utilities/excel_to_database/json");

        // Make sure the directory exists
        if (!folder.exists() || !folder.isDirectory()) {
            System.out.println("JSON directory not found. Skipping data load...");
            return;
        }

        // Filter for files named "csrd_topics_*.json"
        File[] jsonFiles = folder.listFiles(new FilenameFilter() {
            @Override
            public boolean accept(File dir, String name) {
                return name.startsWith("csrd_topics_") && name.endsWith(".json");
            }
        });

        if (jsonFiles == null || jsonFiles.length == 0) {
            System.out.println("No JSON files found in directory. Skipping data load...");
            return;
        }

        ObjectMapper mapper = new ObjectMapper();

        // Process each matching file
        for (File file : jsonFiles) {
            System.out.println("Processing file: " + file.getName());
            CsrdTopicsRootDTO rootDTO = mapper.readValue(file, CsrdTopicsRootDTO.class);
            loaderService.loadTopicsRoot(rootDTO);
        }
    }
}
