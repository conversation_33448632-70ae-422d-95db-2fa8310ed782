package com.example.parabella_csrd_db.utilities;

import com.example.parabella_csrd_db.database.maindatabase.model.authentication.User;
import com.example.parabella_csrd_db.database.maindatabase.model.mithril.*;
import com.example.parabella_csrd_db.database.maindatabase.repository.authentication.UserRepository;
import com.example.parabella_csrd_db.database.maindatabase.repository.mithril.*;
import com.example.parabella_csrd_db.database.maindatabase.repository.mithril.stakeholder_navigation.StakeholderRepository;
import com.example.parabella_csrd_db.dto.mithril.seeder.DataSeedDto;
import com.example.parabella_csrd_db.dto.mithril.seeder.EsrsTopicDto;
import com.example.parabella_csrd_db.dto.mithril.seeder.IroEvaluationSeedDto;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.text.similarity.LevenshteinDistance;
import org.springframework.boot.CommandLineRunner;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.io.InputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Component
@Slf4j
@RequiredArgsConstructor
public class ComprehensiveDataSeeder implements CommandLineRunner {

    // --- Repositories and Configuration ---
    private final ObjectMapper objectMapper;
    private final UserRepository userRepository;
    private final ProjectRepository projectRepository;
    private final CompanyRepository companyRepository;
    private final StakeholderRepository stakeholderRepository;
    private final EsrsTopicsRepository esrsTopicRepository;
    private final EsrsTopicSelectionRepository esrsTopicSelectionRepository;
    private final IroRepository iroRepository;

    private static final String JSON_DATA_FILE_PATH = "json/gd_json.json";
    private static final Long REQUIRED_USER_ID = 66L;
    private static final String PROJECT_NAME = "G+D DMA 2024";
    // --- THRESHOLD FOR FUZZY MATCHING: Max 10 typos allowed ---
    private static final int LEVENSHTEIN_DISTANCE_THRESHOLD = 10;
    // --- CONSTANTS FOR CALCULATIONS ---
    private static final int SCALE = 2;
    private static final RoundingMode ROUNDING_MODE = RoundingMode.HALF_UP;

    @Override
    @Transactional
    public void run(String... args) throws Exception {
        // --- MODIFICATION START: Check if data already exists ---
        // First, check for the project associated with the specific user.
        Optional<Project> existingProject = projectRepository.findAll().stream()
                .filter(p -> PROJECT_NAME.equals(p.getProjectName())
                        && p.getUser() != null
                        && REQUIRED_USER_ID.equals(p.getUser().getId()))
                .findFirst();

        if (existingProject.isPresent()) {
            // If the project exists, find the associated company.
            Optional<Company> existingCompany = companyRepository.findAll().stream()
                    .filter(c -> c.getProject() != null && existingProject.get().getId().equals(c.getProject().getId()))
                    .findFirst();

            if (existingCompany.isPresent()) {
                // An existing IRO count for the company is our indicator that data has been seeded.
                // Note: You may need to add `long countByCompanyId(Long companyId);` to your IroRepository.
                if (iroRepository.countByCompanyId(existingCompany.get().getId()) > 0) {
                    log.info("✅ Data for project '{}' already populated. Seeding will be skipped.", PROJECT_NAME);
                    return; // Exit the run method
                }
            }
        }
        // --- MODIFICATION END ---

        log.info("🚀 Starting comprehensive data seeding for project '{}'...", PROJECT_NAME);

        List<DataSeedDto> data = loadJsonData();
        if (data.isEmpty()) {
            log.warn("No data found in JSON file [{}]. Aborting seed.", JSON_DATA_FILE_PATH);
            return;
        }

        User projectOwner = userRepository.findById(REQUIRED_USER_ID)
                .orElseThrow(() -> new IllegalStateException(
                        String.format("Seeding failed: Required User with ID %d not found.", REQUIRED_USER_ID)
                ));

        List<EsrsTopic> esrsTopicsFromDb = esrsTopicRepository.findAll();
        if (esrsTopicsFromDb.isEmpty()) {
            log.error("Seeding failed: The 'esrs_topic' table is empty. It must be pre-populated.");
            return;
        }

        Project project = findOrCreateProject(projectOwner);
        Company company = findOrCreateCompany(project);
        clearExistingProjectData(company);
        Map<String, Stakeholder> stakeholders = findOrCreateStakeholders(data, company, project);
        seedIroData(data, company, stakeholders, esrsTopicsFromDb);

        log.info("✅ Comprehensive data seeding and overwrite completed for Project '{}'.", PROJECT_NAME);
    }

    private void seedIroData(List<DataSeedDto> data, Company company, Map<String, Stakeholder> stakeholders, List<EsrsTopic> esrsTopicsFromDb) {
        Map<String, EsrsTopicSelection> selectionCache = new HashMap<>();
        Map<Long, Set<String>> iroNameCache = new HashMap<>();

        for (DataSeedDto row : data) {
            EsrsTopic esrsTopic = findBestTopicMatch(row.getEsrsTopic(), esrsTopicsFromDb);
            if (esrsTopic == null) continue;

            Stakeholder stakeholder = stakeholders.get(row.getStakeholder());
            if (stakeholder == null) continue;

            String selectionKey = String.format("%d-%d-%d", company.getId(), esrsTopic.getId(), stakeholder.getId());
            EsrsTopicSelection selection = selectionCache.get(selectionKey);

            if (selection == null) {
                log.info("Creating new EsrsTopicSelection for key: {}", selectionKey);
                selection = new EsrsTopicSelection();
                selection.setCompany(company);
                selection.setEsrsTopic(esrsTopic);
                selection.setStakeholder(stakeholder);
                selection.setRelevant(false);
                selection = esrsTopicSelectionRepository.save(selection);
                selectionCache.put(selectionKey, selection);
            }

            boolean isIroRelevant = row.getIro() != null && row.getIro().getName() != null && !row.getIro().getName().isBlank();

            if (isIroRelevant) {
                if (!selection.getRelevant()) {
                    selection.setRelevant(true);
                }

                Set<String> existingIroNames = iroNameCache.computeIfAbsent(selection.getId(), k -> new HashSet<>());
                if (existingIroNames.contains(row.getIro().getName())) {
                    continue;
                }

                log.info("Creating new IRO: '{}' and linking to selection ID {}", row.getIro().getName(), selection.getId());
                Iro iro = new Iro();
                iro.setCompany(company);
                iro.setStakeholder(stakeholder);
                iro.setEsrsTopicSelection(selection);
                iro.setName(row.getIro().getName());
                iro.setIroType(row.getIro().getIroType().toLowerCase());

                IroEvaluation evaluation = mapIroEvaluation(row.getIroEvaluation(), company);

                String finalRelevance = performAndSetCalculations(iro, evaluation);

                iro.setIroEvaluation(evaluation);
                evaluation.setIro(iro);
                iroRepository.save(iro);
                existingIroNames.add(row.getIro().getName());

                selection.setFinalRelevance(finalRelevance);
                esrsTopicSelectionRepository.save(selection);

            } else {
                if (!selection.getRelevant() && row.getIroEvaluation() != null) {
                    selection.setReasonIrrelevance(row.getIroEvaluation().getDescription());
                    esrsTopicSelectionRepository.save(selection);
                }
            }
        }
    }

    private String performAndSetCalculations(Iro iro, IroEvaluation evaluation) {
        if (iro == null || evaluation == null) {
            return "Not Relevant";
        }

        BigDecimal finalScore;
        String finalRelevance;
        BigDecimal threshold = new BigDecimal("2");

        evaluation.setImpactMaterialityPotentialImpact("0.00");
        evaluation.setFinancialMaterialityPotentialImpact("0.00");

        if ("impact".equalsIgnoreCase(iro.getIroType())) {
            BigDecimal scale = Optional.ofNullable(evaluation.getScale()).map(BigDecimal::valueOf).orElse(new BigDecimal("1"));
            BigDecimal scope = Optional.ofNullable(evaluation.getScope()).map(BigDecimal::valueOf).orElse(new BigDecimal("1"));

            boolean isNegativeImpact = "negative".equalsIgnoreCase(evaluation.getPositiveNegativeImpact());
            BigDecimal irreversibility = isNegativeImpact
                    ? Optional.ofNullable(evaluation.getIrreversibility()).map(BigDecimal::valueOf).orElse(new BigDecimal("1"))
                    : BigDecimal.ZERO;

            if (isNegativeImpact) {
                finalScore = scale.add(scope).add(irreversibility).divide(new BigDecimal("3"), SCALE, ROUNDING_MODE);
            } else {
                finalScore = scale.add(scope).divide(new BigDecimal("2"), SCALE, ROUNDING_MODE);
            }

            finalRelevance = finalScore.compareTo(threshold) >= 0 ? "Relevant" : "Not Relevant";

            if ("yes".equalsIgnoreCase(evaluation.getHumanRightsImpact())) {
                finalScore = new BigDecimal("5.00");
                finalRelevance = "Relevant";
            }

            evaluation.setImpactMaterialityPotentialImpact(String.format(Locale.US, "%.2f", finalScore));
            evaluation.setResultMaterialityAssessment(finalRelevance);

        } else if ("financial".equalsIgnoreCase(iro.getIroType())) {
            BigDecimal financialEffect = Optional.ofNullable(evaluation.getFinancialMaterialityActualImpact()).map(BigDecimal::valueOf).orElse(new BigDecimal("1"));
            BigDecimal probability = Optional.ofNullable(evaluation.getProbability()).map(BigDecimal::valueOf).orElse(new BigDecimal("0.2"));

            finalScore = financialEffect.multiply(probability).setScale(SCALE, ROUNDING_MODE);
            finalRelevance = finalScore.compareTo(threshold) >= 0 ? "Relevant" : "Not Relevant";

            evaluation.setFinancialMaterialityPotentialImpact(String.format(Locale.US, "%.2f", finalScore));
            evaluation.setResultMaterialityAssessment(finalRelevance);
        } else {
            finalRelevance = "Not Relevant";
        }

        return finalRelevance;
    }

    private EsrsTopic findBestTopicMatch(EsrsTopicDto dto, List<EsrsTopic> allTopics) {
        if (dto == null) return null;

        EsrsTopic bestMatch = null;
        int minDistance = Integer.MAX_VALUE;

        String sourceString = buildNormalizedString(dto);
        LevenshteinDistance levenshtein = new LevenshteinDistance(LEVENSHTEIN_DISTANCE_THRESHOLD);

        for (EsrsTopic dbTopic : allTopics) {
            String targetString = buildNormalizedString(dbTopic);
            int distance = levenshtein.apply(sourceString, targetString);

            if (distance != -1 && distance < minDistance) {
                minDistance = distance;
                bestMatch = dbTopic;
                if (minDistance == 0) break;
            }
        }

        if (bestMatch != null) {
            log.trace("Fuzzy match found for '{}'. Best match: '{}' with distance {}.", sourceString, buildNormalizedString(bestMatch), minDistance);
        }
        return bestMatch;
    }

    private String normalize(String input) {
        return (input == null) ? "" : input.trim().toLowerCase();
    }

    private String buildNormalizedString(EsrsTopic topic) {
        return String.join("|",
                normalize(topic.getTopic()),
                normalize(topic.getSubtopic()),
                normalize(topic.getSubSubTopic())
        );
    }

    private String buildNormalizedString(EsrsTopicDto dto) {
        String subSubTopic = dto.getSubSubTopic();
        if (subSubTopic != null && ("Social".equalsIgnoreCase(dto.getArea()) || "Governance".equalsIgnoreCase(dto.getArea()))) {
            subSubTopic = subSubTopic.replaceAll("^[A-Z][0-9]+[:\\s]*", "").trim();
        }
        return String.join("|",
                normalize(dto.getTopic()),
                normalize(dto.getSubtopic()),
                normalize(subSubTopic)
        );
    }

    private List<DataSeedDto> loadJsonData() throws Exception {
        try (InputStream inputStream = new ClassPathResource(JSON_DATA_FILE_PATH).getInputStream()) {
            return objectMapper.readValue(inputStream, new TypeReference<>() {});
        }
    }

    private void clearExistingProjectData(Company company) {
        log.warn("Clearing existing IRO and Topic Selection data for company ID: {} before seeding.", company.getId());
        iroRepository.deleteAll(iroRepository.findAllByCompanyId(company.getId()));
        esrsTopicSelectionRepository.deleteAll(esrsTopicSelectionRepository.findAllByCompanyId(company.getId()));
        log.info("Data clearing complete. Changes will be flushed to the database.");
    }

    private Project findOrCreateProject(User owner) {
        return projectRepository.findAll().stream()
                .filter(p -> PROJECT_NAME.equals(p.getProjectName()) && owner.getId().equals(p.getUser().getId()))
                .findFirst()
                .orElseGet(() -> {
                    log.info("Project '{}' not found for user ID {}. Creating new project.", PROJECT_NAME, owner.getId());
                    Project project = new Project();
                    project.setUser(owner);
                    project.setProjectName(PROJECT_NAME);
                    project.setProjectDescription("Automatically generated project from the 2024 DMA assessment data seeder.");
                    project.setCreatedAt(LocalDateTime.now());
                    project.setProjectType("CSRD_DMA");
                    return projectRepository.save(project);
                });
    }

    private Company findOrCreateCompany(Project project) {
        return companyRepository.findAll().stream()
                .filter(c -> c.getProject() != null && project.getId().equals(c.getProject().getId()))
                .findFirst()
                .orElseGet(() -> {
                    log.info("Company for project '{}' not found. Creating new company 'Giesecke+Devrient'.", project.getProjectName());
                    Company company = new Company();
                    company.setProject(project);
                    company.setCompanyName("Giesecke+Devrient");
                    company.setIndustry("Technology and Security");
                    company.setNumEmployees("14000");
                    company.setIsSubCompany(false);
                    company.setAddress("Prinzregentenstrasse 159, 81677 Munich, Germany");
                    company.setVat("*********");
                    company.setRevenues(2530000000.0);
                    return companyRepository.save(company);
                });
    }

    private Map<String, Stakeholder> findOrCreateStakeholders(List<DataSeedDto> data, Company company, Project project) {
        Map<String, Stakeholder> existingStakeholders = stakeholderRepository.findAll().stream()
                .filter(s -> s.getProject() != null && project.getId().equals(s.getProject().getId()))
                .collect(Collectors.toMap(Stakeholder::getName, Function.identity()));

        Set<String> requiredStakeholderNames = data.stream()
                .map(DataSeedDto::getStakeholder)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

        Random random = new Random();
        for (String name : requiredStakeholderNames) {
            existingStakeholders.computeIfAbsent(name, n -> {
                log.info("Creating new stakeholder '{}' for project '{}'", n, project.getProjectName());
                Stakeholder stakeholder = new Stakeholder();
                stakeholder.setName(n);
                stakeholder.setCompany(company);
                stakeholder.setProject(project);
                stakeholder.setEmail(n.toLowerCase().replaceAll("\\s+", ".") + "@g-d.com");
                stakeholder.setRole("Stakeholder Contributor");
                stakeholder.setStakeholderType(n);
                stakeholder.setToken(UUID.randomUUID().toString());
                stakeholder.setStatus(StakeholderStatus.INVITED);
                int totalDataPoints = 5 + random.nextInt(16);
                int completedDataPoints = random.nextInt(totalDataPoints + 1);
                stakeholder.setTotalDatapoints(totalDataPoints);
                stakeholder.setCompletedDatapoints(completedDataPoints);
                stakeholder.setIs_responsible(false);
                stakeholder.setValueChainObjects(new ArrayList<>());
                stakeholder.setEsrsTopics(new ArrayList<>());
                return stakeholderRepository.save(stakeholder);
            });
        }
        return existingStakeholders;
    }

    private IroEvaluation mapIroEvaluation(IroEvaluationSeedDto dto, Company company) {
        IroEvaluation eval = new IroEvaluation();
        eval.setCompany(company);
        eval.setDescription(dto.getDescription());
        eval.setDirectIndirectImpact(dto.getDirectIndirectImpact());
        eval.setAffectedArea(dto.getAffectedArea());
        eval.setTimeHorizon(dto.getTimeHorizon() != null ? dto.getTimeHorizon().trim() : null);
        eval.setPositiveNegativeImpact(dto.getPositiveNegativeImpact());
        eval.setActualPotentialImpact(dto.getActualPotentialImpact());
        eval.setHumanRightsImpact(dto.getHumanRightsImpact());
        eval.setScale(dto.getScale());
        eval.setScope(dto.getScope());
        eval.setIrreversibility(dto.getIrreversibility());
        eval.setRiskOpportunity(dto.getRiskOpportunity());
        eval.setFinancialMaterialityActualImpact(dto.getFinancialMaterialityActualImpact());
        eval.setProbability(dto.getProbability());
        eval.setIsLocked(false);
        eval.setLastModifiedBy("System-Seeder");
        eval.setLastModifiedAt(LocalDateTime.now());
        return eval;
    }
}