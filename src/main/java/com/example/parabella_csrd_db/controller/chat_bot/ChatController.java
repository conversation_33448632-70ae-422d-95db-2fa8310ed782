package com.example.parabella_csrd_db.controller.chat_bot;

import com.example.parabella_csrd_db.dto.ai.*;
import com.example.parabella_csrd_db.dto.ai.mithril.DescriptionEffectRequest;
import com.example.parabella_csrd_db.dto.ai.mithril.ExtendIrrelevanceReasonRequest;
import com.example.parabella_csrd_db.dto.ai.mithril.ReasonForIrrelevanceRequest;
import com.example.parabella_csrd_db.database.maindatabase.model.authentication.User;
import com.example.parabella_csrd_db.database.maindatabase.model.chatBot.Conversation;
import com.example.parabella_csrd_db.security.services.UserDetailsServiceImpl;
import com.example.parabella_csrd_db.service.ai.PromptingService;
import com.example.parabella_csrd_db.service.chat_bot.ConversationService;
import com.example.parabella_csrd_db.service.chat_bot.OpenAIService;
import com.example.parabella_csrd_db.service.mithril.ValueChainObjectService;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * Controller to handle chat-related endpoints for the CSRD
 * double materiality assessment. Delegates prompt generation
 * to PromptingService and model inference to OpenAIService.
 */
@RestController
@RequestMapping("/api/chat")
public class ChatController {

    private final ConversationService conversationService;
    private final OpenAIService openAIService;
    private final UserDetailsServiceImpl userService;
    private final ValueChainObjectService valueChainObjectService;
    private final PromptingService promptingService;

    @Autowired
    public ChatController(
            ConversationService conversationService,
            OpenAIService openAIService,
            UserDetailsServiceImpl userService,
            ValueChainObjectService valueChainObjectService,
            PromptingService promptingService
    ) {
        this.conversationService = conversationService;
        this.openAIService = openAIService;
        this.userService = userService;
        this.valueChainObjectService = valueChainObjectService;
        this.promptingService = promptingService;
    }

    @PostMapping("/send")
    public ChatMessageResponse sendMessage(@RequestBody ChatMessageRequest request, Authentication authentication) {
        String username = authentication.getName();
        User user = userService.findByUsername(username);
        Conversation conversation = conversationService.getOrCreateConversation(user);

        // Add user's message to conversation
        conversationService.addMessage(conversation, "user", request.getMessage());

        // Build messages for OpenAI API
        List<Map<String, String>> messages = conversationService.buildMessages(conversation);

        try {
            String assistantReply = openAIService.getChatCompletion(messages);

            // Add assistant's reply to conversation
            conversationService.addMessage(conversation, "assistant", assistantReply);

            return new ChatMessageResponse(assistantReply);
        } catch (Exception e) {
            // Handle exceptions as appropriate in your environment
            throw new RuntimeException("Failed to get response from AI service", e);
        }
    }

    @CrossOrigin(origins = {"https://storage.googleapis.com", "http://localhost:5173"})
    @GetMapping("/history")
    public List<MessageDTO> getConversationHistory(Authentication authentication) {
        String username = authentication.getName();
        User user = userService.findByUsername(username);
        Conversation conversation = conversationService.getOrCreateConversation(user);

        // Map Message entities to MessageDTO
        List<MessageDTO> dtoList = conversation.getMessages().stream()
                .map(msg -> {
                    User conversationUser = conversation.getUser();
                    return new MessageDTO(
                            msg.getId(),
                            msg.getRole(),
                            msg.getContent(),
                            msg.getTimestamp(),
                            conversation.getId(),
                            conversationUser != null ? conversationUser.getId() : null,
                            conversationUser != null ? conversationUser.getUsername() : null
                    );
                })
                .toList();

        return dtoList;
    }

    /**
     * Endpoint to generate an irrelevance reason prompt & get the LLM response
     */
    @CrossOrigin(origins = {"https://storage.googleapis.com", "http://localhost:5173"})
    @PostMapping("/generate-irrelevance-reason")
    public ChatMessageResponse generateIrrelevanceReason(
            @RequestBody ReasonForIrrelevanceRequest request,
            Authentication authentication
    ) {
        // If needed, you can fetch user info here
        // String username = authentication.getName();
        // User user = userService.findByUsername(username);

        // Build the prompt via PromptingService
        String prompt = promptingService.buildIrrelevanceReasonPrompt(request);
        System.out.println("[IrrelevanceReason Prompt] " + prompt);

        try {
            String assistantReply = openAIService.getCompletionForReason(prompt);
            return new ChatMessageResponse(assistantReply);
        } catch (Exception e) {
            throw new RuntimeException("Failed to generate irrelevance reason", e);
        }
    }

    /**
     * Endpoint to refine or extend an irrelevance reason
     */
    @PostMapping("/extend-irrelevance-reason")
    public ChatMessageResponse extendIrrelevanceReason(
            @RequestBody ExtendIrrelevanceReasonRequest request,
            Authentication authentication
    ) {
        // Build a new prompt to refine the existing text
        String prompt = promptingService.buildExtendIrrelevanceReasonPrompt(request);

        try {
            String assistantReply = openAIService.getCompletionForReason(prompt);
            return new ChatMessageResponse(assistantReply);
        } catch (Exception e) {
            throw new RuntimeException("Failed to extend irrelevance reason", e);
        }
    }

    /**
     * Endpoint to generate a Description or an Effect (depending on generationType)
     */
    @PostMapping("/generateDescriptionEffect")
    public ChatMessageResponse generateDescriptionEffect(
            @RequestBody DescriptionEffectRequest request,
            Authentication authentication
    ) {
        // Build the prompt
        String prompt = promptingService.buildDescriptionEffectPrompt(request);
        System.out.println("[Description/Effect Prompt] " + prompt);

        try {
            String assistantReply = openAIService.getCompletionForReason(prompt);
            return new ChatMessageResponse(assistantReply);
        } catch (Exception e) {
            throw new RuntimeException("Failed to generate text for " + request.getGenerationType(), e);
        }
    }
}
