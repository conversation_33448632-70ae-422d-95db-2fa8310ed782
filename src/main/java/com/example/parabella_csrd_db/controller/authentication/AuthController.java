package com.example.parabella_csrd_db.controller.authentication;

import com.example.parabella_csrd_db.dto.authentication.request.ForgotPasswordRequest;
import com.example.parabella_csrd_db.dto.authentication.request.LoginRequest;
import com.example.parabella_csrd_db.dto.authentication.request.SignUpRequest;
import com.example.parabella_csrd_db.dto.authentication.response.JwtResponse;
import com.example.parabella_csrd_db.dto.authentication.response.MessageResponse;
import com.example.parabella_csrd_db.database.maindatabase.model.authentication.ERole;
import com.example.parabella_csrd_db.database.maindatabase.model.authentication.Role;
import com.example.parabella_csrd_db.database.maindatabase.model.authentication.User;
import com.example.parabella_csrd_db.database.maindatabase.repository.authentication.RoleRepository;
import com.example.parabella_csrd_db.database.maindatabase.repository.authentication.UserRepository;
import com.example.parabella_csrd_db.security.jwt.JwtUtils;
import com.example.parabella_csrd_db.security.services.UserDetailsImpl;
import com.example.parabella_csrd_db.security.utils.ForgotPasswordService;
import com.example.parabella_csrd_db.security.utils.TotpUtils;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.web.bind.annotation.*;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Authentication Controller for the Parabella CSRD Application
 *
 * This controller handles all authentication-related operations including:
 * - User registration and login
 * - Two-Factor Authentication (2FA) using TOTP (Time-based One-Time Password)
 * - Password reset functionality
 * - JWT token generation and validation
 * - Role-based access control
 *
 * Security Features:
 * - CORS enabled for specific origins (Google Cloud Storage and localhost)
 * - JWT-based authentication with role-based authorization
 * - Mandatory 2FA for all users using TOTP
 * - Secure password reset with token-based verification
 * - Password encryption using BCrypt
 *
 * Supported Roles:
 * - ROLE_USER: Standard user access
 * - ROLE_MODERATOR: Moderator privileges
 * - ROLE_ADMIN: Full administrative access
 *
 * API Endpoints:
 * - POST /api/auth/signup: User registration with 2FA setup
 * - POST /api/auth/signin: User authentication with 2FA verification
 * - POST /api/auth/forgot-password: Initiate password reset process
 * - POST /api/auth/reset-password: Complete password reset with token
 * - POST /api/auth/verify2fa: Verify 2FA setup during registration
 * - PATCH /api/auth/enable2fa: Enable 2FA for existing users
 * - PATCH /api/auth/disable2fa: Disable 2FA (requires authentication)
 *
 * <AUTHOR> Development Team
 * @version 1.0
 * @since 2024
 */
@CrossOrigin(origins = {"https://storage.googleapis.com", "http://localhost:5173"})
@RestController
@RequestMapping("/api/auth")
public class AuthController {
    @Autowired
    AuthenticationManager authenticationManager;

    @Autowired
    UserRepository userRepository;

    @Autowired
    RoleRepository roleRepository;

    @Autowired
    PasswordEncoder encoder;

    @Autowired
    JwtUtils jwtUtils;

    @Autowired
    private ForgotPasswordService forgotPasswordService;

    /**
     * Initiates the password reset process for a user account
     *
     * This endpoint allows users to request a password reset by providing their email address.
     * If the email is associated with an existing account, a secure reset link will be sent
     * to the user's email address. The response is intentionally generic to prevent email enumeration attacks.
     *
     * Security Considerations:
     * - Generic response message prevents email enumeration
     * - Reset tokens are time-limited and single-use
     * - Rate limiting should be implemented to prevent abuse
     *
     * @param request ForgotPasswordRequest containing the user's email address
     * @return ResponseEntity with a generic success message regardless of email existence
     *
     * @apiNote POST /api/auth/forgot-password
     * @since 1.0
     */
    @PostMapping("/forgot-password")
    public ResponseEntity<?> forgotPassword(@RequestBody @Valid ForgotPasswordRequest request) {
        forgotPasswordService.initiatePasswordReset(request.getEmail());

        return ResponseEntity.ok(
                Collections.singletonMap("message", "If that email is associated with an account, a reset link was sent.")
        );
    }


    /**
     * Completes the password reset process using a valid reset token
     *
     * This endpoint allows users to set a new password after clicking the reset link
     * sent to their email. The token must be valid and not expired for the reset to succeed.
     *
     * Security Considerations:
     * - Tokens are single-use and expire after a set time period
     * - New password should meet complexity requirements
     * - Old password is invalidated immediately
     * - User sessions are terminated after password reset
     *
     * @param token The unique reset token from the email link
     * @param newPassword The new password to set for the user account
     * @return boolean indicating success (true) or failure (false) of the reset operation
     *
     * @apiNote POST /api/auth/reset-password
     * @since 1.0
     */
    @PostMapping("/reset-password")
    public boolean resetPassword(@RequestParam String token,
                                 @RequestParam String newPassword) {
        return forgotPasswordService.resetPassword(token, newPassword);
    }

    /**
     * Authenticates a user with username/password and mandatory 2FA verification
     *
     * This endpoint handles the complete user authentication flow including:
     * 1. Username/password verification
     * 2. TOTP (2FA) setup for new users
     * 3. TOTP verification for existing users
     * 4. JWT token generation upon successful authentication
     *
     * Authentication Flow:
     * - If user has no TOTP secret: Generate secret, return QR code for setup
     * - If user has TOTP secret: Validate provided TOTP code
     * - If TOTP is valid: Generate and return JWT token with user details
     *
     * Security Features:
     * - Mandatory 2FA for all users
     * - JWT tokens contain user ID, username, email, and roles
     * - Failed authentication returns appropriate error messages
     *
     * @param loginRequest LoginRequest containing username, password, and optional TOTP code
     * @return ResponseEntity containing either:
     *         - JWT token and user details (successful authentication)
     *         - QR code URL for 2FA setup (new users)
     *         - Error message (authentication failure)
     *
     * @throws RuntimeException if user is not found after successful authentication
     *
     * @apiNote POST /api/auth/signin
     * @since 1.0
     */
    @PostMapping("/signin")
    public ResponseEntity<?> authenticateUser(@Valid @RequestBody LoginRequest loginRequest) {
        Authentication authentication = authenticationManager.authenticate(
                new UsernamePasswordAuthenticationToken(loginRequest.getUsername(), loginRequest.getPassword())
        );

        SecurityContextHolder.getContext().setAuthentication(authentication);
        UserDetailsImpl userDetails = (UserDetailsImpl) authentication.getPrincipal();
        User user = userRepository.findByUsername(userDetails.getUsername())
                .orElseThrow(() -> new RuntimeException("User not found after authentication"));

        // Check if user needs TOTP setup
        if (user.getTotpSecret() == null || user.getTotpSecret().isEmpty()) {
            String totpSecret = TotpUtils.generateSecret();
            user.setTotpSecret(totpSecret);
            userRepository.save(user);
            String qrUrl = TotpUtils.getQrCodeUrl(totpSecret, user.getEmail(), "Parabella_Csrd_App");
            return ResponseEntity.status(HttpServletResponse.SC_UNAUTHORIZED)
                    .body(new HashMap<String,Object>() {{
                        put("message", "2FA setup required");
                        put("qrCodeUrl", qrUrl);
                        put("username", user.getUsername());
                    }});
        }

        // If user already has TOTP secret, validate TOTP code
        Integer providedCode = loginRequest.getTotpCode();
        if (providedCode == null || !TotpUtils.validateCode(user.getTotpSecret(), providedCode)) {
            return ResponseEntity.status(HttpServletResponse.SC_UNAUTHORIZED)
                    .body(new MessageResponse("Error: Invalid or missing TOTP code"));
        }

        // TOTP valid, generate JWT
        String jwt = jwtUtils.generateJwtToken(authentication);
        List<String> roles = userDetails.getAuthorities().stream()
                .map(GrantedAuthority::getAuthority)
                .collect(Collectors.toList());

        return ResponseEntity.ok(new JwtResponse(
                jwt,
                userDetails.getId(),
                userDetails.getUsername(),
                userDetails.getEmail(),
                roles
        ));
    }

    /**
     * Registers a new user account with automatic 2FA setup
     *
     * This endpoint creates a new user account with the following features:
     * - Username and email uniqueness validation
     * - Password encryption using BCrypt
     * - Automatic role assignment (default: ROLE_USER)
     * - Automatic TOTP secret generation for 2FA
     * - QR code generation for authenticator app setup
     *
     * Role Assignment Logic:
     * - No roles specified: Assigns ROLE_USER
     * - "admin": Assigns ROLE_ADMIN
     * - "mod": Assigns ROLE_MODERATOR
     * - Any other value: Assigns ROLE_USER
     *
     * Security Features:
     * - Prevents duplicate usernames and emails
     * - Passwords are encrypted before storage
     * - Mandatory 2FA setup for all new accounts
     * - Returns QR code for immediate authenticator setup
     *
     * @param signUpRequest SignUpRequest containing username, email, password, and optional roles
     * @return ResponseEntity containing either:
     *         - Success message with QR code URL for 2FA setup
     *         - Error message if username or email already exists
     *
     * @throws RuntimeException if required roles are not found in the database
     *
     * @apiNote POST /api/auth/signup
     * @since 1.0
     */
    @PostMapping("/signup")
    public ResponseEntity<?> registerUser(@Valid @RequestBody SignUpRequest signUpRequest) {
        if (userRepository.existsByUsername(signUpRequest.getUsername())) {
            return ResponseEntity.badRequest().body(new MessageResponse("Error: Username is already taken!"));
        }

        if (userRepository.existsByEmail(signUpRequest.getEmail())) {
            return ResponseEntity.badRequest().body(new MessageResponse("Error: Email is already in use!"));
        }

        User user = new User(signUpRequest.getUsername(),
                signUpRequest.getEmail(),
                encoder.encode(signUpRequest.getPassword()));

        Set<String> strRoles = signUpRequest.getRole();
        Set<Role> roles = new HashSet<>();

        if (strRoles == null) {
            Role userRole = roleRepository.findByName(ERole.ROLE_USER)
                    .orElseThrow(() -> new RuntimeException("Error: Role is not found."));
            roles.add(userRole);
        } else {
            for (String role : strRoles) {
                switch (role) {
                    case "admin":
                        Role adminRole = roleRepository.findByName(ERole.ROLE_ADMIN)
                                .orElseThrow(() -> new RuntimeException("Error: Role is not found."));
                        roles.add(adminRole);
                        break;
                    case "mod":
                        Role modRole = roleRepository.findByName(ERole.ROLE_MODERATOR)
                                .orElseThrow(() -> new RuntimeException("Error: Role is not found."));
                        roles.add(modRole);
                        break;
                    default:
                        Role userRole = roleRepository.findByName(ERole.ROLE_USER)
                                .orElseThrow(() -> new RuntimeException("Error: Role is not found."));
                        roles.add(userRole);
                }
            }
        }
        user.setRoles(roles);

        // Generate TOTP secret for the new user
        String totpSecret = TotpUtils.generateSecret();
        user.setTotpSecret(totpSecret);
        userRepository.save(user);
        String qrUrl = TotpUtils.getQrCodeUrl(totpSecret, user.getEmail(), "Parabella_Csrd_App");

        return ResponseEntity.ok(new HashMap<String,Object>() {{
            put("message", "User registered successfully! Set up your authenticator with this QR:");
            put("qrCodeUrl", qrUrl);
            put("username", user.getUsername());
        }});
    }

    /**
     * Verifies 2FA setup by validating a TOTP code during user registration
     *
     * This endpoint is used to verify that a user has successfully set up their
     * authenticator app after registration. It validates the TOTP code generated
     * by the user's authenticator app against the stored secret.
     *
     * Verification Process:
     * 1. Validates that username and TOTP code are provided
     * 2. Checks that the user exists and has a TOTP secret
     * 3. Validates the provided TOTP code against the stored secret
     * 4. Returns success or failure message
     *
     * Security Considerations:
     * - Requires valid TOTP code from authenticator app
     * - Prevents authentication without proper 2FA setup
     * - Validates code format and numeric value
     *
     * @param payload Map containing "username" and "code" keys
     * @return ResponseEntity with success message if verification succeeds,
     *         error message if verification fails or parameters are invalid
     *
     * @throws RuntimeException if user is not found
     * @throws NumberFormatException if TOTP code format is invalid
     *
     * @apiNote POST /api/auth/verify2fa
     * @since 1.0
     */
    @PostMapping("/verify2fa")
    public ResponseEntity<?> verify2FA(@RequestBody Map<String, String> payload) {
        String username = payload.get("username");
        String codeStr = payload.get("code");

        if (username == null || codeStr == null) {
            return ResponseEntity.badRequest().body(new MessageResponse("Username and TOTP code are required"));
        }

        Integer code;
        try {
            code = Integer.valueOf(codeStr);
        } catch (NumberFormatException e) {
            return ResponseEntity.badRequest().body(new MessageResponse("Invalid TOTP code format"));
        }

        User user = userRepository.findByUsername(username)
                .orElseThrow(() -> new RuntimeException("User not found"));

        if (user.getTotpSecret() == null || user.getTotpSecret().isEmpty()) {
            return ResponseEntity.badRequest().body(new MessageResponse("No TOTP secret found for user. Setup not initialized."));
        }

        if (TotpUtils.validateCode(user.getTotpSecret(), code)) {
            return ResponseEntity.ok(new MessageResponse("2FA verified. You can now log in with TOTP code."));
        } else {
            return ResponseEntity.status(HttpServletResponse.SC_UNAUTHORIZED)
                    .body(new MessageResponse("Invalid TOTP code"));
        }
    }

    /**
     * Enables 2FA for an existing authenticated user
     *
     * This endpoint allows existing users to enable 2FA on their account.
     * It generates a new TOTP secret and provides a QR code for authenticator setup.
     * This method requires the user to be already authenticated.
     *
     * Process:
     * 1. Extracts user details from authentication context
     * 2. Checks if 2FA is already enabled
     * 3. Generates new TOTP secret if not already enabled
     * 4. Returns QR code URL for authenticator app setup
     *
     * Security Requirements:
     * - User must be authenticated (valid JWT token required)
     * - Prevents enabling 2FA if already enabled
     * - Generates secure TOTP secret
     *
     * @param authentication Spring Security Authentication object containing user details
     * @return ResponseEntity with QR code URL if 2FA is successfully enabled,
     *         error message if 2FA is already enabled
     *
     * @throws RuntimeException if authenticated user is not found in database
     *
     * @apiNote PATCH /api/auth/enable2fa
     * @since 1.0
     */
    @PatchMapping("/enable2fa")
    public ResponseEntity<?> enable2FA(Authentication authentication) {
        UserDetailsImpl userDetails = (UserDetailsImpl) authentication.getPrincipal();
        User user = userRepository.findByUsername(userDetails.getUsername())
                .orElseThrow(() -> new RuntimeException("User not found"));

        if (user.getTotpSecret() == null || user.getTotpSecret().isEmpty()) {
            String newSecret = TotpUtils.generateSecret();
            user.setTotpSecret(newSecret);
            userRepository.save(user);
            String qrUrl = TotpUtils.getQrCodeUrl(newSecret, user.getEmail(), "Parabella_Csrd_App");
            return ResponseEntity.ok(new MessageResponse("2FA enabled. Scan this QR: " + qrUrl));
        } else {
            return ResponseEntity.badRequest().body(new MessageResponse("2FA is already enabled."));
        }
    }

    /**
     * Disables 2FA for an authenticated user
     *
     * This endpoint allows authenticated users to disable 2FA on their account.
     * It removes the TOTP secret from the user's account, effectively disabling
     * two-factor authentication. This method requires the user to be authenticated.
     *
     * Security Considerations:
     * - User must be authenticated (valid JWT token required)
     * - Removes TOTP secret from user account
     * - User will only need username/password for future logins
     * - Consider requiring additional verification before disabling 2FA
     *
     * Process:
     * 1. Extracts user details from authentication context
     * 2. Sets TOTP secret to null in user account
     * 3. Saves updated user to database
     * 4. Returns success confirmation
     *
     * @param authentication Spring Security Authentication object containing user details
     * @return ResponseEntity with success message confirming 2FA has been disabled
     *
     * @throws RuntimeException if authenticated user is not found in database
     *
     * @apiNote PATCH /api/auth/disable2fa
     * @since 1.0
     */
    @PatchMapping("/disable2fa")
    public ResponseEntity<?> disable2FA(Authentication authentication) {
        UserDetailsImpl userDetails = (UserDetailsImpl) authentication.getPrincipal();
        User user = userRepository.findByUsername(userDetails.getUsername())
                .orElseThrow(() -> new RuntimeException("User not found"));

        user.setTotpSecret(null);
        userRepository.save(user);
        return ResponseEntity.ok(new MessageResponse("2FA disabled."));
    }
}
