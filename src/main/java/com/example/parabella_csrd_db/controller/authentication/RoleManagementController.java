package com.example.parabella_csrd_db.controller.authentication;

import com.example.parabella_csrd_db.database.maindatabase.repository.authentication.PermissionRepository;
import com.example.parabella_csrd_db.dto.authentication.permission.PermissionDto;
import com.example.parabella_csrd_db.dto.authentication.role.RoleCreateDto;
import com.example.parabella_csrd_db.dto.authentication.role.RoleDto;
import com.example.parabella_csrd_db.dto.authentication.role.RolePermissionsUpdateDto;
import com.example.parabella_csrd_db.service.authentication.ManagementMapper;
import com.example.parabella_csrd_db.service.authentication.RoleManagementService;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import java.util.List;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/api/role-management")
@RequiredArgsConstructor
public class RoleManagementController {

    private final RoleManagementService roleService;
    private final PermissionRepository permissionRepository; // For fetching all permissions
    private final ManagementMapper mapper;

    @GetMapping("/roles")
    @PreAuthorize("hasAuthority('role.view')")
    public ResponseEntity<List<RoleDto>> getRoles() {
        return ResponseEntity.ok(roleService.findAllRoles());
    }

    @GetMapping("/permissions")
    @PreAuthorize("hasAuthority('role.view')")
    public ResponseEntity<List<PermissionDto>> getPermissions() {
        List<PermissionDto> permissions = permissionRepository.findAll().stream()
                .map(mapper::toPermissionDto)
                .collect(Collectors.toList());
        return ResponseEntity.ok(permissions);
    }


    @PostMapping("/roles")
    @PreAuthorize("hasAuthority('role.create')")
    public ResponseEntity<RoleDto> createRole(@Valid @RequestBody RoleCreateDto createDto) {
        return new ResponseEntity<>(roleService.createRole(createDto), HttpStatus.CREATED);
    }

    @PutMapping("/roles/{roleId}")
    @PreAuthorize("hasAuthority('role.edit')")
    public ResponseEntity<RoleDto> updateRolePermissions(@PathVariable Integer roleId, @Valid @RequestBody RolePermissionsUpdateDto updateDto) {
        return ResponseEntity.ok(roleService.updateRolePermissions(roleId, updateDto));
    }

    @DeleteMapping("/roles/{roleId}")
    @PreAuthorize("hasAuthority('role.delete')")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    public void deleteRole(@PathVariable Integer roleId) {
        roleService.deleteRole(roleId);
    }
}