package com.example.parabella_csrd_db.controller.audit_trail;

import com.example.parabella_csrd_db.dto.audit_trail.AuditLogDTO;
import com.example.parabella_csrd_db.dto.audit_trail.FieldChangeDTO;
import com.example.parabella_csrd_db.service.audit_trail.GenericAuditService;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/audit")
public class GenericAuditController {

    private final GenericAuditService auditService;

    public GenericAuditController(GenericAuditService auditService) {
        this.auditService = auditService;
    }

    // Returns all logged events from a custom table or Envers query
    @GetMapping("/logs")
    public List<AuditLogDTO> getAllLogs() {
        return auditService.getAllAuditLogs();
    }

    // If you still want the original single-entity revisions:
    @GetMapping
    public List<Number> getRevisions(@RequestParam String entity, @RequestParam Long id)
            throws ClassNotFoundException {
        Class<?> clazz = Class.forName("com.example.parabella_csrd_db.model.mithril." + entity);
        return auditService.getRevisions(clazz, id);
    }

    @GetMapping("/logs/project/{projectId}")
    public List<AuditLogDTO> getAllLogsForProject(@PathVariable Long projectId) {
        System.out.println(auditService.getAllAuditLogsForProject(projectId));
        return auditService.getAllAuditLogsForProject(projectId);
    }

    @GetMapping("/logs/{entity}/{entityId}/{revision}/diff")
    public List<FieldChangeDTO> getRevisionDiff(
            @PathVariable String entity,
            @PathVariable Long entityId,
            @PathVariable int revision
    ) throws ClassNotFoundException {
        Class<?> clazz = Class.forName("com.example.parabella_csrd_db.database.maindatabase.model.mithril." + entity);
        return auditService.getFieldChangesForRevision(clazz, entityId, revision);
    }

}

