package com.example.parabella_csrd_db.controller.mithril;

import com.example.parabella_csrd_db.dto.mithril.ValueChainObjectDTO;
import com.example.parabella_csrd_db.service.mithril.ValueChainObjectService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@CrossOrigin(origins = {"https://storage.googleapis.com", "http://localhost:5173"})
@RestController
@RequestMapping("/api/value-chain-objects")
public class ValueChainObjectController {

    @Autowired
    private ValueChainObjectService valueChainObjectService;

    /**
     * Create a new ValueChainObject.
     *
     * @param vcoDTO the ValueChainObjectDTO containing value chain object data
     * @return the created ValueChainObjectDTO
     */
    @CrossOrigin(origins = {"https://storage.googleapis.com", "http://localhost:5173"})
    @PostMapping
    public ValueChainObjectDTO createValueChainObject(@RequestBody ValueChainObjectDTO vcoDTO) {
        System.out.println(vcoDTO.getCompanyId());
        return valueChainObjectService.createValueChainObject(vcoDTO);
    }

    /**
     * Update an existing ValueChainObject.
     *
     * @param id     the ID of the ValueChainObject to update
     * @param vcoDTO the ValueChainObjectDTO containing updated data
     * @return the updated ValueChainObjectDTO
     */
    @CrossOrigin(origins = {"https://storage.googleapis.com", "http://localhost:5173"})
    @PutMapping("/{id}")
    public ValueChainObjectDTO updateValueChainObject(@PathVariable Long id, @RequestBody ValueChainObjectDTO vcoDTO) {
        return valueChainObjectService.updateValueChainObject(id, vcoDTO);
    }

    /**
     * Get a ValueChainObject by its ID.
     *
     * @param id the ID of the ValueChainObject
     * @return the ValueChainObjectDTO
     */
    @CrossOrigin(origins = {"https://storage.googleapis.com", "http://localhost:5173"})
    @GetMapping("/{id}")
    public ValueChainObjectDTO getValueChainObject(@PathVariable Long id) {
        return valueChainObjectService.getValueChainObject(id);
    }

    /**
     * Delete a ValueChainObject by its ID.
     *
     * @param id the ID of the ValueChainObject to delete
     */
    @CrossOrigin(origins = {"https://storage.googleapis.com", "http://localhost:5173"})
    @DeleteMapping("/{id}")
    public void deleteValueChainObject(@PathVariable Long id) {
        valueChainObjectService.deleteValueChainObject(id);
    }
}