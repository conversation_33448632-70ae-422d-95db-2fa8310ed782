package com.example.parabella_csrd_db.controller.mithril;

import com.example.parabella_csrd_db.dto.mithril.StakeholderDTO;
import com.example.parabella_csrd_db.service.mithril.StakeholderService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
@CrossOrigin(origins = {"https://storage.googleapis.com", "http://localhost:5173"})
@RestController
@RequestMapping("/api/stakeholders")
public class StakeholderController {

    @Autowired
    private StakeholderService stakeholderService;

    @CrossOrigin(origins = {"https://storage.googleapis.com", "http://localhost:5173"})
    @PostMapping
    public ResponseEntity<StakeholderDTO> createStakeholder(@RequestBody StakeholderDTO stakeholderDTO) {
        StakeholderDTO createdStakeholder = stakeholderService.createStakeholder(stakeholderDTO);
        return ResponseEntity.status(HttpStatus.CREATED).body(createdStakeholder);
    }

    @CrossOrigin(origins = {"https://storage.googleapis.com", "http://localhost:5173"})
    @PutMapping("/{id}")
    public ResponseEntity<StakeholderDTO> updateStakeholder(@PathVariable Long id, @RequestBody StakeholderDTO stakeholderDTO) {
        StakeholderDTO updatedStakeholder = stakeholderService.updateStakeholder(id, stakeholderDTO);
        return ResponseEntity.ok(updatedStakeholder);
    }

    @CrossOrigin(origins = {"https://storage.googleapis.com", "http://localhost:5173"})
    @GetMapping("/company/{companyId}")
    public ResponseEntity<List<StakeholderDTO>> getStakeholdersByCompanyId(@PathVariable Long companyId) {
        List<StakeholderDTO> stakeholders = stakeholderService.getStakeholdersByCompanyId(companyId);
        return ResponseEntity.ok(stakeholders);
    }

    @CrossOrigin(origins = {"https://storage.googleapis.com", "http://localhost:5173"})
    @GetMapping("/token/{token}")
    public ResponseEntity<StakeholderDTO> getStakeholderByToken(@PathVariable String token) {
        StakeholderDTO stakeholderDTO = stakeholderService.getStakeholderByToken(token);
        return ResponseEntity.ok(stakeholderDTO);
    }

    /**
     * This method creates the Stakeholders with a unique token in the backend and sends a mail to their Email
     * addresses.
     *
     * @param stakeholders the added stakeholders from the frontend
     * @return Sending a mail to the selected stakeholders
     */
    @CrossOrigin(origins = {"https://storage.googleapis.com", "http://localhost:5173"})
    @PostMapping("/send-stakeholder-emails")
    public ResponseEntity<Void> sendStakeholderEmails(@RequestBody List<StakeholderDTO> stakeholders) {
        System.out.println(stakeholders);
        stakeholderService.sendStakeholderEmails(stakeholders);
        return ResponseEntity.ok().build();
    }


}