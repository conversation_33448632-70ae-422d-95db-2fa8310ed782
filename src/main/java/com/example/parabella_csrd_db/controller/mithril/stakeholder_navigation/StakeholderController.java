//package com.example.parabella_csrd_db.controller.stakeholder_navigation;
//
//
//
//import com.example.parabella_csrd_db.dto.mithril.StakeholderDTO;
//import com.example.parabella_csrd_db.maindatabase.mithril.model.StakeholderStatus;
//
//import com.example.parabella_csrd_db.service.mithril.StakeholderService;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.http.HttpStatus;
//import org.springframework.http.ResponseEntity;
//import org.springframework.web.bind.annotation.*;
//
//import java.util.List;
//import java.util.Map;
//
//
//@CrossOrigin(origins = {"https://storage.googleapis.com", "http://localhost:5173"})
//@RestController
//@RequestMapping("/api/stakeholder")
//public class StakeholderController {
//
//    @Autowired
//    private StakeholderService stakeholderService;
//
//
//
//}
//
//    /**
//     * Gets the stakeholder data in the following format:
//     *
//     * @param token which identifies the stakeholder
//     * @return The stakeholder object which has following structure:
//     *  TODO: JSON Structure
//     */
//    @CrossOrigin(origins = {"https://storage.googleapis.com", "http://localhost:5173"})
//    @GetMapping("/stakeholder-data/{token}")
//    public ResponseEntity<StakeholderDTO> getStakeholderData(@PathVariable String token) {
//        StakeholderDTO data = stakeholderService.getStakeholderData(token);
//        return ResponseEntity.ok(data);
//    }
//
//
//    /**
//     * Saves the progress of a stakeholder of filling data fields from the ESRS Selection given by the ESG Manager
//     * or Super User
//     *
//     * @param token          which identifies the stakeholder
//     * @param stepdataSaving The progress of the stakeholder
//     * @return The saved progress as json of the specific stakeholder. The json has the following structure:
//     * TODO: JSON Structure
//     */
//    @CrossOrigin(origins = {"https://storage.googleapis.com", "http://localhost:5173"})
//    @PostMapping("/stakeholder-stepdata-saving/{token}")
//    public ResponseEntity<?> saveStakeholderStepDataSaving(@PathVariable String token, @RequestBody String stepdataSaving) {
//        try {
//            stakeholderService.saveStakeholderStepDataSaving(token, stepdataSaving);
//            return ResponseEntity.ok().build();
//        } catch (Exception e) {
//            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body("Error saving data: " + e.getMessage());
//        }
//    }
//
//    /*
//     *
//     *
//     * Status and Progress Update Section
//     *
//     *
//     */
//
//    /**
//     * @param token
//     * @param newStatusString
//     * @return
//     */
//    @CrossOrigin(origins = {"https://storage.googleapis.com", "http://localhost:5173"})
//    @PostMapping("/update-status/{token}")
//    public ResponseEntity<Void> updateStakeholderStatus(
//            @PathVariable String token,
//            @RequestBody String newStatusString  // Receive as a String
//    ) {
//        try {
//            StakeholderStatus newStatus = StakeholderStatus.valueOf(newStatusString);
//            stakeholderService.updateStakeholderStatus(token, newStatus);
//            return ResponseEntity.ok().build();
//        } catch (IllegalArgumentException e) {
//            return ResponseEntity.badRequest().build();  // Invalid enum value
//        } catch (Exception e) {
//            e.printStackTrace();
//            return ResponseEntity.internalServerError().build();
//        }
//    }
//
//    /**
//     * @param token
//     * @param progressData
//     * @return
//     */
//    @CrossOrigin(origins = {"https://storage.googleapis.com", "http://localhost:5173"})
//    @PostMapping("/update-progress/{token}")
//    public ResponseEntity<?> updateStakeholderProgress(
//            @PathVariable String token,
//            @RequestBody Map<String, Integer> progressData
//    ) {
//        try {
//            int completedDatapoints = progressData.get("completedDatapoints");
//            int totalDatapoints = progressData.get("totalDatapoints");
//            stakeholderService.updateStakeholderProgress(token, completedDatapoints, totalDatapoints);
//            return ResponseEntity.ok().build();
//        } catch (Exception e) {
//            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
//                    .body("Error updating progress: " + e.getMessage());
//        }
//    }
//
//    /**
//     * @param token
//     * @return
//     */
//    @CrossOrigin(origins = {"https://storage.googleapis.com", "http://localhost:5173"})
//    @GetMapping("/status/{token}")
//    public ResponseEntity<?> getStakeholderStatus(@PathVariable String token) {
//        try {
//            StakeholderStatus status = stakeholderService.getStakeholderStatus(token);
//            return ResponseEntity.ok(status);
//        } catch (Exception e) {
//            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body("Error getting status: " + e.getMessage());
//        }
//    }
//
//    /**
//     * @param projectId
//     * @return
//     */
//    @CrossOrigin(origins = {"https://storage.googleapis.com", "http://localhost:5173"})
//    @GetMapping("/dashboard/{projectId}")
//    public ResponseEntity<?> getDashboardData(@PathVariable Long projectId) {
//        try {
//            Map<String, Object> dashboardData = stakeholderService.getDashboardData(projectId);
//            return ResponseEntity.ok(dashboardData);
//        } catch (Exception e) {
//            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
//                    .body("Error fetching dashboard data: " + e.getMessage());
//        }
//    }
//}