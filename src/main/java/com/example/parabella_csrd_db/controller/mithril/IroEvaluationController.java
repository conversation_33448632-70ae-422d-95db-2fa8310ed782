package com.example.parabella_csrd_db.controller.mithril;

import com.example.parabella_csrd_db.dto.mithril.IroEvaluationDTO;
import com.example.parabella_csrd_db.service.mithril.IroEvaluationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/iroEval")
public class IroEvaluationController {

    @Autowired
    private IroEvaluationService iroEvaluationService;


    @GetMapping("/{id}")
    public ResponseEntity<IroEvaluationDTO> getIroById(@PathVariable Long id) {
        IroEvaluationDTO details = iroEvaluationService.getIroEvaluation(id);
        return ResponseEntity.ok(details);
    }

    @GetMapping("/topicId/{id}")
    public ResponseEntity<IroEvaluationDTO> getIroByTopicId(@PathVariable Long id) {
        IroEvaluationDTO evaluationDTO = iroEvaluationService.getIroEvaluationByTopicSelectionId(id);
        return ResponseEntity.ok(evaluationDTO);
    }

    @GetMapping("/company/{companyId}")
    public ResponseEntity<List<IroEvaluationDTO>> getEsrsTopicDetailsByCompanyId(@PathVariable Long companyId) {
        List<IroEvaluationDTO> detailsList = iroEvaluationService.getIroEvaluationsByCompanyId(companyId);
        return ResponseEntity.ok(detailsList);
    }


    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteIroEvaluation(@PathVariable Long id) {
        iroEvaluationService.deleteIroEvaluation(id);
        return ResponseEntity.noContent().build();
    }

    @PostMapping()
    public ResponseEntity<IroEvaluationDTO> createOrUpdateIroEvaluation(
            @RequestBody IroEvaluationDTO detailsDTO,
            @RequestParam String stakeholderName
    ) {

        IroEvaluationDTO savedDetails = iroEvaluationService.createOrUpdateIroEvaluation(detailsDTO, stakeholderName);
        return ResponseEntity.ok(savedDetails);
    }

}