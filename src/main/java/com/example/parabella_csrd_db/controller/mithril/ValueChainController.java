
package com.example.parabella_csrd_db.controller.mithril;


import com.example.parabella_csrd_db.database.maindatabase.model.mithril.ValueChain;
import com.example.parabella_csrd_db.database.maindatabase.repository.mithril.ValueChainRepository;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Optional;

@RestController
@RequestMapping("/api/value-chains")
public class ValueChainController {

    private final ValueChainRepository valueChainRepository;

    public ValueChainController(ValueChainRepository valueChainRepository) {
        this.valueChainRepository = valueChainRepository;
    }

    // Get all ValueChains
    @GetMapping
    public ResponseEntity<List<ValueChain>> getAllValueChains() {
        List<ValueChain> valueChains = valueChainRepository.findAll();
        return ResponseEntity.ok(valueChains);
    }

    // Get ValueChains by Industry Name
    @GetMapping("/industries/{industryName}")
    public ResponseEntity<List<ValueChain>> getValueChainsByIndustry(@PathVariable String industryName) {
        List<ValueChain> valueChains = valueChainRepository.findByIndustry_Name(industryName);
        if (valueChains.isEmpty()) {
            return ResponseEntity.notFound().build();
        }
        return ResponseEntity.ok(valueChains);
    }

    // Get ValueChain by ID
    @GetMapping("/{id}")
    public ResponseEntity<ValueChain> getValueChainById(@PathVariable Long id) {
        Optional<ValueChain> valueChain = valueChainRepository.findById(id);
        return valueChain.map(ResponseEntity::ok)
                .orElseGet(() -> ResponseEntity.notFound().build());
    }

    // Create a new ValueChain
    @PostMapping
    public ResponseEntity<ValueChain> createValueChain(@RequestBody ValueChain valueChain) {
        // Check for duplicate
        if (valueChainRepository.findByValueChainKeyAndIndustry_Name(valueChain.getValueChainKey(), valueChain.getIndustry().getName()).isPresent()) {
            return ResponseEntity.badRequest().body(null);
        }
        ValueChain savedValueChain = valueChainRepository.save(valueChain);
        return ResponseEntity.ok(savedValueChain);
    }

    // Update an existing ValueChain
    @PutMapping("/{id}")
    public ResponseEntity<ValueChain> updateValueChain(@PathVariable Long id, @RequestBody ValueChain valueChainDetails) {
        Optional<ValueChain> optionalValueChain = valueChainRepository.findById(id);
        if (!optionalValueChain.isPresent()) {
            return ResponseEntity.notFound().build();
        }
        ValueChain valueChain = optionalValueChain.get();
        valueChain.setValueChainKey(valueChainDetails.getValueChainKey());
        valueChain.setIndustry(valueChainDetails.getIndustry());
        ValueChain updatedValueChain = valueChainRepository.save(valueChain);
        return ResponseEntity.ok(updatedValueChain);
    }

    // Delete a ValueChain
    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteValueChain(@PathVariable Long id) {
        Optional<ValueChain> optionalValueChain = valueChainRepository.findById(id);
        if (!optionalValueChain.isPresent()) {
            return ResponseEntity.notFound().build();
        }
        valueChainRepository.delete(optionalValueChain.get());
        return ResponseEntity.noContent().build();
    }
}
