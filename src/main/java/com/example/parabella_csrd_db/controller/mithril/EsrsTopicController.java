package com.example.parabella_csrd_db.controller.mithril;

import com.example.parabella_csrd_db.database.maindatabase.model.mithril.EsrsTopic;

import com.example.parabella_csrd_db.service.utilities.excel_to_database.EsrsTopicService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@CrossOrigin(origins = {"https://storage.googleapis.com", "http://localhost:5173"})
@RestController
@RequestMapping("/api/esrs-topics")
public class EsrsTopicController {

    @Autowired
    private EsrsTopicService esrsTopicService;

    @CrossOrigin(origins = {"https://storage.googleapis.com", "http://localhost:5173"})
    @GetMapping("/subtopics")
    public ResponseEntity<List<EsrsTopic>> getAllSubtopics() {
        System.out.println(esrsTopicService.getAllSubtopics());
        return ResponseEntity.ok(esrsTopicService.getAllSubtopics());
    }

    @CrossOrigin(origins = {"https://storage.googleapis.com", "http://localhost:5173"})
    @GetMapping("/subtopics/area/{area}")
    public ResponseEntity<List<EsrsTopic>> getSubtopicsByArea(@PathVariable String area) {
        return ResponseEntity.ok(esrsTopicService.getSubtopicsByArea(area));
    }

    @CrossOrigin(origins = {"https://storage.googleapis.com", "http://localhost:5173"})
    @GetMapping("/subtopics/esrs-code/{esrsCode}")
    public ResponseEntity<List<EsrsTopic>> getSubtopicsByEsrsCode(@PathVariable String esrsCode) {
        return ResponseEntity.ok(esrsTopicService.getSubtopicsByEsrsCode(esrsCode));
    }
}