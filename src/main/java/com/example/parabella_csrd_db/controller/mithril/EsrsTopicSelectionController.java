package com.example.parabella_csrd_db.controller.mithril;

import com.example.parabella_csrd_db.dto.mithril.EsrsTopicSelectionDTO;
import com.example.parabella_csrd_db.service.mithril.EsrsTopicSelectionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * ESRS Topic Selection Controller for Company-Specific Materiality Assessment
 *
 * This controller manages the selection and relevance determination of ESRS topics
 * for specific companies within the double materiality analysis process. It handles
 * the critical step where companies identify which ESRS topics are material to
 * their business operations and sustainability impact.
 *
 * Core Functionality:
 * The controller manages EsrsTopicSelection entities that represent the relationship
 * between companies and ESRS topics, including relevance determinations and
 * justifications for materiality decisions.
 *
 * Key Operations:
 * - Create topic selections for companies
 * - Update relevance determinations and justifications
 * - Retrieve company-specific topic selections
 * - Manage topic selection lifecycle (CRUD operations)
 *
 * Materiality Assessment Process:
 * 1. Companies review all available ESRS topics
 * 2. Determine relevance for each topic to their business
 * 3. Provide justification for irrelevant topics
 * 4. Create EsrsTopicSelection records for tracking
 * 5. Use selections as basis for stakeholder assessment
 *
 * Data Structure:
 * Each EsrsTopicSelection contains:
 * - Company ID: The company making the selection
 * - ESRS Topic ID: The specific topic being evaluated
 * - Relevant: Boolean indicating materiality determination
 * - Reason for Irrelevance: Justification when topic is deemed non-material
 *
 * Integration Points:
 * - Company management (topic selections per company)
 * - ESRS Topic management (available topics for selection)
 * - Stakeholder assessment (topics for stakeholder evaluation)
 * - IRO evaluation (material topics for impact assessment)
 * - Reporting and compliance (materiality documentation)
 *
 * Business Context:
 * - Supports CSRD compliance through systematic topic evaluation
 * - Enables focused materiality assessment on relevant topics
 * - Provides audit trail for materiality decisions
 * - Facilitates stakeholder engagement on material topics
 *
 * API Endpoints:
 * - POST /api/esrs-topic-selections: Create new topic selection
 * - GET /api/esrs-topic-selections/{id}: Get specific selection
 * - GET /api/esrs-topic-selections: Get all selections
 * - PUT /api/esrs-topic-selections/{id}: Update existing selection
 * - DELETE /api/esrs-topic-selections/{id}: Delete selection
 * - GET /api/esrs-topic-selections/company/{companyId}: Get selections by company
 *
 * <AUTHOR> Development Team
 * @version 1.0
 * @since 2024
 */
@RestController
@RequestMapping("/api/esrs-topic-selections")
public class EsrsTopicSelectionController {

    @Autowired
    private EsrsTopicSelectionService esrsTopicSelectionService;

    /**
     * Creates a new ESRS topic selection for a company's materiality assessment
     *
     * This endpoint creates a record of a company's determination regarding the
     * materiality of a specific ESRS topic. This is a critical step in the
     * double materiality analysis where companies identify which topics are
     * relevant to their business operations and sustainability impact.
     *
     * Selection Process:
     * 1. Company reviews specific ESRS topic details
     * 2. Determines if topic is material to their business
     * 3. Provides justification if topic is deemed irrelevant
     * 4. Creates selection record for audit trail
     * 5. Selection becomes basis for stakeholder assessment
     *
     * Required Fields:
     * - companyId: ID of the company making the selection
     * - esrsTopicId: ID of the ESRS topic being evaluated
     * - relevant: Boolean indicating materiality determination
     *
     * Optional Fields:
     * - reasonIrrelevance: Justification when topic is deemed non-material
     *
     * Business Impact:
     * - Determines which topics proceed to stakeholder assessment
     * - Creates audit trail for materiality decisions
     * - Supports CSRD compliance documentation
     * - Enables focused analysis on material topics
     *
     * @param dto EsrsTopicSelectionDTO containing selection data
     * @return The created EsrsTopicSelectionDTO with generated ID
     *
     * @throws IllegalArgumentException if required fields are missing

     * @apiNote POST /api/esrs-topic-selections
     * @since 1.0
     */
    @PostMapping
    public EsrsTopicSelectionDTO createEsrsTopicSelection(@RequestBody EsrsTopicSelectionDTO dto) {
        System.out.println("EsrsTopicId: " + dto.getEsrsTopicId());
        System.out.println("CompanyId: " + dto.getCompanyId());

        return esrsTopicSelectionService.createEsrsTopicSelection(dto);
    }


    /**
     * Get an EsrsTopicSelection by ID.
     *
     * @param id The ID of the EsrsTopicSelection.
     * @return The EsrsTopicSelectionDTO with the specified ID.
     */
    @GetMapping("/{id}")
    public EsrsTopicSelectionDTO getEsrsTopicSelection(@PathVariable Long id) {
        return esrsTopicSelectionService.getEsrsTopicSelection(id);
    }

    /**
     * Get all EsrsTopicSelections.
     *
     * @return A list of all EsrsTopicSelectionDTOs.
     */
    @GetMapping
    public List<EsrsTopicSelectionDTO> getAllEsrsTopicSelections() {
        return esrsTopicSelectionService.getAllEsrsTopicSelections();
    }

    /**
     * Update an existing EsrsTopicSelection.
     *
     * @param id  The ID of the EsrsTopicSelection to update.
     * @param dto Data transfer object containing updated data.
     * @return The updated EsrsTopicSelectionDTO.
     */
    @PutMapping("/{id}")
    public EsrsTopicSelectionDTO updateEsrsTopicSelection(@PathVariable Long id, @RequestBody EsrsTopicSelectionDTO dto) {
        return esrsTopicSelectionService.updateEsrsTopicSelection(id, dto);
    }

    /**
     * Delete an EsrsTopicSelection by ID.
     *
     * @param id The ID of the EsrsTopicSelection to delete.
     */
    @DeleteMapping("/{id}")
    public void deleteEsrsTopicSelection(@PathVariable Long id) {
        esrsTopicSelectionService.deleteEsrsTopicSelection(id);
    }

    /**
     * Get all EsrsTopicSelections for a specific company.
     *
     * @param companyId The ID of the company.
     * @return A list of EsrsTopicSelectionDTOs.
     */
    @GetMapping("/company/{companyId}")
    public List<EsrsTopicSelectionDTO> getEsrsTopicSelectionsByCompanyId(@PathVariable Long companyId) {
        return esrsTopicSelectionService.getEsrsTopicSelectionsByCompanyId(companyId);
    }


}
