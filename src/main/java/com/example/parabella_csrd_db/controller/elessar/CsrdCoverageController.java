package com.example.parabella_csrd_db.controller.elessar;// src/main/java/com/example/parabella_csrd_db/controller/CsrdCoverageController.java


import com.example.parabella_csrd_db.dto.elessar.coverage.CoverageAnalysisResultDto;
import com.example.parabella_csrd_db.service.elessar.CsrdCoverageService;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api/projects/{projectId}/coverage-analysis")
@RequiredArgsConstructor

public class CsrdCoverageController {

    private final CsrdCoverageService csrdCoverageService;

    @GetMapping
    public ResponseEntity<CoverageAnalysisResultDto> getCoverageAnalysis(
            @PathVariable Long projectId) {
        // Basic check - Add more robust project existence check if needed
        if (projectId == null || projectId <= 0) {
            return ResponseEntity.badRequest().build();
        }
        try {
            CoverageAnalysisResultDto result = csrdCoverageService.analyzeCoverage(projectId);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            // Log the exception properly
            // Consider specific exceptions for 'project not found' etc.
            return ResponseEntity.internalServerError().build(); // Or more specific error
        }
    }
}