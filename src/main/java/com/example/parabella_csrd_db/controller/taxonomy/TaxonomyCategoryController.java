package com.example.parabella_csrd_db.controller.taxonomy;


import com.example.parabella_csrd_db.database.maindatabase.model.taxonomy.TaxonomyCategory;
import com.example.parabella_csrd_db.service.taxonomy.TaxonomyService;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/categories")

public class TaxonomyCategoryController {

    private final TaxonomyService taxonomyService;

    public TaxonomyCategoryController(TaxonomyService taxonomyService) {
        this.taxonomyService = taxonomyService;
    }

    @GetMapping
    public List<TaxonomyCategory> getAllCategories() {
        return taxonomyService.getAllCategories();
    }

    @GetMapping("/{id}")
    public ResponseEntity<TaxonomyCategory> getCategoryById(@PathVariable Long id) {
        return taxonomyService.getCategoryById(id)
                .map(ResponseEntity::ok)
                .orElse(ResponseEntity.notFound().build());
    }

    @PostMapping
    public ResponseEntity<TaxonomyCategory> createCategory(@RequestBody TaxonomyCategory category) {
        TaxonomyCategory saved = taxonomyService.createOrUpdateCategory(category);
        return ResponseEntity.ok(saved);
    }

    @PutMapping("/{id}")
    public ResponseEntity<TaxonomyCategory> updateCategory(@PathVariable Long id, @RequestBody TaxonomyCategory category) {
        // In a real app, you might do more robust checks here
        category.setId(id);
        TaxonomyCategory updated = taxonomyService.createOrUpdateCategory(category);
        return ResponseEntity.ok(updated);
    }

    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteCategory(@PathVariable Long id) {
        taxonomyService.deleteCategory(id);
        return ResponseEntity.noContent().build();
    }
}
