package com.example.parabella_csrd_db.dto.elessar.coverage;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CoverageAnalysisResultDto {
    private Long projectId;
    private long totalEsrsDatapoints;
    private long totalCoveredDatapoints;
    private long totalUncoveredDatapoints;
    private double overallCoveragePercentage;
    private Map<String, CategoryCoverageStatsDto> coverageByStandard; // Key: ESRS Standard (e.g., "ESRS E1")
    private List<CoverageDatapointDto> datapoints; // Detailed list of all datapoints and their status
}