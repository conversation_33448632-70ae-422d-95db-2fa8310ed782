package com.example.parabella_csrd_db.dto.elessar;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;
import java.util.Map;

@Getter
@Setter
public class CsrdFieldDTO {
    // Basic attributes
    @JsonProperty("type")
    private String fieldType;     // e.g. "radioButtonGroup", "checkbox", "subSection"
    private String label;    // e.g. "Do policies address negative impacts on:"


    // If field is a single or multi-option input
    private List<String> options;  // ["Yes", "No", "Air quality", ...]

    // For conditional logic:
    // e.g. { "Yes": [ { type: "textField", label: "..."} ], "No": [ ... ] }
    private Map<String, List<CsrdFieldDTO>> conditionalFields;

    // "subSection" case: an array of sub-sections
    // e.g. "subSections": [ { "title": "Short Term", "fields": [...] } ]
    private List<CsrdSubSectionDTO> subSections;

    // getters & setters
}

