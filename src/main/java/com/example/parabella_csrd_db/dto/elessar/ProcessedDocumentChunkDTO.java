package com.example.parabella_csrd_db.dto.elessar;

import lombok.Builder;
import lombok.Data;

import java.time.OffsetDateTime;
// import java.util.Map; // No longer needed for Map
import java.util.List; // <<<--- IMPORT THIS

@Data
@Builder
public class ProcessedDocumentChunkDTO {
    private Long id;
    private Long projectId;
    private String uploadedByUserId;
    private String documentName;
    private Integer chunkIndex;
    private String chunkText; // Or maybe only summary for performance
    private String chunkSummary;
    private String disclosureRequirement;
    private String sourceId;
    // private Map<String, Object> dataSourceIds; // <<<--- CHANGE FROM THIS
    private List<Object> dataSourceIds; // <<<--- TO THIS (or List<String>)
    private Float matchScore;
    private Object metadata;
    private OffsetDateTime createdAt;
    // Exclude embedding fields
}