package com.example.parabella_csrd_db.dto.elessar;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.util.Map;

@Getter
@Setter
@Data // Lombok: Generates getters, setters, toString, equals, hashCode
public class EsrsDatapointDTO {
    private Long id;
    private String sourceId;
    private String sheetName;
    private String esrsStandard;
    private String disclosureRequirement;
    private String paragraph;
    private String relatedAr;
    private String dataPointName;
    private String dataType;
    private String dataResponse; // The actual user-entered data
    private String dataUnit;
    private Boolean conditionalDp;
    private Map<String, Object> metadata;
    // Add embedding if you plan to send it, though it might be large
}