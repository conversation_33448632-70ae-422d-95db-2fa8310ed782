package com.example.parabella_csrd_db.dto.elessar;// src/main/java/com/example/parabella_csrd_db/dto/ai/AutofillDatapointRequestDTO.java


import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class AutofillDatapointRequestDTO {
    private Long projectId;
    private String companyName;
    private String companyIndustry;
    private Long datapointId;
    private String datapointLabel;
    private String disclosureRequirement;
    private String sourceId;
    private String datapointDataType; // << NEW FIELD
    private List<DocumentChunkContentDTO> documentChunks;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DocumentChunkContentDTO {
        private String documentName;
        private Integer chunkIndex;
        private String chunkText;
        private String chunkSummary;
    }
}