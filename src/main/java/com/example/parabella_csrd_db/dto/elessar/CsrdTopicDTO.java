package com.example.parabella_csrd_db.dto.elessar;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
public class CsrdTopicDTO {

    private Long id;
    @JsonProperty("topicCode")
    private String code;  // e.g. "ESRS_E2"
    @JsonProperty("topicLabel")
    private String name; // e.g. "Pollution"
    private List<CsrdSubtopicDTO> subtopics;
}
