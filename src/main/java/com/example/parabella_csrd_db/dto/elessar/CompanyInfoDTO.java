package com.example.parabella_csrd_db.dto.elessar;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class CompanyInfoDTO {

    // Include ID for responses, but typically ignored or null on creation/update requests
    // ReadOnly ensures it's included in serialization (responses) but not expected in deserialization (requests)
    @JsonProperty(access = JsonProperty.Access.READ_ONLY)
    private Long id;

    @NotBlank(message = "Company name cannot be blank")
    @Size(max = 255, message = "Company name must not exceed 255 characters")
    private String companyName;

    @Size(max = 100, message = "Revenue description must not exceed 100 characters")
    private String revenue; // Validation added if needed: @NotBlank?

    @Size(max = 5000, message = "Industry description must not exceed 5000 characters") // Example limit for TEXT
    private String industry; // Validation added if needed: @NotBlank?

    @NotBlank(message = "Company size cannot be blank")
    @Size(max = 50, message = "Company size must not exceed 50 characters")
    private String size;

    @NotBlank(message = "Number of employees cannot be blank")
    @Size(max = 50, message = "Number of employees description must not exceed 50 characters")
    private String numberOfEmployees;
}
