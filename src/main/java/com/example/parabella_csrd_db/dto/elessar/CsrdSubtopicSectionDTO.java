package com.example.parabella_csrd_db.dto.elessar;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
public class CsrdSubtopicSectionDTO {

    @JsonProperty("sectionID")
    private String sectionId;
    @JsonProperty("sectionTitle")
    private String sectionTitle;    // e.g. "Addressing Negative Impacts..."
    private List<CsrdFieldDTO> fields;  // the fields in this section
}
