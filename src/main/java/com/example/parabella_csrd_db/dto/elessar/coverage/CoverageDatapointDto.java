package com.example.parabella_csrd_db.dto.elessar.coverage;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CoverageDatapointDto {
    private Long esrsDatapointId; // ID from EsrsDatapoint table
    private String sourceId;
    private String esrsStandard;
    private String disclosureRequirement;
    private String dataPointName;
    private boolean covered;
    private List<Long> coveringChunkIds; // IDs of ProcessedDocumentChunk covering this
    private List<String> coveringDocumentNames; // Names of documents containing covering chunks
}