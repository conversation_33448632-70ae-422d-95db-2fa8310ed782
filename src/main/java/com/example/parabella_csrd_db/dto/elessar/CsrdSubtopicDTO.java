package com.example.parabella_csrd_db.dto.elessar;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
public class CsrdSubtopicDTO {

    private Long id;
    @JsonProperty("csrd_subtopic_id")
    private String csrdSubtopicId;// e.g. "E2-1"

    @JsonProperty("csrd_subtopic_label")
    private String csrdSubtopicLabel;
    // e.g. "Policies related to pollution"
    private List<EsrsDatapointDTO> datapoints;
}

