package com.example.parabella_csrd_db.dto.elessar;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Null;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.time.LocalDateTime;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class CsrdProjectDTO {

    // ReadOnly: Sent in responses, ignored in requests
    @JsonProperty(access = JsonProperty.Access.READ_ONLY)
    private Long id;

    // Required during creation request, sent in responses
    @NotNull(message = "User ID cannot be null for creation request")
    private Long userId; // SECURITY WARNING: Should come from Security Context, not request body.

    @NotBlank(message = "Project name cannot be blank")
    @Size(max = 200, message = "Project name must not exceed 200 characters")
    private String projectName;

    @Size(max = 5000, message = "Project description must not exceed 5000 characters")
    private String projectDescription;

    @NotBlank(message = "Project type cannot be blank")
    @Pattern(regexp = "^(?i)(company|companyGroup)$", message = "Project type must be 'company' or 'companyGroup' (case-insensitive)")
    private String projectType; // Receive as String, validate pattern

    // ReadOnly: Set by server, sent in responses, ignored in requests
    @JsonProperty(access = JsonProperty.Access.READ_ONLY)
    private LocalDateTime createdAt;

    // ReadOnly: Set by server, sent in responses, ignored in requests
    @JsonProperty(access = JsonProperty.Access.READ_ONLY)
    private LocalDateTime updatedAt;

    // Optional: Include CompanyInfo in responses.
    // In requests (like project creation), this might be null or ignored.
    // @Valid allows nested validation if CompanyInfoDTO is included in a request.
    @Valid
    private CompanyInfoDTO companyInfo;
}