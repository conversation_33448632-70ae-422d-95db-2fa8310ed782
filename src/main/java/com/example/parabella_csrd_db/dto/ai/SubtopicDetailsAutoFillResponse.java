package com.example.parabella_csrd_db.dto.ai;

import java.util.Map;

public class SubtopicDetailsAutoFillResponse {
    private Map<String, Object> autoFilledFields;

    // Constructors
    public SubtopicDetailsAutoFillResponse() {}

    public SubtopicDetailsAutoFillResponse(Map<String, Object> autoFilledFields) {
        this.autoFilledFields = autoFilledFields;
    }

    // Getters and Setters
    public Map<String, Object> getAutoFilledFields() {
        return autoFilledFields;
    }
    public void setAutoFilledFields(Map<String, Object> autoFilledFields) {
        this.autoFilledFields = autoFilledFields;
    }
}
