package com.example.parabella_csrd_db.dto.ai;// src/main/java/com/example/parabella_csrd_db/dto/chat_bot/SubtopicDetailsAutoFillRequest.java



import com.example.parabella_csrd_db.database.maindatabase.model.elessar.dashboard.CsrdSubtopicSection;

import java.util.List;

public class SubtopicDetailsAutoFillRequest {
    private String companyName;
    private String industry;
    private String subtopicId;
    private String subtopicLabel;
    private List<CsrdSubtopicSection> sections;

    // Constructors
    public SubtopicDetailsAutoFillRequest() {}

    public SubtopicDetailsAutoFillRequest(String companyName, String industry, String subtopicId, String subtopicLabel, List<CsrdSubtopicSection> sections) {
        this.companyName = companyName;
        this.industry = industry;
        this.subtopicId = subtopicId;
        this.subtopicLabel = subtopicLabel;
        this.sections = sections;
    }

    // Getters and Setters
    public String getCompanyName() {
        return companyName;
    }
    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }
    public String getIndustry() {
        return industry;
    }
    public void setIndustry(String industry) {
        this.industry = industry;
    }
    public String getSubtopicId() {
        return subtopicId;
    }
    public void setSubtopicId(String subtopicId) {
        this.subtopicId = subtopicId;
    }
    public String getSubtopicLabel() {
        return subtopicLabel;
    }
    public void setSubtopicLabel(String subtopicLabel) {
        this.subtopicLabel = subtopicLabel;
    }
    public List<CsrdSubtopicSection> getSections() {
        return sections;
    }
    public void setSections(List<CsrdSubtopicSection> sections) {
        this.sections = sections;
    }
}
