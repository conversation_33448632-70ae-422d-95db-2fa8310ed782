package com.example.parabella_csrd_db.dto.mithril.seeder;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

// This DTO specifically parses the "iroEvaluation" object from the JSON.
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class IroEvaluationSeedDto {
    private String description;
    private String directIndirectImpact;
    private String affectedArea;
    private String timeHorizon;
    private String positiveNegativeImpact;
    private String actualPotentialImpact;
    private String humanRightsImpact;
    private Integer scale;
    private Integer scope;
    private Integer irreversibility;
    private String riskOpportunity;
    private Integer financialMaterialityActualImpact;
    private Double probability;
}