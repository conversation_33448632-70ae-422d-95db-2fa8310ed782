package com.example.parabella_csrd_db.dto.mithril;


import com.example.parabella_csrd_db.database.maindatabase.model.mithril.StakeholderStatus;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
@Data
public class StakeholderDTO {
    private Long id;
    private String name;
    private String role;
    private String email;
    private String stakeholderType;
    private Long companyId; // Reference to Company ID
    private String token;
    private String companyName;
    private Long projectId; // Reference to Project
    private StakeholderStatus status;

    // Add this field as a list of IDs
    private List<Long> valueChainObjects;

    private int completedDatapoints;

    private int totalDatapoints;

    private boolean is_responsible;

    public boolean getIs_responsible() {
        return is_responsible;
    }


}
