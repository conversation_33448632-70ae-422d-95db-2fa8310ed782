// src/main/java/com/example/parabella_csrd_db/dto/mithril/IroEvaluationDTO.java
package com.example.parabella_csrd_db.dto.mithril;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

@Getter
@Setter
public class IroEvaluationDTO {
    private Long id;
    private Long companyId;
    private Long iroId;

    // --- Common Fields ---
    private String description;
    private String directIndirectImpact;
    private String affectedArea;
    private String timeHorizon;

    // --- Impact IRO Fields ---
    private String positiveNegativeImpact;
    private String actualPotentialImpact;
    private String humanRightsImpact;
    private Integer scale;
    private Integer scope;
    private Integer irreversibility;

    // --- Financial IRO Fields ---
    private String riskOpportunity;
    private Integer financialMaterialityActualImpact; // Repurposed for "Financial Effect"
    private Double probability; // Repurposed for "Probability" slider

    // --- Calculated Result Fields ---
    private String impactMaterialityPotentialImpact;
    private String financialMaterialityPotentialImpact;
    private String resultMaterialityAssessment;

    // --- Metadata ---
    private String lastModifiedBy;
    private LocalDateTime lastModifiedAt;

    @JsonProperty("is_locked")
    private Boolean is_locked;

    public Boolean getIs_locked() {
        return is_locked;
    }

    public void setIs_locked(Boolean is_locked) {
        this.is_locked = is_locked;
    }

    /*
     * Obsolete fields removed:
     * - effect
     * - connection
     * - basisOfAssessmentOfFinancialImpact
     * - timeSpan
     * - impactMaterialityActualImpact
     */
}