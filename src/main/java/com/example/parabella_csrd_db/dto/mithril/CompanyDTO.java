package com.example.parabella_csrd_db.dto.mithril;



import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
public class CompanyDTO {
    private Long id;
    private String companyName;
    private String address;
    private String vat;
    private String numEmployees;
    private Double revenues;
    private String industry;
    private Boolean isSubCompany;
    private List<ValueChainObjectDTO> valueChainObjects;
    private List<StakeholderDTO> stakeholders;
    private List<EsrsTopicDTO> esrsTopics;
    private Long companyGroupId; // Reference to CompanyGroup



    private Long projectId; // Add




    public Long getCompanyGroupId() {
        return companyGroupId;
    }

    public void setCompanyGroupId(Long companyGroupId) {
        this.companyGroupId = companyGroupId;
    }

    public List<EsrsTopicDTO> getEsrsTopics() {
        return esrsTopics;
    }

    public void setEsrsTopics(List<EsrsTopicDTO> esrsTopics) {
        this.esrsTopics = esrsTopics;
    }

    public List<StakeholderDTO> getStakeholders() {
        return stakeholders;
    }

    public void setStakeholders(List<StakeholderDTO> stakeholders) {
        this.stakeholders = stakeholders;
    }

    public List<ValueChainObjectDTO> getValueChainObjects() {
        return valueChainObjects;
    }

    public void setValueChainObjects(List<ValueChainObjectDTO> valueChainObjects) {
        this.valueChainObjects = valueChainObjects;
    }

    public Boolean getSubCompany() {
        return isSubCompany;
    }

    public void setSubCompany(Boolean subCompany) {
        isSubCompany = subCompany;
    }

    public String getIndustry() {
        return industry;
    }

    public void setIndustry(String industry) {
        this.industry = industry;
    }

    public Double getRevenues() {
        return revenues;
    }

    public void setRevenues(Double revenues) {
        this.revenues = revenues;
    }

    public String getNumEmployees() {
        return numEmployees;
    }

    public void setNumEmployees(String numEmployees) {
        this.numEmployees = numEmployees;
    }

    public String getVat() {
        return vat;
    }

    public void setVat(String vat) {
        this.vat = vat;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }
}
