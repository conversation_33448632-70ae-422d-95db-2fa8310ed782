package com.example.parabella_csrd_db.dto.mithril;

public class CsrdDataDTO {

    private Long id;
    private String efrag_id;
    private String esrs;
    private String dr;
    private String paragraph;
    private String relatedAr;
    private String name;
    private String hyperlink;
    private String dataType;
    private String dataResponse;
    private String dataUnit;
    private String conditionalOrAlternativeDp;
    private String caseStudy;
    private String additionalInformation;
    private String comment;

    public String getEfrag_id() {
        return efrag_id;
    }

    public void setEfrag_id(String efrag_id) {
        this.efrag_id = efrag_id;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getEsrs() {
        return esrs;
    }

    public void setEsrs(String esrs) {
        this.esrs = esrs;
    }

    public String getDr() {
        return dr;
    }

    public void setDr(String dr) {
        this.dr = dr;
    }

    public String getParagraph() {
        return paragraph;
    }

    public void setParagraph(String paragraph) {
        this.paragraph = paragraph;
    }

    public String getRelatedAr() {
        return relatedAr;
    }

    public void setRelatedAr(String relatedAr) {
        this.relatedAr = relatedAr;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getHyperlink() {
        return hyperlink;
    }

    public void setHyperlink(String hyperlink) {
        this.hyperlink = hyperlink;
    }

    public String getDataType() {
        return dataType;
    }

    public void setDataType(String dataType) {
        this.dataType = dataType;
    }

    public String getDataResponse() {
        return dataResponse;
    }

    public void setDataResponse(String dataResponse) {
        this.dataResponse = dataResponse;
    }

    public String getDataUnit() {
        return dataUnit;
    }

    public void setDataUnit(String dataUnit) {
        this.dataUnit = dataUnit;
    }

    public String getConditionalOrAlternativeDp() {
        return conditionalOrAlternativeDp;
    }

    public void setConditionalOrAlternativeDp(String conditionalOrAlternativeDp) {
        this.conditionalOrAlternativeDp = conditionalOrAlternativeDp;
    }

    public String getCaseStudy() {
        return caseStudy;
    }

    public void setCaseStudy(String caseStudy) {
        this.caseStudy = caseStudy;
    }

    public String getAdditionalInformation() {
        return additionalInformation;
    }

    public void setAdditionalInformation(String additionalInformation) {
        this.additionalInformation = additionalInformation;
    }

    public String getComment() {
        return comment;
    }

    public void setComment(String comment) {
        this.comment = comment;
    }
}
