package com.example.parabella_csrd_db.dto.authentication.request;

import jakarta.validation.constraints.NotBlank;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
public class LoginRequest {
    @NotBlank
    private String username;

    @NotBlank
    private String password;

    // Optional field for TOTP code
    private Integer totpCode;

    public LoginRequest(String testuser, String password, int i) {
        this.username = testuser;
        this.password = password;
        this.totpCode = i;
    }
}
