package com.example.parabella_csrd_db.service.ai;


import com.example.parabella_csrd_db.dto.ai.mithril.ExtendIrrelevanceReasonRequest;
import com.example.parabella_csrd_db.dto.ai.mithril.DescriptionEffectRequest;
import com.example.parabella_csrd_db.dto.ai.mithril.ReasonForIrrelevanceRequest;
import com.example.parabella_csrd_db.dto.elessar.AutofillDatapointRequestDTO;

/**
 * Defines methods to build AI prompts for different scenarios
 * across your CSRD double materiality assessment flow.
 */
public interface PromptingService {

    /**
     * Build a prompt to generate the 'Reason for Irrelevance' of an ESRS KPI.
     *
     * @param request The user request containing context about company, ESRS KPI, etc.
     * @return The prompt text to send to the LLM.
     */
    String buildIrrelevanceReasonPrompt(ReasonForIrrelevanceRequest request);

    /**
     * Build a prompt to extend or refine the existing 'Reason for Irrelevance.'
     *
     * @param request The user request containing the current text and user instructions.
     * @return The prompt text to send to the LLM.
     */
    String buildExtendIrrelevanceReasonPrompt(ExtendIrrelevanceReasonRequest request);

    /**
     * Build a prompt to generate either a Description or an Effect section,
     * based on the given request’s generationType.
     *
     * @param request The user request containing mainTopic, subTopic, subSubTopic, etc.
     * @return The prompt text to send to the LLM.
     */
    String buildDescriptionEffectPrompt(DescriptionEffectRequest request);

    /**
     * Builds a prompt to generate an audit-ready response for a CSRD datapoint
     * based on provided document chunks and datapoint context.
     *
     * @param request The DTO containing datapoint info and document chunks.
     * @return The prompt text to send to the LLM.
     */
    String buildAutofillDatapointPrompt(AutofillDatapointRequestDTO request);
}