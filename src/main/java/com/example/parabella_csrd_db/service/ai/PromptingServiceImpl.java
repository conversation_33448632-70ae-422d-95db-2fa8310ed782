package com.example.parabella_csrd_db.service.ai;


import com.example.parabella_csrd_db.dto.ai.mithril.DescriptionEffectRequest;
import com.example.parabella_csrd_db.dto.ai.mithril.ExtendIrrelevanceReasonRequest;
import com.example.parabella_csrd_db.dto.ai.mithril.ReasonForIrrelevanceRequest;
import com.example.parabella_csrd_db.dto.elessar.AutofillDatapointRequestDTO;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;

@Service
public class PromptingServiceImpl implements PromptingService {


    // CORRECTED DEFINITION: Make it a static final field of the class
    private static final List<String> NUMERIC_DATA_TYPES = Arrays.asList(
            "integer", "decimal", "monetary", "mass", "volume",
            "energy", "intensity", "ghgemissions", "mdr-p", "percent", "gyear"
    );

    public String buildIrrelevanceReasonPrompt(ReasonForIrrelevanceRequest request) {
        // You can further break down this method if your prompt is large or re-usable
        String prompt = "Given the following company information and the ESRS KPI, "
                + "generate a brief, audit-ready reason why this ESRS KPI is not relevant "
                + "to the company’s ESG reporting under CSRD.\n\n"
                + "Company Name: " + request.getCompanyName() + "\n"
                + "Company Industry: " + request.getCompanyIndustry() + "\n"
                // Optionally include value chain data or other context
                // + "Value Chain Items: " + request.getValueChainItems() + "\n"
                + "ESRS Criteria: " + request.getEsrsCriteria() + "\n"
                + "ESRS KPI: " + request.getEsrsKpi() + "\n\n"
                + "The reason should be succinct, formal, and compliance-ready, "
                + "referencing the company's specifics. "
                + "Maximum length: 6 sentences.";

        return prompt;
    }

    @Override
    public String buildExtendIrrelevanceReasonPrompt(ExtendIrrelevanceReasonRequest request) {
        // This prompt refines or extends existing text
        String currentText = request.getCurrentText();
        String userPrompt = request.getUserPrompt();

        String prompt = "You are an assistant that refines and extends an existing 'Reason for Irrelevance.'\n\n"
                + "Current reason text:\n"
                + currentText + "\n\n"
                + "User wants to further clarify or expand on this text with the following instructions:\n"
                + userPrompt + "\n\n"
                + "Please provide an updated reason for irrelevance that incorporates the user’s instructions. "
                + "Ensure the final text is succinct, formal, and compliance-ready.";

        return prompt;
    }

    @Override
    public String buildDescriptionEffectPrompt(DescriptionEffectRequest req) {
        // Note how we incorporate mainTopic, subtopic, and subSubTopic
        String generationType = req.getGenerationType() != null ? req.getGenerationType().toUpperCase() : "";
        String baseInfo =
                "Risk or Chance: " + req.getRiskChance() + "\n" +
                        "Affected Area: " + req.getAffectedArea() + "\n" +
                        "Main Topic: " + req.getMainTopic() + "\n" +
                        "Subtopic: " + req.getSubtopic() + "\n" +
                        "Sub-Sub-Topic: " + req.getSubSubTopic() + "\n";

        switch (generationType) {
            case "DESCRIPTION":
                return "Given the following ESRS topic details:\n"
                        + baseInfo
                        + "\nGenerate a short Description of the topic. "
                        + "For example, if the Main Topic is Climate Protection with a Subtopic - Adaptation to Climate Change, "
                        + "and a Sub-Sub-Topic is Temperature Rise, describe how temperature rise generally influences conditions. "
                        + "Keep it concise (maximum 6 sentences) and compliance-ready. The Effect will be handled in the next field.\n";

            case "EFFECT":
                return "Given the following ESRS topic details:\n"
                        + baseInfo
                        + "\nGenerate a short Effect description based on the previous Description. "
                        + "Factor in the Affected Area (own operation, upstream value chain, etc.). "
                        + "Keep it concise (maximum 6 sentences) and compliance-ready.\n";

            default:
                // fallback
                return "Invalid generationType (please specify DESCRIPTION or EFFECT)\n\n"
                        + "Details:\n"
                        + baseInfo;
        }
    }



    @Override
    public String buildAutofillDatapointPrompt(AutofillDatapointRequestDTO request) {
        StringBuilder promptBuilder = new StringBuilder();
        boolean hasDocumentChunks = request.getDocumentChunks() != null && !request.getDocumentChunks().isEmpty();
        // Now NUMERIC_DATA_TYPES is accessible as it's a static member of the class
        boolean isNumericDatapoint = request.getDatapointDataType() != null &&
                NUMERIC_DATA_TYPES.contains(request.getDatapointDataType().toLowerCase().trim());

        promptBuilder.append("You are an expert ESG reporting assistant. Your task is to generate a response for a specific CSRD datapoint.\n\n");

        promptBuilder.append("COMPANY CONTEXT:\n");
        promptBuilder.append("- Company Name: ").append(request.getCompanyName()).append("\n");
        promptBuilder.append("- Company Industry: ").append(request.getCompanyIndustry()).append("\n\n");

        promptBuilder.append("DATAPOINT DETAILS:\n");
        promptBuilder.append("- Datapoint Label/Question: ").append(request.getDatapointLabel()).append("\n");
        promptBuilder.append("- Datapoint Type: ").append(request.getDatapointDataType() != null ? request.getDatapointDataType() : "Unknown").append("\n");
        if (request.getDisclosureRequirement() != null && !request.getDisclosureRequirement().isEmpty()) {
            promptBuilder.append("- Disclosure Requirement Context: ").append(request.getDisclosureRequirement()).append("\n");
        }
        if (request.getSourceId() != null && !request.getSourceId().isEmpty()) {
            promptBuilder.append("- ESRS Reference (Source ID): ").append(request.getSourceId()).append("\n");
        }
        promptBuilder.append("\n");

        promptBuilder.append("INSTRUCTIONS:\n");
        if (hasDocumentChunks) {
            promptBuilder.append("1. You have been provided with relevant excerpts from the company's documents (see 'PROVIDED DOCUMENT EXCERPTS' below).\n");
            promptBuilder.append("2. Your primary goal is to synthesize these excerpts into a comprehensive, audit-ready, and CSRD-compliant response that directly addresses the 'DATAPOINT DETAILS'.\n");
            promptBuilder.append("3. Do NOT simply copy-paste the excerpts. Instead, analyze, interpret, and rephrase the information from the excerpts to create a coherent and formal narrative or statement suitable for a CSRD report.\n");
            promptBuilder.append("4. If multiple excerpts touch upon different aspects of the datapoint, integrate them logically.\n");
            promptBuilder.append("5. Ensure the language used is professional, precise, and aligns with standard ESG reporting practices.\n");
            promptBuilder.append("6. While the core information MUST come from the provided excerpts, you can structure the response, add appropriate connecting phrases, and ensure it flows well as a standalone piece of text for the report.\n");
            promptBuilder.append("7. If, after careful analysis, the provided excerpts genuinely do not contain information relevant to the datapoint, you should state: 'The provided document excerpts do not contain sufficient information to address this specific datapoint.' Do not invent information or use external knowledge beyond structuring and phrasing the response based on the excerpts.\n");
        } else { // No document chunks provided
            promptBuilder.append("1. No specific document excerpts were provided for this datapoint.\n");
            promptBuilder.append("2. Generate a DRAFT response for the datapoint. This response should be a plausible, well-reasoned text that a company in the specified industry might report for this ESRS datapoint.\n");
            promptBuilder.append("3. The draft should be consistent with the nature of the company (name, industry) and the requirements of the datapoint.\n");
            promptBuilder.append("4. Use your general knowledge of ESG reporting, ESRS standards, and typical industry practices to formulate the draft.\n");
            promptBuilder.append("5. The draft should be clearly written, professional, and serve as a good starting point for the user.\n");
            promptBuilder.append("6. Keep the response concise and focused on the datapoint's requirements.\n");
            promptBuilder.append("7. Do NOT state that information was not found. Instead, provide the best possible draft based on the context given.\n");
        }

        if (isNumericDatapoint) {
            promptBuilder.append("8. CRITICAL: Since this is a numeric/quantitative datapoint (Type: ").append(request.getDatapointDataType()).append("), your response MUST BE ONLY THE NUMERIC VALUE. For example, if the value is 123.45, respond with '123.45'. Do NOT include any surrounding text, units, currency symbols, or explanations. The system will handle units or formatting separately. If a number cannot be determined from context (or reasonably drafted if no context), respond with an empty string or 'NaN'.\n");
        } else {
            promptBuilder.append("8. If the datapoint asks for quantitative data and it's present (if chunks provided) or can be plausibly drafted (if no chunks), provide it. If it asks for qualitative information, provide that.\n");
        }
        promptBuilder.append("\n");


        if (hasDocumentChunks) {
            promptBuilder.append("PROVIDED DOCUMENT EXCERPTS (Use these as the factual basis for your response):\n");
            AtomicInteger counter = new AtomicInteger(1);
            request.getDocumentChunks().forEach(chunk -> {
                promptBuilder.append("Excerpt ").append(counter.getAndIncrement()).append(":\n");
                if (chunk.getDocumentName() != null && !chunk.getDocumentName().isEmpty()) {
                    promptBuilder.append("  Source Document: ").append(chunk.getDocumentName());
                    if (chunk.getChunkIndex() != null) {
                        promptBuilder.append(" (Chunk ").append(chunk.getChunkIndex() + 1).append(")\n");
                    } else {
                        promptBuilder.append("\n");
                    }
                }
                String contentToUse = (chunk.getChunkText() != null && !chunk.getChunkText().isBlank()) ? chunk.getChunkText() : chunk.getChunkSummary();
                if (contentToUse != null && !contentToUse.isBlank()) {
                    promptBuilder.append("  Content: \"").append(contentToUse.trim()).append("\"\n\n");
                } else {
                    promptBuilder.append("  Content: [No text content available for this chunk]\n\n");
                }
            });
        } else {
            promptBuilder.append("NO DOCUMENT EXCERPTS PROVIDED. Generating a draft based on general knowledge for the company and datapoint.\n\n");
        }

        promptBuilder.append("CSRD-COMPLIANT RESPONSE FOR THE DATAPOINT (").append(request.getDatapointLabel()).append("):\n");

        return promptBuilder.toString();
    }
}

