package com.example.parabella_csrd_db.service;


import com.example.parabella_csrd_db.database.maindatabase.model.csrd_tables.EmissionData;
import com.example.parabella_csrd_db.database.maindatabase.repository.EmissionDataRepository;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.FileInputStream;
import java.io.IOException;

@Service
public class EmissionDataService {

    @Autowired
    private EmissionDataRepository repository;

    @Transactional
    public void importDataFromExcel(String filePath) throws IOException {
        try (FileInputStream fis = new FileInputStream(filePath);
             Workbook workbook = new XSSFWorkbook(fis)) {

            Sheet sheet = workbook.getSheetAt(0);
            for (Row row : sheet) {
                if (row.getRowNum() == 0) {
                    continue; // Skip header row
                }

                EmissionData data = new EmissionData();
                data.setActivity(row.getCell(0).getStringCellValue());
                data.setReduction(row.getCell(1).getNumericCellValue());

                repository.save(data);
            }
        }
    }
}
