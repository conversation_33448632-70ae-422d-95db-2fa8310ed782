// Package: com.example.parabella_csrd_db.service
package com.example.parabella_csrd_db.service.chat_bot;



import com.example.parabella_csrd_db.database.maindatabase.model.authentication.User;

import com.example.parabella_csrd_db.database.maindatabase.model.chatBot.Conversation;
import com.example.parabella_csrd_db.database.maindatabase.model.chatBot.Message;
import com.example.parabella_csrd_db.database.maindatabase.repository.chatBot.ConversationRepository;
import com.example.parabella_csrd_db.database.maindatabase.repository.chatBot.MessageRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
public class ConversationService {

    @Autowired
    private ConversationRepository conversationRepository;

    @Autowired
    private MessageRepository messageRepository;

    public Conversation getOrCreateConversation(User user) {
        return conversationRepository.findByUser(user)
                .orElseGet(() -> {
                    Conversation conversation = new Conversation(user);
                    return conversationRepository.save(conversation);
                });
    }

    public void addMessage(Conversation conversation, String role, String content) {
        Message message = new Message(role, content, conversation);
        messageRepository.save(message);
        conversation.getMessages().add(message);
    }

    public List<Message> getMessages(Conversation conversation) {
        return conversation.getMessages();
    }

    public List<Map<String, String>> buildMessages(Conversation conversation) {
        return conversation.getMessages().stream()
                .map(msg -> Map.of("role", msg.getRole(), "content", msg.getContent()))
                .collect(Collectors.toList());
    }
}
