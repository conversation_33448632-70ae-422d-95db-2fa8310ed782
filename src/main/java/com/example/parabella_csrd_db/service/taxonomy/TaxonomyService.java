package com.example.parabella_csrd_db.service.taxonomy;


import com.example.parabella_csrd_db.database.maindatabase.model.taxonomy.TaxonomyActivity;
import com.example.parabella_csrd_db.database.maindatabase.model.taxonomy.TaxonomyCategory;
import com.example.parabella_csrd_db.database.maindatabase.repository.taxonomy.TaxonomyActivityRepository;
import com.example.parabella_csrd_db.database.maindatabase.repository.taxonomy.TaxonomyCategoryRepository;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

@Service
public class TaxonomyService {

    private final TaxonomyCategoryRepository categoryRepository;
    private final TaxonomyActivityRepository activityRepository;

    public TaxonomyService(TaxonomyCategoryRepository categoryRepository,
                           TaxonomyActivityRepository activityRepository) {
        this.categoryRepository = categoryRepository;
        this.activityRepository = activityRepository;
    }

    // Category methods
    public List<TaxonomyCategory> getAllCategories() {
        return categoryRepository.findAll();
    }

    public Optional<TaxonomyCategory> getCategoryById(Long id) {
        return categoryRepository.findById(id);
    }

    public TaxonomyCategory createOrUpdateCategory(TaxonomyCategory category) {
        return categoryRepository.save(category);
    }

    public void deleteCategory(Long id) {
        categoryRepository.deleteById(id);
    }

    // Activity methods
    public List<TaxonomyActivity> getAllActivities() {
        return activityRepository.findAll();
    }

    public Optional<TaxonomyActivity> getActivityById(Long id) {
        return activityRepository.findById(id);
    }

    public TaxonomyActivity createOrUpdateActivity(TaxonomyActivity activity) {
        return activityRepository.save(activity);
    }

    public void deleteActivity(Long id) {
        activityRepository.deleteById(id);
    }
}
