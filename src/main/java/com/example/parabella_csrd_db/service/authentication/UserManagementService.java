package com.example.parabella_csrd_db.service.authentication;

import com.example.parabella_csrd_db.database.maindatabase.model.authentication.Role;
import com.example.parabella_csrd_db.database.maindatabase.model.authentication.User;
import com.example.parabella_csrd_db.database.maindatabase.repository.authentication.RoleRepository;

import com.example.parabella_csrd_db.database.maindatabase.repository.authentication.UserRepository;
import com.example.parabella_csrd_db.database.maindatabase.repository.authentication.VerificationTokenRepository;
import com.example.parabella_csrd_db.dto.authentication.user.UserDto;
import com.example.parabella_csrd_db.dto.authentication.user.UserInviteRequest;
import com.example.parabella_csrd_db.dto.authentication.user.UserUpdateRoleRequest;
import com.example.parabella_csrd_db.database.maindatabase.model.authentication.VerificationToken;
import com.example.parabella_csrd_db.service.mail.EmailService;
import jakarta.mail.MessagingException;
import jakarta.persistence.EntityNotFoundException;
import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

//TODO: username handling
@Service
@RequiredArgsConstructor
@Transactional
public class UserManagementService {

    private final UserRepository userRepository;
    private final RoleRepository roleRepository;
    private final PasswordEncoder passwordEncoder;
    private final ManagementMapper mapper;
    private final EmailService emailService;

    private final VerificationTokenRepository tokenRepository;

    @Value("${app.security.token.invite-expiry-hours:24}")
    private int tokenExpiryHours;

    public List<UserDto> findAllUsers() {
        return userRepository.findAll().stream().map(mapper::toUserDto).collect(Collectors.toList());
    }

    public UserDto inviteUser(UserInviteRequest inviteRequest) {
        if (userRepository.existsByEmail(inviteRequest.email())) {
            throw new IllegalArgumentException("Error: Email is already in use!");
        }
        Role role = roleRepository.findById(inviteRequest.roleId())
                .orElseThrow(() -> new EntityNotFoundException("Role not found with id: " + inviteRequest.roleId()));

        User newUser = new User();
        newUser.setEmail(inviteRequest.email());
        newUser.setUsername("test"); // Use email as initial username
        // Generate a secure, non-guessable temporary password.
        String tempPassword = UUID.randomUUID().toString();
        newUser.setPassword(passwordEncoder.encode(tempPassword));
        newUser.setRole(role);
        newUser.setEnabled(false); // User is not active until they set their password

        User savedUser = userRepository.save(newUser);

        // Generate and save the verification token
        String token = UUID.randomUUID().toString();
        VerificationToken verificationToken = new VerificationToken(token, savedUser, tokenExpiryHours);
        tokenRepository.save(verificationToken);

        // Send the invitation email
        try {
            emailService.sendUserInvitationEmail(savedUser.getEmail(), token);
        } catch (MessagingException e) {
            // In a real app, you might have a retry mechanism or log this failure for manual intervention.
            // For now, we'll throw a runtime exception to indicate a critical failure.
            throw new RuntimeException("Unable to send invitation email. Please try again later.", e);
        }

        return mapper.toUserDto(savedUser);
    }

    public UserDto updateUserRole(Long userId, UserUpdateRoleRequest updateRequest) {
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new EntityNotFoundException("User not found with id: " + userId));
        Role role = roleRepository.findById(updateRequest.roleId())
                .orElseThrow(() -> new EntityNotFoundException("Role not found with id: " + updateRequest.roleId()));
        user.setRole(role);
        User updatedUser = userRepository.save(user);
        return mapper.toUserDto(updatedUser);
    }

    public void deleteUser(Long userId) {
        // Add checks here to prevent user from deleting themselves, etc.
        userRepository.deleteById(userId);
    }
}