package com.example.parabella_csrd_db.service.authentication;

import com.example.parabella_csrd_db.database.maindatabase.model.authentication.Role;

import com.example.parabella_csrd_db.database.maindatabase.repository.authentication.PermissionRepository;
import com.example.parabella_csrd_db.database.maindatabase.repository.authentication.RoleRepository;
import com.example.parabella_csrd_db.database.maindatabase.repository.authentication.UserRepository;

import com.example.parabella_csrd_db.dto.authentication.role.RoleCreateDto;
import com.example.parabella_csrd_db.dto.authentication.role.RoleDto;
import com.example.parabella_csrd_db.dto.authentication.role.RolePermissionsUpdateDto;
import jakarta.persistence.EntityNotFoundException;
import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import java.util.List;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Transactional
public class RoleManagementService {

    private final RoleRepository roleRepository;
    private final PermissionRepository permissionRepository;
    private final UserRepository userRepository;
    private final ManagementMapper mapper;

    public List<RoleDto> findAllRoles() {
        return roleRepository.findAll().stream().map(mapper::toRoleDto).collect(Collectors.toList());
    }

    public RoleDto createRole(RoleCreateDto createDto) {
        if (roleRepository.existsByName(createDto.name())) {
            throw new IllegalArgumentException("Role with name '" + createDto.name() + "' already exists.");
        }
        Role newRole = new Role(createDto.name());
        Role savedRole = roleRepository.save(newRole);
        return mapper.toRoleDto(savedRole);
    }

    public RoleDto updateRolePermissions(Integer roleId, RolePermissionsUpdateDto updateDto) {
        Role role = roleRepository.findById(roleId)
                .orElseThrow(() -> new EntityNotFoundException("Role not found with id: " + roleId));

        role.setPermissions(permissionRepository.findByIdIn(updateDto.permissionIds()));
        Role updatedRole = roleRepository.save(role);
        return mapper.toRoleDto(updatedRole);
    }

    public void deleteRole(Integer roleId) {
        if (userRepository.existsByRoleId(roleId)) {
            throw new IllegalStateException("Cannot delete role: it is currently assigned to one or more users.");
        }
        // Also prevent deleting core roles
        Role role = roleRepository.findById(roleId).orElseThrow(() -> new EntityNotFoundException("Role not found"));
        if ("Admin".equals(role.getName()) || "User".equals(role.getName())) {
            throw new IllegalStateException("Cannot delete core system roles.");
        }
        roleRepository.deleteById(roleId);
    }
}