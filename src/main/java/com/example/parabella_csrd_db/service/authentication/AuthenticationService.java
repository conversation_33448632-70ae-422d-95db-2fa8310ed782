package com.example.parabella_csrd_db.service.authentication;

import com.example.parabella_csrd_db.database.maindatabase.model.authentication.Role;
import com.example.parabella_csrd_db.database.maindatabase.model.authentication.User;
import com.example.parabella_csrd_db.database.maindatabase.model.authentication.VerificationToken;
import com.example.parabella_csrd_db.database.maindatabase.repository.authentication.RoleRepository;
import com.example.parabella_csrd_db.database.maindatabase.repository.authentication.UserRepository;
import com.example.parabella_csrd_db.database.maindatabase.repository.authentication.VerificationTokenRepository;
import com.example.parabella_csrd_db.dto.authentication.request.LoginRequest;
import com.example.parabella_csrd_db.dto.authentication.request.SetPasswordRequest;
import com.example.parabella_csrd_db.dto.authentication.request.SignUpRequest;
import com.example.parabella_csrd_db.dto.authentication.response.JwtResponse;
import com.example.parabella_csrd_db.security.jwt.JwtUtils;
import com.example.parabella_csrd_db.security.services.UserDetailsImpl;
import com.example.parabella_csrd_db.security.utils.TotpUtils;
import com.example.parabella_csrd_db.service.mail.EmailService;
import jakarta.mail.MessagingException;
import jakarta.persistence.EntityNotFoundException;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * Service class for handling all authentication and user session logic.
 * This includes user registration, login, 2FA, password management, and JWT generation.
 */
@Service
@RequiredArgsConstructor
@Transactional
public class AuthenticationService {

    private final AuthenticationManager authenticationManager;
    private final UserRepository userRepository;
    private final RoleRepository roleRepository;
    private final VerificationTokenRepository tokenRepository;
    private final PasswordEncoder encoder;
    private final JwtUtils jwtUtils;
    private final EmailService emailService;

    @Value("${app.security.token.reset-expiry-hours:1}")
    private int tokenExpiryHours;


    /**
     * Authenticates a user and handles the 2FA flow.
     * @param loginRequest The user's login credentials and TOTP code.
     * @return An object that is either a JwtResponse (on success) or a Map containing QR code info (for 2FA setup).
     */
    public Object authenticate(LoginRequest loginRequest) {
        Authentication authentication = authenticationManager.authenticate(
                new UsernamePasswordAuthenticationToken(loginRequest.getUsername(), loginRequest.getPassword())
        );

        SecurityContextHolder.getContext().setAuthentication(authentication);
        UserDetailsImpl userDetails = (UserDetailsImpl) authentication.getPrincipal();
        User user = userRepository.findByUsername(userDetails.getUsername())
                .orElseThrow(() -> new EntityNotFoundException("User not found after authentication"));

        // Handle 2FA Setup if needed
        if (user.getTotpSecret() == null || user.getTotpSecret().isEmpty()) {
            String totpSecret = TotpUtils.generateSecret();
            user.setTotpSecret(totpSecret);
            userRepository.save(user);
            String qrUrl = TotpUtils.getQrCodeUrl(totpSecret, user.getEmail(), "Parabella_Csrd_App");
            Map<String, Object> response = new HashMap<>();
            response.put("message", "2FA setup required");
            response.put("qrCodeUrl", qrUrl);
            response.put("username", user.getUsername());
            return response;
        }

        // Validate existing 2FA code
        Integer providedCode = loginRequest.getTotpCode();
        if (providedCode == null || !TotpUtils.validateCode(user.getTotpSecret(), providedCode)) {
            throw new IllegalArgumentException("Error: Invalid or missing TOTP code");
        }

        // 2FA is valid, generate and return JWT
        String jwt = jwtUtils.generateJwtToken(authentication);
        List<String> roles = userDetails.getAuthorities().stream()
                .map(GrantedAuthority::getAuthority)
                .collect(Collectors.toList());

        List<String> permissions = user.getRole().getPermissions().stream()
                .map(p -> p.getFunctionName())
                .collect(Collectors.toList());


        return new JwtResponse(
                jwt,
                userDetails.getId(),
                userDetails.getUsername(),
                userDetails.getEmail(),
                roles,
                permissions
        );
    }

    /**
     * Registers a new user and initiates the 2FA setup process.
     * @param signUpRequest The details for the new user.
     * @return A map containing a success message and the QR code URL for 2FA setup.
     */
    public Map<String, Object> register(SignUpRequest signUpRequest) {
        if (userRepository.existsByUsername(signUpRequest.getUsername())) {
            throw new IllegalArgumentException("Error: Username is already taken!");
        }
        if (userRepository.existsByEmail(signUpRequest.getEmail())) {
            throw new IllegalArgumentException("Error: Email is already in use!");
        }

        User user = new User(signUpRequest.getUsername(),
                signUpRequest.getEmail(),
                encoder.encode(signUpRequest.getPassword()));

        Role userRole = roleRepository.findByName("User")
                .orElseThrow(() -> new RuntimeException("Error: Default 'User' role not found."));
        user.setRole(userRole);

        String totpSecret = TotpUtils.generateSecret();
        user.setTotpSecret(totpSecret);
        userRepository.save(user);

        String qrUrl = TotpUtils.getQrCodeUrl(totpSecret, user.getEmail(), "Parabella_Csrd_App");
        Map<String, Object> response = new HashMap<>();
        response.put("message", "User registered successfully! Set up your authenticator with this QR:");
        response.put("qrCodeUrl", qrUrl);
        response.put("username", user.getUsername());
        return response;
    }

    /**
     * Initiates the password reset process by generating a token and sending an email.
     * @param email The email address of the user requesting a password reset.
     */
    public void initiatePasswordReset(String email) {
//        userRepository.findByEmail(email).ifPresent(user -> {
//            String token = UUID.randomUUID().toString();
//            VerificationToken verificationToken = new VerificationToken(token, user, tokenExpiryHours);
//            tokenRepository.save(verificationToken);
//
//            try {
//                // Assuming you have an email service method for this
//                emailService.sendPasswordResetEmail(user.getEmail(), token);
//            } catch (MessagingException e) {
//                // Log the error. Don't expose failure to the user for security reasons.
//                System.err.println("Failed to send password reset email: " + e.getMessage());
//            }
//        });
//        // We do not throw an error if the user is not found to prevent email enumeration.
    }

    /**
     * Resets a user's password using a valid token.
     * @param token The password reset token.
     * @param newPassword The new password.
     */
    public void resetPassword(String token, String newPassword) {
        VerificationToken verificationToken = tokenRepository.findByToken(token)
                .orElseThrow(() -> new IllegalArgumentException("Invalid password reset token."));

        if (verificationToken.isExpired()) {
            tokenRepository.delete(verificationToken);
            throw new IllegalArgumentException("Password reset token has expired.");
        }

        User user = verificationToken.getUser();
        user.setPassword(encoder.encode(newPassword));
        userRepository.save(user);
        tokenRepository.delete(verificationToken); // Invalidate token after use
    }

    /**
     * Sets a new password for a user, typically after an invitation.
     * @param request The request containing the token and new password.
     */
    public void setNewPassword(SetPasswordRequest request) {
        VerificationToken verificationToken = tokenRepository.findByToken(request.token())
                .orElseThrow(() -> new IllegalArgumentException("Invalid token."));

        if (verificationToken.isExpired()) {
            tokenRepository.delete(verificationToken);
            throw new IllegalArgumentException("Token has expired.");
        }

        User user = verificationToken.getUser();
        user.setPassword(encoder.encode(request.password()));
        user.setEnabled(true); // Activate the user
        userRepository.save(user);
        tokenRepository.delete(verificationToken); // Invalidate token after use
    }

    /**
     * Verifies the TOTP code during the initial 2FA setup.
     * @param username The user's username.
     * @param code The TOTP code from their authenticator app.
     */
    public void verify2fa(String username, Integer code) {
        User user = userRepository.findByUsername(username)
                .orElseThrow(() -> new EntityNotFoundException("User not found"));

        if (user.getTotpSecret() == null || user.getTotpSecret().isEmpty()) {
            throw new IllegalArgumentException("2FA setup has not been initiated for this user.");
        }
        if (!TotpUtils.validateCode(user.getTotpSecret(), code)) {
            throw new IllegalArgumentException("Invalid TOTP code.");
        }
        // If successful, do nothing. The user can now proceed to log in.
    }
}