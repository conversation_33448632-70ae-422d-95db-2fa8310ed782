package com.example.parabella_csrd_db.service.authentication;

import com.example.parabella_csrd_db.database.maindatabase.model.authentication.Permission;
import com.example.parabella_csrd_db.database.maindatabase.model.authentication.Role;
import com.example.parabella_csrd_db.database.maindatabase.model.authentication.User;

import com.example.parabella_csrd_db.dto.authentication.permission.PermissionDto;
import com.example.parabella_csrd_db.dto.authentication.role.RoleDto;
import com.example.parabella_csrd_db.dto.authentication.user.UserDto;
import org.springframework.stereotype.Component;

import java.util.stream.Collectors;

@Component
public class ManagementMapper {

    public PermissionDto toPermissionDto(Permission permission) {
        return new PermissionDto(permission.getId(), permission.getFunctionKey(), permission.getFunctionName(), permission.getCategory());
    }

    public RoleDto toRoleDto(Role role) {
        return new RoleDto(
                role.getId(),
                role.getName(),
                role.getPermissions().stream().map(this::toPermissionDto).collect(Collectors.toSet())
        );
    }

    public UserDto toUserDto(User user) {
        return new UserDto(
                user.getId(),
                user.getUsername(),
                user.getEmail(),
                user.getRole() != null ? user.getRole().getName() : "No Role Assigned",
                user.getRole() != null ? user.getRole().getId() : null
        );
    }
}