//package com.example.parabella_csrd_db.service;
//
//import com.example.parabella_csrd_db.dto.mithril.CsrdDataDTO;
//import com.example.parabella_csrd_db.database.maindatabase.model.csrd_tables.CsrdData;
//import com.example.parabella_csrd_db.database.maindatabase.repository.csrd_tables.CsrdDataRepository;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Service;
//
//import java.util.List;
//import java.util.stream.Collectors;
//
//@Service
//public class CsrdDataService {
//
//    @Autowired
//    private CsrdDataRepository repository;
//
//    public List<CsrdDataDTO> getAllCsrdData() {
//        List<CsrdData> entities = repository.findAll();
//        return entities.stream().map(this::convertToDto).collect(Collectors.toList());
//    }
//
//    public CsrdDataDTO save(CsrdDataDTO csrdDataDto) {
//        CsrdData csrdData = convertToEntity(csrdDataDto);
//        CsrdData savedCsrdData = repository.save(csrdData);
//        return convertToDto(savedCsrdData);
//    }
//
//    public void delete(Long id) {
//        repository.deleteById(id);
//    }
//
//    private CsrdDataDTO convertToDto(CsrdData entity) {
//        CsrdDataDTO dto = new CsrdDataDTO();
//        dto.setId(entity.getId());
//        dto.setEfrag_id(entity.getEfragID());
//        dto.setEsrs(entity.getEsrs());
//        dto.setDr(entity.getDr());
//        dto.setParagraph(entity.getParagraph());
//        dto.setRelatedAr(entity.getRelatedAr());
//        dto.setName(entity.getName());
//        dto.setHyperlink(entity.getHyperlink());
//        dto.setDataType(entity.getDataType());
//        dto.setDataResponse(entity.getDataResponse());
//        dto.setDataUnit(entity.getDataUnit());
//        dto.setConditionalOrAlternativeDp(entity.getConditionalOrAlternativeDp());
//        dto.setCaseStudy(entity.getCaseStudy());
//        dto.setAdditionalInformation(entity.getAdditionalInformation());
//        dto.setComment(entity.getComment());
//        return dto;
//    }
//
//    private CsrdData convertToEntity(CsrdDataDTO dto) {
//        CsrdData entity = new CsrdData();
//        entity.setId(dto.getId());
//        entity.setEfragID(dto.getEfrag_id());
//        entity.setEsrs(dto.getEsrs());
//        entity.setDr(dto.getDr());
//        entity.setParagraph(dto.getParagraph());
//        entity.setRelatedAr(dto.getRelatedAr());
//        entity.setName(dto.getName());
//        entity.setHyperlink(dto.getHyperlink());
//        entity.setDataType(dto.getDataType());
//        entity.setDataResponse(dto.getDataResponse());
//        entity.setDataUnit(dto.getDataUnit());
//        entity.setConditionalOrAlternativeDp(dto.getConditionalOrAlternativeDp());
//        entity.setCaseStudy(dto.getCaseStudy());
//        entity.setAdditionalInformation(dto.getAdditionalInformation());
//        entity.setComment(dto.getComment());
//        return entity;
//    }
//}
