// src/main/java/com/example/parabella_csrd_db/service/NotificationService.java

package com.example.parabella_csrd_db.service.notification;


import com.example.parabella_csrd_db.dto.notification.NotificationDTO;
import com.example.parabella_csrd_db.database.maindatabase.model.notification.Notification;
import com.example.parabella_csrd_db.database.maindatabase.model.mithril.Project;
import com.example.parabella_csrd_db.database.maindatabase.model.mithril.Stakeholder;
import com.example.parabella_csrd_db.database.maindatabase.repository.notification.NotificationRepository;

import com.example.parabella_csrd_db.database.maindatabase.repository.mithril.ProjectRepository;
import com.example.parabella_csrd_db.database.maindatabase.repository.mithril.stakeholder_navigation.StakeholderRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class NotificationService {

    @Autowired
    private NotificationRepository notificationRepository;

    @Autowired
    private StakeholderRepository stakeholderRepository;

    @Autowired
    private ProjectRepository projectRepository;

    // Get notifications for a project
    public List<NotificationDTO> getNotificationsForProject(Long projectId) {
        Project project = projectRepository.findById(projectId)
                .orElseThrow(() -> new RuntimeException("Project not found"));
        List<Notification> notifications = notificationRepository.findByProjectOrderByCreatedAtDesc(project);
        return notifications.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    // Mark a notification as read
    public void markNotificationAsRead(Long notificationId) {
        Notification notification = notificationRepository.findById(notificationId)
                .orElseThrow(() -> new RuntimeException("Notification not found"));
        notification.setRead(true);
        notification.setReadAt(LocalDateTime.now());
        notificationRepository.save(notification);
    }

    // Create a notification for a project
    public NotificationDTO createNotificationForProject(Long projectId, String message) {
        Project project = projectRepository.findById(projectId)
                .orElseThrow(() -> new RuntimeException("Project not found"));
        Notification notification = new Notification(message, project);
        notificationRepository.save(notification);
        return convertToDTO(notification);
    }

    // Notify main route (project) when a datapoint is locked
    public void notifyProjectOfLockedDatapoint(Long stakeholderId, Long topicId) {
        Stakeholder stakeholder = stakeholderRepository.findById(stakeholderId)
                .orElseThrow(() -> new RuntimeException("Stakeholder not found"));

        Project project = stakeholder.getProject();
        if (project == null) {
            throw new RuntimeException("Project not found for the stakeholder");
        }

        String message = "Stakeholder " + stakeholder.getName() + " has locked datapoint for topic ID " + topicId;

        Notification notification = new Notification(message, project);
        notificationRepository.save(notification);
    }

    private NotificationDTO convertToDTO(Notification notification) {
        return new NotificationDTO(
                notification.getId(),
                notification.getMessage(),
                notification.isRead(),
                notification.getCreatedAt()
        );
    }
}
