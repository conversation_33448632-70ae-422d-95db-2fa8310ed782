package com.example.parabella_csrd_db.service.audit_trail;

// ... other imports ...
import com.example.parabella_csrd_db.database.maindatabase.model.mithril.*;
import com.example.parabella_csrd_db.dto.audit_trail.AuditLogDTO;
import com.example.parabella_csrd_db.dto.audit_trail.FieldChangeDTO;
import com.example.parabella_csrd_db.database.maindatabase.model.audit_trail.CustomRevisionEntity;

import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import org.hibernate.envers.AuditReader;
import org.hibernate.envers.AuditReaderFactory;
import org.hibernate.envers.RevisionType;
import org.hibernate.envers.query.AuditEntity;
import org.hibernate.envers.query.AuditQuery;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class GenericAuditService {

    private static final Logger log = LoggerFactory.getLogger(GenericAuditService.class);

    @PersistenceContext
    private EntityManager entityManager;

    // Define relationships for clarity
    private static final Map<Class<?>, String> DIRECT_PROJECT_LINK = Map.ofEntries(
            Map.entry(Project.class, "id"), // Project's own ID is the link
            Map.entry(Company.class, "project_id"),
            Map.entry(CompanyGroup.class, "project_id"),
            Map.entry(Stakeholder.class, "project_id")
            // Add others directly linked to Project...
            // Map.entry(Industry.class, "project_id"), // Example
            // Map.entry(ValueChain.class, "project_id") // Example
    );

    // Define indirect links: Entity -> Property linking to Parent -> Parent Class
    private static final Map<Class<?>, Map.Entry<String, Class<?>>> INDIRECT_LINKS = Map.ofEntries(
            Map.entry(Iro.class, Map.entry("company_id", Company.class)),
            Map.entry(IroEvaluation.class, Map.entry("company_id", Company.class)),
            // Add other indirect links, e.g., ValueChainObject -> value_chain_id -> ValueChain
            Map.entry(ValueChainObject.class, Map.entry("company_id", Company.class)), // Example
            Map.entry(EsrsTopicSelection.class, Map.entry("company_id", Company.class)) // Example if it links to company
    );

    // List *all* potentially relevant entities for the project view
    private static final List<Class<?>> ALL_PROJECT_RELATED_ENTITIES = Arrays.asList(
            Project.class,
            Company.class ,
            CompanyGroup.class,
            Stakeholder.class,
            // EsrsTopic.class, // Handled via indirect link
            IroEvaluation.class, // Handled via indirect link
            EsrsTopicSelection.class, // Handled via indirect link if applicable
            // Industry.class, // Handled via direct link if applicable
            // ValueChain.class, // Handled via direct link if applicable
            ValueChainObject.class, // Handled via indirect link if applicable
            Iro.class // Handled via indirect link
            // Add ALL classes that eventually relate back to the project
    );


    /**
     * Get all revisions (revision numbers) for a given entity and ID.
     */
    public List<Number> getRevisions(Class<?> entityClass, Object entityId) {
        AuditReader reader = AuditReaderFactory.get(entityManager);
        return reader.getRevisions(entityClass, entityId);
    }


    /**
     * Returns the audit logs only for the given projectId,
     * for all entities that link back (directly or indirectly) to that Project.
     */
    public List<AuditLogDTO> getAllAuditLogsForProject(Long projectId) {
        List<AuditLogDTO> allLogs = new ArrayList<>();
        AuditReader reader = AuditReaderFactory.get(entityManager);

        // --- Step 1: Find IDs of Parent Entities linked to the Project ---
        // We need IDs of entities that act as parents for indirect links.
        // Example: Find all Company IDs associated with the project.
        Map<Class<?>, Set<Long>> parentEntityIdsMap = new HashMap<>();

        // Find Company IDs linked to the project
        Set<Long> companyIds = findParentEntityIds(reader, Company.class, "project_id", projectId);
        if (!companyIds.isEmpty()) {
            parentEntityIdsMap.put(Company.class, companyIds);
            log.debug("Found {} Company IDs for project {}", companyIds.size(), projectId);
        }

        // Find CompanyGroup IDs linked to the project (if needed as a parent)
        Set<Long> companyGroupIds = findParentEntityIds(reader, CompanyGroup.class, "project_id", projectId);
        if (!companyGroupIds.isEmpty()) {
            parentEntityIdsMap.put(CompanyGroup.class, companyGroupIds);
            log.debug("Found {} CompanyGroup IDs for project {}", companyGroupIds.size(), projectId);
        }
        // Add similar logic for other potential parent entities (e.g., ValueChain) if needed


        // --- Step 2 & 3: Query Audit Logs for Direct and Indirect Entities ---
        for (Class<?> auditedClass : ALL_PROJECT_RELATED_ENTITIES) { // Iterate through all potentially relevant classes
            AuditQuery query = reader.createQuery().forRevisionsOfEntity(auditedClass, false, true);

            boolean queryAdded = false;

            // Check for Direct Link
            if (DIRECT_PROJECT_LINK.containsKey(auditedClass)) {
                String propertyPath = DIRECT_PROJECT_LINK.get(auditedClass);
                query.add(AuditEntity.property(propertyPath).eq(projectId));
                queryAdded = true;
                log.trace("Querying {} directly linked via {} = {}", auditedClass.getSimpleName(), propertyPath, projectId);
            }
            // Check for Indirect Link
            else if (INDIRECT_LINKS.containsKey(auditedClass)) {
                Map.Entry<String, Class<?>> linkInfo = INDIRECT_LINKS.get(auditedClass);
                String fkPropertyName = linkInfo.getKey(); // e.g., "company_id"
                Class<?> parentClass = linkInfo.getValue(); // e.g., Company.class

                Set<Long> parentIds = parentEntityIdsMap.get(parentClass);

                if (parentIds != null && !parentIds.isEmpty()) {
                    query.add(AuditEntity.property(fkPropertyName).in(parentIds));
                    queryAdded = true;
                    log.trace("Querying {} indirectly linked via {} IN {}", auditedClass.getSimpleName(), fkPropertyName, parentIds);
                } else {
                    log.trace("Skipping {} query: No relevant parent ({}) IDs found for project {}", auditedClass.getSimpleName(), parentClass.getSimpleName(), projectId);
                }
            } else {
                log.warn("Entity {} is listed in ALL_PROJECT_RELATED_ENTITIES but has no defined direct or indirect link mapping.", auditedClass.getSimpleName());
            }


            // --- Step 4: Execute Query and Process Results ---
            if (queryAdded) {
                try {
                    @SuppressWarnings("unchecked")
                    List<Object[]> revisions = query.getResultList();
                    processRevisions(auditedClass, revisions, allLogs);
                } catch (Exception e) {
                    log.error("Error querying revisions for entity {} related to project {}: {}", auditedClass.getSimpleName(), projectId, e.getMessage(), e);
                    // Decide if you want to continue or throw
                }
            }
        }


        // --- Step 5: Sort ---
        allLogs.sort(Comparator.comparing(AuditLogDTO::getTimestamp).reversed());
        log.info("Retrieved {} audit logs in total for project {}", allLogs.size(), projectId);
        return allLogs;
    }

    /**
     * Helper to find distinct IDs of a parent entity type associated with a specific project ID
     * across all revisions.
     */
    private Set<Long> findParentEntityIds(AuditReader reader, Class<?> parentEntityClass, String propertyLinkingToProject, Long projectId) {
        try {
            @SuppressWarnings("unchecked")
            List<Number> ids = reader.createQuery()
                    .forRevisionsOfEntity(parentEntityClass, false, true) // selectIdOnly = true doesn't exist, get minimal data
                    .addProjection(AuditEntity.id().distinct()) // Project distinct IDs
                    .add(AuditEntity.property(propertyLinkingToProject).eq(projectId))
                    .getResultList();

            return ids.stream()
                    .filter(Objects::nonNull) // Ensure no null IDs
                    .map(Number::longValue)   // Convert to Long
                    .collect(Collectors.toSet());
        } catch (Exception e) {
            log.error("Failed to find parent entity IDs for {} linked via {} to project {}: {}",
                    parentEntityClass.getSimpleName(), propertyLinkingToProject, projectId, e.getMessage(), e);
            return Collections.emptySet(); // Return empty set on error
        }
    }

    /**
     * Helper method to process revision data and add to the log list.
     */
    private void processRevisions(Class<?> auditedClass, List<Object[]> revisions, List<AuditLogDTO> logList) {
        for (Object[] revisionData : revisions) {
            Object entity = revisionData[0];
            CustomRevisionEntity revEntity = (CustomRevisionEntity) revisionData[1];
            RevisionType revType = (RevisionType) revisionData[2];

            String action = switch (revType) {
                case ADD -> "CREATE";
                case MOD -> "UPDATE";
                case DEL -> "DELETE";
                default -> "UNKNOWN";
            };

            LocalDateTime revTimestamp = LocalDateTime.ofInstant(
                    Instant.ofEpochMilli(revEntity.getTimestamp()),
                    ZoneId.systemDefault()
            );

            Long entityId = null;
            // Try standard getId() first
            try {
                entityId = (Long) entity.getClass().getMethod("getId").invoke(entity);
            } catch (NoSuchMethodException nsme) {
                // Handle cases where ID might be named differently or accessed via field
                log.trace("getId() not found on {}, trying direct field access for ID.", entity.getClass().getSimpleName());
                try {
                    Field idField = findIdField(entity.getClass());
                    if (idField != null) {
                        idField.setAccessible(true);
                        Object idValue = idField.get(entity);
                        if (idValue instanceof Long) {
                            entityId = (Long) idValue;
                        } else if (idValue instanceof Number) {
                            entityId = ((Number) idValue).longValue();
                        }
                    }
                } catch (Exception reflectEx) {
                    log.warn("Could not retrieve entity ID via reflection for {} at revision {}: {}", auditedClass.getSimpleName(), revEntity.getId(), reflectEx.getMessage());
                }
            }
            catch (Exception e) {
                log.warn("Could not retrieve entity ID via getId() for {} at revision {}: {}", auditedClass.getSimpleName(), revEntity.getId(), e.getMessage());
            }

            AuditLogDTO dto = new AuditLogDTO(
                    revEntity.getId(),
                    auditedClass.getSimpleName(),
                    entityId, // Can be null if retrieval failed or for deletions sometimes
                    revEntity.getModifiedBy(),
                    revTimestamp,
                    action
            );
            logList.add(dto);
        }
    }

    // Helper to find a field commonly named 'id' or annotated with @Id
    private Field findIdField(Class<?> clazz) {
        for (Field field : clazz.getDeclaredFields()) {
            if (field.getName().equals("id") || field.isAnnotationPresent(jakarta.persistence.Id.class) || field.isAnnotationPresent(org.springframework.data.annotation.Id.class) ) {
                return field;
            }
        }
        // Check superclass if needed
        if (clazz.getSuperclass() != null && clazz.getSuperclass() != Object.class) {
            return findIdField(clazz.getSuperclass());
        }
        return null;
    }

    // --- Other existing methods (getFieldChangesForRevision, getAllAuditLogs, etc.) ---
    // Make sure getAllAuditLogs also uses the processRevisions helper if needed
    // Or keep it separate if its logic is intentionally different

    public List<FieldChangeDTO> getFieldChangesForRevision(
            Class<?> entityClass,
            Long entityId,
            int revision
    ) {
        // ... (existing implementation is likely fine) ...
        AuditReader reader = AuditReaderFactory.get(entityManager);
        Object currentState = null;
        Object previousState = null;
        Number previousRevisionNumber = null;

        try {
            currentState = reader.find(entityClass, entityId, revision);
        } catch (Exception e) {
            log.warn("Could not find entity {} with id {} at revision {}: {}", entityClass.getSimpleName(), entityId, revision, e.getMessage());
            // Potentially deleted or revision doesn't exist for this ID
        }

        // Find the revision number just before the requested one *for this entity*
        List<Number> revisions = reader.getRevisions(entityClass, entityId);
        Collections.sort(revisions, Comparator.comparingInt(Number::intValue)); // Ensure ascending order
        for (int i = 0; i < revisions.size(); i++) {
            if (revisions.get(i).intValue() == revision) {
                if (i > 0) {
                    previousRevisionNumber = revisions.get(i - 1);
                }
                break;
            }
        }


        if (previousRevisionNumber != null) {
            try {
                previousState = reader.find(entityClass, entityId, previousRevisionNumber);
            } catch (Exception e) {
                log.warn("Could not find entity {} with id {} at previous revision {}: {}", entityClass.getSimpleName(), entityId, previousRevisionNumber, e.getMessage());
                // It might have been created at the 'revision' passed in.
            }
        }


        List<FieldChangeDTO> changes = new ArrayList<>();

        if (currentState == null && previousState == null) {
            log.warn("No state found for entity {} id {} at revision {} or its predecessor.", entityClass.getSimpleName(), entityId, revision);
            return changes; // No state found at all
        }

        // Determine if it was a CREATE or DELETE based on state existence
        boolean isCreate = previousState == null && currentState != null;
        boolean isDelete = currentState == null && previousState != null; // Check based on RevisionType might be better if available

        Object objectToReflect = isDelete ? previousState : currentState; // Reflect fields from the state that exists
        if (objectToReflect == null) return changes; // Should not happen based on checks above, but safeguard

        // Compare fields by reflection
        for (Field field : getAllFields(objectToReflect.getClass())) { // Use helper to get fields from hierarchy
            field.setAccessible(true);
            try {
                Object newValue = currentState != null ? field.get(currentState) : null;
                Object oldValue = previousState != null ? field.get(previousState) : null;

                // Format values (handle nulls and potentially large objects)
                String newValueStr = formatValue(newValue);
                String oldValueStr = formatValue(oldValue);


                // Report change if:
                // 1. It's a creation and the new value isn't null
                // 2. It's a deletion and the old value wasn't null
                // 3. Values are different (using Objects.equals for null-safety)
                if ( (isCreate && newValue != null) ||
                        (isDelete && oldValue != null) ||
                        (!Objects.equals(oldValue, newValue)) )
                {
                    FieldChangeDTO fc = new FieldChangeDTO();
                    fc.setFieldName(field.getName());
                    // For create/delete, only show the value that existed
                    fc.setOldValue(isCreate ? "[Created]" : oldValueStr); // Indicate creation explicitly
                    fc.setNewValue(isDelete ? "[Deleted]" : newValueStr); // Indicate deletion explicitly
                    // Or adjust logic based on how you want creation/deletion diffs represented
                    if (!isCreate && !isDelete && !Objects.equals(oldValueStr, newValueStr)){ // Only add if actually different strings for MOD
                        changes.add(fc);
                    } else if (isCreate && newValue != null) {
                        changes.add(fc); // Add created fields
                    } else if (isDelete && oldValue != null) {
                        changes.add(fc); // Add deleted fields
                    }

                }
            } catch (Exception e) {
                log.error("Reflection error comparing field {} for entity {} id {}: {}", field.getName(), entityClass.getSimpleName(), entityId, e.getMessage(), e);
            }
        }
        return changes;
    }

    // Helper to format values for DTO, avoiding overly long strings
    private String formatValue(Object value) {
        if (value == null) {
            return null; // Or "null" string if preferred
        }
        // Add logic for collections, custom objects, etc. if needed
        if (value instanceof Collection) {
            return "Collection(size=" + ((Collection<?>) value).size() + ")";
        }
        if (value.getClass().getName().startsWith("com.example")) { // Your domain objects
            // Maybe return "EntityType(id=...)"
            try {
                Method getId = value.getClass().getMethod("getId");
                Object id = getId.invoke(value);
                return value.getClass().getSimpleName() + "(id=" + id + ")";
            } catch (Exception e) {
                return value.getClass().getSimpleName() + "(...)";
            }
        }

        String str = value.toString();
        // Optional: Truncate long strings
        // if (str.length() > 255) {
        //    return str.substring(0, 252) + "...";
        // }
        return str;
    }

    // Helper to get all fields including superclasses
    private List<Field> getAllFields(Class<?> clazz) {
        List<Field> fields = new ArrayList<>();
        while (clazz != null && clazz != Object.class) {
            fields.addAll(Arrays.asList(clazz.getDeclaredFields()));
            clazz = clazz.getSuperclass();
        }
        // Filter out static/transient fields if necessary
        return fields.stream()
                .filter(f -> !java.lang.reflect.Modifier.isStatic(f.getModifiers()))
                .filter(f -> !java.lang.reflect.Modifier.isTransient(f.getModifiers()))
                .collect(Collectors.toList());
    }


    /**
     * Get all logged events (simplified version - consider performance for large datasets).
     */
    public List<AuditLogDTO> getAllAuditLogs() {
        List<AuditLogDTO> allLogs = new ArrayList<>();
        AuditReader reader = AuditReaderFactory.get(entityManager);

        // Re-use the list of all entities potentially audited
        List<Class<?>> auditedEntities = ALL_PROJECT_RELATED_ENTITIES; // Or maintain a separate comprehensive list if needed


        for (Class<?> auditedClass : auditedEntities) {
            try {
                @SuppressWarnings("unchecked")
                List<Object[]> revisions = reader.createQuery()
                        .forRevisionsOfEntity(auditedClass, false, true)
                        .getResultList();
                processRevisions(auditedClass, revisions, allLogs); // Reuse the helper
            } catch (Exception e) {
                log.error("Error querying all revisions for entity {}: {}", auditedClass.getSimpleName(), e.getMessage(), e);
            }
        }

        allLogs.sort(Comparator.comparing(AuditLogDTO::getTimestamp).reversed());
        return allLogs;
    }

    /**
     * Get the entity state at a specific revision.
     */
    public <T> T getAtRevision(Class<T> entityClass, Object entityId, Number revision) {
        AuditReader reader = AuditReaderFactory.get(entityManager);
        return reader.find(entityClass, entityId, revision);
    }

    /**
     * Get all historical snapshots for the entity (with revision metadata).
     */
    @SuppressWarnings("unchecked")
    public <T> List<T> getEntityRevisions(Class<T> entityClass, Object entityId) {
        AuditReader reader = AuditReaderFactory.get(entityManager);
        return reader.createQuery()
                .forRevisionsOfEntity(entityClass, true, true) // selectDeletedEntities = true
                .add(AuditEntity.id().eq(entityId))
                .getResultList();
    }
}