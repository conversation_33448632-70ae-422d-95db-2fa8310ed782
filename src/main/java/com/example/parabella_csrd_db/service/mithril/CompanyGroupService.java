package com.example.parabella_csrd_db.service.mithril;

import com.example.parabella_csrd_db.dto.mithril.CompanyDTO;
import com.example.parabella_csrd_db.dto.mithril.CompanyGroupDTO;
import com.example.parabella_csrd_db.database.maindatabase.model.mithril.Company;
import com.example.parabella_csrd_db.database.maindatabase.model.mithril.CompanyGroup;
import com.example.parabella_csrd_db.database.maindatabase.repository.mithril.CompanyGroupRepository;
import com.example.parabella_csrd_db.database.maindatabase.repository.mithril.CompanyRepository;
import jakarta.transaction.Transactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class CompanyGroupService {

    @Autowired
    private CompanyGroupRepository companyGroupRepository;

    @Autowired
    private CompanyRepository companyRepository;

    @Transactional
    public CompanyGroupDTO createCompanyGroup(CompanyGroupDTO companyGroupDTO) {
        CompanyGroup companyGroup = new CompanyGroup();
        companyGroup.setCompanyGroupName(companyGroupDTO.getCompanyGroupName());
        companyGroup.setCompanyGroupAddress(companyGroupDTO.getCompanyGroupAddress());
        companyGroup.setCompanyGroupVAT(companyGroupDTO.getCompanyGroupVAT());
        companyGroup.setNumEmployees(companyGroupDTO.getNumEmployees());
        companyGroup.setRevenues(companyGroupDTO.getRevenues());
        companyGroup.setIndustry(companyGroupDTO.getIndustry());

        // Save companyGroup first
        companyGroup = companyGroupRepository.save(companyGroup);

        // Handle subCompanies
        if (companyGroupDTO.getSubCompanies() != null) {
            for (CompanyDTO companyDTO : companyGroupDTO.getSubCompanies()) {
                Company company = new Company();
                company.setCompanyName(companyDTO.getCompanyName());
                company.setAddress(companyDTO.getAddress());
                company.setVat(companyDTO.getVat());
                company.setNumEmployees(companyDTO.getNumEmployees());
                company.setRevenues(companyDTO.getRevenues());
                company.setIndustry(companyDTO.getIndustry());
                company.setIsSubCompany(companyDTO.getSubCompany());
                company.setCompanyGroup(companyGroup);
                companyRepository.save(company);
            }
        }

        return convertToDTO(companyGroup);
    }

    @Transactional
    public CompanyGroupDTO updateCompanyGroup(Long id, CompanyGroupDTO companyGroupDTO) {
        CompanyGroup companyGroup = companyGroupRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("CompanyGroup not found"));
        companyGroup.setCompanyGroupName(companyGroupDTO.getCompanyGroupName());
        companyGroup.setCompanyGroupAddress(companyGroupDTO.getCompanyGroupAddress());
        companyGroup.setCompanyGroupVAT(companyGroupDTO.getCompanyGroupVAT());
        companyGroup.setNumEmployees(companyGroupDTO.getNumEmployees());
        companyGroup.setRevenues(companyGroupDTO.getRevenues());
        companyGroup.setIndustry(companyGroupDTO.getIndustry());

        // Update subCompanies
        updateSubCompanies(companyGroup, companyGroupDTO.getSubCompanies());

        companyGroup = companyGroupRepository.save(companyGroup);
        return convertToDTO(companyGroup);
    }

    public CompanyGroupDTO getCompanyGroup(Long id) {
        CompanyGroup companyGroup = companyGroupRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("CompanyGroup not found"));
        return convertToDTO(companyGroup);
    }

    @Transactional
    public void deleteCompanyGroup(Long id) {
        CompanyGroup companyGroup = companyGroupRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("CompanyGroup not found"));
        companyGroupRepository.delete(companyGroup);
    }

    // Helper Methods
    private void updateSubCompanies(CompanyGroup companyGroup, List<CompanyDTO> subCompanyDTOs) {
        // Map existing subcompanies by their IDs for quick access
        Map<Long, Company> existingSubCompanies = companyGroup.getSubCompanies().stream()
                .collect(Collectors.toMap(Company::getId, Function.identity()));

        Set<Long> incomingIds = new HashSet<>();
        if (subCompanyDTOs != null) {
            for (CompanyDTO companyDTO : subCompanyDTOs) {
                Company company;
                if (companyDTO.getId() != null && existingSubCompanies.containsKey(companyDTO.getId())) {
                    // Update existing company
                    company = existingSubCompanies.get(companyDTO.getId());
                } else {
                    // Create new company
                    company = new Company();
                    company.setCompanyGroup(companyGroup);
                }
                // Update fields
                company.setCompanyName(companyDTO.getCompanyName());
                company.setAddress(companyDTO.getAddress());
                company.setVat(companyDTO.getVat());
                company.setNumEmployees(companyDTO.getNumEmployees());
                company.setRevenues(companyDTO.getRevenues());
                company.setIndustry(companyDTO.getIndustry());
                company.setIsSubCompany(companyDTO.getSubCompany());
                companyRepository.save(company);
                incomingIds.add(company.getId());
            }
        }

        // Delete subcompanies not present in incoming DTOs
        List<Company> toDelete = companyGroup.getSubCompanies().stream()
                .filter(c -> !incomingIds.contains(c.getId()))
                .collect(Collectors.toList());

        companyRepository.deleteAll(toDelete);

        // Refresh the company's subcompanies list
        companyGroup.setSubCompanies(companyRepository.findByCompanyGroupId(companyGroup.getId()));
    }


    private CompanyGroupDTO convertToDTO(CompanyGroup companyGroup) {
        CompanyGroupDTO dto = new CompanyGroupDTO();
        dto.setId(companyGroup.getId());
        dto.setCompanyGroupName(companyGroup.getCompanyGroupName());
        dto.setCompanyGroupAddress(companyGroup.getCompanyGroupAddress());
        dto.setCompanyGroupVAT(companyGroup.getCompanyGroupVAT());
        dto.setNumEmployees(companyGroup.getNumEmployees());
        dto.setRevenues(companyGroup.getRevenues());
        dto.setIndustry(companyGroup.getIndustry());


        if (companyGroup.getSubCompanies() != null) {
            List<CompanyDTO> subCompanyDTOs = new ArrayList<>();
            for (Company company : companyGroup.getSubCompanies()) {
                subCompanyDTOs.add(convertCompanyToDTO(company));
            }
            dto.setSubCompanies(subCompanyDTOs);
        }

        return dto;
    }

    private CompanyDTO convertCompanyToDTO(Company company) {
        CompanyDTO dto = new CompanyDTO();
        dto.setId(company.getId());
        dto.setCompanyName(company.getCompanyName());
        dto.setAddress(company.getAddress());
        dto.setVat(company.getVat());
        dto.setNumEmployees(company.getNumEmployees());
        dto.setRevenues(company.getRevenues());
        dto.setIndustry(company.getIndustry());
        dto.setSubCompany(company.getIsSubCompany());
        dto.setCompanyGroupId(company.getCompanyGroup().getId());
        return dto;
    }
}
