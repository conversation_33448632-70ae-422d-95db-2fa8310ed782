package com.example.parabella_csrd_db.service.mithril;

import com.example.parabella_csrd_db.dto.mithril.StakeholderDTO;
import com.example.parabella_csrd_db.database.maindatabase.model.mithril.Company;
import com.example.parabella_csrd_db.database.maindatabase.model.mithril.Project;
import com.example.parabella_csrd_db.database.maindatabase.model.mithril.ValueChainObject;
import com.example.parabella_csrd_db.database.maindatabase.model.mithril.Stakeholder;
import com.example.parabella_csrd_db.database.maindatabase.model.mithril.StakeholderStatus;
import com.example.parabella_csrd_db.database.maindatabase.repository.mithril.CompanyRepository;
import com.example.parabella_csrd_db.database.maindatabase.repository.mithril.ProjectRepository;
import com.example.parabella_csrd_db.database.maindatabase.repository.mithril.ValueChainObjectRepository;
import com.example.parabella_csrd_db.database.maindatabase.repository.mithril.stakeholder_navigation.StakeholderRepository;
import com.example.parabella_csrd_db.service.mail.EmailService;
import jakarta.mail.MessagingException;
import jakarta.transaction.Transactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.security.SecureRandom;
import java.util.ArrayList;
import java.util.Base64;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class StakeholderService {


    private final StakeholderRepository stakeholderRepository;


    private final CompanyRepository companyRepository;


    @Autowired
    private EmailService emailService;


    private final ProjectRepository projectRepository;

    @Autowired
    private ValueChainObjectRepository valueChainObjectRepository;


    @Autowired
    public StakeholderService(StakeholderRepository stakeholderRepository,
                              CompanyRepository companyRepository,
                              ProjectRepository projectRepository) {
        this.stakeholderRepository = stakeholderRepository;
        this.companyRepository = companyRepository;
        this.projectRepository = projectRepository;
    }


    @Transactional
    public void sendStakeholderEmails(List<StakeholderDTO> stakeholderDTOs) {

        for (StakeholderDTO stakeholderDTO : stakeholderDTOs) {
            Stakeholder stakeholder = stakeholderRepository.findById(stakeholderDTO.getId())
                    .orElseThrow(() -> new RuntimeException("Stakeholder not found"));

            if (stakeholder.getToken() == null) {
                stakeholder.setToken(generateUniqueToken());
            }

            stakeholder.setStatus(StakeholderStatus.INVITED);

            stakeholderRepository.save(stakeholder);

            try {
                emailService.sendHtmlEmail(stakeholder.getEmail(), stakeholder.getToken(), stakeholder.getName());
            } catch (MessagingException e) {
                throw new RuntimeException("Failed to send email", e);
            }
        }
    }


    public StakeholderDTO getStakeholderData(String token) {
        Stakeholder stakeholder = stakeholderRepository.findByToken(token)
                .orElseThrow(() -> new RuntimeException("Stakeholder not found"));
        return mapEntityToDTO(stakeholder);
    }


    private String generateUniqueToken() {
        SecureRandom random = new SecureRandom();
        byte[] bytes = new byte[24];
        String token;
        do {
            random.nextBytes(bytes);
            token = Base64.getUrlEncoder().withoutPadding().encodeToString(bytes);
        } while (stakeholderRepository.findByToken(token).isPresent());

        return token;
    }


    public StakeholderDTO createStakeholder(StakeholderDTO stakeholderDTO) {
        stakeholderDTO.setToken(generateUniqueToken());
        Stakeholder stakeholder = mapDTOToEntity(stakeholderDTO);
        stakeholder = stakeholderRepository.save(stakeholder);
        return mapEntityToDTO(stakeholder);
    }

    public StakeholderDTO updateStakeholder(Long id, StakeholderDTO stakeholderDTO) {
        Stakeholder stakeholder = stakeholderRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("Stakeholder not found"));
        mapDTOToEntity(stakeholderDTO, stakeholder);
        stakeholder = stakeholderRepository.save(stakeholder);
        return mapEntityToDTO(stakeholder);
    }

    public List<StakeholderDTO> getStakeholdersByCompanyId(Long companyId) {
        List<Stakeholder> stakeholders = stakeholderRepository.findByCompany_Id(companyId);
        return stakeholders.stream()
                .map(this::mapEntityToDTO)
                .collect(Collectors.toList());
    }

    // Method to get stakeholder by token
    public StakeholderDTO getStakeholderByToken(String token) {
        Stakeholder stakeholder = stakeholderRepository.findByToken(token)
                .orElseThrow(() -> new RuntimeException("Stakeholder not found"));
        return mapEntityToDTO(stakeholder);
    }

    private Stakeholder mapDTOToEntity(StakeholderDTO dto) {
        Stakeholder stakeholder = new Stakeholder();
        mapDTOToEntity(dto, stakeholder);
        return stakeholder;
    }

    private void mapDTOToEntity(StakeholderDTO dto, Stakeholder stakeholder) {
        stakeholder.setName(dto.getName());
        stakeholder.setRole(dto.getRole());
        stakeholder.setEmail(dto.getEmail());
        stakeholder.setStakeholderType(dto.getStakeholderType());
        stakeholder.setToken(dto.getToken());
        stakeholder.setStatus(dto.getStatus());
        stakeholder.setCompletedDatapoints(dto.getCompletedDatapoints());
        stakeholder.setTotalDatapoints(dto.getTotalDatapoints());
        stakeholder.setIs_responsible(dto.getIs_responsible());


        // Handle value chain objects properly
        if (dto.getValueChainObjects() != null && !dto.getValueChainObjects().isEmpty()) {
            List<ValueChainObject> valueChainObjects = valueChainObjectRepository.findAllById(dto.getValueChainObjects());
            stakeholder.setValueChainObjects(valueChainObjects);
        } else {
            stakeholder.setValueChainObjects(new ArrayList<>());
        }

        if (dto.getCompanyId() != null) {
            Company company = companyRepository.findById(dto.getCompanyId())
                    .orElseThrow(() -> new RuntimeException("Company not found"));
            stakeholder.setCompany(company);
        }

        if (dto.getProjectId() != null) {
            Project project = projectRepository.findById(dto.getProjectId())
                    .orElseThrow(() -> new RuntimeException("Project not found"));
            stakeholder.setProject(project);
        }
    }

    private StakeholderDTO mapEntityToDTO(Stakeholder stakeholder) {
        StakeholderDTO dto = new StakeholderDTO();
        dto.setId(stakeholder.getId());
        dto.setName(stakeholder.getName());
        dto.setRole(stakeholder.getRole());
        dto.setEmail(stakeholder.getEmail());
        dto.setStakeholderType(stakeholder.getStakeholderType());
        dto.setToken(stakeholder.getToken());
        dto.setStatus(stakeholder.getStatus());
        dto.setCompletedDatapoints(stakeholder.getCompletedDatapoints());
        dto.setTotalDatapoints(stakeholder.getTotalDatapoints());
        dto.set_responsible(stakeholder.getIs_responsible());

        // Map ValueChainObject entities to their IDs
        if (stakeholder.getValueChainObjects() != null) {
            dto.setValueChainObjects(
                    stakeholder.getValueChainObjects().stream()
                            .map(ValueChainObject::getId)
                            .collect(Collectors.toList())
            );
        } else {
            dto.setValueChainObjects(new ArrayList<>());
        }

        if (stakeholder.getCompany() != null) {
            dto.setCompanyId(stakeholder.getCompany().getId());
            dto.setCompanyName(stakeholder.getCompany().getCompanyName());
        }

        if (stakeholder.getProject() != null) {
            dto.setProjectId(stakeholder.getProject().getId());
        }

        return dto;
    }
}