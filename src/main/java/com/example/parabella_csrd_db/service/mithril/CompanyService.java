package com.example.parabella_csrd_db.service.mithril;

import com.example.parabella_csrd_db.database.maindatabase.model.mithril.*;
import com.example.parabella_csrd_db.database.maindatabase.repository.mithril.*;
import com.example.parabella_csrd_db.dto.mithril.CompanyDTO;
import com.example.parabella_csrd_db.dto.mithril.EsrsTopicDTO;
import com.example.parabella_csrd_db.dto.mithril.StakeholderDTO;
import com.example.parabella_csrd_db.dto.mithril.ValueChainObjectDTO;
import com.example.parabella_csrd_db.database.maindatabase.repository.mithril.stakeholder_navigation.StakeholderRepository;
import jakarta.transaction.Transactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Service class for managing Company entities and related entities.
 */
@Service
public class CompanyService {

    @Autowired
    private CompanyRepository companyRepository;

    @Autowired
    private CompanyGroupRepository companyGroupRepository;

    @Autowired
    private ValueChainObjectRepository valueChainObjectRepository;

    @Autowired
    private StakeholderRepository stakeholderRepository;

    @Autowired
    private EsrsTopicsRepository esrsTopicRepository;

    @Autowired
    private ProjectRepository projectRepository;

    /**
     * Creates a new Company and its related entities.
     *
     * @param companyDTO the CompanyDTO containing company data
     * @return the created CompanyDTO
     */
    @Transactional
    public CompanyDTO createCompany(CompanyDTO companyDTO) {

        Company company = new Company();
        mapCompanyDTOToEntity(companyDTO, company);

        // Save company first to get an ID
        company = companyRepository.save(company);

        // Handle ValueChainObjects
        if (companyDTO.getValueChainObjects() != null) {
            for (ValueChainObjectDTO vcoDTO : companyDTO.getValueChainObjects()) {
                ValueChainObject vco = new ValueChainObject();
                vco.setName(vcoDTO.getName());
                vco.setIndustry(vcoDTO.getIndustry());
                vco.setCompany(company);
                valueChainObjectRepository.save(vco);
            }
        }

        // Handle Stakeholders
        if (companyDTO.getStakeholders() != null) {
            for (StakeholderDTO stakeholderDTO : companyDTO.getStakeholders()) {
                Stakeholder stakeholder = new Stakeholder();
                stakeholder.setName(stakeholderDTO.getName());
                stakeholder.setRole(stakeholderDTO.getRole());
                stakeholder.setEmail(stakeholderDTO.getEmail());
                stakeholder.setStakeholderType(stakeholderDTO.getStakeholderType());
                stakeholder.setCompany(company); // Set the company association

                // Set other fields
                stakeholder.setToken(stakeholderDTO.getToken());
                stakeholder.setStatus(stakeholderDTO.getStatus());
                stakeholder.setCompletedDatapoints(stakeholderDTO.getCompletedDatapoints());
                stakeholder.setTotalDatapoints(stakeholderDTO.getTotalDatapoints());

                // Associate with the same project as the company if projectId is not explicitly set
                if (stakeholderDTO.getProjectId() != null) {
                    Project project = projectRepository.findById(stakeholderDTO.getProjectId())
                            .orElseThrow(() -> new RuntimeException("Project not found"));
                    stakeholder.setProject(project);
                } else if (company.getProject() != null) {
                    stakeholder.setProject(company.getProject());
                }

                stakeholderRepository.save(stakeholder);
            }
        }

        // Handle EsrsTopics
        if (companyDTO.getEsrsTopics() != null) {
            for (EsrsTopicDTO esrsTopicDTO : companyDTO.getEsrsTopics()) {
                EsrsTopic esrsTopic = new EsrsTopic();
                esrsTopic.setArea(esrsTopicDTO.getArea());
                esrsTopic.setEsrsCode(esrsTopicDTO.getEsrsCode());
                esrsTopic.setTopic(esrsTopicDTO.getTopic());
                esrsTopic.setSubtopic(esrsTopicDTO.getSubtopic());
                esrsTopic.setSubSubTopic(esrsTopicDTO.getSubSubTopic());

                esrsTopicRepository.save(esrsTopic);
            }
        }

        return convertToDTO(company);
    }

    /**
     * Updates an existing Company and its related entities.
     *
     * @param id         the ID of the Company to update
     * @param companyDTO the CompanyDTO containing updated data
     * @return the updated CompanyDTO
     */
    @Transactional
    public CompanyDTO updateCompany(Long id, CompanyDTO companyDTO) {

        Company company = companyRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("Company not found"));

        mapCompanyDTOToEntity(companyDTO, company);

        company = companyRepository.save(company);

        // Update related entities without deleting and re-inserting
        //updateValueChainObjects(company, companyDTO.getValueChainObjects());
        //updateStakeholders(company, companyDTO.getStakeholders());
        //updateEsrsTopics(company, companyDTO.getEsrsTopics());


        // Refresh the associations
        company.setValueChainObjects(valueChainObjectRepository.findByCompanyId(company.getId()));
        company.setStakeholders(stakeholderRepository.findByCompany_Id(company.getId()));

        return convertToDTO(company);
    }

    /**
     * Retrieves a Company by its ID.
     *
     * @param id the ID of the Company
     * @return the CompanyDTO
     */
    public CompanyDTO getCompany(Long id) {
        Company company = companyRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("Company not found"));
        return convertToDTO(company);
    }

    /**
     * Retrieves all Companies associated with a Company Group ID.
     *
     * @param groupId the ID of the Company Group
     * @return List of CompanyDTOs
     */
    public List<CompanyDTO> getCompaniesByGroupId(Long groupId) {
        List<Company> companies = companyRepository.findByCompanyGroupId(groupId);
        List<CompanyDTO> companyDTOs = new ArrayList<>();
        for (Company company : companies) {
            companyDTOs.add(convertToDTO(company));
        }
        return companyDTOs;
    }

    /**
     * Deletes a Company by its ID.
     *
     * @param id the ID of the Company to delete
     */
    @Transactional
    public void deleteCompany(Long id) {
        Company company = companyRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("Company not found"));
        companyRepository.delete(company);
    }

    // Helper Methods

    private void mapCompanyDTOToEntity(CompanyDTO companyDTO, Company company) {
        company.setCompanyName(companyDTO.getCompanyName());
        company.setAddress(companyDTO.getAddress());
        company.setVat(companyDTO.getVat());
        company.setNumEmployees(companyDTO.getNumEmployees());
        company.setRevenues(companyDTO.getRevenues());
        company.setIndustry(companyDTO.getIndustry());
        company.setIsSubCompany(companyDTO.getSubCompany());

        // Handle CompanyGroup
        if (companyDTO.getCompanyGroupId() != null) {
            CompanyGroup companyGroup = companyGroupRepository.findById(companyDTO.getCompanyGroupId())
                    .orElseThrow(() -> new RuntimeException("CompanyGroup not found"));
            company.setCompanyGroup(companyGroup);
        } else {
            company.setCompanyGroup(null);
        }

        // Handle Project
        if (companyDTO.getProjectId() != null) {
            Project project = projectRepository.findById(companyDTO.getProjectId())
                    .orElseThrow(() -> new RuntimeException("Project not found"));
            company.setProject(project);
        } else {
            company.setProject(null);
        }
    }

    private void updateValueChainObjects(Company company, List<ValueChainObjectDTO> vcoDTOs) {
        Map<Long, ValueChainObject> existingVcoMap = company.getValueChainObjects().stream()
                .collect(Collectors.toMap(ValueChainObject::getId, Function.identity()));

        Set<Long> incomingIds = new HashSet<>();
        if (vcoDTOs != null) {
            for (ValueChainObjectDTO vcoDTO : vcoDTOs) {
                ValueChainObject vco;
                if (vcoDTO.getId() != null && existingVcoMap.containsKey(vcoDTO.getId())) {
                    // Update existing ValueChainObject
                    vco = existingVcoMap.get(vcoDTO.getId());
                } else {
                    // Create new ValueChainObject
                    vco = new ValueChainObject();
                    vco.setCompany(company);
                }
                vco.setName(vcoDTO.getName());
                vco.setIndustry(vcoDTO.getIndustry());
                valueChainObjectRepository.save(vco);
                incomingIds.add(vco.getId());
            }
        }

        // Delete ValueChainObjects not present in incoming DTOs
        List<ValueChainObject> toDelete = company.getValueChainObjects().stream()
                .filter(vco -> !incomingIds.contains(vco.getId()))
                .collect(Collectors.toList());

        valueChainObjectRepository.deleteAll(toDelete);

        // Refresh the company's ValueChainObjects list
        //company.setValueChainObjects(valueChainObjectRepository.findByCompany_Id(company.getId()));
    }

    private void updateStakeholders(Company company, List<StakeholderDTO> stakeholderDTOs) {
        Map<Long, Stakeholder> existingStakeholderMap = company.getStakeholders().stream()
                .collect(Collectors.toMap(Stakeholder::getId, Function.identity()));

        Set<Long> incomingIds = new HashSet<>();
        if (stakeholderDTOs != null) {
            for (StakeholderDTO stakeholderDTO : stakeholderDTOs) {
                Stakeholder stakeholder;
                if (stakeholderDTO.getId() != null && existingStakeholderMap.containsKey(stakeholderDTO.getId())) {
                    // Update existing Stakeholder
                    stakeholder = existingStakeholderMap.get(stakeholderDTO.getId());
                } else {
                    // Create new Stakeholder
                    stakeholder = new Stakeholder();
                    stakeholder.setCompany(company);
                }
                stakeholder.setName(stakeholderDTO.getName());
                stakeholder.setRole(stakeholderDTO.getRole());
                stakeholder.setEmail(stakeholderDTO.getEmail());
                stakeholder.setStakeholderType(stakeholderDTO.getStakeholderType());

                // Set other fields
                stakeholder.setToken(stakeholderDTO.getToken());
                stakeholder.setStatus(stakeholderDTO.getStatus());
                stakeholder.setCompletedDatapoints(stakeholderDTO.getCompletedDatapoints());
                stakeholder.setTotalDatapoints(stakeholderDTO.getTotalDatapoints());

                // Associate with project
                if (stakeholderDTO.getProjectId() != null) {
                    Project project = projectRepository.findById(stakeholderDTO.getProjectId())
                            .orElseThrow(() -> new RuntimeException("Project not found"));
                    stakeholder.setProject(project);
                } else if (company.getProject() != null) {
                    stakeholder.setProject(company.getProject());
                }

                stakeholderRepository.save(stakeholder);
                incomingIds.add(stakeholder.getId());
            }
        }

        // Delete Stakeholders not present in incoming DTOs
        List<Stakeholder> toDelete = company.getStakeholders().stream()
                .filter(s -> !incomingIds.contains(s.getId()))
                .collect(Collectors.toList());

        //stakeholderRepository.deleteAll(toDelete);

        // Refresh the company's Stakeholders list
        //company.setStakeholders(stakeholderRepository.findByCompany_Id(company.getId()));
    }

    private void updateEsrsTopics(Company company, List<EsrsTopicDTO> esrsTopicDTOs) {
//        Map<Long, EsrsTopic> existingEsrsTopicMap = company.getEsrsTopics().stream()
//                .collect(Collectors.toMap(EsrsTopic::getId, Function.identity()));
//
//        Set<Long> incomingIds = new HashSet<>();
//        if (esrsTopicDTOs != null) {
//            for (EsrsTopicDTO esrsTopicDTO : esrsTopicDTOs) {
//                EsrsTopic esrsTopic;
//                if (esrsTopicDTO.getId() != null && existingEsrsTopicMap.containsKey(esrsTopicDTO.getId())) {
//                    // Update existing EsrsTopic
//                    esrsTopic = existingEsrsTopicMap.get(esrsTopicDTO.getId());
//                } else {
//                    // Create new EsrsTopic
//                    esrsTopic = new EsrsTopic();
//
//                }
//                esrsTopic.setArea(esrsTopicDTO.getArea());
//                esrsTopic.setEsrsCode(esrsTopicDTO.getEsrsCode());
//                esrsTopic.setTopic(esrsTopicDTO.getTopic());
//                esrsTopic.setSubtopic(esrsTopicDTO.getSubtopic());
//                esrsTopic.setSubSubTopic(esrsTopicDTO.getSubSubTopic());
//                esrsTopicRepository.save(esrsTopic);
//                incomingIds.add(esrsTopic.getId());
//            }
//        }
//
//        // Delete EsrsTopics not present in incoming DTOs
//        List<EsrsTopic> toDelete = company.getEsrsTopics().stream()
//                .filter(e -> !incomingIds.contains(e.getId()))
//                .collect(Collectors.toList());
//
//        esrsTopicRepository.deleteAll(toDelete);
//
//        // Refresh the company's EsrsTopics list
//       // company.setEsrsTopics(esrsTopicRepository.findByCompany_Id(company.getId()));
    }

    private CompanyDTO convertToDTO(Company company) {
        CompanyDTO companyDTO = new CompanyDTO();
        companyDTO.setId(company.getId());
        companyDTO.setCompanyName(company.getCompanyName());
        companyDTO.setAddress(company.getAddress());
        companyDTO.setVat(company.getVat());
        companyDTO.setNumEmployees(company.getNumEmployees());
        companyDTO.setRevenues(company.getRevenues());
        companyDTO.setIndustry(company.getIndustry());
        companyDTO.setSubCompany(company.getIsSubCompany());

        if (company.getCompanyGroup() != null) {
            companyDTO.setCompanyGroupId(company.getCompanyGroup().getId());
        }

        // Set projectId
        if (company.getProject() != null) {
            companyDTO.setProjectId(company.getProject().getId());
        }

        // Map ValueChainObjects
        if (company.getValueChainObjects() != null) {
            List<ValueChainObjectDTO> vcoDTOs = new ArrayList<>();
            for (ValueChainObject vco : company.getValueChainObjects()) {
                vcoDTOs.add(convertValueChainObjectToDTO(vco));
            }
            companyDTO.setValueChainObjects(vcoDTOs);
        }

        // Map Stakeholders
        if (company.getStakeholders() != null) {
            List<StakeholderDTO> stakeholderDTOs = new ArrayList<>();
            for (Stakeholder stakeholder : company.getStakeholders()) {
                stakeholderDTOs.add(convertStakeholderToDTO(stakeholder));
            }
            companyDTO.setStakeholders(stakeholderDTOs);
        }

        // Map EsrsTopics
//        if (company.getEsrsTopics() != null) {
//            List<EsrsTopicDTO> esrsTopicDTOs = new ArrayList<>();
//            for (EsrsTopic esrsTopic : company.getEsrsTopics()) {
//                esrsTopicDTOs.add(convertEsrsTopicToDTO(esrsTopic));
//            }
//            companyDTO.setEsrsTopics(esrsTopicDTOs);
//        }

        return companyDTO;
    }

    private ValueChainObjectDTO convertValueChainObjectToDTO(ValueChainObject vco) {
        ValueChainObjectDTO dto = new ValueChainObjectDTO();
        dto.setId(vco.getId());
        dto.setName(vco.getName());
        dto.setIndustry(vco.getIndustry());
        dto.setCompanyId(vco.getCompany().getId());
        return dto;
    }

    private StakeholderDTO convertStakeholderToDTO(Stakeholder stakeholder) {
        StakeholderDTO dto = new StakeholderDTO();
        dto.setId(stakeholder.getId());
        dto.setName(stakeholder.getName());
        dto.setRole(stakeholder.getRole());
        dto.setEmail(stakeholder.getEmail());
        dto.setStakeholderType(stakeholder.getStakeholderType());
        dto.setToken(stakeholder.getToken());
        dto.setStatus(stakeholder.getStatus());
        dto.setCompletedDatapoints(stakeholder.getCompletedDatapoints());
        dto.setTotalDatapoints(stakeholder.getTotalDatapoints());

        // Set companyId
        if (stakeholder.getCompany() != null) {
            dto.setCompanyId(stakeholder.getCompany().getId());
            dto.setCompanyName(stakeholder.getCompany().getCompanyName());
        }

        // Set projectId
        if (stakeholder.getProject() != null) {
            dto.setProjectId(stakeholder.getProject().getId());
        } else if (stakeholder.getCompany() != null && stakeholder.getCompany().getProject() != null) {
            dto.setProjectId(stakeholder.getCompany().getProject().getId());
        }

        return dto;
    }

    private EsrsTopicDTO convertEsrsTopicToDTO(EsrsTopic esrsTopic) {
        EsrsTopicDTO dto = new EsrsTopicDTO();
        dto.setId(esrsTopic.getId());
        dto.setArea(esrsTopic.getArea());
        dto.setEsrsCode(esrsTopic.getEsrsCode());
        dto.setTopic(esrsTopic.getTopic());
        dto.setSubtopic(esrsTopic.getSubtopic());
        dto.setSubSubTopic(esrsTopic.getSubSubTopic());

        return dto;
    }
}
