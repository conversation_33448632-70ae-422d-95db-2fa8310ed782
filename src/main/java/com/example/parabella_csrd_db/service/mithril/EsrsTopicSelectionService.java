package com.example.parabella_csrd_db.service.mithril;

import com.example.parabella_csrd_db.dto.mithril.EsrsTopicSelectionDTO;
import com.example.parabella_csrd_db.database.maindatabase.model.mithril.EsrsTopicSelection;
import com.example.parabella_csrd_db.database.maindatabase.repository.mithril.CompanyRepository;
import com.example.parabella_csrd_db.database.maindatabase.repository.mithril.EsrsTopicSelectionRepository;
import com.example.parabella_csrd_db.database.maindatabase.repository.mithril.EsrsTopicsRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@Service
public class EsrsTopicSelectionService {

    @Autowired
    private EsrsTopicSelectionRepository esrsTopicSelectionRepository;

    @Autowired
    private EsrsTopicsRepository esrsTopicRepository;

    @Autowired
    private CompanyRepository companyRepository;

    public EsrsTopicSelectionDTO createEsrsTopicSelection(EsrsTopicSelectionDTO dto) {
        EsrsTopicSelection selection = new EsrsTopicSelection();
        mapDtoToEntity(dto, selection);
        selection = esrsTopicSelectionRepository.save(selection);
        return convertToDto(selection);
    }

    public EsrsTopicSelectionDTO getEsrsTopicSelection(Long id) {
        EsrsTopicSelection selection = esrsTopicSelectionRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("EsrsTopicSelection not found with id " + id));
        return convertToDto(selection);
    }

    public List<EsrsTopicSelectionDTO> getAllEsrsTopicSelections() {
        return esrsTopicSelectionRepository.findAll().stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    public EsrsTopicSelectionDTO updateEsrsTopicSelection(Long id, EsrsTopicSelectionDTO dto) {
        EsrsTopicSelection selection = esrsTopicSelectionRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("EsrsTopicSelection not found with id " + id));
        mapDtoToEntity(dto, selection);
        selection = esrsTopicSelectionRepository.save(selection);
        return convertToDto(selection);
    }

    public void deleteEsrsTopicSelection(Long id) {
        esrsTopicSelectionRepository.deleteById(id);
    }

    public List<EsrsTopicSelectionDTO> getEsrsTopicSelectionsByCompanyId(Long companyId) {
        List<EsrsTopicSelection> selections = esrsTopicSelectionRepository.findByCompanyId(companyId);
        return selections.stream().map(this::convertToDto).collect(Collectors.toList());
    }

    // === MAPPING HELPERS ===

    private void mapDtoToEntity(EsrsTopicSelectionDTO dto, EsrsTopicSelection entity) {
        // esrsTopic
        entity.setEsrsTopic(
                esrsTopicRepository.findById(dto.getEsrsTopicId())
                        .orElseThrow(() -> new RuntimeException("EsrsTopic not found with id " + dto.getEsrsTopicId()))
        );
        // company
        entity.setCompany(
                companyRepository.findById(dto.getCompanyId())
                        .orElseThrow(() -> new RuntimeException("Company not found with id " + dto.getCompanyId()))
        );
        entity.setRelevant(dto.getRelevant());
        entity.setReasonIrrelevance(dto.getReasonIrrelevance());

        // NEW FIELDS
        entity.setFinalImpactMaterialityActualImpact(dto.getFinalImpactMaterialityActualImpact());
        entity.setFinalFinancialMaterialityActualImpact(dto.getFinalFinancialMaterialityActualImpact());
        entity.setFinalRelevance(dto.getFinalRelevance());
    }

    public EsrsTopicSelectionDTO convertToDto(EsrsTopicSelection selection) {
        EsrsTopicSelectionDTO dto = new EsrsTopicSelectionDTO();
        dto.setId(selection.getId());
        dto.setEsrsTopicId(selection.getEsrsTopic().getId());
        dto.setCompanyId(selection.getCompany().getId());
        dto.setRelevant(selection.getRelevant());
        dto.setReasonIrrelevance(selection.getReasonIrrelevance());

        // We can read additional info from esrsTopic
        dto.setArea(selection.getEsrsTopic().getArea());
        dto.setTopic(selection.getEsrsTopic().getTopic());
        dto.setSubtopic(selection.getEsrsTopic().getSubtopic());

        // NEW FIELDS
        dto.setFinalImpactMaterialityActualImpact(selection.getFinalImpactMaterialityActualImpact());
        dto.setFinalFinancialMaterialityActualImpact(selection.getFinalFinancialMaterialityActualImpact());
        dto.setFinalRelevance(selection.getFinalRelevance());

        return dto;
    }
}
