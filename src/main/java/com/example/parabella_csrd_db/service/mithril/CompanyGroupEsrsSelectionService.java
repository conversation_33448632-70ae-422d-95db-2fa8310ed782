package com.example.parabella_csrd_db.service.mithril;

import com.example.parabella_csrd_db.dto.mithril.CompanyGroupEsrsSelectionDTO;
import com.example.parabella_csrd_db.database.maindatabase.model.mithril.CompanyGroup;
import com.example.parabella_csrd_db.database.maindatabase.model.mithril.CompanyGroupEsrsSelection;
import com.example.parabella_csrd_db.database.maindatabase.model.mithril.EsrsTopic;
import com.example.parabella_csrd_db.database.maindatabase.repository.mithril.CompanyGroupEsrsSelectionRepository;
import com.example.parabella_csrd_db.database.maindatabase.repository.mithril.CompanyGroupRepository;
import com.example.parabella_csrd_db.database.maindatabase.repository.mithril.EsrsTopicsRepository;
import jakarta.transaction.Transactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@Service
public class CompanyGroupEsrsSelectionService {

    @Autowired
    private CompanyGroupEsrsSelectionRepository groupSelectionRepository;

    @Autowired
    private CompanyGroupRepository companyGroupRepository;

    @Autowired
    private EsrsTopicsRepository esrsTopicRepository;

    @Transactional
    public CompanyGroupEsrsSelectionDTO createOrUpdateGroupSelection(CompanyGroupEsrsSelectionDTO selectionDTO) {
        CompanyGroup companyGroup = companyGroupRepository.findById(selectionDTO.getCompanyGroupId())
                .orElseThrow(() -> new RuntimeException("CompanyGroup not found"));

        EsrsTopic esrsTopic = esrsTopicRepository.findById(selectionDTO.getEsrsTopicId())
                .orElseThrow(() -> new RuntimeException("EsrsTopic not found"));

        CompanyGroupEsrsSelection selection = groupSelectionRepository
                .findByCompanyGroupAndEsrsTopic(companyGroup, esrsTopic)
                .orElse(new CompanyGroupEsrsSelection());

        selection.setCompanyGroup(companyGroup);
        selection.setEsrsTopic(esrsTopic);

        selection.setIsRelevantCompanyGroup(selectionDTO.getIsRelevantCompanyGroup());
        selection.setReasonIrrelevance(selectionDTO.getReasonIrrelevance());

        selection = groupSelectionRepository.save(selection);
        return convertToDTO(selection);
    }

    public List<CompanyGroupEsrsSelectionDTO> getGroupSelectionsByCompanyGroupId(Long companyGroupId) {
        List<CompanyGroupEsrsSelection> selections = groupSelectionRepository.findByCompanyGroupId(companyGroupId);
        return selections.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    private CompanyGroupEsrsSelectionDTO convertToDTO(CompanyGroupEsrsSelection selection) {
        CompanyGroupEsrsSelectionDTO dto = new CompanyGroupEsrsSelectionDTO();
        dto.setId(selection.getId());
        dto.setCompanyGroupId(selection.getCompanyGroup().getId());
        dto.setEsrsTopicId(selection.getEsrsTopic().getId());
        dto.setIsRelevantCompanyGroup(selection.getIsRelevantCompanyGroup());
        dto.setReasonIrrelevance(selection.getReasonIrrelevance());

        return dto;
    }
}
