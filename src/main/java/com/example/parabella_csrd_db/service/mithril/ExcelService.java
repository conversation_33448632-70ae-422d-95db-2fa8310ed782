package com.example.parabella_csrd_db.service.mithril;

import com.example.parabella_csrd_db.utilities.ExcelManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.List;
import java.util.Map;

@Service
public class ExcelService {

    private final ExcelManager excelManager;

    @Autowired
    public ExcelService(ExcelManager excelManager) {
        this.excelManager = excelManager;
    }

    public List<Map<String, String>> getExcelData(String fileName) throws IOException {
        return excelManager.readExcel(fileName);
    }

    public Map<String, List<Map<String, String>>> getAllExcelData() throws IOException {
        return excelManager.readAllExcelFiles();
    }
}
