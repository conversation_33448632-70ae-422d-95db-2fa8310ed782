package com.example.parabella_csrd_db.service.mithril;

import com.example.parabella_csrd_db.dto.mithril.ValueChainObjectDTO;
import com.example.parabella_csrd_db.database.maindatabase.model.mithril.Company;
import com.example.parabella_csrd_db.database.maindatabase.model.mithril.ValueChainObject;
import com.example.parabella_csrd_db.database.maindatabase.repository.mithril.CompanyRepository;
import com.example.parabella_csrd_db.database.maindatabase.repository.mithril.ValueChainObjectRepository;
import jakarta.transaction.Transactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class ValueChainObjectService {

    @Autowired
    private ValueChainObjectRepository valueChainObjectRepository;

    @Autowired
    private CompanyRepository companyRepository;

    @Transactional
    public ValueChainObjectDTO createValueChainObject(ValueChainObjectDTO vcoDTO) {
        ValueChainObject vco = new ValueChainObject();
        vco.setName(vcoDTO.getName());
        vco.setIndustry(vcoDTO.getIndustry());

        if (vcoDTO.getCompanyId() != null) {
            Company company = companyRepository.findById(vcoDTO.getCompanyId())
                    .orElseThrow(() -> new RuntimeException("Company not found"));
            vco.setCompany(company);
        }

        vco = valueChainObjectRepository.save(vco);
        return convertToDTO(vco);
    }

    @Transactional
    public ValueChainObjectDTO updateValueChainObject(Long id, ValueChainObjectDTO vcoDTO) {
        ValueChainObject vco = valueChainObjectRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("ValueChainObject not found"));
        vco.setName(vcoDTO.getName());
        vco.setIndustry(vcoDTO.getIndustry());

        if (vcoDTO.getCompanyId() != null) {
            Company company = companyRepository.findById(vcoDTO.getCompanyId())
                    .orElseThrow(() -> new RuntimeException("Company not found"));
            vco.setCompany(company);
        } else {
            vco.setCompany(null);
        }

        vco = valueChainObjectRepository.save(vco);
        return convertToDTO(vco);
    }

    public ValueChainObjectDTO getValueChainObject(Long id) {
        ValueChainObject vco = valueChainObjectRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("ValueChainObject not found"));
        return convertToDTO(vco);
    }

    @Transactional
    public void deleteValueChainObject(Long id) {
        valueChainObjectRepository.deleteById(id);
    }

    private ValueChainObjectDTO convertToDTO(ValueChainObject vco) {
        ValueChainObjectDTO dto = new ValueChainObjectDTO();
        dto.setId(vco.getId());
        dto.setName(vco.getName());
        dto.setIndustry(vco.getIndustry());
        if (vco.getCompany() != null) {
            dto.setCompanyId(vco.getCompany().getId());
        }
        return dto;
    }
}
