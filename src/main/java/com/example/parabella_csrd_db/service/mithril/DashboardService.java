package com.example.parabella_csrd_db.service.mithril;

import com.example.parabella_csrd_db.dto.mithril.DatapointStatus;
import com.example.parabella_csrd_db.dto.mithril.StakeholderProgress;
import com.example.parabella_csrd_db.database.maindatabase.model.mithril.Stakeholder;
import com.example.parabella_csrd_db.database.maindatabase.repository.mithril.IroEvaluationRepository;
import com.example.parabella_csrd_db.database.maindatabase.repository.mithril.stakeholder_navigation.StakeholderRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

// DashboardService.java
@Service
public class DashboardService {

    @Autowired
    private IroEvaluationRepository iroEvaluationRepository;

    @Autowired
    private StakeholderRepository stakeholderRepository;

    public List<StakeholderProgress> getStakeholdersProgress(Long companyId) {
        System.out.println(companyId);
        List<Stakeholder> stakeholders = stakeholderRepository.findByCompany_Id(companyId);


        List<StakeholderProgress> progressList = new ArrayList<>();

        for (Stakeholder stakeholder : stakeholders) {
            StakeholderProgress progress = new StakeholderProgress();
            progress.setName(stakeholder.getName());
            progress.setCompletedDatapoints(stakeholder.getCompletedDatapoints());
            progress.setTotalDatapoints(stakeholder.getTotalDatapoints());

            double progressPercentage = (stakeholder.getTotalDatapoints() > 0)
                    ? ((double) stakeholder.getCompletedDatapoints() / stakeholder.getTotalDatapoints()) * 100
                    : 0;
            progress.setProgress(progressPercentage);
            progressList.add(progress);
        }

        return progressList;
    }

    public DatapointStatus getDatapointStatus(Long companyId) {

        Long totalDatapoints = iroEvaluationRepository.countByCompany_Id(companyId);
        System.out.println("totaldatapoints:" + totalDatapoints);
        Long collectedDatapoints = iroEvaluationRepository.countByCompany_IdAndIsLockedTrue(companyId);
        Long pendingDatapoints = totalDatapoints - collectedDatapoints;

        System.out.println("Pending datapoints" + pendingDatapoints);

        double collectedPercentage = (totalDatapoints > 0)
                ? ((double) collectedDatapoints / totalDatapoints) * 100
                : 0;

        DatapointStatus status = new DatapointStatus();
        status.setCollected(collectedDatapoints);
        status.setPending(pendingDatapoints);
        status.setCollectedPercentage(collectedPercentage);

        // Implement logic to calculate overdue datapoints if applicable
        status.setOverdue(0L); // Placeholder

        return status;
    }
}
