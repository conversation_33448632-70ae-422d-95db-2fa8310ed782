package com.example.parabella_csrd_db.service.mithril;

import com.example.parabella_csrd_db.dto.mithril.IroEvaluationDTO;
import com.example.parabella_csrd_db.database.maindatabase.model.mithril.Iro;
import com.example.parabella_csrd_db.database.maindatabase.model.mithril.IroEvaluation;
import com.example.parabella_csrd_db.database.maindatabase.repository.mithril.CompanyRepository;
import com.example.parabella_csrd_db.database.maindatabase.repository.mithril.IroEvaluationRepository;
import com.example.parabella_csrd_db.database.maindatabase.repository.mithril.IroRepository;
import jakarta.transaction.Transactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class IroEvaluationService {

    @Autowired
    private IroEvaluationRepository iroEvaluationRepository;

    @Autowired
    private IroRepository iroRepository;

    @Autowired
    private CompanyRepository companyRepository; // if needed for additional validations

    public IroEvaluationDTO getIroEvaluation(Long id) {
        IroEvaluation evaluation = iroEvaluationRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("IroEvaluation not found with id: " + id));
        return convertToDTO(evaluation);
    }

    @Transactional
    public IroEvaluationDTO createOrUpdateIroEvaluation(IroEvaluationDTO dto, String stakeholderName) {
        // Ensure that we have an IRO reference for the evaluation
        if (dto.getIroId() == null) {
            throw new RuntimeException("iroId is required for an evaluation");
        }
        Iro iro = iroRepository.findById(dto.getIroId())
                .orElseThrow(() -> new RuntimeException("Iro not found with id: " + dto.getIroId()));



        // Find existing evaluation for this IRO or create a new one if none exists
        IroEvaluation evaluation = iroEvaluationRepository.findByIro(iro)
                .orElse(new IroEvaluation());

        evaluation.setLastModifiedBy(stakeholderName);
        evaluation.setLastModifiedAt(LocalDateTime.now());
        mapDtoToEntity(dto, evaluation);

        // Save the evaluation and return the updated DTO
        evaluation = iroEvaluationRepository.save(evaluation);
        return convertToDTO(evaluation);
    }

    @Transactional
    public void deleteIroEvaluation(Long id) {
        iroEvaluationRepository.deleteById(id);
    }

    public IroEvaluationDTO getIroEvaluationByTopicSelectionId(Long topicSelectionId) {
        IroEvaluation evaluation = iroEvaluationRepository.findByIro_EsrsTopicSelection_Id(topicSelectionId)
                .orElseThrow(() -> new RuntimeException("IroEvaluation not found for topic selection id: " + topicSelectionId));
        return convertToDTO(evaluation);
    }

    public List<IroEvaluationDTO> getIroEvaluationsByCompanyId(Long companyId) {
        List<IroEvaluation> evaluations = iroEvaluationRepository.findByCompanyId(companyId);
        return evaluations.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    private void mapDtoToEntity(IroEvaluationDTO dto, IroEvaluation entity) {
        // Common fields
        entity.setDescription(dto.getDescription());
        entity.setDirectIndirectImpact(dto.getDirectIndirectImpact());
        entity.setAffectedArea(dto.getAffectedArea());
        entity.setTimeHorizon(dto.getTimeHorizon());

        // Impact IRO fields
        entity.setPositiveNegativeImpact(dto.getPositiveNegativeImpact());
        entity.setActualPotentialImpact(dto.getActualPotentialImpact());
        entity.setHumanRightsImpact(dto.getHumanRightsImpact());
        entity.setScale(dto.getScale());
        entity.setScope(dto.getScope());
        entity.setIrreversibility(dto.getIrreversibility());

        // Financial IRO fields
        entity.setRiskOpportunity(dto.getRiskOpportunity());
        entity.setFinancialMaterialityActualImpact(dto.getFinancialMaterialityActualImpact());
        entity.setProbability(dto.getProbability());

        // Result fields
        entity.setImpactMaterialityPotentialImpact(dto.getImpactMaterialityPotentialImpact());
        entity.setFinancialMaterialityPotentialImpact(dto.getFinancialMaterialityPotentialImpact());
        entity.setResultMaterialityAssessment(dto.getResultMaterialityAssessment());

        // Metadata
        entity.setIsLocked(dto.getIs_locked());
        // lastModifiedBy and lastModifiedAt are set in the main service method
    }

    private IroEvaluationDTO convertToDTO(IroEvaluation entity) {
        IroEvaluationDTO dto = new IroEvaluationDTO();
        dto.setId(entity.getId());

        if (entity.getIro() != null) {
            dto.setIroId(entity.getIro().getId());
            if (entity.getIro().getCompany() != null) {
                dto.setCompanyId(entity.getIro().getCompany().getId());
            }
        }

        // Common fields
        dto.setDescription(entity.getDescription());
        dto.setDirectIndirectImpact(entity.getDirectIndirectImpact());
        dto.setAffectedArea(entity.getAffectedArea());
        dto.setTimeHorizon(entity.getTimeHorizon());

        // Impact IRO fields
        dto.setPositiveNegativeImpact(entity.getPositiveNegativeImpact());
        dto.setActualPotentialImpact(entity.getActualPotentialImpact());
        dto.setHumanRightsImpact(entity.getHumanRightsImpact());
        dto.setScale(entity.getScale());
        dto.setScope(entity.getScope());
        dto.setIrreversibility(entity.getIrreversibility());

        // Financial IRO fields
        dto.setRiskOpportunity(entity.getRiskOpportunity());
        dto.setFinancialMaterialityActualImpact(entity.getFinancialMaterialityActualImpact());
        dto.setProbability(entity.getProbability());

        // Result fields
        dto.setImpactMaterialityPotentialImpact(entity.getImpactMaterialityPotentialImpact());
        dto.setFinancialMaterialityPotentialImpact(entity.getFinancialMaterialityPotentialImpact());
        dto.setResultMaterialityAssessment(entity.getResultMaterialityAssessment());

        // Metadata
        dto.setLastModifiedBy(entity.getLastModifiedBy());
        dto.setLastModifiedAt(entity.getLastModifiedAt());
        dto.setIs_locked(entity.getIsLocked());

        return dto;
    }
}
