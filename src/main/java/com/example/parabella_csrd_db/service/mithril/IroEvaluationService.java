package com.example.parabella_csrd_db.service.mithril;

import com.example.parabella_csrd_db.dto.mithril.IroEvaluationDTO;
import com.example.parabella_csrd_db.database.maindatabase.model.mithril.Iro;
import com.example.parabella_csrd_db.database.maindatabase.model.mithril.IroEvaluation;
import com.example.parabella_csrd_db.database.maindatabase.repository.mithril.CompanyRepository;
import com.example.parabella_csrd_db.database.maindatabase.repository.mithril.IroEvaluationRepository;
import com.example.parabella_csrd_db.database.maindatabase.repository.mithril.IroRepository;
import jakarta.transaction.Transactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class IroEvaluationService {

    @Autowired
    private IroEvaluationRepository iroEvaluationRepository;

    @Autowired
    private IroRepository iroRepository;

    @Autowired
    private CompanyRepository companyRepository; // if needed for additional validations

    public IroEvaluationDTO getIroEvaluation(Long id) {
        IroEvaluation evaluation = iroEvaluationRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("IroEvaluation not found with id: " + id));
        return convertToDTO(evaluation);
    }

    @Transactional
    public IroEvaluationDTO createOrUpdateIroEvaluation(IroEvaluationDTO dto, String stakeholderName) {
        // Ensure that we have an IRO reference for the evaluation
        if (dto.getIroId() == null) {
            throw new RuntimeException("iroId is required for an evaluation");
        }
        Iro iro = iroRepository.findById(dto.getIroId())
                .orElseThrow(() -> new RuntimeException("Iro not found with id: " + dto.getIroId()));



        // Find existing evaluation for this IRO or create a new one if none exists
        IroEvaluation evaluation = iroEvaluationRepository.findByIro(iro)
                .orElse(new IroEvaluation());

        evaluation.setLastModifiedBy(stakeholderName);
        evaluation.setLastModifiedAt(LocalDateTime.now());
        mapDtoToEntity(dto, evaluation);

        // Save the evaluation and return the updated DTO
        evaluation = iroEvaluationRepository.save(evaluation);
        return convertToDTO(evaluation);
    }

    @Transactional
    public void deleteIroEvaluation(Long id) {
        iroEvaluationRepository.deleteById(id);
    }

    public IroEvaluationDTO getIroEvaluationByTopicSelectionId(Long topicSelectionId) {
        IroEvaluation evaluation = iroEvaluationRepository.findByIro_EsrsTopicSelection_Id(topicSelectionId)
                .orElseThrow(() -> new RuntimeException("IroEvaluation not found for topic selection id: " + topicSelectionId));
        return convertToDTO(evaluation);
    }

    public List<IroEvaluationDTO> getIroEvaluationsByCompanyId(Long companyId) {
        List<IroEvaluation> evaluations = iroEvaluationRepository.findByCompanyId(companyId);
        return evaluations.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    private void mapDtoToEntity(IroEvaluationDTO dto, IroEvaluation evaluation) {
        evaluation.setActualPotentialImpact(dto.getActualPotentialImpact());
        evaluation.setAffectedArea(dto.getAffectedArea());
        evaluation.setDescription(dto.getDescription());
        evaluation.setEffect(dto.getEffect());
        evaluation.setConnection(dto.getConnection());
        evaluation.setScale(dto.getScale());
        evaluation.setScope(dto.getScope());
        evaluation.setIrreversibility(dto.getIrreversibility());
        evaluation.setProbability(dto.getProbability());
        evaluation.setImpactMaterialityActualImpact(dto.getImpactMaterialityActualImpact());
        evaluation.setImpactMaterialityPotentialImpact(dto.getImpactMaterialityPotentialImpact());
        evaluation.setBasisOfAssessmentOfFinancialImpact(dto.getBasisOfAssessmentOfFinancialImpact());
        evaluation.setFinancialMaterialityActualImpact(dto.getFinancialMaterialityActualImpact());
        evaluation.setFinancialMaterialityPotentialImpact(dto.getFinancialMaterialityPotentialImpact());
        evaluation.setTimeSpan(dto.getTimeSpan());
        evaluation.setResultMaterialityAssessment(dto.getResultMaterialityAssessment());
        evaluation.setLastModifiedBy(dto.getLastModifiedBy());
        evaluation.setLastModifiedAt(dto.getLastModifiedAt());
        //evaluation.setIs_locked(dto.getLocked());

        // Set the IRO association (this is required for the one-to-one relationship)
        Iro iro = iroRepository.findById(dto.getIroId())
                .orElseThrow(() -> new RuntimeException("Iro not found"));
        evaluation.setIro(iro);
    }

    private IroEvaluationDTO convertToDTO(IroEvaluation evaluation) {
        IroEvaluationDTO dto = new IroEvaluationDTO();
        dto.setId(evaluation.getId());
        dto.setCompanyId(evaluation.getIro().getCompany().getId());
        dto.setActualPotentialImpact(evaluation.getActualPotentialImpact());
        dto.setAffectedArea(evaluation.getAffectedArea());
        dto.setDescription(evaluation.getDescription());
        dto.setEffect(evaluation.getEffect());
        dto.setConnection(evaluation.getConnection());
        dto.setScale(evaluation.getScale());
        dto.setScope(evaluation.getScope());
        dto.setIrreversibility(evaluation.getIrreversibility());
        dto.setProbability(evaluation.getProbability());
        dto.setImpactMaterialityActualImpact(evaluation.getImpactMaterialityActualImpact());
        dto.setImpactMaterialityPotentialImpact(evaluation.getImpactMaterialityPotentialImpact());
        dto.setBasisOfAssessmentOfFinancialImpact(evaluation.getBasisOfAssessmentOfFinancialImpact());
        dto.setFinancialMaterialityActualImpact(evaluation.getFinancialMaterialityActualImpact());
        dto.setFinancialMaterialityPotentialImpact(evaluation.getFinancialMaterialityPotentialImpact());
        dto.setTimeSpan(evaluation.getTimeSpan());
        dto.setResultMaterialityAssessment(evaluation.getResultMaterialityAssessment());
        dto.setLastModifiedBy(evaluation.getLastModifiedBy());
        dto.setLastModifiedAt(evaluation.getLastModifiedAt());
       // dto.setLocked(evaluation.getIs_locked());
        if(evaluation.getIro() != null) {
            dto.setIroId(evaluation.getIro().getId());
        }
        return dto;
    }
}
