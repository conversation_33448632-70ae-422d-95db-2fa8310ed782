package com.example.parabella_csrd_db.service.mithril;

import com.example.parabella_csrd_db.dto.mithril.ProjectDTO;
import com.example.parabella_csrd_db.database.maindatabase.model.authentication.User;
import com.example.parabella_csrd_db.database.maindatabase.model.mithril.Company;
import com.example.parabella_csrd_db.database.maindatabase.model.mithril.CompanyGroup;
import com.example.parabella_csrd_db.database.maindatabase.model.mithril.Project;
import com.example.parabella_csrd_db.database.maindatabase.repository.authentication.UserRepository;
import com.example.parabella_csrd_db.database.maindatabase.repository.mithril.CompanyGroupRepository;
import com.example.parabella_csrd_db.database.maindatabase.repository.mithril.CompanyRepository;
import com.example.parabella_csrd_db.database.maindatabase.repository.mithril.ProjectRepository;
import jakarta.transaction.Transactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
public class ProjectService {

    @Autowired
    private ProjectRepository projectRepository;

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private CompanyRepository companyRepository;

    @Autowired
    private CompanyGroupRepository companyGroupRepository;

    // Find projects by user ID and convert to DTOs
    public List<ProjectDTO> findProjectsByUserId(Long userId) {
        return projectRepository.findByUserId(userId)
                .stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    // Find a project by ID and convert to DTO
    public Optional<ProjectDTO> findProjectDTOById(Long projectId) {
        return projectRepository.findById(projectId)
                .map(this::convertToDTO);
    }

    // Converts Project entity to ProjectDTO
    public ProjectDTO convertToDTO(Project project) {
        ProjectDTO dto = new ProjectDTO();
        dto.setId(project.getId());
        dto.setProjectName(project.getProjectName());
        dto.setProjectDescription(project.getProjectDescription());
        dto.setCreatedAt(project.getCreatedAt());
        dto.setUserId(project.getUser().getId());
        dto.setProjectType(project.getProjectType());
        dto.setCompanyId(project.getCompanyId()); // Add this
        dto.setCompanyGroupId(project.getCompanyGroupId()); // Add this

        // Include additional fields as needed
        return dto;
    }

    // Method to create a new project
    @Transactional
    public ProjectDTO createProject(ProjectDTO projectDTO) {
        Project project = new Project();
        project.setProjectName(projectDTO.getProjectName());
        project.setProjectDescription(projectDTO.getProjectDescription());
        project.setProjectType(projectDTO.getProjectType());


        // Set the user
        User currentUser = getCurrentUser();
        project.setUser(currentUser);

        // Save the project
        Project savedProject = projectRepository.save(project);

        // Automatically create an empty Company or CompanyGroup
        if ("company".equals(projectDTO.getProjectType())) {
            // Create an empty Company
            Company company = new Company();
            company.setProject(savedProject); // Associate with the project
            Company savedCompany = companyRepository.save(company);

            // Set the companyId in the project
            savedProject.setCompanyId(savedCompany.getId());
            projectRepository.save(savedProject); // Save the project again
        } else if ("companyGroup".equals(projectDTO.getProjectType())) {
            // Create an empty CompanyGroup
            CompanyGroup companyGroup = new CompanyGroup();
            companyGroup.setProject(savedProject); // Associate with the project
            CompanyGroup savedCompanyGroup = companyGroupRepository.save(companyGroup);

            // Set the companyGroupId in the project
            savedProject.setCompanyGroupId(savedCompanyGroup.getId());
            projectRepository.save(savedProject); // Save the project again
        } else {
            throw new IllegalArgumentException("Invalid project type: " + projectDTO.getProjectType());
        }

        return convertToDTO(savedProject);
    }

    // Method to update an existing project
    @Transactional
    public ProjectDTO updateProject(ProjectDTO projectDTO) {
        if (projectDTO.getId() == null) {
            throw new IllegalArgumentException("Project ID must not be null for update operation.");
        }

        Project project = projectRepository.findById(projectDTO.getId())
                .orElseThrow(() -> new IllegalArgumentException("Project not found with ID: " + projectDTO.getId()));

        // Ensure that the current user owns the project
        User currentUser = getCurrentUser();
        if (!project.getUser().getId().equals(currentUser.getId())) {
            throw new SecurityException("You do not have permission to update this project.");
        }

        project.setProjectName(projectDTO.getProjectName());
        project.setProjectDescription(projectDTO.getProjectDescription());
        // Note: We generally don't update the project type and associated company/group here.

        // Save the updated project
        Project updatedProject = projectRepository.save(project);
        return convertToDTO(updatedProject);
    }

    // Helper method to get the current authenticated user
    private User getCurrentUser() {
        Object principal = SecurityContextHolder.getContext().getAuthentication().getPrincipal();

        String username;

        if (principal instanceof UserDetails) {
            username = ((UserDetails) principal).getUsername();
        } else {
            username = principal.toString();
        }

        return userRepository.findByUsername(username)
                .orElseThrow(() -> new IllegalArgumentException("User not found with username: " + username));
    }

    @Transactional
    public void deleteProject(Long projectId) {
        Project project = projectRepository.findById(projectId)
                .orElseThrow(() -> new IllegalArgumentException("Project not found with ID: " + projectId));

        // Ensure that the current user owns the project
        User currentUser = getCurrentUser();
        if (!project.getUser().getId().equals(currentUser.getId())) {
            throw new SecurityException("You do not have permission to delete this project.");
        }

        // Delete associated entities manually if CascadeType.REMOVE is not set
        if ("company".equals(project.getProjectType())) {
            companyRepository.deleteById(project.getCompanyId());
        } else if ("companyGroup".equals(project.getProjectType())) {
            companyGroupRepository.deleteById(project.getCompanyGroupId());
        }

        // Finally, delete the project
        projectRepository.delete(project);
    }

    @Transactional
    public ProjectDTO copyProject(Long projectId) {
        // Fetch the original project
        Project originalProject = projectRepository.findById(projectId)
                .orElseThrow(() -> new IllegalArgumentException("Project not found with ID: " + projectId));

        // Ensure that the current user owns the original project
        User currentUser = getCurrentUser();

        // Create a new project instance and copy properties
        Project newProject = new Project();
        newProject.setProjectName(originalProject.getProjectName() + " - Copy");
        newProject.setProjectDescription(originalProject.getProjectDescription());
        newProject.setProjectType(originalProject.getProjectType());
        newProject.setUser(currentUser);

        // Save the new project
        Project savedProject = projectRepository.save(newProject);

        // Copy associated entities based on project type
        if ("company".equals(originalProject.getProjectType())) {
            // Copy the company
            Company originalCompany = companyRepository.findById(originalProject.getCompanyId())
                    .orElseThrow(() -> new IllegalArgumentException("Company not found"));

            Company newCompany = new Company();
            newCompany.setProject(savedProject);
            // Copy other properties as needed
            Company savedCompany = companyRepository.save(newCompany);

            // Update the new project with the new company ID
            savedProject.setCompanyId(savedCompany.getId());
        } else if ("companyGroup".equals(originalProject.getProjectType())) {
            // Copy the company group
            CompanyGroup originalGroup = companyGroupRepository.findById(originalProject.getCompanyGroupId())
                    .orElseThrow(() -> new IllegalArgumentException("CompanyGroup not found"));

            CompanyGroup newGroup = new CompanyGroup();
            newGroup.setProject(savedProject);
            // Copy other properties as needed
            CompanyGroup savedGroup = companyGroupRepository.save(newGroup);

            // Update the new project with the new company group ID
            savedProject.setCompanyGroupId(savedGroup.getId());
        }

        // Save the project again with updated company/group ID
        projectRepository.save(savedProject);

        return convertToDTO(savedProject);
    }
}
