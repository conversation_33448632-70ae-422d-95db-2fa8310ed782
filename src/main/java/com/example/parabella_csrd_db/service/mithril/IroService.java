package com.example.parabella_csrd_db.service.mithril;

import com.example.parabella_csrd_db.database.maindatabase.model.mithril.*;
import com.example.parabella_csrd_db.dto.mithril.IroDto;

import com.example.parabella_csrd_db.database.maindatabase.repository.mithril.CompanyRepository;
import com.example.parabella_csrd_db.database.maindatabase.repository.mithril.EsrsTopicSelectionRepository;
import com.example.parabella_csrd_db.database.maindatabase.repository.mithril.EsrsTopicsRepository;
import com.example.parabella_csrd_db.database.maindatabase.repository.mithril.IroRepository;
import com.example.parabella_csrd_db.database.maindatabase.repository.mithril.IroEvaluationRepository;
import com.example.parabella_csrd_db.database.maindatabase.repository.mithril.stakeholder_navigation.StakeholderRepository;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

@Service
@Transactional
public class IroService {

    private final IroRepository iroRepository;
    private final IroEvaluationRepository iroEvaluationRepository;
    private final EsrsTopicsRepository esrsTopicRepository;
    private final StakeholderRepository stakeholderRepository;
    private final CompanyRepository companyRepository;
    private final EsrsTopicSelectionService esrsTopicSelectionService;
    private final EsrsTopicSelectionRepository esrsTopicSelectionRepository;

    public IroService(
            IroRepository iroRepository,
            EsrsTopicsRepository esrsTopicRepository,
            StakeholderRepository stakeholderRepository,
            CompanyRepository companyRepository,
            EsrsTopicSelectionService esrsTopicSelectionService,
            EsrsTopicSelectionRepository esrsTopicSelectionRepository,
            IroEvaluationRepository iroEvaluationRepository
    ) {
        this.iroRepository = iroRepository;
        this.esrsTopicRepository = esrsTopicRepository;
        this.stakeholderRepository = stakeholderRepository;
        this.companyRepository = companyRepository;
        this.esrsTopicSelectionService = esrsTopicSelectionService;
        this.esrsTopicSelectionRepository = esrsTopicSelectionRepository;
        this.iroEvaluationRepository = iroEvaluationRepository;
    }

    public IroDto getIroById(Long id) {
        Iro iro = iroRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("Iro not found with id: " + id));
        return mapEntityToDto(iro);
    }

    public List<IroDto> getAllIros() {
        return iroRepository.findAll()
                .stream()
                .map(this::mapEntityToDto)
                .collect(Collectors.toList());
    }

    public void deleteIro(Long id) {
        Iro iro = iroRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("Iro not found with id: " + id));
        // If there's a cascade=REMOVE or you handle IroEvaluation separately, this is sufficient:
        iroRepository.delete(iro);
    }

    /**
     * Create a new IRO and an empty IroEvaluation.
     */
    public IroDto createIro(IroDto dto) {
        Iro iro = new Iro();
        mapDtoToEntity(dto, iro);

        Iro savedIro = iroRepository.save(iro);

        // Create an empty IroEvaluation linked to this Iro
        IroEvaluation eval = new IroEvaluation();
        eval.setIro(savedIro);
        eval.setCompany(savedIro.getCompany());
        // Set empty defaults (adjust as needed)
        eval.setActualPotentialImpact("");
        eval.setAffectedArea("");
        eval.setDescription("");
        eval.setEffect("");
        eval.setConnection("");
        eval.setScale(0);
        eval.setScope(0);
        eval.setIrreversibility(0);
        eval.setProbability(0.0);
        eval.setImpactMaterialityActualImpact("");
        eval.setImpactMaterialityPotentialImpact("");
        eval.setBasisOfAssessmentOfFinancialImpact("");
        eval.setFinancialMaterialityActualImpact(0);
        eval.setFinancialMaterialityPotentialImpact("");
        eval.setTimeSpan(0.0);
        eval.setResultMaterialityAssessment("");

        iroEvaluationRepository.save(eval);

        return mapEntityToDto(savedIro);
    }

    /**
     * Update existing IRO.
     */
    public IroDto updateIro(Long id, IroDto dto) {
        Iro iro = iroRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("Iro not found with id " + id));

        mapDtoToEntity(dto, iro);
        Iro updated = iroRepository.save(iro);

        return mapEntityToDto(updated);
    }

    public List<IroDto> getIrosByCompanyId(Long companyId) {
        return iroRepository.findAll().stream()
                .filter(iro -> iro.getCompany() != null && iro.getCompany().getId().equals(companyId))
                .map(this::mapEntityToDto)
                .collect(Collectors.toList());
    }

    /**
     * Convert incoming IroDto to Iro entity.
     *
     * The main fix: we do NOT simply do findByEsrsTopicId(...) because we need
     * the correct row from esrs_topic_selection that matches BOTH the company AND
     * the esrs_topic.
     */
    private void mapDtoToEntity(IroDto dto, Iro iro) {
        iro.setName(dto.getName());

        // 1) If there's a given esrsTopicId, we find the correct EsrsTopicSelection row
        //    for that combination of (companyId, esrsTopicId).
        if (dto.getEsrsTopicId() != null) {
            // Example: a custom method in your repository that finds the one matching row,
            // or you do something like:
            EsrsTopicSelection topicSelection = esrsTopicSelectionRepository
                    .findByCompanyIdAndEsrsTopicId(dto.getCompanyId(), dto.getEsrsTopicId())
                    .orElseThrow(() -> new RuntimeException(
                            "EsrsTopicSelection not found for companyId=" + dto.getCompanyId()
                                    + " and esrsTopicId=" + dto.getEsrsTopicId()
                    ));

            iro.setEsrsTopicSelection(topicSelection);
        } else {
            iro.setEsrsTopicSelection(null);
        }

        // 2) Stakeholder
        if (dto.getStakeholderId() != null) {
            Stakeholder stakeholder = stakeholderRepository.findById(dto.getStakeholderId())
                    .orElseThrow(() -> new RuntimeException("Stakeholder not found with id " + dto.getStakeholderId()));
            iro.setStakeholder(stakeholder);
        } else {
            iro.setStakeholder(null);
        }

        // 3) Company is required
        if (dto.getCompanyId() == null) {
            throw new RuntimeException("companyId is required and cannot be null");
        }
        Company company = companyRepository.findById(dto.getCompanyId())
                .orElseThrow(() -> new RuntimeException("Company not found with id " + dto.getCompanyId()));
        iro.setCompany(company);

        // 4) IRO type
        iro.setIroType(dto.getIroType());
        // If you also want to store subSubTopic, do so in the Iro entity.
        // e.g. iro.setSubSubTopic(dto.getSubSubTopic());  <-- if you have that field
    }

    /**
     * Convert from Iro entity back to IroDto.
     *
     * The main fix: do NOT do dto.setEsrsTopicId(iro.getEsrsTopicSelection().getId()).
     * Instead, do:
     *      .getEsrsTopicId()
     * which is the actual "esrs_topic_id" from the esrs_topic_selection row.
     */
    private IroDto mapEntityToDto(Iro iro) {
        IroDto dto = new IroDto();
        dto.setId(iro.getId());
        dto.setName(iro.getName());

        // Instead of using the EsrsTopicSelection's ID, we use getEsrsTopicId().
        if (iro.getEsrsTopicSelection() != null) {
            dto.setEsrsTopicId(iro.getEsrsTopicSelection().getEsrsTopic().getId());
        }

        if (iro.getStakeholder() != null) {
            dto.setStakeholderId(iro.getStakeholder().getId());
        }

        if (iro.getCompany() != null) {
            dto.setCompanyId(iro.getCompany().getId());
        }

        dto.setIroType(iro.getIroType());
        // If you store subSubTopic in Iro, also do dto.setSubSubTopic(iro.getSubSubTopic());
        return dto;
    }
}
