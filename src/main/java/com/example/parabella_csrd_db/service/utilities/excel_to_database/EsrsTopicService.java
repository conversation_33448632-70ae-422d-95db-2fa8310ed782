package com.example.parabella_csrd_db.service.utilities.excel_to_database;


import com.example.parabella_csrd_db.database.maindatabase.model.mithril.EsrsTopic;
import com.example.parabella_csrd_db.database.maindatabase.repository.mithril.EsrsTopicsRepository;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@Service
public class EsrsTopicService {


//    public void loadCSVData(InputStream csvInputStream) throws IOException, CsvValidationException {
//        CSVParser parser = new CSVParserBuilder()
//                .withSeparator(';') // Your CSV uses semicolons as separators
//                .build();
//
//        try (CSVReader csvReader = new CSVReaderBuilder(new InputStreamReader(csvInputStream))
//                .withCSVParser(parser)
//                .withSkipLines(1) // Skip header line
//                .build()) {
//
//            String[] line;
//            while ((line = csvReader.readNext()) != null) {
//                EsrsTopic esrsTopic = new EsrsTopic();
//                esrsTopic.setId(Long.parseLong(line[0]));
//                esrsTopic.setArea(line[1]);
//                esrsTopic.setEsrsCode(line[2]);
//                esrsTopic.setTopic(line[3]);
//                esrsTopic.setSubtopic(line[4]);
//                esrsTopic.setSubSubTopic(line.length > 5 ? line[5] : null);
//
//                esrsTopicRepository.save(esrsTopic);
//            }
//        }
//    }

    @Autowired
    private EsrsTopicsRepository esrsTopicRepository;

    public List<EsrsTopic> getAllSubtopics() {
        return esrsTopicRepository.findAll().stream()
                .filter(topic -> topic.getSubtopic() != null && !topic.getSubtopic().isEmpty())
                .collect(Collectors.toList());
    }

    public List<EsrsTopic> getSubtopicsByArea(String area) {
        return esrsTopicRepository.findByArea(area).stream()
                .filter(topic -> topic.getSubtopic() != null && !topic.getSubtopic().isEmpty())
                .collect(Collectors.toList());
    }

    public List<EsrsTopic> getSubtopicsByEsrsCode(String esrsCode) {
        return esrsTopicRepository.findByEsrsCode(esrsCode).stream()
                .filter(topic -> topic.getSubtopic() != null && !topic.getSubtopic().isEmpty())
                .collect(Collectors.toList());
    }
}