package com.example.parabella_csrd_db.service.elessar;// src/main/java/com/example/parabella_csrd_db/service/csrd/CsrdProjectService.java



import com.example.parabella_csrd_db.dto.elessar.CompanyInfoDTO;
import com.example.parabella_csrd_db.dto.elessar.CsrdProjectDTO;
import com.example.parabella_csrd_db.database.maindatabase.model.authentication.User;

import com.example.parabella_csrd_db.database.maindatabase.model.elessar.CsrdProject;
import com.example.parabella_csrd_db.database.maindatabase.model.elessar.landing.CompanyInfo;
import com.example.parabella_csrd_db.database.maindatabase.repository.authentication.UserRepository;

import com.example.parabella_csrd_db.database.maindatabase.repository.elessar.CsrdProjectRepository;
import com.example.parabella_csrd_db.utilities.mapper.CsrdDtoMapper;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.server.ResponseStatusException; // Import standard exception helper

import java.util.List;

@Service
@RequiredArgsConstructor
public class CsrdProjectService {

    private static final Logger log = LoggerFactory.getLogger(CsrdProjectService.class);

    private final CsrdProjectRepository csrdProjectRepository;
    private final UserRepository userRepository;
    private final CsrdDtoMapper csrdDtoMapper;

    @Transactional(readOnly = true)
    public List<CsrdProjectDTO> getUserCsrdProjects(Long userId) {
        log.info("Fetching CSRD projects for user ID: {}", userId);
        if (!userRepository.existsById(userId)) {
            log.warn("Attempted to fetch projects for non-existent user ID: {}", userId);
            // Throw standard exception - ControllerAdvice or Spring handles this
            throw new ResponseStatusException(HttpStatus.NOT_FOUND, "User not found with id: " + userId);
        }
        List<CsrdProject> projects = csrdProjectRepository.findByUserId(userId);
        log.debug("Found {} CSRD projects for user ID: {}", projects.size(), userId);
        return csrdDtoMapper.toCsrdProjectDTOList(projects);
    }

    @Transactional(readOnly = true)
    public CsrdProjectDTO getCsrdProjectById(Long projectId /*, Long userId - Optional: for security check */) {
        log.info("Fetching CSRD project with ID: {}", projectId);
        CsrdProject project = csrdProjectRepository.findByIdWithCompanyInfo(projectId)
                .orElseThrow(() -> {
                    log.warn("CSRD project not found with ID: {}", projectId);
                    return new ResponseStatusException(HttpStatus.NOT_FOUND, "CsrdProject not found with id: " + projectId);
                });

        // Optional Security Check:
        // if (!project.getUser().getId().equals(userId)) {
        //     throw new ResponseStatusException(HttpStatus.FORBIDDEN, "User does not have permission to access this project.");
        // }

        log.debug("Found CSRD project: {}", project.getProjectName());
        return csrdDtoMapper.toCsrdProjectDTO(project);
    }

    @Transactional
    public CsrdProjectDTO createCsrdProject(CsrdProjectDTO requestDTO) {
        log.info("Attempting to create CSRD project: {}", requestDTO.getProjectName());

        // --- Security Warning ---
        User user = userRepository.findById(requestDTO.getUserId())
                .orElseThrow(() -> {
                    log.error("User specified in create request not found for ID: {}", requestDTO.getUserId());
                    return new ResponseStatusException(HttpStatus.BAD_REQUEST, "User specified in request not found with id: " + requestDTO.getUserId());
                });
        // --- End Security Warning ---

        CsrdProject project = csrdDtoMapper.projectDtoToEntityForCreation(requestDTO, user);

        // --- *** START CHANGE: Auto-create empty CompanyInfo *** ---
        log.debug("Creating initial empty CompanyInfo for new project: {}", project.getProjectName());
        CompanyInfo initialCompanyInfo = new CompanyInfo();
        // You can set default values here if needed, e.g.,
        // initialCompanyInfo.setSize("Medium");
        project.setCompanyInfo(initialCompanyInfo); // Associate the empty CompanyInfo
        // --- *** END CHANGE *** ---


        CsrdProject savedProject = csrdProjectRepository.save(project); // CascadeType.ALL will save the associated CompanyInfo too
        log.info("Successfully created CSRD project with ID: {} and associated CompanyInfo ID: {}",
                savedProject.getId(),
                savedProject.getCompanyInfo() != null ? savedProject.getCompanyInfo().getId() : "null"); // Log company info ID

        // Return DTO - it should now include the (empty) company info if the mapper is correct
        return csrdDtoMapper.toCsrdProjectDTO(savedProject);
    }

    @Transactional
    public CompanyInfoDTO saveOrUpdateCompanyInfo(Long projectId, CompanyInfoDTO requestDTO /*, Long userId - Optional: for security check */) {
        log.info("Attempting to save/update CompanyInfo for CSRD project ID: {}", projectId);

        CsrdProject project = csrdProjectRepository.findById(projectId)
                .orElseThrow(() -> {
                    log.error("CSRD project not found for ID: {} while saving company info.", projectId);
                    return new ResponseStatusException(HttpStatus.NOT_FOUND, "CsrdProject not found with id: " + projectId);
                });

        // Optional Security Check:

        CompanyInfo companyInfo = project.getCompanyInfo();

        // This part should now always find an existing CompanyInfo after the change above
        if (companyInfo == null) {
            // This case should ideally not happen anymore for newly created projects,
            // but handle defensively for older projects or potential edge cases.
            log.warn("CompanyInfo was null for project ID: {}. Creating new one during update.", projectId);
            companyInfo = csrdDtoMapper.toCompanyInfoEntity(requestDTO);
            project.setCompanyInfo(companyInfo);
        } else {
            log.debug("Updating existing CompanyInfo for project ID: {}.", projectId);
            csrdDtoMapper.updateCompanyInfoFromDTO(requestDTO, companyInfo);
        }

        CsrdProject savedProject = csrdProjectRepository.save(project);
        log.info("Successfully saved/updated CompanyInfo for CSRD project ID: {}", projectId);

        if (savedProject.getCompanyInfo() == null) {
            log.error("CompanyInfo is null after saving project {} - potential issue!", projectId);
            throw new ResponseStatusException(HttpStatus.INTERNAL_SERVER_ERROR, "Failed to retrieve company info after save.");
        }
        return csrdDtoMapper.toCompanyInfoDTO(savedProject.getCompanyInfo());
    }

    @Transactional(readOnly = true)
    public CompanyInfoDTO getCompanyInfoByProjectId(Long projectId /*, Long userId - Optional: for security check */) {
        log.info("Fetching CompanyInfo for CSRD project ID: {}", projectId);
        CsrdProject project = csrdProjectRepository.findByIdWithCompanyInfo(projectId)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "CsrdProject not found with id: " + projectId));

        // Optional Security Check

        // This check should now pass for newly created projects as CompanyInfo is auto-created
        if (project.getCompanyInfo() == null) {
            log.error("CompanyInfo is unexpectedly null for CSRD project ID: {}", projectId);
            // Keep 404 for consistency if somehow it's still null (e.g., manual DB deletion)
            throw new ResponseStatusException(HttpStatus.NOT_FOUND, "CompanyInfo not found for CsrdProject with id: " + projectId);
        }

        log.debug("Found CompanyInfo for project ID: {}", projectId);
        return csrdDtoMapper.toCompanyInfoDTO(project.getCompanyInfo());
    }
}