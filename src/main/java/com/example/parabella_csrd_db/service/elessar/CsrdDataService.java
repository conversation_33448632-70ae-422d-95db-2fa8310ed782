package com.example.parabella_csrd_db.service.elessar;

import com.example.parabella_csrd_db.database.maindatabase.model.elessar.dashboard.CsrdSubtopic;
import com.example.parabella_csrd_db.database.maindatabase.model.elessar.dashboard.CsrdTopic;

import com.example.parabella_csrd_db.database.maindatabase.repository.elessar.CsrdTopicRepository;
import com.example.parabella_csrd_db.database.vectordatabase.model.EsrsDatapoint;
import com.example.parabella_csrd_db.database.vectordatabase.repository.EsrsDatapointRepository;

import com.example.parabella_csrd_db.dto.elessar.CsrdSubtopicDTO;
import com.example.parabella_csrd_db.dto.elessar.CsrdTopicDTO;
import com.example.parabella_csrd_db.dto.elessar.EsrsDatapointDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional; // Important for multi-DB if applicable

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class CsrdDataService {

    private final CsrdTopicRepository csrdTopicRepository;
    private final EsrsDatapointRepository esrsDatapointRepository; // From vectordb

    @Autowired
    public CsrdDataService(CsrdTopicRepository csrdTopicRepository, EsrsDatapointRepository esrsDatapointRepository) {
        this.csrdTopicRepository = csrdTopicRepository;
        this.esrsDatapointRepository = esrsDatapointRepository;
    }

    // Use @Transactional if you have operations spanning multiple datasources
    // and have configured distributed transactions (e.g. JTA)
    // Or, if they are separate transactions, ensure atomicity within each.
    // For read-only, @Transactional(readOnly = true) is good practice.
    @Transactional(readOnly = true, transactionManager = "csrdTransactionManager") // Or your main TM
    public List<CsrdTopicDTO> getAggregatedCsrdData() {
        // 1. Fetch all topics with their subtopics (from 'postgres' DB)
        List<CsrdTopic> topics = csrdTopicRepository.findAllWithSubtopics();


        System.out.println(topics);


        // 2. Fetch all relevant datapoints (from 'vectordb' DB)
        // Optimization: Fetch all once, then group in memory.
        // If EsrsDatapoint table is huge, this might need to be more targeted.
        List<EsrsDatapoint> allDatapoints = esrsDatapointRepository.findAll();

        // Group datapoints by their disclosureRequirement for easier lookup
        // This assumes EsrsDatapoint.disclosureRequirement can be directly mapped or
        // is a prefix to CsrdSubtopic.csrdSubtopicId
        Map<String, List<EsrsDatapoint>> datapointsByDisclosureReq = allDatapoints.stream()
                .filter(dp -> dp.getDisclosureRequirement() != null)
                .collect(Collectors.groupingBy(EsrsDatapoint::getDisclosureRequirement));

        // 3. Map to DTOs
        return topics.stream().map(topic -> {
            CsrdTopicDTO topicDto = new CsrdTopicDTO();
            topicDto.setId(topic.getId());
            topicDto.setCode(topic.getCode());
            topicDto.setName(topic.getName());

            List<CsrdSubtopicDTO> subtopicDtos = topic.getSubtopics().stream().map(subtopic -> {
                CsrdSubtopicDTO subtopicDto = new CsrdSubtopicDTO();
                subtopicDto.setId(subtopic.getId());
                subtopicDto.setCsrdSubtopicId(subtopic.getCsrdSubtopicId());
                subtopicDto.setCsrdSubtopicLabel(subtopic.getCsrdSubtopicLabel());

                // Find datapoints for this subtopic
                // This is the crucial linking logic. Adjust if your linking key is different.
                // This example assumes a direct match on CsrdSubtopic.csrdSubtopicId
              //  List<EsrsDatapoint> relevantDatapoints = datapointsByDisclosureReq
                 //       .getOrDefault(subtopic.getCsrdSubtopicId(), Collections.emptyList());

                // If the match is prefix-based (e.g., "E2-1" for subtopic, and datapoints "E2-1a", "E2-1b")
                 //You might need a more complex filtering strategy on `allDatapoints` here
                 List<EsrsDatapoint> relevantDatapoints = allDatapoints.stream()
                    .filter(dp -> dp.getDisclosureRequirement() != null &&
                                   dp.getSourceId().startsWith(subtopic.getCsrdSubtopicId()))
                    .collect(Collectors.toList());


                subtopicDto.setDatapoints(relevantDatapoints.stream()
                        .map(this::convertToDatapointDto)
                        .collect(Collectors.toList()));
                return subtopicDto;
            }).collect(Collectors.toList());

            topicDto.setSubtopics(subtopicDtos);
            return topicDto;
        }).collect(Collectors.toList());
    }

    private EsrsDatapointDTO convertToDatapointDto(EsrsDatapoint dp) {
        EsrsDatapointDTO dto = new EsrsDatapointDTO();
        dto.setId(dp.getId());
        dto.setSourceId(dp.getSourceId());
        dto.setSheetName(dp.getSheetName());
        dto.setEsrsStandard(dp.getEsrsStandard());
        dto.setDisclosureRequirement(dp.getDisclosureRequirement());
        dto.setParagraph(dp.getParagraph());
        dto.setRelatedAr(dp.getRelatedAr());
        dto.setDataPointName(dp.getDataPointName());
        dto.setDataType(dp.getDataType() != null ? dp.getDataType().toLowerCase() : "narrative"); // Normalize and default
        dto.setDataResponse(dp.getDataResponse());
        dto.setDataUnit(dp.getDataUnit());
        dto.setConditionalDp(dp.getConditionalDp());
        dto.setMetadata(dp.getMetadata());
        return dto;
    }
}