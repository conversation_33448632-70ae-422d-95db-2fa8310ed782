package com.example.parabella_csrd_db.service.elessar;// src/main/java/com/example/parabella_csrd_db/service/CsrdCoverageService.java


import com.example.parabella_csrd_db.database.vectordatabase.model.EsrsDatapoint;
import com.example.parabella_csrd_db.database.vectordatabase.model.ProcessedDocumentChunk;
import com.example.parabella_csrd_db.database.vectordatabase.repository.EsrsDatapointRepository;
import com.example.parabella_csrd_db.database.vectordatabase.repository.ProcessedDocumentChunkRepository;

import com.example.parabella_csrd_db.dto.elessar.coverage.CategoryCoverageStatsDto;
import com.example.parabella_csrd_db.dto.elessar.coverage.CoverageAnalysisResultDto;
import com.example.parabella_csrd_db.dto.elessar.coverage.CoverageDatapointDto;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional; // Important for read-only operations

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
public class CsrdCoverageService {

    private final EsrsDatapointRepository esrsDatapointRepository;
    private final ProcessedDocumentChunkRepository processedDocumentChunkRepository;

    @Transactional(readOnly = true) // Good practice for read operations
    public CoverageAnalysisResultDto analyzeCoverage(Long projectId) {
        log.info("Starting coverage analysis for project ID: {}", projectId);

        // 1. Get all potential ESRS data points (the baseline)
        List<EsrsDatapoint> allEsrsDatapoints = esrsDatapointRepository.findAll();
        if (allEsrsDatapoints.isEmpty()) {
            log.warn("No ESRS datapoints found in the database. Cannot perform analysis.");
            // Return an empty or minimal result
            return CoverageAnalysisResultDto.builder()
                    .projectId(projectId)
                    .totalEsrsDatapoints(0)
                    .totalCoveredDatapoints(0)
                    .totalUncoveredDatapoints(0)
                    .overallCoveragePercentage(0.0)
                    .coverageByStandard(Collections.emptyMap())
                    .datapoints(Collections.emptyList())
                    .build();
        }
        log.debug("Found {} total ESRS datapoints.", allEsrsDatapoints.size());


        // 2. Get all *distinct* source_ids that *have* been extracted for this project
        // Set<String> coveredSourceIds = processedDocumentChunkRepository.findDistinctSourceIdsByProjectId(projectId);
        // log.debug("Found {} distinct covered source_ids for project {}.", coveredSourceIds.size(), projectId);

        // --- Alternative to Step 2 (fetch full chunks if details like doc name/chunk ID are needed) ---
        List<ProcessedDocumentChunk> projectChunks = processedDocumentChunkRepository.findByProjectIdAndSourceIdIsNotNull(projectId);
        log.debug("Found {} processed chunks with source_id for project {}.", projectChunks.size(), projectId);

        // Group chunks by source_id for efficient lookup
        Map<String, List<ProcessedDocumentChunk>> chunksBySourceId = projectChunks.stream()
                .filter(chunk -> chunk.getSourceId() != null) // Should be redundant due to query, but safe
                .collect(Collectors.groupingBy(ProcessedDocumentChunk::getSourceId));
        log.debug("Grouped chunks into {} unique source_ids.", chunksBySourceId.size());
        // -----------------------------------------------------------------------------------------


        List<CoverageDatapointDto> coverageDatapointDtos = new ArrayList<>();
        long coveredCount = 0;

        // 3. Iterate through each ESRS datapoint and check coverage
        for (EsrsDatapoint esrsDp : allEsrsDatapoints) {
            String sourceId = esrsDp.getSourceId();
            boolean isCovered = sourceId != null && chunksBySourceId.containsKey(sourceId); // Check if the map contains the source_id
            // boolean isCovered = sourceId != null && coveredSourceIds.contains(sourceId); // Use this if using findDistinctSourceIdsByProjectId

            List<Long> coveringChunkIds = Collections.emptyList();
            List<String> coveringDocNames = Collections.emptyList();

            if (isCovered) {
                coveredCount++;
                // If using the full chunk fetch approach, get details:
                List<ProcessedDocumentChunk> coveringChunks = chunksBySourceId.getOrDefault(sourceId, Collections.emptyList());
                coveringChunkIds = coveringChunks.stream().map(ProcessedDocumentChunk::getId).distinct().toList();
                coveringDocNames = coveringChunks.stream().map(ProcessedDocumentChunk::getDocumentName).distinct().toList();
            }

            coverageDatapointDtos.add(CoverageDatapointDto.builder()
                    .esrsDatapointId(esrsDp.getId())
                    .sourceId(esrsDp.getSourceId())
                    .esrsStandard(esrsDp.getEsrsStandard())
                    .disclosureRequirement(esrsDp.getDisclosureRequirement())
                    .dataPointName(esrsDp.getDataPointName())
                    .covered(isCovered)
                    .coveringChunkIds(coveringChunkIds) // Add details if fetched
                    .coveringDocumentNames(coveringDocNames) // Add details if fetched
                    .build());
        }

        // 4. Calculate overall and per-standard statistics
        long totalPoints = allEsrsDatapoints.size();
        double overallPercentage = (totalPoints == 0) ? 0.0 : ((double) coveredCount / totalPoints) * 100.0;

        Map<String, CategoryCoverageStatsDto> coverageByStandard = coverageDatapointDtos.stream()
                .filter(dp -> dp.getEsrsStandard() != null)
                .collect(Collectors.groupingBy(
                        CoverageDatapointDto::getEsrsStandard,
                        Collectors.collectingAndThen(Collectors.toList(), list -> {
                            long total = list.size();
                            long covered = list.stream().filter(CoverageDatapointDto::isCovered).count();
                            double percentage = (total == 0) ? 0.0 : ((double) covered / total) * 100.0;
                            return new CategoryCoverageStatsDto(total, covered, percentage);
                        })
                ));

        log.info("Coverage analysis complete for project {}. Overall: {}% ({} / {})",
                projectId, String.format("%.2f", overallPercentage), coveredCount, totalPoints);

        return CoverageAnalysisResultDto.builder()
                .projectId(projectId)
                .totalEsrsDatapoints(totalPoints)
                .totalCoveredDatapoints(coveredCount)
                .totalUncoveredDatapoints(totalPoints - coveredCount)
                .overallCoveragePercentage(overallPercentage)
                .coverageByStandard(coverageByStandard)
                .datapoints(coverageDatapointDtos) // Include the detailed list
                .build();
    }
}