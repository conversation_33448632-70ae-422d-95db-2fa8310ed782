package com.example.parabella_csrd_db.service.elessar;// src/main/java/com/example/parabella_csrd_db/service/vector/ProcessedDocumentChunkService.java
// (or wherever you named it, e.g., service.elessar)


import com.example.parabella_csrd_db.database.vectordatabase.model.ProcessedDocumentChunk;
import com.example.parabella_csrd_db.database.vectordatabase.repository.ProcessedDocumentChunkRepository;

import com.example.parabella_csrd_db.dto.elessar.ProcessedDocumentChunkDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.factory.annotation.Qualifier; // If using qualifiers
import org.springframework.transaction.PlatformTransactionManager; // If using qualifiers

import java.util.List;
import java.util.stream.Collectors;

@Service
public class ProcessedDocumentChunkService {

    private final ProcessedDocumentChunkRepository chunkRepository;

    @Autowired
    public ProcessedDocumentChunkService(ProcessedDocumentChunkRepository chunkRepository) {
        this.chunkRepository = chunkRepository;
    }

    @Transactional(readOnly = true, transactionManager = "vectordbTransactionManager") // Specify correct TM
    public List<ProcessedDocumentChunkDTO> findRelevantChunks(Long projectId, String disclosureRequirement, String sourceId) {
        List<ProcessedDocumentChunk> chunks = chunkRepository.findRelevantChunks(projectId, disclosureRequirement, sourceId);
        return chunks.stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    private ProcessedDocumentChunkDTO convertToDto(ProcessedDocumentChunk chunk) {
        if (chunk == null) return null;
        return ProcessedDocumentChunkDTO.builder()
                .id(chunk.getId())
                .projectId(chunk.getProjectId())
                .uploadedByUserId(chunk.getUploadedByUserId())
                .documentName(chunk.getDocumentName())
                .chunkIndex(chunk.getChunkIndex())
                .chunkText(chunk.getChunkText())
                .chunkSummary(chunk.getChunkSummary())
                .disclosureRequirement(chunk.getDisclosureRequirement())
                .sourceId(chunk.getSourceId())
                .dataSourceIds(chunk.getDataSourceIds()) // This will now correctly be a List<String>
                .matchScore(chunk.getMatchScore())
                .metadata(chunk.getMetadata())
                .createdAt(chunk.getCreatedAt())
                .build();
    }
}