package com.example.parabella_csrd_db.service.elessar;

import com.example.parabella_csrd_db.database.maindatabase.model.elessar.dashboard.*;
import com.example.parabella_csrd_db.database.maindatabase.repository.elessar.*;
import com.example.parabella_csrd_db.dto.elessar.*;

import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Service
public class CsrdLoaderService {

    private final CsrdTopicRepository topicRepository;
    private final CsrdSubtopicRepository subtopicRepository;
    private final CsrdSubtopicSectionRepository sectionRepository;
    private final CsrdFieldRepository fieldRepository;
    private final CsrdFieldOptionRepository optionRepository;
    private final CsrdConditionalFieldRepository conditionalFieldRepository;

    public CsrdLoaderService(CsrdTopicRepository topicRepository,
                             CsrdSubtopicRepository subtopicRepository,
                             CsrdSubtopicSectionRepository sectionRepository,
                             CsrdFieldRepository fieldRepository,
                             CsrdFieldOptionRepository optionRepository,
                             CsrdConditionalFieldRepository conditionalFieldRepository) {
        this.topicRepository = topicRepository;
        this.subtopicRepository = subtopicRepository;
        this.sectionRepository = sectionRepository;
        this.fieldRepository = fieldRepository;
        this.optionRepository = optionRepository;
        this.conditionalFieldRepository = conditionalFieldRepository;
    }

    /**
     * Loads an entire JSON structure that has 'topics' array (ESRS_E2, ESRS_E3, etc.).
     */
    public void loadTopicsRoot(CsrdTopicsRootDTO rootDTO) {
        if (rootDTO.getTopics() == null) return;

        for (CsrdTopicDTO topicDTO : rootDTO.getTopics()) {
            // 1) Create CsrdTopic
            CsrdTopic topicEntity = new CsrdTopic();
            topicEntity.setCode(topicDTO.getCode());
            topicEntity.setName(topicDTO.getName());
            topicEntity = topicRepository.save(topicEntity);

            // 2) For each Subtopic
            List<CsrdSubtopicDTO> subtopics = topicDTO.getSubtopics();
            if (subtopics == null) continue;

            for (CsrdSubtopicDTO subtopicDTO : subtopics) {
                CsrdSubtopic subtopicEntity = new CsrdSubtopic();
                subtopicEntity.setTopic(topicEntity);
                subtopicEntity.setCsrdSubtopicId(subtopicDTO.getCsrdSubtopicId());
                subtopicEntity.setCsrdSubtopicLabel(subtopicDTO.getCsrdSubtopicLabel());
                subtopicEntity = subtopicRepository.save(subtopicEntity);

                // 3) For each Section
                List<CsrdSubtopicSectionDTO> sections = null;
                if (sections == null) continue;

                for (CsrdSubtopicSectionDTO sectionDTO : sections) {
                    CsrdSubtopicSection sectionEntity = new CsrdSubtopicSection();
                    sectionEntity.setSubtopic(subtopicEntity);
                    sectionEntity.setSectionId(sectionDTO.getSectionId());
                    sectionEntity.setSectionTitle(sectionDTO.getSectionTitle());
                    sectionEntity = sectionRepository.save(sectionEntity);

                    // 4) For each Field
                    List<CsrdFieldDTO> fields = sectionDTO.getFields();
                    if (fields == null) continue;

                    for (CsrdFieldDTO fieldDTO : fields) {
                        createFieldAndConditionalFields(fieldDTO, sectionEntity, null, null);
                    }
                }
            }
        }
    }

    /**
     * Creates a field, plus options, plus recursive child fields (conditionalFields).
     * Also handles "subSection" by building sub-fields within that sub-section.
     */
    private CsrdField createFieldAndConditionalFields(
            CsrdFieldDTO fieldDTO,
            CsrdSubtopicSection sectionEntity,
            CsrdField parentField,
            String parentOptionValue
    ) {
        // 1) Create the main field
        CsrdField fieldEntity = new CsrdField();
        fieldEntity.setSection(sectionEntity);
        fieldEntity.setFieldType(fieldDTO.getFieldType());
        fieldEntity.setLabel(fieldDTO.getLabel());
        fieldEntity = fieldRepository.save(fieldEntity);

        // 2) Insert field options if any
        if (fieldDTO.getOptions() != null) {
            for (String opt : fieldDTO.getOptions()) {
                CsrdFieldOption option = new CsrdFieldOption();
                option.setField(fieldEntity);
                option.setOptionValue(opt);
                optionRepository.save(option);
            }
        }

        // 3) If this field is a child of a parent's specific option
        if (parentField != null && parentOptionValue != null) {
            CsrdConditionalField cf = new CsrdConditionalField();
            cf.setParentField(parentField);
            cf.setParentOptionValue(parentOptionValue);
            cf.setChildField(fieldEntity);
            conditionalFieldRepository.save(cf);
        }

        // 4) Recursively create child fields for conditional logic
        if (fieldDTO.getConditionalFields() != null) {
            for (Map.Entry<String, List<CsrdFieldDTO>> entry : fieldDTO.getConditionalFields().entrySet()) {
                String optionValue = entry.getKey();     // e.g. "Yes", "No"
                List<CsrdFieldDTO> childFieldList = entry.getValue();
                for (CsrdFieldDTO child : childFieldList) {
                    createFieldAndConditionalFields(child, sectionEntity, fieldEntity, optionValue);
                }
            }
        }

        // 5) If "subSection", handle subSections array
        if ("subSection".equalsIgnoreCase(fieldDTO.getFieldType()) && fieldDTO.getSubSections() != null) {
            // Each subSection entry
            for (CsrdSubSectionDTO sSec : fieldDTO.getSubSections()) {
                // Create a "subSectionTitle" field to hold the sSec.getTitle()
                CsrdField subSectionTitleField = new CsrdField();
                subSectionTitleField.setSection(sectionEntity);
                subSectionTitleField.setFieldType("subSectionTitle");
                subSectionTitleField.setLabel(sSec.getTitle());
                subSectionTitleField = fieldRepository.save(subSectionTitleField);

                // Link it conditionally to the parent "subSection" field if desired
                CsrdConditionalField cField = new CsrdConditionalField();
                cField.setParentField(fieldEntity);
                cField.setParentOptionValue("N/A"); // sentinel
                cField.setChildField(subSectionTitleField);
                conditionalFieldRepository.save(cField);

                // Now create fields inside this sub-section
                if (sSec.getFields() != null) {
                    for (CsrdFieldDTO subSecField : sSec.getFields()) {
                        createFieldAndConditionalFields(subSecField, sectionEntity, subSectionTitleField, "N/A");
                    }
                }
            }
        }

        return fieldEntity;
    }
}
