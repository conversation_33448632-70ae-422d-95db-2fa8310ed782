package com.example.parabella_csrd_db.controller.mithril;

import com.example.parabella_csrd_db.dto.mithril.ProjectDTO;
import com.example.parabella_csrd_db.service.mithril.ProjectService;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser; // For security context
import org.springframework.test.web.servlet.MockMvc;


import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

import static org.hamcrest.Matchers.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.*;
import static org.springframework.security.test.web.servlet.request.SecurityMockMvcRequestPostProcessors.csrf; // Import csrf
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@WebMvcTest(ProjectController.class)
// Add @WithMockUser to simulate an authenticated user for endpoints requiring it
// This avoids needing the full SecurityContext setup from the service tests here
@WithMockUser(username = "testuser")
class ProjectControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean // Use MockBean for service dependency in controller tests
    private ProjectService projectService;

    @Autowired
    private ObjectMapper objectMapper; // For converting objects to JSON

    private ProjectDTO projectDTO1;
    private ProjectDTO projectDTO2;

    @BeforeEach
    void setUp() {
        projectDTO1 = new ProjectDTO();
        projectDTO1.setId(1L);
        projectDTO1.setProjectName("Project One");
        projectDTO1.setProjectDescription("Desc One");
        projectDTO1.setProjectType("company");
        projectDTO1.setUserId(100L);
        projectDTO1.setCompanyId(10L);
        projectDTO1.setCreatedAt(LocalDateTime.now());

        projectDTO2 = new ProjectDTO();
        projectDTO2.setId(2L);
        projectDTO2.setProjectName("Project Two");
        projectDTO2.setProjectDescription("Desc Two");
        projectDTO2.setProjectType("companyGroup");
        projectDTO2.setUserId(100L);
        projectDTO2.setCompanyGroupId(20L);
        projectDTO2.setCreatedAt(LocalDateTime.now());
    }

    @Test
    void getProjectsByUser_shouldReturnListOfProjects() throws Exception {
        // Arrange
        Long userId = 100L;
        when(projectService.findProjectsByUserId(userId)).thenReturn(List.of(projectDTO1, projectDTO2));

        // Act & Assert
        mockMvc.perform(get("/api/projects/user/{userId}", userId)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$", hasSize(2)))
                .andExpect(jsonPath("$[0].id", is(1)))
                .andExpect(jsonPath("$[0].projectName", is("Project One")))
                .andExpect(jsonPath("$[1].id", is(2)))
                .andExpect(jsonPath("$[1].projectName", is("Project Two")));

        verify(projectService, times(1)).findProjectsByUserId(userId);
    }

    @Test
    void getProjectById_whenProjectExists_shouldReturnProject() throws Exception {
        // Arrange
        Long projectId = 1L;
        when(projectService.findProjectDTOById(projectId)).thenReturn(Optional.of(projectDTO1));

        // Act & Assert
        mockMvc.perform(get("/api/projects/{projectId}", projectId)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.id", is(1)))
                .andExpect(jsonPath("$.projectName", is("Project One")));

        verify(projectService, times(1)).findProjectDTOById(projectId);
    }

    @Test
    void getProjectById_whenProjectNotFound_shouldReturnNotFound() throws Exception {
        // Arrange
        Long projectId = 99L;
        when(projectService.findProjectDTOById(projectId)).thenReturn(Optional.empty());

        // Act & Assert
        mockMvc.perform(get("/api/projects/{projectId}", projectId)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isNotFound());

        verify(projectService, times(1)).findProjectDTOById(projectId);
    }

    @Test
    void createProject_shouldCreateAndReturnProject() throws Exception {
        // Arrange
        // Input DTO (ID will be ignored by service create method)
        ProjectDTO inputDTO = new ProjectDTO();
        inputDTO.setProjectName("New Project");
        inputDTO.setProjectDescription("New Desc");
        inputDTO.setProjectType("company");

        // Output DTO (returned by service with ID assigned)
        ProjectDTO outputDTO = new ProjectDTO();
        outputDTO.setId(5L);
        outputDTO.setProjectName("New Project");
        outputDTO.setProjectDescription("New Desc");
        outputDTO.setProjectType("company");
        outputDTO.setUserId(100L); // Assuming user id is set by service
        outputDTO.setCompanyId(50L); // Assuming company id is set
        outputDTO.setCreatedAt(LocalDateTime.now());


        when(projectService.createProject(any(ProjectDTO.class))).thenReturn(outputDTO);

        // Act & Assert
        mockMvc.perform(post("/api/projects/createProject")
                        .with(csrf()) // Add CSRF token for POST/PUT/DELETE if Spring Security CSRF is enabled
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(inputDTO)))
                .andExpect(status().isCreated())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.id", is(5)))
                .andExpect(jsonPath("$.projectName", is("New Project")))
                .andExpect(jsonPath("$.companyId", is(50)));

        verify(projectService, times(1)).createProject(any(ProjectDTO.class));
    }

    @Test
    void updateProject_whenProjectExists_shouldUpdateAndReturnProject() throws Exception {
        // Arrange
        Long projectId = 1L;
        ProjectDTO inputDTO = projectDTO1; // Use existing DTO as input
        inputDTO.setProjectName("Updated Name"); // Modify a field

        ProjectDTO outputDTO = new ProjectDTO(); // DTO returned by service
        outputDTO.setId(projectId);
        outputDTO.setProjectName("Updated Name");
        outputDTO.setProjectDescription(inputDTO.getProjectDescription());
        outputDTO.setProjectType(inputDTO.getProjectType());
        outputDTO.setUserId(inputDTO.getUserId());
        outputDTO.setCompanyId(inputDTO.getCompanyId());
        outputDTO.setCreatedAt(inputDTO.getCreatedAt());

        when(projectService.updateProject(any(ProjectDTO.class))).thenReturn(outputDTO);

        // Act & Assert
        mockMvc.perform(put("/api/projects/{projectId}", projectId)
                        .with(csrf()) // Add CSRF token
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(inputDTO)))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.id", is(1)))
                .andExpect(jsonPath("$.projectName", is("Updated Name")));


        // Verify the DTO passed to the service had the correct ID
        verify(projectService, times(1)).updateProject(argThat(dto -> dto.getId().equals(projectId) && dto.getProjectName().equals("Updated Name")));
    }

    @Test
    void updateProject_whenProjectNotFound_shouldReturnError() throws Exception {
        // Arrange
        Long projectId = 99L;
        ProjectDTO inputDTO = new ProjectDTO();
        inputDTO.setId(projectId); // ID is set in controller before calling service
        inputDTO.setProjectName("Update Fail");

        // Simulate service throwing exception when project not found
        when(projectService.updateProject(any(ProjectDTO.class)))
                .thenThrow(new IllegalArgumentException("Project not found with ID: " + projectId));

        // Act & Assert
        mockMvc.perform(put("/api/projects/{projectId}", projectId)
                        .with(csrf())
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(inputDTO)))
                // Depending on ExceptionHandler setup, could be 404 or 400
                // Let's assume a generic handler might return 500 or a specific one 404/400
                .andExpect(status().isInternalServerError()); // Adjust if you have specific exception handling

        verify(projectService, times(1)).updateProject(any(ProjectDTO.class));
    }


    @Test
    void deleteProject_whenProjectExists_shouldReturnNoContent() throws Exception {
        // Arrange
        Long projectId = 1L;
        doNothing().when(projectService).deleteProject(projectId);

        // Act & Assert
        mockMvc.perform(delete("/api/projects/{projectId}", projectId)
                        .with(csrf())) // Add CSRF token
                .andExpect(status().isNoContent());

        verify(projectService, times(1)).deleteProject(projectId);
    }

    @Test
    void deleteProject_whenProjectNotFound_shouldReturnError() throws Exception {
        // Arrange
        Long projectId = 99L;
        // Simulate service throwing exception
        doThrow(new IllegalArgumentException("Project not found with ID: " + projectId))
                .when(projectService).deleteProject(projectId);

        // Act & Assert
        mockMvc.perform(delete("/api/projects/{projectId}", projectId)
                        .with(csrf()))
                .andExpect(status().isInternalServerError()); // Adjust based on exception handling

        verify(projectService, times(1)).deleteProject(projectId);
    }


    @Test
    void copyProject_shouldCreateCopyAndReturnProject() throws Exception {
        // Arrange
        Long originalProjectId = 1L;

        ProjectDTO copiedProjectDTO = new ProjectDTO();
        copiedProjectDTO.setId(6L); // New ID for the copy
        copiedProjectDTO.setProjectName(projectDTO1.getProjectName() + " - Copy");
        copiedProjectDTO.setProjectDescription(projectDTO1.getProjectDescription());
        copiedProjectDTO.setProjectType(projectDTO1.getProjectType());
        copiedProjectDTO.setUserId(projectDTO1.getUserId());
        copiedProjectDTO.setCompanyId(60L); // New company ID for the copy
        copiedProjectDTO.setCreatedAt(LocalDateTime.now());


        when(projectService.copyProject(originalProjectId)).thenReturn(copiedProjectDTO);

        // Act & Assert
        mockMvc.perform(post("/api/projects/copy/{projectId}", originalProjectId)
                        .with(csrf()) // Add CSRF token for POST
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isCreated())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.id", is(6)))
                .andExpect(jsonPath("$.projectName", is(projectDTO1.getProjectName() + " - Copy")))
                .andExpect(jsonPath("$.companyId", is(60)));

        verify(projectService, times(1)).copyProject(originalProjectId);
    }

    @Test
    void copyProject_whenOriginalNotFound_shouldReturnError() throws Exception {
        // Arrange
        Long originalProjectId = 99L;
        when(projectService.copyProject(originalProjectId))
                .thenThrow(new IllegalArgumentException("Project not found with ID: " + originalProjectId));


        // Act & Assert
        mockMvc.perform(post("/api/projects/copy/{projectId}", originalProjectId)
                        .with(csrf())
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isInternalServerError()); // Adjust based on exception handling

        verify(projectService, times(1)).copyProject(originalProjectId);
    }
}