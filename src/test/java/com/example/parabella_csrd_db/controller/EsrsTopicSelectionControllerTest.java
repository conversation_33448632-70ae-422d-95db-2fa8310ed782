//package com.example.parabella_csrd_db.controller;
//
//import com.example.parabella_csrd_db.dto.mithril.EsrsTopicSelectionDTO;
//import com.example.parabella_csrd_db.service.mithril.EsrsTopicSelectionService;
//import com.fasterxml.jackson.databind.ObjectMapper;
//import org.junit.jupiter.api.BeforeEach;
//import org.junit.jupiter.api.Test;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
//import org.springframework.boot.test.context.SpringBootTest;
//import org.springframework.boot.test.mock.mockito.MockBean;
//import org.springframework.http.MediaType;
//import org.springframework.security.test.context.support.WithMockUser;
//import org.springframework.test.web.servlet.MockMvc;
//
//import java.util.List;
//
//import static org.mockito.ArgumentMatchers.any;
//import static org.mockito.Mockito.when;
//import static org.springframework.security.test.web.servlet.request.SecurityMockMvcRequestPostProcessors.csrf;
//import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
//import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;
//
//@SpringBootTest
//@AutoConfigureMockMvc
//class EsrsTopicSelectionControllerTest {
//
//    @Autowired
//    private MockMvc mockMvc;
//
//    @MockBean
//    private EsrsTopicSelectionService esrsTopicSelectionService;
//
//    @Autowired
//    private ObjectMapper objectMapper;
//
//    private EsrsTopicSelectionDTO testDTO;
//
//    @BeforeEach
//    void setUp() {
//        testDTO = new EsrsTopicSelectionDTO();
//        testDTO.setId(1L);
//        testDTO.setCompanyId(1L);
//        testDTO.setEsrsTopicId(1L);
//        testDTO.setStakeholderId(1L);
//        testDTO.setRelevant(true);
//    }
//
//    @Test
//    @WithMockUser
//    void createEsrsTopicSelection_WithValidData_ShouldReturnCreated() throws Exception {
//        when(esrsTopicSelectionService.createEsrsTopicSelection(any(EsrsTopicSelectionDTO.class)))
//                .thenReturn(testDTO);
//
//        mockMvc.perform(post("/api/esrs-topic-selections")
//                        .with(csrf())
//                        .contentType(MediaType.APPLICATION_JSON)
//                        .content(objectMapper.writeValueAsString(testDTO)))
//                .andExpect(status().isOk())
//                .andExpect(jsonPath("$.relevant").value(true));
//    }
//
//    @Test
//    @WithMockUser
//    void getSelectionsByStakeholderId_ShouldReturnList() throws Exception {
//        when(esrsTopicSelectionService.getSelectionsByStakeholderId(1L))
//                .thenReturn(List.of(testDTO));
//
//        mockMvc.perform(get("/api/esrs-topic-selections/stakeholder/1")
//                        .with(csrf()))
//                .andExpect(status().isOk())
//                .andExpect(jsonPath("$[0].stakeholderId").value(1L));
//    }
//
//    @Test
//    @WithMockUser
//    void getEsrsTopicSelectionsByCompanyId_ShouldReturnList() throws Exception {
//        when(esrsTopicSelectionService.getEsrsTopicSelectionsByCompanyId(1L))
//                .thenReturn(List.of(testDTO));
//
//        mockMvc.perform(get("/api/esrs-topic-selections/company/1")
//                        .with(csrf()))
//                .andExpect(status().isOk())
//                .andExpect(jsonPath("$[0].companyId").value(1L));
//    }
//
//    @Test
//    void accessProtectedEndpoint_WithoutAuth_ShouldReturnUnauthorized() throws Exception {
//        mockMvc.perform(get("/api/esrs-topic-selections/1"))
//                .andExpect(status().isUnauthorized());
//    }
//}