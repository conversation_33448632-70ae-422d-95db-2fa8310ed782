package com.example.parabella_csrd_db.controller.authentication;

import com.example.parabella_csrd_db.database.maindatabase.model.authentication.Permission;
import com.example.parabella_csrd_db.database.maindatabase.model.authentication.Role;
import com.example.parabella_csrd_db.database.maindatabase.model.authentication.User;
import com.example.parabella_csrd_db.database.maindatabase.repository.authentication.RoleRepository;
import com.example.parabella_csrd_db.database.maindatabase.repository.authentication.UserRepository;
import com.example.parabella_csrd_db.database.maindatabase.repository.authentication.VerificationTokenRepository;
import com.example.parabella_csrd_db.dto.authentication.request.LoginRequest;
import com.example.parabella_csrd_db.dto.authentication.request.SignUpRequest;
import com.example.parabella_csrd_db.security.jwt.JwtUtils;
import com.example.parabella_csrd_db.security.services.UserDetailsImpl;
import com.example.parabella_csrd_db.security.utils.ForgotPasswordService;
import com.example.parabella_csrd_db.security.utils.TotpUtils;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.MockedStatic;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.ResultActions;

import java.util.*;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.Mockito.*;
// ADD THIS IMPORT
import static org.springframework.security.test.web.servlet.request.SecurityMockMvcRequestPostProcessors.csrf;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@WebMvcTest(AuthController.class)
class AuthControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

    @MockBean
    private AuthenticationManager authenticationManager;

    @MockBean
    private UserRepository userRepository;

    @MockBean
    private VerificationTokenRepository tokenRepository;

    @MockBean
    private RoleRepository roleRepository;

    @MockBean
    private PasswordEncoder passwordEncoder;

    @MockBean
    private JwtUtils jwtUtils;

    @MockBean
    private ForgotPasswordService forgotPasswordService;

    private MockedStatic<TotpUtils> totpUtilsMockedStatic;

    @BeforeEach
    void setUp() {
        totpUtilsMockedStatic = mockStatic(TotpUtils.class);
    }

    @AfterEach
    void tearDown() {
        totpUtilsMockedStatic.close();
    }

    private Authentication createMockAuthentication() {
        UserDetailsImpl userDetails = new UserDetailsImpl(1L, "testuser", "<EMAIL>", "password", Collections.emptyList(), "Admin");
        return new UsernamePasswordAuthenticationToken(userDetails, null, userDetails.getAuthorities());
    }

    @Test
    void whenSignIn_withValidCredentialsAndTotp_thenReturnsJwtResponse() throws Exception {
        // Arrange omitted for brevity...
        LoginRequest loginRequest = new LoginRequest("testuser", "password", 123456);
        // ... same arrangement as before
        Role userRole = new Role();
        userRole.setName("ROLE_USER");
        Permission viewDashboard = new Permission();
        viewDashboard.setFunctionName("VIEW_DASHBOARD");
        userRole.setPermissions(Set.of(viewDashboard));
        User mockUser = new User("testuser", "<EMAIL>", "encodedPassword");
        mockUser.setId(1L);
        mockUser.setTotpSecret("a-valid-secret");
        mockUser.setRole(userRole);
        Authentication mockAuthentication = createMockAuthentication();
        when(authenticationManager.authenticate(any())).thenReturn(mockAuthentication);
        when(userRepository.findByUsername("testuser")).thenReturn(Optional.of(mockUser));
        totpUtilsMockedStatic.when(() -> TotpUtils.validateCode("a-valid-secret", 123456)).thenReturn(true);
        when(jwtUtils.generateJwtToken(mockAuthentication)).thenReturn("dummy.jwt.token");


        // Act & Assert
        mockMvc.perform(post("/api/auth/signin")
                        .with(csrf()) // <-- FIX
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(loginRequest)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.accessToken").value("dummy.jwt.token"))
                .andExpect(jsonPath("$.username").value("testuser"))
                .andExpect(jsonPath("$.permissions[0]").value("VIEW_DASHBOARD"));
    }

    @Test
    void whenSignIn_withInvalidCredentials_thenThrowsUnauthorized() throws Exception {
        // Arrange
        LoginRequest loginRequest = new LoginRequest("testuser", "wrongpassword", 0);
        when(authenticationManager.authenticate(any(UsernamePasswordAuthenticationToken.class)))
                .thenThrow(new BadCredentialsException("Bad credentials"));

        // Act & Assert
        mockMvc.perform(post("/api/auth/signin")
                        .with(csrf()) // <-- FIX
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(loginRequest)))
                .andExpect(status().isUnauthorized());
    }

    @Test
    void whenSignIn_forFirstTime_thenRequires2faSetup() throws Exception {
        // Arrange
        LoginRequest loginRequest = new LoginRequest("newuser", "password", 0);
        User mockUser = new User("newuser", "<EMAIL>", "encodedPassword");
        mockUser.setTotpSecret(null);
        Authentication mockAuthentication = createMockAuthentication();
        when(authenticationManager.authenticate(any())).thenReturn(mockAuthentication);
        when(userRepository.findByUsername(any())).thenReturn(Optional.of(mockUser));
        totpUtilsMockedStatic.when(TotpUtils::generateSecret).thenReturn("new-secret");
        totpUtilsMockedStatic.when(() -> TotpUtils.getQrCodeUrl(anyString(), anyString(), anyString())).thenReturn("http://qr.code/url");

        // Act & Assert
        mockMvc.perform(post("/api/auth/signin")
                        .with(csrf()) // <-- FIX
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(loginRequest)))
                .andExpect(status().isUnauthorized())
                .andExpect(jsonPath("$.message").value("2FA setup required"))
                .andExpect(jsonPath("$.qrCodeUrl").value("http://qr.code/url"));
    }

    @Test
    void whenSignIn_withInvalidTotpCode_thenReturnsUnauthorized() throws Exception {
        // Arrange
        LoginRequest loginRequest = new LoginRequest("testuser", "password", 999999);
        User mockUser = new User("testuser", "<EMAIL>", "encodedPassword");
        mockUser.setTotpSecret("a-valid-secret");
        when(authenticationManager.authenticate(any())).thenReturn(createMockAuthentication());
        when(userRepository.findByUsername("testuser")).thenReturn(Optional.of(mockUser));
        totpUtilsMockedStatic.when(() -> TotpUtils.validateCode("a-valid-secret", 999999)).thenReturn(false);

        // Act & Assert
        mockMvc.perform(post("/api/auth/signin")
                        .with(csrf()) // <-- FIX
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(loginRequest)))
                .andExpect(status().isUnauthorized())
                .andExpect(jsonPath("$.message").value("Error: Invalid or missing TOTP code"));
    }

    @Test
    void whenRegisterUser_withValidData_thenSucceeds() throws Exception {
        // Arrange
        SignUpRequest signUpRequest = new SignUpRequest("newuser", "<EMAIL>", "password123");
        Role defaultRole = new Role();
        defaultRole.setName("User");
        when(userRepository.existsByUsername("newuser")).thenReturn(false);
        when(userRepository.existsByEmail("<EMAIL>")).thenReturn(false);
        when(passwordEncoder.encode("password123")).thenReturn("encodedPassword123");
        when(roleRepository.findByName("User")).thenReturn(Optional.of(defaultRole));
        totpUtilsMockedStatic.when(TotpUtils::generateSecret).thenReturn("new-user-secret");
        totpUtilsMockedStatic.when(() -> TotpUtils.getQrCodeUrl(any(), any(), any())).thenReturn("http://new.qr/url");

        // Act
        ResultActions result = mockMvc.perform(post("/api/auth/signup")
                .with(csrf()) // <-- FIX
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(signUpRequest)));

        // Assert
        result.andExpect(status().isOk())
                .andExpect(jsonPath("$.message").value("User registered successfully! Set up your authenticator with this QR:"))
                .andExpect(jsonPath("$.qrCodeUrl").value("http://new.qr/url"));
    }

    @Test
    void whenRegisterUser_withExistingUsername_thenReturnsBadRequest() throws Exception {
        // Arrange
        SignUpRequest signUpRequest = new SignUpRequest("existinguser", "<EMAIL>", "password123");
        when(userRepository.existsByUsername("existinguser")).thenReturn(true);

        // Act & Assert
        mockMvc.perform(post("/api/auth/signup")
                        .with(csrf()) // <-- FIX
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(signUpRequest)))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.message").value("Error: Username is already taken!"));
    }

    @Test
    void whenRegisterUser_withExistingEmail_thenReturnsBadRequest() throws Exception {
        // Arrange
        SignUpRequest signUpRequest = new SignUpRequest("newuser", "<EMAIL>", "password123");
        when(userRepository.existsByUsername("newuser")).thenReturn(false);
        when(userRepository.existsByEmail("<EMAIL>")).thenReturn(true);

        // Act & Assert
        mockMvc.perform(post("/api/auth/signup")
                        .with(csrf()) // <-- FIX
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(signUpRequest)))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.message").value("Error: Email is already in use!"));
    }

    @Test
    void whenForgotPassword_thenDelegatesToService() throws Exception {
        // Arrange
        Map<String, String> request = Map.of("email", "<EMAIL>");

        // Act
        mockMvc.perform(post("/api/auth/forgot-password")
                        .with(csrf()) // <-- FIX
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.message").value("If that email is associated with an account, a reset link was sent."));
    }

    @Test
    void whenResetPassword_thenDelegatesToService() throws Exception {
        // Arrange
        when(forgotPasswordService.resetPassword("valid-token", "newStrongPassword")).thenReturn(true);

        // Act & Assert
        mockMvc.perform(post("/api/auth/reset-password")
                        .with(csrf()) // <-- FIX
                        .param("token", "valid-token")
                        .param("newPassword", "newStrongPassword"))
                .andExpect(status().isOk());
    }

    @Test
    void whenVerify2FA_withValidCode_thenSucceeds() throws Exception {
        // Arrange
        Map<String, String> payload = Map.of("username", "testuser", "code", "123456");
        User mockUser = new User("testuser", "<EMAIL>", "password");
        mockUser.setTotpSecret("a-valid-secret");
        when(userRepository.findByUsername("testuser")).thenReturn(Optional.of(mockUser));
        totpUtilsMockedStatic.when(() -> TotpUtils.validateCode("a-valid-secret", 123456)).thenReturn(true);

        // Act & Assert
        mockMvc.perform(post("/api/auth/verify2fa")
                        .with(csrf()) // <-- FIX
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(payload)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.message").value("2FA verified. You can now log in with TOTP code."));
    }

    @Test
    void whenVerify2FA_withInvalidCode_thenReturnsUnauthorized() throws Exception {
        // Arrange
        Map<String, String> payload = Map.of("username", "testuser", "code", "999999");
        User mockUser = new User("testuser", "<EMAIL>", "password");
        mockUser.setTotpSecret("a-valid-secret");
        when(userRepository.findByUsername("testuser")).thenReturn(Optional.of(mockUser));
        totpUtilsMockedStatic.when(() -> TotpUtils.validateCode(anyString(), anyInt())).thenReturn(false);

        // Act & Assert
        mockMvc.perform(post("/api/auth/verify2fa")
                        .with(csrf()) // <-- FIX
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(payload)))
                .andExpect(status().isUnauthorized())
                .andExpect(jsonPath("$.message").value("Invalid TOTP code"));
    }
}