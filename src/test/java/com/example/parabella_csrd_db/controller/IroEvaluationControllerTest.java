//package com.example.parabella_csrd_db.controller;
//
//import com.example.parabella_csrd_db.dto.mithril.IroEvaluationDTO;
//import com.example.parabella_csrd_db.service.mithril.IroEvaluationService;
//import com.fasterxml.jackson.databind.ObjectMapper;
//import org.junit.jupiter.api.BeforeEach;
//import org.junit.jupiter.api.Test;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
//import org.springframework.boot.test.context.SpringBootTest;
//import org.springframework.boot.test.mock.mockito.MockBean;
//import org.springframework.http.MediaType;
//import org.springframework.security.test.context.support.WithMockUser;
//import org.springframework.test.web.servlet.MockMvc;
//
//import java.util.List;
//
//import static org.mockito.ArgumentMatchers.any;
//import static org.mockito.ArgumentMatchers.eq;
//import static org.mockito.Mockito.when;
//import static org.springframework.security.test.web.servlet.request.SecurityMockMvcRequestPostProcessors.csrf;
//import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.delete;
//import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
//import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
//import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
//import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;
//
//@SpringBootTest
//@AutoConfigureMockMvc
//class IroEvaluationControllerTest {
//
//    @Autowired
//    private MockMvc mockMvc;
//
//    @MockBean
//    private IroEvaluationService iroEvaluationService;
//
//    @Autowired
//    private ObjectMapper objectMapper;
//
//    private IroEvaluationDTO testDTO;
//
//    @BeforeEach
//    void setUp() {
//        testDTO = new IroEvaluationDTO();
//        testDTO.setId(1L);
//        testDTO.setCompanyId(1L);
//        testDTO.setEsrsTopicId(1L);
//        testDTO.setIroId(1L); // now required for the one-to-one mapping with IRO
//        testDTO.setRiskChance("risk");
//        testDTO.setScale(3);
//    }
//
//    @Test
//    @WithMockUser
//    void createOrUpdateIroEvaluation_ShouldReturnDetails() throws Exception {
//        when(iroEvaluationService.createOrUpdateIroEvaluation(any(IroEvaluationDTO.class), eq("testUser")))
//                .thenReturn(testDTO);
//
//        mockMvc.perform(post("/api/iroEval")
//                        .param("stakeholderName", "testUser")
//                        .with(csrf())
//                        .contentType(MediaType.APPLICATION_JSON)
//                        .content(objectMapper.writeValueAsString(testDTO)))
//                .andExpect(status().isOk())
//                .andExpect(jsonPath("$.riskChance").value("risk"));
//    }
//
//    @Test
//    @WithMockUser
//    void getIroEvaluationsByCompanyId_ShouldReturnList() throws Exception {
//        when(iroEvaluationService.getIroEvaluationsByCompanyId(1L))
//                .thenReturn(List.of(testDTO));
//
//        mockMvc.perform(get("/api/iroEval/company/1")
//                        .with(csrf()))
//                .andExpect(status().isOk())
//                .andExpect(jsonPath("$[0].riskChance").value("risk"));
//    }
//
//    @Test
//    @WithMockUser
//    void getIroEvaluation_WhenExists_ShouldReturnDetails() throws Exception {
//        when(iroEvaluationService.getIroEvaluation(1L)).thenReturn(testDTO);
//
//        mockMvc.perform(get("/api/iroEval/1")
//                        .with(csrf()))
//                .andExpect(status().isOk())
//                .andExpect(jsonPath("$.riskChance").value("risk"));
//    }
//
//    @Test
//    @WithMockUser
//    void getIroEvaluationByTopicId_ShouldReturnDetails() throws Exception {
//        when(iroEvaluationService.getIroEvaluationByTopicSelectionId(1L)).thenReturn(testDTO);
//
//        mockMvc.perform(get("/api/iroEval/topicId/1")
//                        .with(csrf()))
//                .andExpect(status().isOk())
//                .andExpect(jsonPath("$.riskChance").value("risk"));
//    }
//
//    @Test
//    @WithMockUser
//    void deleteIroEvaluation_ShouldReturnNoContent() throws Exception {
//        mockMvc.perform(delete("/api/iroEval/1")
//                        .with(csrf()))
//                .andExpect(status().isNoContent());
//    }
//}
