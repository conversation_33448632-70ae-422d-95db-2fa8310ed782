package com.example.parabella_csrd_db.controller;

import com.example.parabella_csrd_db.dto.mithril.StakeholderDTO;
import com.example.parabella_csrd_db.database.maindatabase.model.mithril.StakeholderStatus;
import com.example.parabella_csrd_db.service.mithril.StakeholderService;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;

import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;
import static org.springframework.security.test.web.servlet.request.SecurityMockMvcRequestPostProcessors.csrf;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@SpringBootTest
@AutoConfigureMockMvc
class StakeholderControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private StakeholderService stakeholderService;

    @Autowired
    private ObjectMapper objectMapper;

    private StakeholderDTO testDTO;

    @BeforeEach
    void setUp() {
        testDTO = new StakeholderDTO();
        testDTO.setId(1L);
        testDTO.setName("Test Stakeholder");
        testDTO.setEmail("<EMAIL>");
        testDTO.setCompanyId(1L);
        testDTO.setStatus(StakeholderStatus.INVITED);
    }

    @Test
    @WithMockUser
    void createStakeholder_WithValidData_ShouldReturnCreated() throws Exception {
        when(stakeholderService.createStakeholder(any(StakeholderDTO.class)))
                .thenReturn(testDTO);

        mockMvc.perform(post("/api/stakeholders")
                        .with(csrf())
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(testDTO)))
                .andExpect(status().isCreated())
                .andExpect(jsonPath("$.name").value(testDTO.getName()));
    }

    @Test
    @WithMockUser
    void sendStakeholderEmails_ShouldReturnOk() throws Exception {
        mockMvc.perform(post("/api/stakeholders/send-stakeholder-emails")
                        .with(csrf())
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(List.of(testDTO))))
                .andExpect(status().isOk());
    }

    @Test
    @WithMockUser
    void getStakeholderByToken_ShouldReturnStakeholder() throws Exception {
        when(stakeholderService.getStakeholderByToken("test-token")).thenReturn(testDTO);

        mockMvc.perform(get("/api/stakeholders/token/test-token")
                        .with(csrf()))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.name").value(testDTO.getName()));
    }
}