// CompanyGroupRepositoryTest.java
package com.example.parabella_csrd_db.repository;

import com.example.parabella_csrd_db.database.maindatabase.model.mithril.Company;
import com.example.parabella_csrd_db.database.maindatabase.model.mithril.CompanyGroup;
import com.example.parabella_csrd_db.database.maindatabase.repository.mithril.CompanyGroupRepository;
import com.example.parabella_csrd_db.database.maindatabase.repository.mithril.CompanyRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import org.testcontainers.containers.PostgreSQLContainer;
import org.testcontainers.junit.jupiter.Container;
import org.testcontainers.junit.jupiter.Testcontainers;

import java.util.Optional;

import static org.assertj.core.api.Assertions.assertThat;

@SpringBootTest
@Testcontainers
@ActiveProfiles("test")
@Transactional
class CompanyGroupRepositoryTest {

    @Container
    private static final PostgreSQLContainer<?> postgres = new PostgreSQLContainer<>("postgres:15-alpine")
            .withDatabaseName("testdb")
            .withUsername("postgres")
            .withPassword("admin");

    @Autowired
    private CompanyGroupRepository companyGroupRepository;

    @Autowired
    private CompanyRepository companyRepository;

    private CompanyGroup testCompanyGroup;

    @BeforeEach
    void setUp() {
        testCompanyGroup = new CompanyGroup();
        testCompanyGroup.setCompanyGroupName("Test Group");
        testCompanyGroup.setCompanyGroupVAT("TEST123");
        testCompanyGroup.setIndustry("Test Industry");
        testCompanyGroup = companyGroupRepository.save(testCompanyGroup);
    }

    @Test
    void findByCompanyGroupName_ShouldReturnCompanyGroup() {
        // Act
        Optional<CompanyGroup> found = companyGroupRepository.findByCompanyGroupName("Test Group");

        // Assert
        assertThat(found).isPresent();
        assertThat(found.get().getCompanyGroupName()).isEqualTo("Test Group");
    }

    @Test
    void findByCompanyGroupVAT_ShouldReturnCompanyGroup() {
        // Act
        Optional<CompanyGroup> found = companyGroupRepository.findByCompanyGroupVAT("TEST123");

        // Assert
        assertThat(found).isPresent();
        assertThat(found.get().getCompanyGroupVAT()).isEqualTo("TEST123");
    }

    @Test
    void save_WithSubCompanies_ShouldPersistRelationships() {
        // Arrange
        Company subCompany = new Company();
        subCompany.setCompanyName("Sub Company");
        subCompany.setCompanyGroup(testCompanyGroup);
        companyRepository.save(subCompany);

        // Act
        CompanyGroup found = companyGroupRepository.findById(testCompanyGroup.getId()).get();

        // Assert
        assertThat(found.getSubCompanies()).hasSize(1);
        assertThat(found.getSubCompanies().get(0).getCompanyName()).isEqualTo("Sub Company");
    }
}