
package com.example.parabella_csrd_db.repository;

import com.example.parabella_csrd_db.database.maindatabase.model.mithril.Company;
import com.example.parabella_csrd_db.database.maindatabase.model.mithril.CompanyGroup;
import com.example.parabella_csrd_db.database.maindatabase.repository.mithril.CompanyGroupRepository;
import com.example.parabella_csrd_db.database.maindatabase.repository.mithril.CompanyRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;
import org.testcontainers.containers.PostgreSQLContainer;
import org.testcontainers.junit.jupiter.Container;
import org.testcontainers.junit.jupiter.Testcontainers;


import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;

@SpringBootTest
@Testcontainers
@ActiveProfiles("test")
@Transactional
class CompanyRepositoryTest {

    @Container
    private static final PostgreSQLContainer<?> postgres = new PostgreSQLContainer<>("postgres:15-alpine")
            .withDatabaseName("testdb")
            .withUsername("postgres")
            .withPassword("admin");

    @Autowired
    private CompanyRepository companyRepository;

    @Autowired
    private CompanyGroupRepository companyGroupRepository;

    private CompanyGroup testCompanyGroup;

    @BeforeEach
    void setUp() {
        // Create test company group
        testCompanyGroup = new CompanyGroup();
        testCompanyGroup = companyGroupRepository.save(testCompanyGroup);
    }

    @Test
    void findByCompanyGroupId_ShouldReturnCompanies() {
        // Arrange
        Company company1 = createTestCompany("Company 1", testCompanyGroup);
        Company company2 = createTestCompany("Company 2", testCompanyGroup);
        companyRepository.saveAll(List.of(company1, company2));

        // Act
        List<Company> foundCompanies = companyRepository.findByCompanyGroupId(testCompanyGroup.getId());

        // Assert
        assertThat(foundCompanies).hasSize(2);
        assertThat(foundCompanies)
                .extracting(Company::getCompanyName)
                .containsExactlyInAnyOrder("Company 1", "Company 2");
    }

    @Test
    void save_ShouldPersistCompany() {
        // Arrange
        Company company = createTestCompany("Test Company", testCompanyGroup);

        // Act
        Company savedCompany = companyRepository.save(company);

        // Assert
        assertThat(savedCompany.getId()).isNotNull();
        assertThat(savedCompany.getCompanyName()).isEqualTo("Test Company");
        assertThat(savedCompany.getCompanyGroup().getId()).isEqualTo(testCompanyGroup.getId());
    }

    private Company createTestCompany(String name, CompanyGroup group) {
        Company company = new Company();
        company.setCompanyName(name);
        company.setAddress("Test Address");
        company.setVat("Test VAT");
        company.setCompanyGroup(group);
        company.setIndustry("Test Industry");
        company.setIsSubCompany(false);
        return company;
    }
}
