//package com.example.parabella_csrd_db.repository;
//
//import com.example.parabella_csrd_db.maindatabase.mithril.model.Company;
//import com.example.parabella_csrd_db.maindatabase.mithril.model.EsrsTopic;
//import com.example.parabella_csrd_db.maindatabase.mithril.model.Iro;
//import com.example.parabella_csrd_db.maindatabase.mithril.model.IroEvaluation;
//import com.example.parabella_csrd_db.maindatabase.mithril.repository.CompanyRepository;
//import com.example.parabella_csrd_db.maindatabase.mithril.repository.EsrsTopicsRepository;
//import com.example.parabella_csrd_db.maindatabase.mithril.repository.IroEvaluationRepository;
//import com.example.parabella_csrd_db.maindatabase.mithril.repository.IroRepository;
//import org.junit.jupiter.api.BeforeEach;
//import org.junit.jupiter.api.Test;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.boot.test.context.SpringBootTest;
//import org.springframework.test.context.ActiveProfiles;
//import org.springframework.transaction.annotation.Transactional;
//import org.testcontainers.containers.PostgreSQLContainer;
//import org.testcontainers.junit.jupiter.Container;
//import org.testcontainers.junit.jupiter.Testcontainers;
//
//import java.util.List;
//import java.util.Optional;
//
//import static org.assertj.core.api.Assertions.assertThat;
//
//@SpringBootTest
//@Testcontainers
//@ActiveProfiles("test")
//@Transactional
//class IroEvaluationRepositoryTest {
//
//    @Container
//    private static final PostgreSQLContainer<?> postgres = new PostgreSQLContainer<>("postgres:15-alpine")
//            .withDatabaseName("testdb")
//            .withUsername("postgres")
//            .withPassword("admin");
//
//    @Autowired
//    private IroEvaluationRepository iroEvaluationRepository;
//
//    @Autowired
//    private CompanyRepository companyRepository;
//
//    @Autowired
//    private EsrsTopicsRepository esrsTopicRepository;
//
//    @Autowired
//    private IroRepository iroRepository;
//
//    private Company testCompany;
//    private EsrsTopic testEsrsTopic;
//    private IroEvaluation testEvaluation;
//
//    @BeforeEach
//    void setUp() {
//        // Create test company
//        testCompany = new Company();
//        testCompany.setCompanyName("Test Company");
//        testCompany = companyRepository.save(testCompany);
//
//        // Create test ESRS topic
//        testEsrsTopic = new EsrsTopic();
//        testEsrsTopic.setArea("Test Area");
//        testEsrsTopic.setEsrsCode("TST");
//        testEsrsTopic.setTopic("Test Topic");
//        testEsrsTopic.setSubtopic("Test Subtopic");
//        testEsrsTopic.setSubSubTopic("Test SubSubTopic");
//        testEsrsTopic = esrsTopicRepository.save(testEsrsTopic);
//
//        // Create test evaluation details (including the associated IRO)
//        testEvaluation = createTestEvaluation(testCompany, testEsrsTopic);
//    }
//
//    @Test
//    void findByCompanyId_ShouldReturnEvaluation() {
//        // Act
//        List<IroEvaluation> foundEvaluations = iroEvaluationRepository.findByCompanyId(testCompany.getId());
//
//        // Assert
//        assertThat(foundEvaluations).hasSize(1);
//        assertThat(foundEvaluations.get(0).getCompany().getId()).isEqualTo(testCompany.getId());
//    }
//
//    @Test
//    void findByIro_EsrsTopic_Id_ShouldReturnEvaluation() {
//        // Act
//        Optional<IroEvaluation> foundEvaluationOpt = iroEvaluationRepository.findByIro_EsrsTopic_Id(testEsrsTopic.getId());
//
//        // Assert
//        assertThat(foundEvaluationOpt).isPresent();
//        IroEvaluation evaluation = foundEvaluationOpt.get();
//        assertThat(evaluation.getIro().getEsrsTopic().getId()).isEqualTo(testEsrsTopic.getId());
//    }
//
//    private IroEvaluation createTestEvaluation(Company company, EsrsTopic topic) {
//        // Create a test IRO that is associated with the test company and ESRS topic
//        Iro testIro = new Iro();
//        testIro.setName("Test IRO");
//        testIro.setDescription("Test IRO Description");
//        testIro.setCompany(company);
//        testIro.setEsrsTopic(topic);
//        testIro = iroRepository.save(testIro);
//
//        // Create and save the IroEvaluation instance linked to the IRO
//        IroEvaluation evaluation = new IroEvaluation();
//        evaluation.setCompany(company);
//        evaluation.setIro(testIro);
//        evaluation.setActualPotentialImpact("risk");
//        evaluation.setAffectedArea("own operation");
//        evaluation.setDescription("Test description");
//        evaluation.setScale(3);
//        return iroEvaluationRepository.save(evaluation);
//    }
//}
