// ValueChainObjectRepositoryTest.java
package com.example.parabella_csrd_db.repository;

import com.example.parabella_csrd_db.database.maindatabase.model.mithril.Company;
import com.example.parabella_csrd_db.database.maindatabase.model.mithril.ValueChainObject;
import com.example.parabella_csrd_db.database.maindatabase.repository.mithril.CompanyRepository;
import com.example.parabella_csrd_db.database.maindatabase.repository.mithril.ValueChainObjectRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;
import org.testcontainers.containers.PostgreSQLContainer;
import org.testcontainers.junit.jupiter.Container;
import org.testcontainers.junit.jupiter.Testcontainers;

import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;

@SpringBootTest
@Testcontainers
@ActiveProfiles("test")
@Transactional
class ValueChainObjectRepositoryTest {

    @Container
    private static final PostgreSQLContainer<?> postgres = new PostgreSQLContainer<>("postgres:15-alpine")
            .withDatabaseName("testdb")
            .withUsername("postgres")
            .withPassword("admin");

    @Autowired
    private ValueChainObjectRepository valueChainObjectRepository;

    @Autowired
    private CompanyRepository companyRepository;

    private Company testCompany;

    @BeforeEach
    void setUp() {
        // Create test company
        testCompany = new Company();
        testCompany.setCompanyName("Test Company");
        testCompany = companyRepository.save(testCompany);

        // Create test value chain objects
        createTestVCO("Supplier 1", "Manufacturing", testCompany);
        createTestVCO("Supplier 2", "Manufacturing", testCompany);
    }

    @Test
    void findByCompanyId_ShouldReturnValueChainObjects() {
        // Act
        List<ValueChainObject> found = valueChainObjectRepository.findByCompanyId(testCompany.getId());

        // Assert
        assertThat(found).hasSize(2);
        assertThat(found)
                .extracting(ValueChainObject::getCompany)
                .extracting(Company::getId)
                .containsOnly(testCompany.getId());
    }

    @Test
    void findByIndustry_ShouldReturnValueChainObjects() {
        // Act
        List<ValueChainObject> found = valueChainObjectRepository.findByIndustry("Manufacturing");

        // Assert
        assertThat(found).hasSize(2);
        assertThat(found)
                .extracting(ValueChainObject::getIndustry)
                .containsOnly("Manufacturing");
    }

    @Test
    void save_ShouldPersistValueChainObject() {
        // Arrange
        ValueChainObject vco = new ValueChainObject();
        vco.setName("New Supplier");
        vco.setIndustry("Logistics");
        vco.setCompany(testCompany);

        // Act
        ValueChainObject saved = valueChainObjectRepository.save(vco);

        // Assert
        assertThat(saved.getId()).isNotNull();
        assertThat(saved.getName()).isEqualTo("New Supplier");
        assertThat(saved.getCompany()).isEqualTo(testCompany);
    }

    private ValueChainObject createTestVCO(String name, String industry, Company company) {
        ValueChainObject vco = new ValueChainObject();
        vco.setName(name);
        vco.setIndustry(industry);
        vco.setCompany(company);
        return valueChainObjectRepository.save(vco);
    }
}