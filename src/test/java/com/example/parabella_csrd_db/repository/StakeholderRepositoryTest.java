package com.example.parabella_csrd_db.repository;

import com.example.parabella_csrd_db.database.maindatabase.model.mithril.Company;
import com.example.parabella_csrd_db.database.maindatabase.model.mithril.Project;
import com.example.parabella_csrd_db.database.maindatabase.model.mithril.Stakeholder;
import com.example.parabella_csrd_db.database.maindatabase.model.mithril.StakeholderStatus;
import com.example.parabella_csrd_db.database.maindatabase.repository.mithril.CompanyRepository;
import com.example.parabella_csrd_db.database.maindatabase.repository.mithril.ProjectRepository;
import com.example.parabella_csrd_db.database.maindatabase.repository.mithril.stakeholder_navigation.StakeholderRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;
import org.testcontainers.containers.PostgreSQLContainer;
import org.testcontainers.junit.jupiter.Container;
import org.testcontainers.junit.jupiter.Testcontainers;

import java.util.List;
import java.util.Optional;

import static org.assertj.core.api.Assertions.assertThat;

@SpringBootTest
@Testcontainers
@ActiveProfiles("test")
@Transactional
class StakeholderRepositoryTest {

    @Container
    private static final PostgreSQLContainer<?> postgres = new PostgreSQLContainer<>("postgres:15-alpine")
            .withDatabaseName("testdb")
            .withUsername("postgres")
            .withPassword("admin");

    @Autowired
    private StakeholderRepository stakeholderRepository;

    @Autowired
    private CompanyRepository companyRepository;

    @Autowired
    private ProjectRepository projectRepository;

    private Company testCompany;
    private Project testProject;

    @BeforeEach
    void setUp() {
        testCompany = new Company();
        testCompany.setCompanyName("Test Company");
        testCompany = companyRepository.save(testCompany);

        testProject = new Project();
        testProject.setProjectName("Test Project");
        testProject = projectRepository.save(testProject);
    }

    @Test
    void findByToken_ShouldReturnStakeholder() {
        // Arrange
        Stakeholder stakeholder = createTestStakeholder("Test Stakeholder", "<EMAIL>", "unique-token");

        // Act
        Optional<Stakeholder> found = stakeholderRepository.findByToken("unique-token");

        // Assert
        assertThat(found).isPresent();
        assertThat(found.get().getToken()).isEqualTo("unique-token");
    }

    @Test
    void findByCompanyId_ShouldReturnStakeholders() {
        // Arrange
        createTestStakeholder("Stakeholder 1", "<EMAIL>", "token1");
        createTestStakeholder("Stakeholder 2", "<EMAIL>", "token2");

        // Act
        List<Stakeholder> stakeholders = stakeholderRepository.findByCompany_Id(testCompany.getId());

        // Assert
        assertThat(stakeholders).hasSize(2);
        assertThat(stakeholders)
                .extracting(Stakeholder::getCompany)
                .extracting(Company::getId)
                .containsOnly(testCompany.getId());
    }

    @Test
    void findByProjectId_ShouldReturnStakeholders() {
        // Arrange
        createTestStakeholder("Stakeholder 1", "<EMAIL>", "token1");
        createTestStakeholder("Stakeholder 2", "<EMAIL>", "token2");

        // Act
        List<Stakeholder> stakeholders = stakeholderRepository.findByProjectId(testProject.getId());

        // Assert
        assertThat(stakeholders).hasSize(2);
        assertThat(stakeholders)
                .extracting(Stakeholder::getProject)
                .extracting(Project::getId)
                .containsOnly(testProject.getId());
    }

    private Stakeholder createTestStakeholder(String name, String email, String token) {
        Stakeholder stakeholder = new Stakeholder();
        stakeholder.setName(name);
        stakeholder.setEmail(email);
        stakeholder.setToken(token);
        stakeholder.setCompany(testCompany);
        stakeholder.setProject(testProject);
        stakeholder.setStatus(StakeholderStatus.INVITED);
        stakeholder.setRole("TEST_ROLE");

        return stakeholderRepository.save(stakeholder);
    }
}