package com.example.parabella_csrd_db.repository;

import com.example.parabella_csrd_db.database.maindatabase.model.authentication.User;
import com.example.parabella_csrd_db.database.maindatabase.model.mithril.Project;
import com.example.parabella_csrd_db.database.maindatabase.repository.authentication.UserRepository;
import com.example.parabella_csrd_db.database.maindatabase.repository.mithril.ProjectRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.DynamicPropertyRegistry;
import org.springframework.test.context.DynamicPropertySource;
import org.springframework.transaction.annotation.Transactional;
import org.testcontainers.containers.PostgreSQLContainer;
import org.testcontainers.junit.jupiter.Container;
import org.testcontainers.junit.jupiter.Testcontainers;

import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;

@SpringBootTest
@Testcontainers
@ActiveProfiles("test")
@Transactional
class ProjectRepositoryTest {

    @Container
    private static final PostgreSQLContainer<?> postgres = new PostgreSQLContainer<>("postgres:15-alpine")
            .withDatabaseName("testdb")
            .withUsername("postgres")
            .withPassword("admin");

    @DynamicPropertySource
    static void configureProperties(DynamicPropertyRegistry registry) {
        registry.add("spring.datasource.url", postgres::getJdbcUrl);
        registry.add("spring.datasource.username", postgres::getUsername);
        registry.add("spring.datasource.password", postgres::getPassword);
        registry.add("spring.datasource.driver-class-name", () -> "org.postgresql.Driver");
    }

    @Autowired
    private ProjectRepository projectRepository;

    @Autowired
    private UserRepository userRepository;

    private User testUser;

    @BeforeEach
    void setUp() {
        // Create and save a test user
        testUser = new User();
        testUser.setUsername("testuser");
        testUser.setPassword("password");
        testUser.setEmail("<EMAIL>");
        userRepository.save(testUser);
    }

    @Test
    void findByUserId_ShouldReturnProjectsForGivenUser() {
        // Arrange
        Project project1 = createProject("Project 1", "Description 1");
        Project project2 = createProject("Project 2", "Description 2");

        // Act
        List<Project> projects = projectRepository.findByUserId(testUser.getId());

        // Assert
        assertThat(projects).hasSize(2);
        assertThat(projects)
                .extracting(Project::getProjectName)
                .containsExactlyInAnyOrder("Project 1", "Project 2");
    }

    @Test
    void findByUserId_WithNoProjects_ShouldReturnEmptyList() {
        // Act
        List<Project> projects = projectRepository.findByUserId(testUser.getId());

        // Assert
        assertThat(projects).isEmpty();
    }

    @Test
    void save_ShouldPersistProject() {
        // Arrange
        Project project = new Project();
        project.setProjectName("New Project");
        project.setProjectDescription("New Description");
        project.setProjectType("company");
        project.setUser(testUser);

        // Act
        Project savedProject = projectRepository.save(project);

        // Assert
        assertThat(savedProject.getId()).isNotNull();
        assertThat(savedProject.getProjectName()).isEqualTo("New Project");
        assertThat(savedProject.getProjectType()).isEqualTo("company");
        assertThat(savedProject.getUser()).isEqualTo(testUser);
    }

    @Test
    void delete_ShouldRemoveProject() {
        // Arrange
        Project project = createProject("Project to Delete", "Description");
        Long projectId = project.getId();

        // Act
        projectRepository.delete(project);

        // Assert
        assertThat(projectRepository.findById(projectId)).isEmpty();
    }

    // Helper method to create and save a project
    private Project createProject(String name, String description) {
        Project project = new Project();
        project.setProjectName(name);
        project.setProjectDescription(description);
        project.setProjectType("company");
        project.setUser(testUser);
        return projectRepository.save(project);
    }
}