// EsrsTopicSelectionRepositoryTest.java
package com.example.parabella_csrd_db.repository;

import com.example.parabella_csrd_db.database.maindatabase.model.mithril.Company;
import com.example.parabella_csrd_db.database.maindatabase.model.mithril.EsrsTopic;
import com.example.parabella_csrd_db.database.maindatabase.model.mithril.EsrsTopicSelection;
import com.example.parabella_csrd_db.database.maindatabase.model.mithril.Stakeholder;
import com.example.parabella_csrd_db.database.maindatabase.repository.mithril.CompanyRepository;
import com.example.parabella_csrd_db.database.maindatabase.repository.mithril.EsrsTopicSelectionRepository;
import com.example.parabella_csrd_db.database.maindatabase.repository.mithril.EsrsTopicsRepository;
import com.example.parabella_csrd_db.database.maindatabase.repository.mithril.stakeholder_navigation.StakeholderRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;
import org.testcontainers.containers.PostgreSQLContainer;
import org.testcontainers.junit.jupiter.Container;
import org.testcontainers.junit.jupiter.Testcontainers;

import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;

@SpringBootTest
@Testcontainers
@ActiveProfiles("test")
@Transactional
class EsrsTopicSelectionRepositoryTest {

    @Container
    private static final PostgreSQLContainer<?> postgres = new PostgreSQLContainer<>("postgres:15-alpine")
            .withDatabaseName("testdb")
            .withUsername("postgres")
            .withPassword("admin");

    @Autowired
    private EsrsTopicSelectionRepository esrsTopicSelectionRepository;

    @Autowired
    private CompanyRepository companyRepository;

    @Autowired
    private EsrsTopicsRepository esrsTopicRepository;

    @Autowired
    private StakeholderRepository stakeholderRepository;

    private Company testCompany;
    private EsrsTopic testTopic;
    private Stakeholder testStakeholder;
    private EsrsTopicSelection testSelection;

    @BeforeEach
    void setUp() {
        // Create test company
        testCompany = new Company();
        testCompany.setCompanyName("Test Company");
        testCompany = companyRepository.save(testCompany);

        // Create test ESRS topic
        testTopic = new EsrsTopic();
        testTopic.setArea("Test Area");
        testTopic = esrsTopicRepository.save(testTopic);

        // Create test stakeholder
        testStakeholder = new Stakeholder();
        testStakeholder.setName("Test Stakeholder");
        testStakeholder.setCompany(testCompany);
        testStakeholder = stakeholderRepository.save(testStakeholder);

        // Create test selection
        testSelection = createTestSelection();
    }

    @Test
    void findByCompanyId_ShouldReturnSelections() {
        // Act
        List<EsrsTopicSelection> found = esrsTopicSelectionRepository.findByCompanyId(testCompany.getId());

        // Assert
        assertThat(found).hasSize(1);
        assertThat(found.get(0).getCompany().getId()).isEqualTo(testCompany.getId());
    }

    @Test
    void findByStakeholderId_ShouldReturnSelections() {
        // Act
        List<EsrsTopicSelection> found = esrsTopicSelectionRepository.findByStakeholderId(testStakeholder.getId());

        // Assert
        assertThat(found).hasSize(1);
        assertThat(found.get(0).getStakeholder().getId()).isEqualTo(testStakeholder.getId());
    }

    @Test
    void findByCompanyIdAndRelevantTrue_ShouldReturnRelevantSelections() {
        // Act
        List<EsrsTopicSelection> found = esrsTopicSelectionRepository
                .findByCompanyIdAndRelevantTrue(testCompany.getId());

        // Assert
        assertThat(found).hasSize(1);
        assertThat(found.get(0).getRelevant()).isTrue();
    }

    private EsrsTopicSelection createTestSelection() {
        EsrsTopicSelection selection = new EsrsTopicSelection();
        selection.setCompany(testCompany);
        selection.setEsrsTopic(testTopic);
        selection.setStakeholder(testStakeholder);
        selection.setRelevant(true);
        selection.setReasonIrrelevance(null);
        return esrsTopicSelectionRepository.save(selection);
    }
}
