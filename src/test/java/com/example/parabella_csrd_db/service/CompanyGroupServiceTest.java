// CompanyGroupServiceTest.java
package com.example.parabella_csrd_db.service;

import com.example.parabella_csrd_db.dto.mithril.CompanyDTO;
import com.example.parabella_csrd_db.dto.mithril.CompanyGroupDTO;
import com.example.parabella_csrd_db.database.maindatabase.model.mithril.Company;
import com.example.parabella_csrd_db.database.maindatabase.model.mithril.CompanyGroup;
import com.example.parabella_csrd_db.database.maindatabase.repository.mithril.CompanyGroupRepository;
import com.example.parabella_csrd_db.database.maindatabase.repository.mithril.CompanyRepository;
import com.example.parabella_csrd_db.service.mithril.CompanyGroupService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class CompanyGroupServiceTest {

    @Mock
    private CompanyGroupRepository companyGroupRepository;

    @Mock
    private CompanyRepository companyRepository;

    @InjectMocks
    private CompanyGroupService companyGroupService;

    private CompanyGroupDTO testCompanyGroupDTO;
    private CompanyGroup testCompanyGroup;
    private List<Company> testSubCompanies;

    @BeforeEach
    void setUp() {
        // Setup test CompanyGroupDTO
        testCompanyGroupDTO = new CompanyGroupDTO();
        testCompanyGroupDTO.setCompanyGroupName("Test Group");
        testCompanyGroupDTO.setCompanyGroupVAT("TEST123");
        testCompanyGroupDTO.setIndustry("Test Industry");

        // Setup test CompanyGroup
        testCompanyGroup = new CompanyGroup();
        testCompanyGroup.setId(1L);
        testCompanyGroup.setCompanyGroupName("Test Group");
        testCompanyGroup.setCompanyGroupVAT("TEST123");
        testCompanyGroup.setIndustry("Test Industry");

        // Setup test SubCompanies
        testSubCompanies = new ArrayList<>();
        Company subCompany = new Company();
        subCompany.setId(1L);
        subCompany.setCompanyName("Sub Company");
        subCompany.setCompanyGroup(testCompanyGroup);
        testSubCompanies.add(subCompany);
    }

    @Test
    void createCompanyGroup_WithValidData_ShouldCreateCompanyGroup() {
        // Arrange
        when(companyGroupRepository.save(any(CompanyGroup.class))).thenReturn(testCompanyGroup);

        // Act
        CompanyGroupDTO result = companyGroupService.createCompanyGroup(testCompanyGroupDTO);

        // Assert
        assertThat(result).isNotNull();
        assertThat(result.getCompanyGroupName()).isEqualTo(testCompanyGroupDTO.getCompanyGroupName());
        verify(companyGroupRepository).save(any(CompanyGroup.class));
    }

    @Test
    void createCompanyGroup_WithSubCompanies_ShouldCreateCompanyGroupAndCompanies() {
        // Arrange
        CompanyDTO subCompanyDTO = new CompanyDTO();
        subCompanyDTO.setCompanyName("Sub Company");
        testCompanyGroupDTO.setSubCompanies(List.of(subCompanyDTO));

        when(companyGroupRepository.save(any(CompanyGroup.class))).thenReturn(testCompanyGroup);

        // Act
        CompanyGroupDTO result = companyGroupService.createCompanyGroup(testCompanyGroupDTO);

        // Assert
        assertThat(result).isNotNull();
        verify(companyRepository, atLeastOnce()).save(any(Company.class));
    }

    @Test
    void updateCompanyGroup_ShouldUpdateExistingGroup() {
        // Arrange
        when(companyGroupRepository.findById(1L)).thenReturn(Optional.of(testCompanyGroup));
        when(companyGroupRepository.save(any(CompanyGroup.class))).thenReturn(testCompanyGroup);

        // Act
        CompanyGroupDTO result = companyGroupService.updateCompanyGroup(1L, testCompanyGroupDTO);

        // Assert
        assertThat(result).isNotNull();
        verify(companyGroupRepository).save(any(CompanyGroup.class));
    }

    @Test
    void updateCompanyGroup_WithNonExistentId_ShouldThrowException() {
        // Arrange
        when(companyGroupRepository.findById(1L)).thenReturn(Optional.empty());

        // Act & Assert
        assertThrows(RuntimeException.class, () ->
                companyGroupService.updateCompanyGroup(1L, testCompanyGroupDTO));
    }
}