package com.example.parabella_csrd_db.service;

import com.example.parabella_csrd_db.database.maindatabase.model.mithril.*;
import com.example.parabella_csrd_db.database.maindatabase.repository.mithril.*;
import com.example.parabella_csrd_db.dto.mithril.CompanyDTO;
import com.example.parabella_csrd_db.dto.mithril.EsrsTopicDTO;
import com.example.parabella_csrd_db.dto.mithril.StakeholderDTO;
import com.example.parabella_csrd_db.dto.mithril.ValueChainObjectDTO;
import com.example.parabella_csrd_db.database.maindatabase.repository.mithril.stakeholder_navigation.StakeholderRepository;
import com.example.parabella_csrd_db.service.mithril.CompanyService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class CompanyServiceTest {

    @Mock
    private CompanyRepository companyRepository;
    @Mock
    private CompanyGroupRepository companyGroupRepository;
    @Mock
    private ValueChainObjectRepository valueChainObjectRepository;
    @Mock
    private StakeholderRepository stakeholderRepository;
    @Mock
    private EsrsTopicsRepository esrsTopicRepository;
    @Mock
    private ProjectRepository projectRepository;

    @InjectMocks
    private CompanyService companyService;

    private Company company;
    private CompanyDTO companyDTO;
    private Project project;
    private CompanyGroup companyGroup;
    private ValueChainObject vco1;
    private Stakeholder stakeholder1;
    private EsrsTopic esrsTopic1;

    @BeforeEach
    void setUp() {
        // Common Test Data
        project = new Project();
        project.setId(1L);
        project.setProjectName("Test Project");

        companyGroup = new CompanyGroup();
        companyGroup.setId(10L);


        company = new Company();
        company.setId(1L);
        company.setCompanyName("Test Company");
        company.setAddress("123 Test St");
        company.setVat("VAT123");
        company.setNumEmployees("100");
        company.setRevenues(1000000.0);
        company.setIndustry("Tech");
        company.setIsSubCompany(false);
        company.setProject(project);
        company.setCompanyGroup(companyGroup);

        vco1 = new ValueChainObject();
        vco1.setId(101L);
        vco1.setName("VCO 1");
        vco1.setIndustry("Supply");
        vco1.setCompany(company);

        stakeholder1 = new Stakeholder();
        stakeholder1.setId(201L);
        stakeholder1.setName("Stakeholder 1");
        stakeholder1.setRole("Investor");
        stakeholder1.setEmail("<EMAIL>");
        stakeholder1.setStakeholderType("External");
        stakeholder1.setCompany(company);
        stakeholder1.setProject(project); // Assume stakeholder inherits project from company

        company.setValueChainObjects(List.of(vco1));
        company.setStakeholders(List.of(stakeholder1));

        // Note: EsrsTopic is not directly linked in the Company entity provided
        esrsTopic1 = new EsrsTopic();
        esrsTopic1.setId(301L);
        esrsTopic1.setArea("E");
        esrsTopic1.setEsrsCode("E1");
        esrsTopic1.setTopic("Climate");

        // DTO Setup
        companyDTO = new CompanyDTO();
        companyDTO.setId(1L);
        companyDTO.setCompanyName("Test Company DTO");
        companyDTO.setAddress("456 DTO St");
        companyDTO.setVat("VAT456");
        companyDTO.setNumEmployees("200");
        companyDTO.setRevenues(2000000.0);
        companyDTO.setIndustry("Finance");
        companyDTO.setSubCompany(true);
        companyDTO.setProjectId(project.getId());
        companyDTO.setCompanyGroupId(companyGroup.getId());

        ValueChainObjectDTO vcoDTO1 = new ValueChainObjectDTO();
        vcoDTO1.setId(101L);
        vcoDTO1.setName("VCO 1 DTO");
        vcoDTO1.setIndustry("Logistics");
        companyDTO.setValueChainObjects(List.of(vcoDTO1));

        StakeholderDTO stakeholderDTO1 = new StakeholderDTO();
        stakeholderDTO1.setId(201L);
        stakeholderDTO1.setName("Stakeholder 1 DTO");
        stakeholderDTO1.setRole("Customer");
        stakeholderDTO1.setEmail("<EMAIL>");
        stakeholderDTO1.setStakeholderType("Internal");
        companyDTO.setStakeholders(List.of(stakeholderDTO1));

        EsrsTopicDTO esrsTopicDTO1 = new EsrsTopicDTO();
        esrsTopicDTO1.setId(301L);
        esrsTopicDTO1.setArea("S");
        esrsTopicDTO1.setEsrsCode("S1");
        esrsTopicDTO1.setTopic("Own Workforce");
        companyDTO.setEsrsTopics(List.of(esrsTopicDTO1)); // Although not linked, DTO has it
    }

    @Test
    void createCompany_whenValidInput_shouldSaveAndReturnDTO() {
        // Arrange
        when(projectRepository.findById(anyLong())).thenReturn(Optional.of(project));
        when(companyGroupRepository.findById(anyLong())).thenReturn(Optional.of(companyGroup));
        when(companyRepository.save(any(Company.class))).thenAnswer(invocation -> {
            Company savedCompany = invocation.getArgument(0);
            savedCompany.setId(1L); // Simulate saving and getting an ID
            return savedCompany;
        });
        when(valueChainObjectRepository.save(any(ValueChainObject.class))).thenReturn(vco1);
        when(stakeholderRepository.save(any(Stakeholder.class))).thenReturn(stakeholder1);
        when(esrsTopicRepository.save(any(EsrsTopic.class))).thenReturn(esrsTopic1);

        // Act
        CompanyDTO createdCompanyDTO = companyService.createCompany(companyDTO);

        // Assert
        assertNotNull(createdCompanyDTO);
        assertEquals(companyDTO.getCompanyName(), createdCompanyDTO.getCompanyName());
        assertEquals(project.getId(), createdCompanyDTO.getProjectId());
        assertEquals(companyGroup.getId(), createdCompanyDTO.getCompanyGroupId());
        assertEquals(1L, createdCompanyDTO.getId()); // Ensure ID is assigned

        // Verify interactions
        verify(projectRepository, times(1)).findById(companyDTO.getProjectId());
        verify(companyGroupRepository, times(1)).findById(companyDTO.getCompanyGroupId());
        verify(companyRepository, times(1)).save(any(Company.class));
        verify(valueChainObjectRepository, times(companyDTO.getValueChainObjects().size())).save(any(ValueChainObject.class));
        verify(stakeholderRepository, times(companyDTO.getStakeholders().size())).save(any(Stakeholder.class));
        verify(esrsTopicRepository, times(companyDTO.getEsrsTopics().size())).save(any(EsrsTopic.class)); // Still saves EsrsTopics even if not linked

        // Verify associations were set in saved entities (using ArgumentCaptor if needed for more detail)
        verify(valueChainObjectRepository).save(argThat(vco -> vco.getCompany() != null && vco.getCompany().getId() == 1L));
        verify(stakeholderRepository).save(argThat(s -> s.getCompany() != null && s.getCompany().getId() == 1L));
        verify(stakeholderRepository).save(argThat(s -> s.getProject() != null && s.getProject().getId() == project.getId()));
    }

    @Test
    void createCompany_whenProjectNotFound_shouldThrowException() {
        // Arrange
        when(projectRepository.findById(anyLong())).thenReturn(Optional.empty());
        // No need to mock group repo if project fails first

        // Act & Assert
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            companyService.createCompany(companyDTO);
        });
        assertEquals("Project not found", exception.getMessage());
        verify(companyRepository, never()).save(any());
    }

    @Test
    void createCompany_whenCompanyGroupNotFound_shouldThrowException() {
        // Arrange
        when(projectRepository.findById(anyLong())).thenReturn(Optional.of(project));
        when(companyGroupRepository.findById(anyLong())).thenReturn(Optional.empty());

        // Act & Assert
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            companyService.createCompany(companyDTO);
        });
        assertEquals("CompanyGroup not found", exception.getMessage());
        verify(companyRepository, never()).save(any());
    }

    @Test
    void createCompany_withNullLists_shouldHandleGracefully() {
        // Arrange
        companyDTO.setValueChainObjects(null);
        companyDTO.setStakeholders(null);
        companyDTO.setEsrsTopics(null);

        when(projectRepository.findById(anyLong())).thenReturn(Optional.of(project));
        when(companyGroupRepository.findById(anyLong())).thenReturn(Optional.of(companyGroup));
        when(companyRepository.save(any(Company.class))).thenReturn(company); // Use the base company setup

        // Act
        CompanyDTO createdCompanyDTO = companyService.createCompany(companyDTO);

        // Assert
        assertNotNull(createdCompanyDTO);
        assertEquals(companyDTO.getCompanyName(), createdCompanyDTO.getCompanyName());
        verify(companyRepository, times(1)).save(any(Company.class));
        verify(valueChainObjectRepository, never()).save(any());
        verify(stakeholderRepository, never()).save(any());
        verify(esrsTopicRepository, never()).save(any());
    }

    @Test
    void updateCompany_whenCompanyFound_shouldUpdateAndReturnDTO() {
        // Arrange
        Long companyId = 1L;
        companyDTO.setId(companyId); // Ensure DTO ID matches

        // Mocks for finding the existing company and its relations
        when(companyRepository.findById(companyId)).thenReturn(Optional.of(company));
        when(projectRepository.findById(companyDTO.getProjectId())).thenReturn(Optional.of(project));
        when(companyGroupRepository.findById(companyDTO.getCompanyGroupId())).thenReturn(Optional.of(companyGroup));
        when(companyRepository.save(any(Company.class))).thenAnswer(invocation -> invocation.getArgument(0));

        // Mocks for fetching related entities after update (as per current implementation)
        when(valueChainObjectRepository.findByCompanyId(companyId)).thenReturn(List.of(vco1)); // Simulating fetch after update
        when(stakeholderRepository.findByCompany_Id(companyId)).thenReturn(List.of(stakeholder1)); // Simulating fetch after update


        // Act
        CompanyDTO updatedCompanyDTO = companyService.updateCompany(companyId, companyDTO);

        // Assert
        assertNotNull(updatedCompanyDTO);
        assertEquals(companyId, updatedCompanyDTO.getId());
        assertEquals(companyDTO.getCompanyName(), updatedCompanyDTO.getCompanyName()); // Check updated field
        assertEquals(companyDTO.getAddress(), updatedCompanyDTO.getAddress());
        assertEquals(companyDTO.getVat(), updatedCompanyDTO.getVat());
        assertEquals(companyDTO.getNumEmployees(), updatedCompanyDTO.getNumEmployees());
        assertEquals(companyDTO.getRevenues(), updatedCompanyDTO.getRevenues());
        assertEquals(companyDTO.getIndustry(), updatedCompanyDTO.getIndustry());
        assertEquals(companyDTO.getSubCompany(), updatedCompanyDTO.getSubCompany());
        assertEquals(companyDTO.getProjectId(), updatedCompanyDTO.getProjectId());
        assertEquals(companyDTO.getCompanyGroupId(), updatedCompanyDTO.getCompanyGroupId());

        // Verify main save operation
        verify(companyRepository, times(1)).findById(companyId);
        verify(companyRepository, times(1)).save(any(Company.class));

        // Verify related entities are fetched again (as per current implementation)
        verify(valueChainObjectRepository, times(1)).findByCompanyId(companyId);
        verify(stakeholderRepository, times(1)).findByCompany_Id(companyId);

        // IMPORTANT: Verify that the update methods for related entities are NOT called
        // because they are commented out in the service
        // If uncommented, these verifications would need to change significantly
        verify(valueChainObjectRepository, never()).save(any(ValueChainObject.class)); // Assuming update logic is commented out
        verify(stakeholderRepository, never()).save(any(Stakeholder.class)); // Assuming update logic is commented out
        verify(valueChainObjectRepository, never()).deleteAll(anyList()); // Assuming update logic is commented out
        verify(stakeholderRepository, never()).deleteAll(anyList()); // Assuming update logic is commented out
        verify(esrsTopicRepository, never()).save(any(EsrsTopic.class)); // Assuming update logic is commented out
        verify(esrsTopicRepository, never()).deleteAll(anyList()); // Assuming update logic is commented out
    }

    @Test
    void updateCompany_whenCompanyNotFound_shouldThrowException() {
        // Arrange
        Long nonExistentId = 99L;
        when(companyRepository.findById(nonExistentId)).thenReturn(Optional.empty());

        // Act & Assert
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            companyService.updateCompany(nonExistentId, companyDTO);
        });
        assertEquals("Company not found", exception.getMessage());
        verify(companyRepository, never()).save(any());
    }

    @Test
    void updateCompany_whenNewProjectNotFound_shouldThrowException() {
        // Arrange
        Long companyId = 1L;
        companyDTO.setProjectId(999L); // Non-existent project ID

        when(companyRepository.findById(companyId)).thenReturn(Optional.of(company));
        when(projectRepository.findById(999L)).thenReturn(Optional.empty());
        // Don't need to mock group repo if project fails

        // Act & Assert
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            companyService.updateCompany(companyId, companyDTO);
        });
        assertEquals("CompanyGroup not found", exception.getMessage());
        verify(companyRepository, never()).save(any());
    }

    @Test
    void updateCompany_whenNewGroupNotFound_shouldThrowException() {
        // Arrange
        Long companyId = 1L;
        companyDTO.setCompanyGroupId(998L); // Non-existent group ID

        when(companyRepository.findById(companyId)).thenReturn(Optional.of(company));
        when(projectRepository.findById(companyDTO.getProjectId())).thenReturn(Optional.of(project)); // Assume project is found
        when(companyGroupRepository.findById(998L)).thenReturn(Optional.empty());

        // Act & Assert
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            companyService.updateCompany(companyId, companyDTO);
        });
        assertEquals("CompanyGroup not found", exception.getMessage());
        verify(companyRepository, never()).save(any());
    }


    @Test
    void getCompany_whenCompanyFound_shouldReturnDTO() {
        // Arrange
        Long companyId = 1L;
        when(companyRepository.findById(companyId)).thenReturn(Optional.of(company));
        // Simulate fetching related entities for DTO conversion
        company.setValueChainObjects(List.of(vco1));
        company.setStakeholders(List.of(stakeholder1));

        // Act
        CompanyDTO foundCompanyDTO = companyService.getCompany(companyId);

        // Assert
        assertNotNull(foundCompanyDTO);
        assertEquals(company.getId(), foundCompanyDTO.getId());
        assertEquals(company.getCompanyName(), foundCompanyDTO.getCompanyName());
        assertEquals(company.getProject().getId(), foundCompanyDTO.getProjectId());
        assertEquals(company.getCompanyGroup().getId(), foundCompanyDTO.getCompanyGroupId());
        assertFalse(foundCompanyDTO.getValueChainObjects().isEmpty());
        assertEquals(vco1.getId(), foundCompanyDTO.getValueChainObjects().get(0).getId());
        assertFalse(foundCompanyDTO.getStakeholders().isEmpty());
        assertEquals(stakeholder1.getId(), foundCompanyDTO.getStakeholders().get(0).getId());
        assertEquals(stakeholder1.getCompany().getCompanyName(), foundCompanyDTO.getStakeholders().get(0).getCompanyName());
        assertEquals(stakeholder1.getProject().getId(), foundCompanyDTO.getStakeholders().get(0).getProjectId());
        // assertNull(foundCompanyDTO.getEsrsTopics()); // Since conversion is commented out

        verify(companyRepository, times(1)).findById(companyId);
    }

    @Test
    void getCompany_whenCompanyNotFound_shouldThrowException() {
        // Arrange
        Long nonExistentId = 99L;
        when(companyRepository.findById(nonExistentId)).thenReturn(Optional.empty());

        // Act & Assert
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            companyService.getCompany(nonExistentId);
        });
        assertEquals("Company not found", exception.getMessage());
    }

    @Test
    void getCompaniesByGroupId_whenCompaniesExist_shouldReturnListOfDTOs() {
        // Arrange
        Long groupId = 10L;
        Company company2 = new Company(); // Another company in the same group
        company2.setId(2L);
        company2.setCompanyName("Another Company");
        company2.setCompanyGroup(companyGroup);
        company2.setProject(project);
        company2.setValueChainObjects(Collections.emptyList()); // Simplification for this test
        company2.setStakeholders(Collections.emptyList()); // Simplification

        List<Company> companies = List.of(company, company2);
        when(companyRepository.findByCompanyGroupId(groupId)).thenReturn(companies);

        // Act
        List<CompanyDTO> companyDTOs = companyService.getCompaniesByGroupId(groupId);

        // Assert
        assertNotNull(companyDTOs);
        assertEquals(2, companyDTOs.size());
        assertEquals(company.getId(), companyDTOs.get(0).getId());
        assertEquals(company2.getId(), companyDTOs.get(1).getId());
        assertEquals(company.getCompanyName(), companyDTOs.get(0).getCompanyName());
        assertEquals(company2.getCompanyName(), companyDTOs.get(1).getCompanyName());

        verify(companyRepository, times(1)).findByCompanyGroupId(groupId);
    }

    @Test
    void getCompaniesByGroupId_whenNoCompaniesExist_shouldReturnEmptyList() {
        // Arrange
        Long groupId = 11L;
        when(companyRepository.findByCompanyGroupId(groupId)).thenReturn(Collections.emptyList());

        // Act
        List<CompanyDTO> companyDTOs = companyService.getCompaniesByGroupId(groupId);

        // Assert
        assertNotNull(companyDTOs);
        assertTrue(companyDTOs.isEmpty());
        verify(companyRepository, times(1)).findByCompanyGroupId(groupId);
    }

    @Test
    void deleteCompany_whenCompanyFound_shouldDeleteCompany() {
        // Arrange
        Long companyId = 1L;
        when(companyRepository.findById(companyId)).thenReturn(Optional.of(company));
        doNothing().when(companyRepository).delete(any(Company.class));

        // Act
        companyService.deleteCompany(companyId);

        // Assert
        verify(companyRepository, times(1)).findById(companyId);
        verify(companyRepository, times(1)).delete(company);
    }

    @Test
    void deleteCompany_whenCompanyNotFound_shouldThrowException() {
        // Arrange
        Long nonExistentId = 99L;
        when(companyRepository.findById(nonExistentId)).thenReturn(Optional.empty());

        // Act & Assert
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            companyService.deleteCompany(nonExistentId);
        });
        assertEquals("Company not found", exception.getMessage());
        verify(companyRepository, never()).delete(any());
    }
}