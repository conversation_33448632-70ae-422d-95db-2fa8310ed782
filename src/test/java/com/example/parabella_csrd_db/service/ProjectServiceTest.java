package com.example.parabella_csrd_db.service;

import com.example.parabella_csrd_db.dto.mithril.ProjectDTO;
import com.example.parabella_csrd_db.database.maindatabase.model.authentication.User;
import com.example.parabella_csrd_db.database.maindatabase.model.mithril.Company;
import com.example.parabella_csrd_db.database.maindatabase.model.mithril.CompanyGroup;
import com.example.parabella_csrd_db.database.maindatabase.model.mithril.Project;
import com.example.parabella_csrd_db.database.maindatabase.repository.authentication.UserRepository;
import com.example.parabella_csrd_db.database.maindatabase.repository.mithril.CompanyGroupRepository;
import com.example.parabella_csrd_db.database.maindatabase.repository.mithril.CompanyRepository;
import com.example.parabella_csrd_db.database.maindatabase.repository.mithril.ProjectRepository;
import com.example.parabella_csrd_db.service.mithril.ProjectService;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;


import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class ProjectServiceTest {

    @Mock
    private ProjectRepository projectRepository;
    @Mock
    private UserRepository userRepository;
    @Mock
    private CompanyRepository companyRepository;
    @Mock
    private CompanyGroupRepository companyGroupRepository;

    @Mock
    private SecurityContext securityContext;
    @Mock
    private Authentication authentication;
    @Mock
    private UserDetails userDetails;

    // Mock static SecurityContextHolder
    private MockedStatic<SecurityContextHolder> mockedSecurityContextHolder;


    @InjectMocks
    private ProjectService projectService;

    private User currentUser;
    private Project project1;
    private Project project2;
    private ProjectDTO projectDTO;
    private Company company;
    private CompanyGroup companyGroup;

    @BeforeEach
    void setUp() {
        currentUser = new User();
        currentUser.setId(1L);
        currentUser.setUsername("testuser");

        project1 = new Project();
        project1.setId(10L);
        project1.setProjectName("Project Alpha");
        project1.setProjectDescription("First test project");
        project1.setProjectType("company");
        project1.setCreatedAt(LocalDateTime.now().minusDays(1));
        project1.setUser(currentUser);
        project1.setCompanyId(100L);
        project1.setCompanyGroupId(null);

        project2 = new Project();
        project2.setId(11L);
        project2.setProjectName("Project Beta");
        project2.setProjectDescription("Second test project");
        project2.setProjectType("companyGroup");
        project2.setCreatedAt(LocalDateTime.now());
        project2.setUser(currentUser);
        project2.setCompanyId(null);
        project2.setCompanyGroupId(200L);

        projectDTO = new ProjectDTO();
        projectDTO.setId(10L); // Will be ignored in create, used in update
        projectDTO.setProjectName("DTO Project");
        projectDTO.setProjectDescription("DTO Description");
        projectDTO.setProjectType("company"); // Default type for DTO tests
        projectDTO.setUserId(currentUser.getId()); // Can be set, but service uses current user

        company = new Company();
        company.setId(100L);
        company.setCompanyName("Associated Company");
        company.setProject(project1);

        companyGroup = new CompanyGroup();
        companyGroup.setId(200L);

        companyGroup.setProject(project2);

        // --- Mock Security Context ---
        // Mock static method SecurityContextHolder.getContext()
        mockedSecurityContextHolder = mockStatic(SecurityContextHolder.class);
        mockedSecurityContextHolder.when(SecurityContextHolder::getContext).thenReturn(securityContext);
        when(securityContext.getAuthentication()).thenReturn(authentication);
        when(authentication.getPrincipal()).thenReturn(userDetails);
        when(userDetails.getUsername()).thenReturn(currentUser.getUsername());
        when(userRepository.findByUsername(currentUser.getUsername())).thenReturn(Optional.of(currentUser));
        // --- End Mock Security Context ---
    }

    @AfterEach
    void tearDown() {
        // Close the static mock
        mockedSecurityContextHolder.close();
    }


    @Test
    void findProjectsByUserId_whenProjectsExist_shouldReturnListOfDTOs() {
        // Arrange
        when(projectRepository.findByUserId(currentUser.getId())).thenReturn(List.of(project1, project2));

        // Act
        List<ProjectDTO> result = projectService.findProjectsByUserId(currentUser.getId());

        // Assert
        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals(project1.getId(), result.get(0).getId());
        assertEquals(project1.getProjectName(), result.get(0).getProjectName());
        assertEquals(project1.getCompanyId(), result.get(0).getCompanyId());
        assertEquals(project2.getId(), result.get(1).getId());
        assertEquals(project2.getProjectName(), result.get(1).getProjectName());
        assertEquals(project2.getCompanyGroupId(), result.get(1).getCompanyGroupId());
        verify(projectRepository, times(1)).findByUserId(currentUser.getId());
    }

    @Test
    void findProjectsByUserId_whenNoProjectsExist_shouldReturnEmptyList() {
        // Arrange
        when(projectRepository.findByUserId(currentUser.getId())).thenReturn(Collections.emptyList());

        // Act
        List<ProjectDTO> result = projectService.findProjectsByUserId(currentUser.getId());

        // Assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(projectRepository, times(1)).findByUserId(currentUser.getId());
    }

    @Test
    void findProjectDTOById_whenProjectExists_shouldReturnOptionalDTO() {
        // Arrange
        when(projectRepository.findById(project1.getId())).thenReturn(Optional.of(project1));

        // Act
        Optional<ProjectDTO> result = projectService.findProjectDTOById(project1.getId());

        // Assert
        assertTrue(result.isPresent());
        assertEquals(project1.getId(), result.get().getId());
        assertEquals(project1.getProjectName(), result.get().getProjectName());
        verify(projectRepository, times(1)).findById(project1.getId());
    }

    @Test
    void findProjectDTOById_whenProjectNotFound_shouldReturnEmptyOptional() {
        // Arrange
        when(projectRepository.findById(99L)).thenReturn(Optional.empty());

        // Act
        Optional<ProjectDTO> result = projectService.findProjectDTOById(99L);

        // Assert
        assertTrue(result.isEmpty());
        verify(projectRepository, times(1)).findById(99L);
    }

    @Test
    void createProject_whenTypeIsCompany_shouldCreateProjectAndCompany() {
        // Arrange
        projectDTO.setProjectType("company");
        ArgumentCaptor<Project> projectCaptor = ArgumentCaptor.forClass(Project.class);
        ArgumentCaptor<Company> companyCaptor = ArgumentCaptor.forClass(Company.class);

        Project savedProjectInitial = new Project(); // Simulate initial save
        savedProjectInitial.setId(12L);
        savedProjectInitial.setUser(currentUser);
        savedProjectInitial.setProjectName(projectDTO.getProjectName());
        savedProjectInitial.setProjectType(projectDTO.getProjectType());


        Company savedCompany = new Company();
        savedCompany.setId(101L);
        savedCompany.setProject(savedProjectInitial); // Linked project

        Project savedProjectFinal = savedProjectInitial; // Simulate second save
        savedProjectFinal.setCompanyId(savedCompany.getId()); // ID set


        when(projectRepository.save(projectCaptor.capture()))
                .thenReturn(savedProjectInitial) // First save call
                .thenReturn(savedProjectFinal);   // Second save call (after setting companyId)
        when(companyRepository.save(companyCaptor.capture())).thenReturn(savedCompany);


        // Act
        ProjectDTO createdProjectDTO = projectService.createProject(projectDTO);

        // Assert
        assertNotNull(createdProjectDTO);
        assertEquals(savedProjectInitial.getId(), createdProjectDTO.getId());
        assertEquals(projectDTO.getProjectName(), createdProjectDTO.getProjectName());
        assertEquals(projectDTO.getProjectType(), createdProjectDTO.getProjectType());
        assertEquals(savedCompany.getId(), createdProjectDTO.getCompanyId()); // Check company ID is set
        assertNull(createdProjectDTO.getCompanyGroupId());
        assertEquals(currentUser.getId(), createdProjectDTO.getUserId());

        // Verify captures and interactions
        List<Project> capturedProjects = projectCaptor.getAllValues();
        assertEquals(2, capturedProjects.size()); // Saved twice
        assertEquals(projectDTO.getProjectName(), capturedProjects.get(0).getProjectName());
        assertEquals(currentUser, capturedProjects.get(0).getUser());
        assertNull(capturedProjects.get(0).getCompanyId()); // Initial save has no company ID

        assertEquals(savedCompany.getId(), capturedProjects.get(1).getCompanyId()); // Second save has company ID

        Company capturedCompany = companyCaptor.getValue();
        assertNotNull(capturedCompany.getProject());
        assertEquals(savedProjectInitial.getId(), capturedCompany.getProject().getId()); // Company linked to project


        verify(userRepository, times(1)).findByUsername(currentUser.getUsername());
        verify(projectRepository, times(2)).save(any(Project.class)); // Saved twice
        verify(companyRepository, times(1)).save(any(Company.class));
        verify(companyGroupRepository, never()).save(any(CompanyGroup.class));
    }

    @Test
    void createProject_whenTypeIsCompanyGroup_shouldCreateProjectAndCompanyGroup() {
        // Arrange
        projectDTO.setProjectType("companyGroup");
        ArgumentCaptor<Project> projectCaptor = ArgumentCaptor.forClass(Project.class);
        ArgumentCaptor<CompanyGroup> groupCaptor = ArgumentCaptor.forClass(CompanyGroup.class);

        Project savedProjectInitial = new Project(); // Simulate initial save
        savedProjectInitial.setId(13L);
        savedProjectInitial.setUser(currentUser);
        savedProjectInitial.setProjectName(projectDTO.getProjectName());
        savedProjectInitial.setProjectType(projectDTO.getProjectType());


        CompanyGroup savedGroup = new CompanyGroup();
        savedGroup.setId(201L);
        savedGroup.setProject(savedProjectInitial); // Linked project

        Project savedProjectFinal = savedProjectInitial; // Simulate second save
        savedProjectFinal.setCompanyGroupId(savedGroup.getId()); // ID set

        when(projectRepository.save(projectCaptor.capture()))
                .thenReturn(savedProjectInitial) // First save
                .thenReturn(savedProjectFinal);   // Second save
        when(companyGroupRepository.save(groupCaptor.capture())).thenReturn(savedGroup);

        // Act
        ProjectDTO createdProjectDTO = projectService.createProject(projectDTO);

        // Assert
        assertNotNull(createdProjectDTO);
        assertEquals(savedProjectInitial.getId(), createdProjectDTO.getId());
        assertEquals(projectDTO.getProjectName(), createdProjectDTO.getProjectName());
        assertEquals(projectDTO.getProjectType(), createdProjectDTO.getProjectType());
        assertNull(createdProjectDTO.getCompanyId());
        assertEquals(savedGroup.getId(), createdProjectDTO.getCompanyGroupId()); // Check group ID is set
        assertEquals(currentUser.getId(), createdProjectDTO.getUserId());

        // Verify captures and interactions
        List<Project> capturedProjects = projectCaptor.getAllValues();
        assertEquals(2, capturedProjects.size()); // Saved twice
        assertEquals(projectDTO.getProjectName(), capturedProjects.get(0).getProjectName());
        assertEquals(currentUser, capturedProjects.get(0).getUser());
        assertNull(capturedProjects.get(0).getCompanyGroupId()); // Initial save has no group ID

        assertEquals(savedGroup.getId(), capturedProjects.get(1).getCompanyGroupId()); // Second save has group ID

        CompanyGroup capturedGroup = groupCaptor.getValue();
        assertNotNull(capturedGroup.getProject());
        assertEquals(savedProjectInitial.getId(), capturedGroup.getProject().getId()); // Group linked to project

        verify(userRepository, times(1)).findByUsername(currentUser.getUsername());
        verify(projectRepository, times(2)).save(any(Project.class));
        verify(companyRepository, never()).save(any(Company.class));
        verify(companyGroupRepository, times(1)).save(any(CompanyGroup.class));
    }

    @Test
    void createProject_whenInvalidType_shouldThrowException() {
        // Arrange
        projectDTO.setProjectType("invalidType");

        // Act & Assert
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () -> {
            projectService.createProject(projectDTO);
        });
        assertEquals("Invalid project type: invalidType", exception.getMessage());
        verify(projectRepository, never()).save(any());
        verify(companyRepository, never()).save(any());
        verify(companyGroupRepository, never()).save(any());
    }

    @Test
    void createProject_whenUserNotFound_shouldThrowException() {
        // Arrange
        when(userRepository.findByUsername(anyString())).thenReturn(Optional.empty());

        // Act & Assert
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () -> {
            projectService.createProject(projectDTO);
        });
        assertTrue(exception.getMessage().startsWith("User not found with username:"));
        verify(projectRepository, never()).save(any());
    }

    @Test
    void updateProject_whenValid_shouldUpdateAndReturnDTO() {
        // Arrange
        Long projectId = project1.getId();
        projectDTO.setId(projectId); // Set ID for update
        projectDTO.setProjectName("Updated Name");
        projectDTO.setProjectDescription("Updated Desc");
        // Project type and company/group links are generally not updated here

        when(projectRepository.findById(projectId)).thenReturn(Optional.of(project1));
        when(projectRepository.save(any(Project.class))).thenAnswer(inv -> inv.getArgument(0));

        // Act
        ProjectDTO updatedDTO = projectService.updateProject(projectDTO);

        // Assert
        assertNotNull(updatedDTO);
        assertEquals(projectId, updatedDTO.getId());
        assertEquals("Updated Name", updatedDTO.getProjectName());
        assertEquals("Updated Desc", updatedDTO.getProjectDescription());
        assertEquals(project1.getProjectType(), updatedDTO.getProjectType()); // Type shouldn't change
        assertEquals(project1.getCompanyId(), updatedDTO.getCompanyId()); // Link shouldn't change
        assertEquals(project1.getUser().getId(), updatedDTO.getUserId());

        verify(projectRepository, times(1)).findById(projectId);
        verify(userRepository, times(1)).findByUsername(currentUser.getUsername());
        verify(projectRepository, times(1)).save(argThat(p ->
                p.getId().equals(projectId) &&
                        p.getProjectName().equals("Updated Name") &&
                        p.getProjectDescription().equals("Updated Desc") &&
                        p.getUser().getId().equals(currentUser.getId()) // Ensure user is retained
        ));
    }

    @Test
    void updateProject_whenIdIsNull_shouldThrowException() {
        // Arrange
        projectDTO.setId(null);

        // Act & Assert
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () -> {
            projectService.updateProject(projectDTO);
        });
        assertEquals("Project ID must not be null for update operation.", exception.getMessage());
        verify(projectRepository, never()).findById(any());
        verify(projectRepository, never()).save(any());
    }

    @Test
    void updateProject_whenProjectNotFound_shouldThrowException() {
        // Arrange
        Long nonExistentId = 99L;
        projectDTO.setId(nonExistentId);
        when(projectRepository.findById(nonExistentId)).thenReturn(Optional.empty());

        // Act & Assert
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () -> {
            projectService.updateProject(projectDTO);
        });
        assertEquals("Project not found with ID: " + nonExistentId, exception.getMessage());
        verify(projectRepository, times(1)).findById(nonExistentId);
        verify(projectRepository, never()).save(any());
    }

    @Test
    void updateProject_whenUserNotOwner_shouldThrowSecurityException() {
        // Arrange
        Long projectId = project1.getId();
        projectDTO.setId(projectId);

        User anotherUser = new User();
        anotherUser.setId(2L);
        anotherUser.setUsername("otheruser");
        project1.setUser(anotherUser); // Project owned by someone else

        when(projectRepository.findById(projectId)).thenReturn(Optional.of(project1));
        // Current user mock remains 'testuser' (ID 1L)

        // Act & Assert
        SecurityException exception = assertThrows(SecurityException.class, () -> {
            projectService.updateProject(projectDTO);
        });
        assertEquals("You do not have permission to update this project.", exception.getMessage());
        verify(projectRepository, times(1)).findById(projectId);
        verify(userRepository, times(1)).findByUsername(currentUser.getUsername());
        verify(projectRepository, never()).save(any());
    }

    @Test
    void deleteProject_whenCompanyTypeAndOwned_shouldDeleteProjectAndCompany() {
        // Arrange
        Long projectId = project1.getId(); // project1 is 'company' type
        Long companyId = project1.getCompanyId();

        when(projectRepository.findById(projectId)).thenReturn(Optional.of(project1));
        doNothing().when(companyRepository).deleteById(companyId);
        doNothing().when(projectRepository).delete(project1);


        // Act
        projectService.deleteProject(projectId);

        // Assert
        verify(projectRepository, times(1)).findById(projectId);
        verify(userRepository, times(1)).findByUsername(currentUser.getUsername());
        verify(companyRepository, times(1)).deleteById(companyId);
        verify(companyGroupRepository, never()).deleteById(anyLong());
        verify(projectRepository, times(1)).delete(project1);
    }

    @Test
    void deleteProject_whenGroupTypeAndOwned_shouldDeleteProjectAndGroup() {
        // Arrange
        Long projectId = project2.getId(); // project2 is 'companyGroup' type
        Long groupId = project2.getCompanyGroupId();

        when(projectRepository.findById(projectId)).thenReturn(Optional.of(project2));
        doNothing().when(companyGroupRepository).deleteById(groupId);
        doNothing().when(projectRepository).delete(project2);

        // Act
        projectService.deleteProject(projectId);

        // Assert
        verify(projectRepository, times(1)).findById(projectId);
        verify(userRepository, times(1)).findByUsername(currentUser.getUsername());
        verify(companyRepository, never()).deleteById(anyLong());
        verify(companyGroupRepository, times(1)).deleteById(groupId);
        verify(projectRepository, times(1)).delete(project2);
    }

    @Test
    void deleteProject_whenProjectNotFound_shouldThrowException() {
        // Arrange
        Long nonExistentId = 99L;
        when(projectRepository.findById(nonExistentId)).thenReturn(Optional.empty());

        // Act & Assert
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () -> {
            projectService.deleteProject(nonExistentId);
        });
        assertEquals("Project not found with ID: " + nonExistentId, exception.getMessage());
        verify(projectRepository, times(1)).findById(nonExistentId);
        verify(projectRepository, never()).delete(any());
        verify(companyRepository, never()).deleteById(any());
        verify(companyGroupRepository, never()).deleteById(any());
    }

    @Test
    void deleteProject_whenUserNotOwner_shouldThrowSecurityException() {
        // Arrange
        Long projectId = project1.getId();
        User anotherUser = new User();
        anotherUser.setId(1L);
        anotherUser.setUsername("otheruser");
        project1.setUser(anotherUser); // Project owned by someone else

        when(projectRepository.findById(projectId)).thenReturn(Optional.of(project1));
        // Current user mock remains 'testuser' (ID 1L)

        // Act & Assert
        SecurityException exception = assertThrows(SecurityException.class, () -> {
            projectService.deleteProject(projectId);
        });
        assertEquals("You do not have permission to delete this project.", exception.getMessage());
        verify(projectRepository, times(1)).findById(projectId);
        verify(userRepository, times(1)).findByUsername(currentUser.getUsername());
        verify(projectRepository, never()).delete(any());
        verify(companyRepository, never()).deleteById(any());
        verify(companyGroupRepository, never()).deleteById(any());
    }

    @Test
    void copyProject_whenCompanyType_shouldCopyProjectAndCompany() {
        // Arrange
        Long originalProjectId = project1.getId();
        Long originalCompanyId = project1.getCompanyId();

        Project newSavedProjectInitial = new Project();
        newSavedProjectInitial.setId(15L); // New ID
        newSavedProjectInitial.setProjectName(project1.getProjectName() + " - Copy");
        newSavedProjectInitial.setProjectType(project1.getProjectType());
        newSavedProjectInitial.setUser(currentUser);

        Company originalCompany = new Company();
        originalCompany.setId(originalCompanyId);
        originalCompany.setProject(project1);
        //... other company properties would be here

        Company newSavedCompany = new Company();
        newSavedCompany.setId(105L); // New company ID
        newSavedCompany.setProject(newSavedProjectInitial);

        Project newSavedProjectFinal = newSavedProjectInitial;
        newSavedProjectFinal.setCompanyId(newSavedCompany.getId()); // Set link in final project save

        when(projectRepository.findById(originalProjectId)).thenReturn(Optional.of(project1));
        when(companyRepository.findById(originalCompanyId)).thenReturn(Optional.of(originalCompany));
        when(projectRepository.save(any(Project.class)))
                .thenReturn(newSavedProjectInitial) // First save (new project)
                .thenReturn(newSavedProjectFinal); // Second save (with company ID)
        when(companyRepository.save(any(Company.class))).thenReturn(newSavedCompany);

        // Act
        ProjectDTO copiedDTO = projectService.copyProject(originalProjectId);

        // Assert
        assertNotNull(copiedDTO);
        assertEquals(newSavedProjectInitial.getId(), copiedDTO.getId());
        assertEquals(project1.getProjectName() + " - Copy", copiedDTO.getProjectName());
        assertEquals(project1.getProjectType(), copiedDTO.getProjectType());
        assertEquals(currentUser.getId(), copiedDTO.getUserId());
        assertEquals(newSavedCompany.getId(), copiedDTO.getCompanyId());
        assertNull(copiedDTO.getCompanyGroupId());

        verify(projectRepository, times(1)).findById(originalProjectId);
        verify(userRepository, times(1)).findByUsername(currentUser.getUsername());
        verify(companyRepository, times(1)).findById(originalCompanyId);
        verify(projectRepository, times(2)).save(any(Project.class));
        verify(companyRepository, times(1)).save(argThat(c -> c.getProject() != null && c.getProject().getId().equals(newSavedProjectInitial.getId())));
        verify(companyGroupRepository, never()).findById(anyLong());
        verify(companyGroupRepository, never()).save(any(CompanyGroup.class));
    }

    @Test
    void copyProject_whenGroupType_shouldCopyProjectAndGroup() {
        // Arrange
        Long originalProjectId = project2.getId();
        Long originalGroupId = project2.getCompanyGroupId();

        Project newSavedProjectInitial = new Project();
        newSavedProjectInitial.setId(16L); // New ID
        newSavedProjectInitial.setProjectName(project2.getProjectName() + " - Copy");
        newSavedProjectInitial.setProjectType(project2.getProjectType());
        newSavedProjectInitial.setUser(currentUser);

        CompanyGroup originalGroup = new CompanyGroup();
        originalGroup.setId(originalGroupId);
        originalGroup.setProject(project2);
        //... other group properties

        CompanyGroup newSavedGroup = new CompanyGroup();
        newSavedGroup.setId(205L); // New group ID
        newSavedGroup.setProject(newSavedProjectInitial);

        Project newSavedProjectFinal = newSavedProjectInitial;
        newSavedProjectFinal.setCompanyGroupId(newSavedGroup.getId()); // Set link in final project save

        when(projectRepository.findById(originalProjectId)).thenReturn(Optional.of(project2));
        when(companyGroupRepository.findById(originalGroupId)).thenReturn(Optional.of(originalGroup));
        when(projectRepository.save(any(Project.class)))
                .thenReturn(newSavedProjectInitial) // First save
                .thenReturn(newSavedProjectFinal); // Second save
        when(companyGroupRepository.save(any(CompanyGroup.class))).thenReturn(newSavedGroup);

        // Act
        ProjectDTO copiedDTO = projectService.copyProject(originalProjectId);

        // Assert
        assertNotNull(copiedDTO);
        assertEquals(newSavedProjectInitial.getId(), copiedDTO.getId());
        assertEquals(project2.getProjectName() + " - Copy", copiedDTO.getProjectName());
        assertEquals(project2.getProjectType(), copiedDTO.getProjectType());
        assertEquals(currentUser.getId(), copiedDTO.getUserId());
        assertNull(copiedDTO.getCompanyId());
        assertEquals(newSavedGroup.getId(), copiedDTO.getCompanyGroupId());

        verify(projectRepository, times(1)).findById(originalProjectId);
        verify(userRepository, times(1)).findByUsername(currentUser.getUsername());
        verify(companyGroupRepository, times(1)).findById(originalGroupId);
        verify(projectRepository, times(2)).save(any(Project.class));
        verify(companyGroupRepository, times(1)).save(argThat(cg -> cg.getProject() != null && cg.getProject().getId().equals(newSavedProjectInitial.getId())));
        verify(companyRepository, never()).findById(anyLong());
        verify(companyRepository, never()).save(any(Company.class));
    }

    @Test
    void copyProject_whenOriginalProjectNotFound_shouldThrowException() {
        // Arrange
        Long nonExistentId = 99L;
        when(projectRepository.findById(nonExistentId)).thenReturn(Optional.empty());

        // Act & Assert
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () -> {
            projectService.copyProject(nonExistentId);
        });
        assertEquals("Project not found with ID: " + nonExistentId, exception.getMessage());
        verify(projectRepository, times(1)).findById(nonExistentId);
        verify(projectRepository, never()).save(any());
    }

    @Test
    void copyProject_whenOriginalCompanyNotFound_shouldThrowException() {
        // Arrange
        Long originalProjectId = project1.getId(); // Company type
        Long originalCompanyId = project1.getCompanyId();

        when(projectRepository.findById(originalProjectId)).thenReturn(Optional.of(project1));
        when(companyRepository.findById(originalCompanyId)).thenReturn(Optional.empty()); // Company missing

        Project newSavedProjectInitial = new Project(); // Still need to mock the first save
        newSavedProjectInitial.setId(15L);
        when(projectRepository.save(any(Project.class))).thenReturn(newSavedProjectInitial);


        // Act & Assert
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () -> {
            projectService.copyProject(originalProjectId);
        });
        assertEquals("Company not found", exception.getMessage()); // Assuming this message
        verify(projectRepository, times(1)).findById(originalProjectId);
        verify(projectRepository, times(1)).save(any(Project.class)); // Saves the new project first
        verify(companyRepository, times(1)).findById(originalCompanyId);
        verify(companyRepository, never()).save(any(Company.class)); // Fails before saving new company
        verify(projectRepository, times(1)).save(any(Project.class)); // Only the initial project save happens
    }

}