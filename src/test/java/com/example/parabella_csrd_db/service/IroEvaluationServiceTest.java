//package com.example.parabella_csrd_db.service;
//
//import com.example.parabella_csrd_db.dto.mithril.IroEvaluationDTO;
//import com.example.parabella_csrd_db.maindatabase.mithril.model.Company;
//import com.example.parabella_csrd_db.maindatabase.mithril.model.EsrsTopic;
//import com.example.parabella_csrd_db.maindatabase.mithril.model.IroEvaluation;
//import com.example.parabella_csrd_db.maindatabase.mithril.repository.CompanyRepository;
//import com.example.parabella_csrd_db.maindatabase.mithril.repository.IroEvaluationRepository;
//import com.example.parabella_csrd_db.maindatabase.mithril.repository.EsrsTopicsRepository;
//import com.example.parabella_csrd_db.service.mithril.IroEvaluationService;
//import org.junit.jupiter.api.BeforeEach;
//import org.junit.jupiter.api.Test;
//import org.junit.jupiter.api.extension.ExtendWith;
//import org.mockito.InjectMocks;
//import org.mockito.Mock;
//import org.mockito.junit.jupiter.MockitoExtension;
//
//import java.util.List;
//import java.util.Optional;
//
//import static org.assertj.core.api.Assertions.assertThat;
//import static org.junit.jupiter.api.Assertions.assertThrows;
//import static org.mockito.ArgumentMatchers.any;
//import static org.mockito.Mockito.*;
//
//@ExtendWith(MockitoExtension.class)
//class IroEvaluationServiceTest {
//
//    @Mock
//    private IroEvaluationRepository esrsTopicDetailsRepository;
//    @Mock
//    private EsrsTopicsRepository esrsTopicRepository;
//    @Mock
//    private CompanyRepository companyRepository;
//
//    @InjectMocks
//    private IroEvaluationService iroEvaluationService;
//
//    private IroEvaluationDTO testDTO;
//    private Company testCompany;
//    private EsrsTopic testTopic;
//    private IroEvaluation testDetails;
//
//    @BeforeEach
//    void setUp() {
//        testCompany = new Company();
//        testCompany.setId(1L);
//
//        testTopic = new EsrsTopic();
//        testTopic.setId(1L);
//
//        testDetails = new IroEvaluation();
//        testDetails.setId(1L);
//        testDetails.setCompany(testCompany);
//        testDetails.setActualPotentialImpact("risk");
//        testDetails.setScale(3);
//
//        testDTO = new IroEvaluationDTO();
//        testDTO.setCompanyId(1L);
//        testDTO.setEsrsTopicId(1L);
//        testDTO.setRiskChance("risk");
//        testDTO.setScale(3);
//    }
//
//    @Test
//    void createOrUpdateEsrsTopicDetails_WhenNew_ShouldCreate() {
//        // Arrange
//        when(companyRepository.findById(1L)).thenReturn(Optional.of(testCompany));
//        when(esrsTopicRepository.findById(1L)).thenReturn(Optional.of(testTopic));
//        when(esrsTopicDetailsRepository.findByCompanyAndEsrsTopic(testCompany, testTopic))
//                .thenReturn(Optional.empty());
//        when(esrsTopicDetailsRepository.save(any())).thenReturn(testDetails);
//
//        // Act
//        IroEvaluationDTO result = iroEvaluationService
//                .createOrUpdateEsrsTopicDetails(testDTO, "testUser");
//
//        // Assert
//        assertThat(result).isNotNull();
//        assertThat(result.getRiskChance()).isEqualTo("risk");
//        verify(esrsTopicDetailsRepository).save(any());
//    }
//
//    @Test
//    void createOrUpdateEsrsTopicDetails_WhenExisting_ShouldUpdate() {
//        // Arrange
//        when(companyRepository.findById(1L)).thenReturn(Optional.of(testCompany));
//        when(esrsTopicRepository.findById(1L)).thenReturn(Optional.of(testTopic));
//        when(esrsTopicDetailsRepository.findByCompanyAndEsrsTopic(testCompany, testTopic))
//                .thenReturn(Optional.of(testDetails));
//        when(esrsTopicDetailsRepository.save(any())).thenReturn(testDetails);
//
//        // Act
//        IroEvaluationDTO result = iroEvaluationService
//                .createOrUpdateEsrsTopicDetails(testDTO, "testUser");
//
//        // Assert
//        assertThat(result).isNotNull();
//        verify(esrsTopicDetailsRepository).save(any());
//    }
//
//    @Test
//    void getEsrsTopicDetailsByCompanyId_ShouldReturnList() {
//        // Arrange
//        when(esrsTopicDetailsRepository.findByCompanyId(1L))
//                .thenReturn(List.of(testDetails));
//
//        // Act
//        List<IroEvaluationDTO> results = iroEvaluationService.getEsrsTopicDetailsByCompanyId(1L);
//
//        // Assert
//        assertThat(results).hasSize(1);
//        assertThat(results.get(0).getRiskChance()).isEqualTo("risk");
//    }
//}
