// ValueChainObjectServiceTest.java
package com.example.parabella_csrd_db.service;

import com.example.parabella_csrd_db.dto.mithril.ValueChainObjectDTO;
import com.example.parabella_csrd_db.database.maindatabase.model.mithril.Company;
import com.example.parabella_csrd_db.database.maindatabase.model.mithril.ValueChainObject;
import com.example.parabella_csrd_db.database.maindatabase.repository.mithril.CompanyRepository;
import com.example.parabella_csrd_db.database.maindatabase.repository.mithril.ValueChainObjectRepository;
import com.example.parabella_csrd_db.service.mithril.ValueChainObjectService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Optional;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class ValueChainObjectServiceTest {

    @Mock
    private ValueChainObjectRepository valueChainObjectRepository;

    @Mock
    private CompanyRepository companyRepository;

    @InjectMocks
    private ValueChainObjectService valueChainObjectService;

    private ValueChainObjectDTO testDTO;
    private Company testCompany;
    private ValueChainObject testVCO;

    @BeforeEach
    void setUp() {
        testCompany = new Company();
        testCompany.setId(1L);
        testCompany.setCompanyName("Test Company");

        testDTO = new ValueChainObjectDTO();
        testDTO.setName("Test VCO");
        testDTO.setIndustry("Manufacturing");
        testDTO.setCompanyId(1L);

        testVCO = new ValueChainObject();
        testVCO.setId(1L);
        testVCO.setName("Test VCO");
        testVCO.setIndustry("Manufacturing");
        testVCO.setCompany(testCompany);
    }

    @Test
    void createValueChainObject_WithValidData_ShouldCreateVCO() {
        // Arrange
        when(companyRepository.findById(1L)).thenReturn(Optional.of(testCompany));
        when(valueChainObjectRepository.save(any(ValueChainObject.class))).thenReturn(testVCO);

        // Act
        ValueChainObjectDTO result = valueChainObjectService.createValueChainObject(testDTO);

        // Assert
        assertThat(result).isNotNull();
        assertThat(result.getName()).isEqualTo(testDTO.getName());
        assertThat(result.getCompanyId()).isEqualTo(testDTO.getCompanyId());
        verify(valueChainObjectRepository).save(any(ValueChainObject.class));
    }

    @Test
    void createValueChainObject_WithInvalidCompanyId_ShouldThrowException() {
        // Arrange
        when(companyRepository.findById(1L)).thenReturn(Optional.empty());

        // Act & Assert
        assertThrows(RuntimeException.class, () ->
                valueChainObjectService.createValueChainObject(testDTO));
    }

    @Test
    void updateValueChainObject_WithValidData_ShouldUpdateVCO() {
        // Arrange
        when(valueChainObjectRepository.findById(1L)).thenReturn(Optional.of(testVCO));
        when(companyRepository.findById(1L)).thenReturn(Optional.of(testCompany));
        when(valueChainObjectRepository.save(any(ValueChainObject.class))).thenReturn(testVCO);

        // Act
        ValueChainObjectDTO result = valueChainObjectService.updateValueChainObject(1L, testDTO);

        // Assert
        assertThat(result).isNotNull();
        assertThat(result.getName()).isEqualTo(testDTO.getName());
        verify(valueChainObjectRepository).save(any(ValueChainObject.class));
    }
}