//package com.example.parabella_csrd_db.service;
//
//import com.example.parabella_csrd_db.dto.mithril.EsrsTopicSelectionDTO;
//import com.example.parabella_csrd_db.maindatabase.mithril.model.Company;
//import com.example.parabella_csrd_db.maindatabase.mithril.model.EsrsTopic;
//import com.example.parabella_csrd_db.maindatabase.mithril.model.EsrsTopicSelection;
//import com.example.parabella_csrd_db.maindatabase.mithril.model.Stakeholder;
//import com.example.parabella_csrd_db.maindatabase.mithril.repository.CompanyRepository;
//import com.example.parabella_csrd_db.maindatabase.mithril.repository.EsrsTopicSelectionRepository;
//import com.example.parabella_csrd_db.maindatabase.mithril.repository.EsrsTopicsRepository;
//import com.example.parabella_csrd_db.maindatabase.stakeholder_navigation.mithril.repository.StakeholderRepository;
//import com.example.parabella_csrd_db.service.mithril.EsrsTopicSelectionService;
//import org.junit.jupiter.api.BeforeEach;
//import org.junit.jupiter.api.Test;
//import org.junit.jupiter.api.extension.ExtendWith;
//import org.mockito.InjectMocks;
//import org.mockito.Mock;
//import org.mockito.junit.jupiter.MockitoExtension;
//
//import java.util.List;
//import java.util.Optional;
//
//import static org.assertj.core.api.Assertions.assertThat;
//import static org.junit.jupiter.api.Assertions.assertThrows;
//import static org.mockito.ArgumentMatchers.any;
//import static org.mockito.Mockito.*;
//
//@ExtendWith(MockitoExtension.class)
//class EsrsTopicSelectionServiceTest {
//
//    @Mock
//    private EsrsTopicSelectionRepository esrsTopicSelectionRepository;
//    @Mock
//    private EsrsTopicsRepository esrsTopicRepository;
//    @Mock
//    private StakeholderRepository stakeholderRepository;
//    @Mock
//    private CompanyRepository companyRepository;
//
//    @InjectMocks
//    private EsrsTopicSelectionService esrsTopicSelectionService;
//
//    private EsrsTopicSelectionDTO testDTO;
//    private Company testCompany;
//    private EsrsTopic testTopic;
//    private Stakeholder testStakeholder;
//    private EsrsTopicSelection testSelection;
//
//    @BeforeEach
//    void setUp() {
//        testCompany = new Company();
//        testCompany.setId(1L);
//
//        testTopic = new EsrsTopic();
//        testTopic.setId(1L);
//
//        testStakeholder = new Stakeholder();
//        testStakeholder.setId(1L);
//
//        testDTO = new EsrsTopicSelectionDTO();
//        testDTO.setCompanyId(1L);
//        testDTO.setEsrsTopicId(1L);
//        testDTO.setStakeholderId(1L);
//        testDTO.setRelevant(true);
//
//        testSelection = new EsrsTopicSelection();
//        testSelection.setId(1L);
//        testSelection.setCompany(testCompany);
//        testSelection.setEsrsTopic(testTopic);
//        testSelection.setStakeholder(testStakeholder);
//        testSelection.setRelevant(true);
//    }
//
//    @Test
//    void createEsrsTopicSelection_WithValidData_ShouldCreateSelection() {
//        // Arrange
//        when(companyRepository.findById(1L)).thenReturn(Optional.of(testCompany));
//        when(esrsTopicRepository.findById(1L)).thenReturn(Optional.of(testTopic));
//        when(stakeholderRepository.findById(1L)).thenReturn(Optional.of(testStakeholder));
//        when(esrsTopicSelectionRepository.save(any())).thenReturn(testSelection);
//
//        // Act
//        EsrsTopicSelectionDTO result = esrsTopicSelectionService.createEsrsTopicSelection(testDTO);
//
//        // Assert
//        assertThat(result).isNotNull();
//        assertThat(result.getRelevant()).isTrue();
//        verify(esrsTopicSelectionRepository).save(any());
//    }
//
//    @Test
//    void getSelectionsByStakeholderId_ShouldReturnList() {
//        // Arrange
//        when(esrsTopicSelectionRepository.findByStakeholderId(1L))
//                .thenReturn(List.of(testSelection));
//
//        // Act
//        List<EsrsTopicSelectionDTO> results = esrsTopicSelectionService.getSelectionsByStakeholderId(1L);
//
//        // Assert
//        assertThat(results).hasSize(1);
//        assertThat(results.get(0).getStakeholderId()).isEqualTo(1L);
//    }
//}