//package com.example.parabella_csrd_db.service;
//
//import com.example.parabella_csrd_db.dto.mithril.StakeholderDTO;
//import com.example.parabella_csrd_db.maindatabase.mithril.model.Company;
//import com.example.parabella_csrd_db.maindatabase.mithril.model.Project;
//import com.example.parabella_csrd_db.maindatabase.mithril.model.Stakeholder;
//import com.example.parabella_csrd_db.maindatabase.mithril.model.StakeholderStatus;
//import com.example.parabella_csrd_db.maindatabase.mithril.repository.CompanyRepository;
//import com.example.parabella_csrd_db.maindatabase.mithril.repository.ProjectRepository;
//import com.example.parabella_csrd_db.maindatabase.stakeholder_navigation.mithril.repository.StakeholderRepository;
//import com.example.parabella_csrd_db.service.mithril.StakeholderService;
//import jakarta.mail.internet.MimeMessage;
//import org.junit.jupiter.api.BeforeEach;
//import org.junit.jupiter.api.Test;
//import org.junit.jupiter.api.extension.ExtendWith;
//import org.mockito.InjectMocks;
//import org.mockito.Mock;
//import org.mockito.junit.jupiter.MockitoExtension;
//import org.springframework.mail.javamail.JavaMailSender;
//import org.springframework.test.util.ReflectionTestUtils;
//
//import java.util.List;
//import java.util.Optional;
//
//import static org.assertj.core.api.Assertions.assertThat;
//import static org.junit.jupiter.api.Assertions.assertThrows;
//import static org.mockito.ArgumentMatchers.any;
//import static org.mockito.Mockito.*;
//
//@ExtendWith(MockitoExtension.class)
//class StakeholderServiceTest {
//
//    @Mock
//    private StakeholderRepository stakeholderRepository;
//    @Mock
//    private CompanyRepository companyRepository;
//    @Mock
//    private ProjectRepository projectRepository;
//    @Mock
//    private JavaMailSender emailSender;
//
//    @InjectMocks
//    private StakeholderService stakeholderService;
//
//    private StakeholderDTO testDTO;
//    private Stakeholder testStakeholder;
//    private Company testCompany;
//    private Project testProject;
//
//    @BeforeEach
//    void setUp() {
//        ReflectionTestUtils.setField(stakeholderService, "frontendUrl", "http://localhost:3000");
//        ReflectionTestUtils.setField(stakeholderService, "frontendBasePath", "/");
//
//        testCompany = new Company();
//        testCompany.setId(1L);
//        testCompany.setCompanyName("Test Company");
//
//        testProject = new Project();
//        testProject.setId(1L);
//
//        testDTO = new StakeholderDTO();
//        testDTO.setName("Test Stakeholder");
//        testDTO.setEmail("<EMAIL>");
//        testDTO.setCompanyId(1L);
//        testDTO.setProjectId(1L);
//        testDTO.setRole("TEST_ROLE");
//        testDTO.setStatus(StakeholderStatus.INVITED);
//
//        testStakeholder = new Stakeholder();
//        testStakeholder.setId(1L);
//        testStakeholder.setName("Test Stakeholder");
//        testStakeholder.setEmail("<EMAIL>");
//        testStakeholder.setCompany(testCompany);
//        testStakeholder.setProject(testProject);
//        testStakeholder.setStatus(StakeholderStatus.INVITED);
//    }
//
//    @Test
//    void createStakeholder_WithValidData_ShouldCreateStakeholder() {
//        // Arrange
//        when(companyRepository.findById(1L)).thenReturn(Optional.of(testCompany));
//        when(projectRepository.findById(1L)).thenReturn(Optional.of(testProject));
//        when(stakeholderRepository.save(any(Stakeholder.class))).thenReturn(testStakeholder);
//
//        // Act
//        StakeholderDTO result = stakeholderService.createStakeholder(testDTO);
//
//        // Assert
//        assertThat(result).isNotNull();
//        assertThat(result.getName()).isEqualTo(testDTO.getName());
//        assertThat(result.getToken()).isNotNull();
//        verify(stakeholderRepository).save(any(Stakeholder.class));
//    }
//
//    @Test
//    void sendStakeholderEmails_ShouldSendEmailsAndUpdateStatus() {
//        // Arrange
//        testStakeholder.setToken(null);
//        when(stakeholderRepository.findById(1L)).thenReturn(Optional.of(testStakeholder));
//        when(stakeholderRepository.findByToken(anyString())).thenReturn(Optional.empty());
//        when(stakeholderRepository.save(any(Stakeholder.class))).thenReturn(testStakeholder);
//
//        // Act
//        stakeholderService.sendStakeholderEmails(List.of(testDTO));
//
//        // Assert
//        verify(emailSender).send((MimeMessage) any());
//        verify(stakeholderRepository).save(argThat(stakeholder ->
//                stakeholder.getStatus() == StakeholderStatus.INVITED &&
//                        stakeholder.getToken() != null
//        ));
//    }
//
//    @Test
//    void getStakeholderByToken_ShouldReturnStakeholder() {
//        // Arrange
//        when(stakeholderRepository.findByToken("test-token")).thenReturn(Optional.of(testStakeholder));
//
//        // Act
//        StakeholderDTO result = stakeholderService.getStakeholderByToken("test-token");
//
//        // Assert
//        assertThat(result).isNotNull();
//        assertThat(result.getName()).isEqualTo(testStakeholder.getName());
//    }
//}