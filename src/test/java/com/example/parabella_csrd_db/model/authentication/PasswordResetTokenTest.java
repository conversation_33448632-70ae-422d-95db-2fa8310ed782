package com.example.parabella_csrd_db.model.authentication;

import com.example.parabella_csrd_db.database.maindatabase.model.authentication.PasswordResetToken;
import com.example.parabella_csrd_db.database.maindatabase.model.authentication.User;
import org.junit.jupiter.api.Test;

import java.time.Instant;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * Unit tests for the PasswordResetToken entity class.
 */
class PasswordResetTokenTest {

    @Test
    void testPasswordResetTokenSettersAndGetters() {
        // Arrange
        PasswordResetToken token = new PasswordResetToken();
        User user = new User("testuser", "<EMAIL>", "password");
        Instant expiryDate = Instant.now().plusSeconds(3600); // 1 hour from now

        // Act
        token.setId(1L);
        token.setToken("reset-token-123");
        token.setUser(user);
        token.setExpiryDate(expiryDate);

        // Assert
        assertThat(token.getId()).isEqualTo(1L);
        assertThat(token.getToken()).isEqualTo("reset-token-123");
        assertThat(token.getUser()).isEqualTo(user);
        assertThat(token.getExpiryDate()).isEqualTo(expiryDate);
    }

    @Test
    void testDefaultConstructor() {
        // Arrange & Act
        PasswordResetToken token = new PasswordResetToken();

        // Assert
        assertThat(token).isNotNull();
        assertThat(token.getId()).isNull();
        assertThat(token.getToken()).isNull();
        assertThat(token.getUser()).isNull();
        assertThat(token.getExpiryDate()).isNull();
    }
}
