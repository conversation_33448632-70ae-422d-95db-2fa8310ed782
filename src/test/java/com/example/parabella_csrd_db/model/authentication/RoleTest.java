//package com.example.parabella_csrd_db.model.authentication;
//
//import com.example.parabella_csrd_db.database.maindatabase.model.authentication.ERole;
//import com.example.parabella_csrd_db.database.maindatabase.model.authentication.Role;
//import org.junit.jupiter.api.Test;
//
//import static org.assertj.core.api.Assertions.assertThat;
//
///**
// * Unit tests for the Role entity class.
// */
//class RoleTest {
//
//    @Test
//    void testRoleCreation() {
//        // Arrange & Act
//        Role role = new Role(ERole.ROLE_ADMIN);
//
//        // Assert
//        assertThat(role.getName()).isEqualTo(ERole.ROLE_ADMIN);
//    }
//
//    @Test
//    void testRoleSettersAndGetters() {
//        // Arrange
//        Role role = new Role();
//
//        // Act
//        role.setId(1);
//        role.setName(ERole.ROLE_USER);
//
//        // Assert
//        assertThat(role.getId()).isEqualTo(1);
//        assertThat(role.getName()).isEqualTo(ERole.ROLE_USER);
//    }
//
//    @Test
//    void testDefaultConstructor() {
//        // Arrange & Act
//        Role role = new Role();
//
//        // Assert
//        assertThat(role).isNotNull();
//        assertThat(role.getId()).isNull();
//        assertThat(role.getName()).isNull();
//    }
//}
