//package com.example.parabella_csrd_db.model.authentication;
//
//import com.example.parabella_csrd_db.database.maindatabase.model.authentication.ERole;
//import com.example.parabella_csrd_db.database.maindatabase.model.authentication.Role;
//import com.example.parabella_csrd_db.database.maindatabase.model.authentication.User;
//import org.junit.jupiter.api.Test;
//import java.util.HashSet;
//import java.util.Set;
//
//import static org.assertj.core.api.Assertions.assertThat;
//
///**
// * Unit tests for the User entity class.
// */
//class UserTest {
//
//    @Test
//    void testUserCreation() {
//        // Arrange
//        String username = "testuser";
//        String email = "<EMAIL>";
//        String password = "password123";
//
//        // Act
//        User user = new User(username, email, password);
//
//        // Assert
//        assertThat(user.getUsername()).isEqualTo(username);
//        assertThat(user.getEmail()).isEqualTo(email);
//        assertThat(user.getPassword()).isEqualTo(password);
//        assertThat(user.getRoles()).isNotNull().isEmpty();
//    }
//
//    @Test
//    void testUserRoles() {
//        // Arrange
//        User user = new User("testuser", "<EMAIL>", "password123");
//        Role adminRole = new Role(ERole.ROLE_ADMIN);
//        Role userRole = new Role(ERole.ROLE_USER);
//        Set<Role> roles = new HashSet<>();
//        roles.add(adminRole);
//        roles.add(userRole);
//
//        // Act
//        user.setRoles(roles);
//
//        // Assert
//        assertThat(user.getRoles()).hasSize(2);
//        assertThat(user.getRoles()).contains(adminRole, userRole);
//    }
//
//    @Test
//    void testUserSettersAndGetters() {
//        // Arrange
//        User user = new User();
//
//        // Act
//        user.setId(1L);
//        user.setUsername("testuser");
//        user.setEmail("<EMAIL>");
//        user.setPassword("password123");
//        user.setTotpSecret("TOTP123456");
//
//        // Assert
//        assertThat(user.getId()).isEqualTo(1L);
//        assertThat(user.getUsername()).isEqualTo("testuser");
//        assertThat(user.getEmail()).isEqualTo("<EMAIL>");
//        assertThat(user.getPassword()).isEqualTo("password123");
//        assertThat(user.getTotpSecret()).isEqualTo("TOTP123456");
//    }
//}
