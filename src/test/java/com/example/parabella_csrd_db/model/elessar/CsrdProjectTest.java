package com.example.parabella_csrd_db.model.elessar;

import com.example.parabella_csrd_db.database.maindatabase.model.authentication.User;
import com.example.parabella_csrd_db.database.maindatabase.model.elessar.CsrdProject;
import com.example.parabella_csrd_db.database.maindatabase.model.elessar.ProjectType;
import com.example.parabella_csrd_db.database.maindatabase.model.elessar.landing.CompanyInfo;
import org.junit.jupiter.api.Test;

import java.time.LocalDateTime;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * Unit tests for the CsrdProject entity class.
 */
class CsrdProjectTest {

    @Test
    void testCsrdProjectSettersAndGetters() {
        // Arrange
        CsrdProject project = new CsrdProject();
        User user = new User();
        user.setId(1L);
        user.setUsername("testuser");
        
        CompanyInfo companyInfo = new CompanyInfo();
        companyInfo.setId(1L);
        companyInfo.setCompanyName("Test Company");
        
        LocalDateTime createdAt = LocalDateTime.now();
        LocalDateTime updatedAt = LocalDateTime.now();

        // Act
        project.setId(1L);
        project.setUser(user);
        project.setProjectName("Test Project");
        project.setProjectDescription("Test Description");
        project.setProjectType(ProjectType.COMPANY);
        project.setCreatedAt(createdAt);
        project.setUpdatedAt(updatedAt);
        project.setCompanyInfo(companyInfo);

        // Assert
        assertThat(project.getId()).isEqualTo(1L);
        assertThat(project.getUser()).isEqualTo(user);
        assertThat(project.getProjectName()).isEqualTo("Test Project");
        assertThat(project.getProjectDescription()).isEqualTo("Test Description");
        assertThat(project.getProjectType()).isEqualTo(ProjectType.COMPANY);
        assertThat(project.getCreatedAt()).isEqualTo(createdAt);
        assertThat(project.getUpdatedAt()).isEqualTo(updatedAt);
        assertThat(project.getCompanyInfo()).isEqualTo(companyInfo);
        assertThat(project.getCompanyInfo().getCompanyName()).isEqualTo("Test Company");
    }

    @Test
    void testDefaultConstructor() {
        // Arrange & Act
        CsrdProject project = new CsrdProject();

        // Assert
        assertThat(project).isNotNull();
        assertThat(project.getId()).isNull();
        assertThat(project.getUser()).isNull();
        assertThat(project.getProjectName()).isNull();
        assertThat(project.getProjectDescription()).isNull();
        assertThat(project.getProjectType()).isNull();
        assertThat(project.getCreatedAt()).isNull();
        assertThat(project.getUpdatedAt()).isNull();
        assertThat(project.getCompanyInfo()).isNull();
    }

    @Test
    void testAllArgsConstructor() {
        // Arrange
        User user = new User();
        user.setId(1L);
        
        CompanyInfo companyInfo = new CompanyInfo();
        companyInfo.setId(1L);
        
        LocalDateTime createdAt = LocalDateTime.now();
        LocalDateTime updatedAt = LocalDateTime.now();

        // Act
        CsrdProject project = new CsrdProject(
                1L,
                user,
                "Test Project",
                "Test Description",
                ProjectType.COMPANY,
                createdAt,
                updatedAt,
                companyInfo
        );

        // Assert
        assertThat(project.getId()).isEqualTo(1L);
        assertThat(project.getUser()).isEqualTo(user);
        assertThat(project.getProjectName()).isEqualTo("Test Project");
        assertThat(project.getProjectDescription()).isEqualTo("Test Description");
        assertThat(project.getProjectType()).isEqualTo(ProjectType.COMPANY);
        assertThat(project.getCreatedAt()).isEqualTo(createdAt);
        assertThat(project.getUpdatedAt()).isEqualTo(updatedAt);
        assertThat(project.getCompanyInfo()).isEqualTo(companyInfo);
    }
}
