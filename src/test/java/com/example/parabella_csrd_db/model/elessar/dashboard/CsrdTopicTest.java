package com.example.parabella_csrd_db.model.elessar.dashboard;

import com.example.parabella_csrd_db.database.maindatabase.model.elessar.dashboard.CsrdSubtopic;
import com.example.parabella_csrd_db.database.maindatabase.model.elessar.dashboard.CsrdTopic;
import org.junit.jupiter.api.Test;

import java.util.ArrayList;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * Unit tests for the CsrdTopic entity class.
 */
class CsrdTopicTest {

    @Test
    void testCsrdTopicSettersAndGetters() {
        // Arrange
        CsrdTopic topic = new CsrdTopic();
        List<CsrdSubtopic> subtopics = new ArrayList<>();
        
        CsrdSubtopic subtopic = new CsrdSubtopic();
        subtopic.setId(1L);
        subtopic.setCsrdSubtopicId("E1-1");
        subtopic.setCsrdSubtopicLabel("Climate Strategy");
        subtopics.add(subtopic);

        // Act
        topic.setId(1L);
        topic.setCode("ESRS_E1");
        topic.setName("Climate Change");
        topic.setSubtopics(subtopics);

        // Assert
        assertThat(topic.getId()).isEqualTo(1L);
        assertThat(topic.getCode()).isEqualTo("ESRS_E1");
        assertThat(topic.getName()).isEqualTo("Climate Change");
        assertThat(topic.getSubtopics()).hasSize(1);
        assertThat(topic.getSubtopics().get(0).getCsrdSubtopicId()).isEqualTo("E1-1");
    }

    @Test
    void testDefaultConstructor() {
        // Arrange & Act
        CsrdTopic topic = new CsrdTopic();

        // Assert
        assertThat(topic).isNotNull();
        assertThat(topic.getId()).isNull();
        assertThat(topic.getCode()).isNull();
        assertThat(topic.getName()).isNull();
        assertThat(topic.getSubtopics()).isNotNull().isEmpty();
    }

    @Test
    void testAddSubtopic() {
        // Arrange
        CsrdTopic topic = new CsrdTopic();
        CsrdSubtopic subtopic = new CsrdSubtopic();
        subtopic.setId(1L);
        subtopic.setCsrdSubtopicId("E1-1");
        subtopic.setCsrdSubtopicLabel("Climate Strategy");

        // Act
        topic.getSubtopics().add(subtopic);

        // Assert
        assertThat(topic.getSubtopics()).hasSize(1);
        assertThat(topic.getSubtopics().get(0)).isEqualTo(subtopic);
    }
}
