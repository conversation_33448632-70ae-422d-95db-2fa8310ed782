package com.example.parabella_csrd_db.model.elessar.landing;

import com.example.parabella_csrd_db.database.maindatabase.model.elessar.landing.CompanyInfo;
import org.junit.jupiter.api.Test;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * Unit tests for the CompanyInfo entity class.
 */
class CompanyInfoTest {

    @Test
    void testCompanyInfoSettersAndGetters() {
        // Arrange
        CompanyInfo companyInfo = new CompanyInfo();

        // Act
        companyInfo.setId(1L);
        companyInfo.setCompanyName("Test Company");
        companyInfo.setIndustry("Technology");
        companyInfo.setSize("Medium");
        companyInfo.setRevenue("1M-10M");
        companyInfo.setNumberOfEmployees("100-500");

        // Assert
        assertThat(companyInfo.getId()).isEqualTo(1L);
        assertThat(companyInfo.getCompanyName()).isEqualTo("Test Company");
        assertThat(companyInfo.getIndustry()).isEqualTo("Technology");
        assertThat(companyInfo.getSize()).isEqualTo("Medium");
        assertThat(companyInfo.getRevenue()).isEqualTo("1M-10M");
        assertThat(companyInfo.getNumberOfEmployees()).isEqualTo("100-500");
    }

    @Test
    void testDefaultConstructor() {
        // Arrange & Act
        CompanyInfo companyInfo = new CompanyInfo();

        // Assert
        assertThat(companyInfo).isNotNull();
        assertThat(companyInfo.getId()).isNull();
        assertThat(companyInfo.getCompanyName()).isNull();
        assertThat(companyInfo.getIndustry()).isNull();
        assertThat(companyInfo.getSize()).isNull();
        assertThat(companyInfo.getRevenue()).isNull();
        assertThat(companyInfo.getNumberOfEmployees()).isNull();
    }

    @Test
    void testAllArgsConstructor() {
        // Arrange & Act
        CompanyInfo companyInfo = new CompanyInfo(
                1L,
                "Test Company",
                "1M-10M",
                "Technology",
                "Medium",
                "100-500"
        );

        // Assert
        assertThat(companyInfo.getId()).isEqualTo(1L);
        assertThat(companyInfo.getCompanyName()).isEqualTo("Test Company");
        assertThat(companyInfo.getRevenue()).isEqualTo("1M-10M");
        assertThat(companyInfo.getIndustry()).isEqualTo("Technology");
        assertThat(companyInfo.getSize()).isEqualTo("Medium");
        assertThat(companyInfo.getNumberOfEmployees()).isEqualTo("100-500");
    }
}
