package com.example.parabella_csrd_db.model.elessar.dashboard;

import com.example.parabella_csrd_db.database.maindatabase.model.elessar.dashboard.CsrdConditionalField;
import com.example.parabella_csrd_db.database.maindatabase.model.elessar.dashboard.CsrdField;
import com.example.parabella_csrd_db.database.maindatabase.model.elessar.dashboard.CsrdFieldOption;
import com.example.parabella_csrd_db.database.maindatabase.model.elessar.dashboard.CsrdSubtopicSection;
import org.junit.jupiter.api.Test;

import java.util.ArrayList;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * Unit tests for the CsrdField entity class.
 */
class CsrdFieldTest {

    @Test
    void testCsrdFieldSettersAndGetters() {
        // Arrange
        CsrdField field = new CsrdField();
        CsrdSubtopicSection section = new CsrdSubtopicSection();
        section.setId(1L);
        section.setSectionId("E1-1-1");
        
        List<CsrdFieldOption> options = new ArrayList<>();
        CsrdFieldOption option = new CsrdFieldOption();
        option.setId(1L);
        option.setOptionValue("Yes");
        options.add(option);
        
        List<CsrdConditionalField> conditionalChildren = new ArrayList<>();
        CsrdConditionalField conditionalField = new CsrdConditionalField();
        conditionalField.setId(1L);
        conditionalField.setParentOptionValue("Yes");
        conditionalChildren.add(conditionalField);

        // Act
        field.setId(1L);
        field.setSection(section);
        field.setFieldType("checkbox");
        field.setLabel("Test Field");
        field.setOptions(options);
        field.setConditionalChildren(conditionalChildren);

        // Assert
        assertThat(field.getId()).isEqualTo(1L);
        assertThat(field.getSection()).isEqualTo(section);
        assertThat(field.getSection().getSectionId()).isEqualTo("E1-1-1");
        assertThat(field.getFieldType()).isEqualTo("checkbox");
        assertThat(field.getLabel()).isEqualTo("Test Field");
        assertThat(field.getOptions()).hasSize(1);
        assertThat(field.getOptions().get(0).getOptionValue()).isEqualTo("Yes");
        assertThat(field.getConditionalChildren()).hasSize(1);
        assertThat(field.getConditionalChildren().get(0).getParentOptionValue()).isEqualTo("Yes");
    }

    @Test
    void testDefaultConstructor() {
        // Arrange & Act
        CsrdField field = new CsrdField();

        // Assert
        assertThat(field).isNotNull();
        assertThat(field.getId()).isNull();
        assertThat(field.getSection()).isNull();
        assertThat(field.getFieldType()).isNull();
        assertThat(field.getLabel()).isNull();
        assertThat(field.getOptions()).isNotNull().isEmpty();
        assertThat(field.getConditionalChildren()).isNotNull().isEmpty();
    }

    @Test
    void testAddOption() {
        // Arrange
        CsrdField field = new CsrdField();
        CsrdFieldOption option = new CsrdFieldOption();
        option.setId(1L);
        option.setOptionValue("Yes");

        // Act
        field.getOptions().add(option);

        // Assert
        assertThat(field.getOptions()).hasSize(1);
        assertThat(field.getOptions().get(0)).isEqualTo(option);
    }

    @Test
    void testAddConditionalChild() {
        // Arrange
        CsrdField field = new CsrdField();
        CsrdConditionalField conditionalField = new CsrdConditionalField();
        conditionalField.setId(1L);
        conditionalField.setParentOptionValue("Yes");

        // Act
        field.getConditionalChildren().add(conditionalField);

        // Assert
        assertThat(field.getConditionalChildren()).hasSize(1);
        assertThat(field.getConditionalChildren().get(0)).isEqualTo(conditionalField);
    }
}
