package com.example.parabella_csrd_db.model.elessar.dashboard;

import com.example.parabella_csrd_db.database.maindatabase.model.elessar.dashboard.CsrdSubtopic;
import com.example.parabella_csrd_db.database.maindatabase.model.elessar.dashboard.CsrdSubtopicSection;
import com.example.parabella_csrd_db.database.maindatabase.model.elessar.dashboard.CsrdTopic;
import org.junit.jupiter.api.Test;

import java.util.ArrayList;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * Unit tests for the CsrdSubtopic entity class.
 */
class CsrdSubtopicTest {

    @Test
    void testCsrdSubtopicSettersAndGetters() {
        // Arrange
        CsrdSubtopic subtopic = new CsrdSubtopic();
        CsrdTopic topic = new CsrdTopic();
        topic.setId(1L);
        topic.setCode("ESRS_E1");
        topic.setName("Climate Change");
        
        List<CsrdSubtopicSection> sections = new ArrayList<>();
        CsrdSubtopicSection section = new CsrdSubtopicSection();
        section.setId(1L);
        section.setSectionId("E1-1-1");
        section.setSectionTitle("Climate Strategy Section");
        sections.add(section);

        // Act
        subtopic.setId(1L);
        subtopic.setTopic(topic);
        subtopic.setCsrdSubtopicId("E1-1");
        subtopic.setCsrdSubtopicLabel("Climate Strategy");
        subtopic.setSections(sections);

        // Assert
        assertThat(subtopic.getId()).isEqualTo(1L);
        assertThat(subtopic.getTopic()).isEqualTo(topic);
        assertThat(subtopic.getTopic().getCode()).isEqualTo("ESRS_E1");
        assertThat(subtopic.getCsrdSubtopicId()).isEqualTo("E1-1");
        assertThat(subtopic.getCsrdSubtopicLabel()).isEqualTo("Climate Strategy");
        assertThat(subtopic.getSections()).hasSize(1);
        assertThat(subtopic.getSections().get(0).getSectionId()).isEqualTo("E1-1-1");
    }

    @Test
    void testDefaultConstructor() {
        // Arrange & Act
        CsrdSubtopic subtopic = new CsrdSubtopic();

        // Assert
        assertThat(subtopic).isNotNull();
        assertThat(subtopic.getId()).isNull();
        assertThat(subtopic.getTopic()).isNull();
        assertThat(subtopic.getCsrdSubtopicId()).isNull();
        assertThat(subtopic.getCsrdSubtopicLabel()).isNull();
        assertThat(subtopic.getSections()).isNotNull().isEmpty();
    }

    @Test
    void testAddSection() {
        // Arrange
        CsrdSubtopic subtopic = new CsrdSubtopic();
        CsrdSubtopicSection section = new CsrdSubtopicSection();
        section.setId(1L);
        section.setSectionId("E1-1-1");
        section.setSectionTitle("Climate Strategy Section");

        // Act
        subtopic.getSections().add(section);

        // Assert
        assertThat(subtopic.getSections()).hasSize(1);
        assertThat(subtopic.getSections().get(0)).isEqualTo(section);
    }
}
