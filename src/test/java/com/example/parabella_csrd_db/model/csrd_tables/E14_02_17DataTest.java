package com.example.parabella_csrd_db.model.csrd_tables;

import com.example.parabella_csrd_db.database.maindatabase.model.csrd_tables.e14_02_17Data;
import org.junit.jupiter.api.Test;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * Unit tests for the e14_02_17Data entity class.
 */
class E14_02_17DataTest {

    @Test
    void testE14_02_17DataSettersAndGetters() {
        // Arrange
        e14_02_17Data data = new e14_02_17Data();

        // Act
        data.setId(1L);
        data.setScope("Scope 1");
        data.setBaseYear(2020);
        data.setTarget2030(50);
        data.setTarget2035(75);
        data.setTarget2050(100);
        data.setExplanation("Test explanation");

        // Assert
        assertThat(data.getId()).isEqualTo(1L);
        assertThat(data.getScope()).isEqualTo("Scope 1");
        assertThat(data.getBaseYear()).isEqualTo(2020);
        assertThat(data.getTarget2030()).isEqualTo(50);
        assertThat(data.getTarget2035()).isEqualTo(75);
        assertThat(data.getTarget2050()).isEqualTo(100);
        assertThat(data.getExplanation()).isEqualTo("Test explanation");
    }

    @Test
    void testDefaultConstructor() {
        // Arrange & Act
        e14_02_17Data data = new e14_02_17Data();

        // Assert
        assertThat(data).isNotNull();
        assertThat(data.getId()).isNull();
        assertThat(data.getScope()).isNull();
        assertThat(data.getBaseYear()).isEqualTo(0);
        assertThat(data.getTarget2030()).isEqualTo(0);
        assertThat(data.getTarget2035()).isEqualTo(0);
        assertThat(data.getTarget2050()).isEqualTo(0);
        assertThat(data.getExplanation()).isNull();
    }
}
