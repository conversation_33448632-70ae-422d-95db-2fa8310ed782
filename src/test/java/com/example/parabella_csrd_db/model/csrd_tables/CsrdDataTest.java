package com.example.parabella_csrd_db.model.csrd_tables;

import com.example.parabella_csrd_db.database.maindatabase.model.csrd_tables.CsrdData;
import org.junit.jupiter.api.Test;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * Unit tests for the CsrdData entity class.
 */
class CsrdDataTest {

    @Test
    void testCsrdDataSettersAndGetters() {
        // Arrange
        CsrdData csrdData = new CsrdData();

        // Act
        csrdData.setId(1L);
        csrdData.setEfragID("EFRAG001");
        csrdData.setEsrs("ESRS E1");
        csrdData.setDr("DR1");
        csrdData.setParagraph("Paragraph 1");
        csrdData.setRelatedAr("Related AR");
        csrdData.setName("Test Data");
        csrdData.setHyperlink("http://example.com");
        csrdData.setDataType("Numeric");
        csrdData.setDataResponse("100");
        csrdData.setDataUnit("kg");
        csrdData.setConditionalOrAlternativeDp("Conditional");
        csrdData.setCaseStudy("Case Study");
        csrdData.setAdditionalInformation("Additional Info");
        csrdData.setComment("Test Comment");

        // Assert
        assertThat(csrdData.getId()).isEqualTo(1L);
        assertThat(csrdData.getEfragID()).isEqualTo("EFRAG001");
        assertThat(csrdData.getEsrs()).isEqualTo("ESRS E1");
        assertThat(csrdData.getDr()).isEqualTo("DR1");
        assertThat(csrdData.getParagraph()).isEqualTo("Paragraph 1");
        assertThat(csrdData.getRelatedAr()).isEqualTo("Related AR");
        assertThat(csrdData.getName()).isEqualTo("Test Data");
        assertThat(csrdData.getHyperlink()).isEqualTo("http://example.com");
        assertThat(csrdData.getDataType()).isEqualTo("Numeric");
        assertThat(csrdData.getDataResponse()).isEqualTo("100");
        assertThat(csrdData.getDataUnit()).isEqualTo("kg");
        assertThat(csrdData.getConditionalOrAlternativeDp()).isEqualTo("Conditional");
        assertThat(csrdData.getCaseStudy()).isEqualTo("Case Study");
        assertThat(csrdData.getAdditionalInformation()).isEqualTo("Additional Info");
        assertThat(csrdData.getComment()).isEqualTo("Test Comment");
    }

    @Test
    void testDefaultConstructor() {
        // Arrange & Act
        CsrdData csrdData = new CsrdData();

        // Assert
        assertThat(csrdData).isNotNull();
        assertThat(csrdData.getId()).isNull();
        assertThat(csrdData.getEfragID()).isNull();
        assertThat(csrdData.getEsrs()).isNull();
        assertThat(csrdData.getDr()).isNull();
        assertThat(csrdData.getParagraph()).isNull();
        assertThat(csrdData.getRelatedAr()).isNull();
        assertThat(csrdData.getName()).isNull();
        assertThat(csrdData.getHyperlink()).isNull();
        assertThat(csrdData.getDataType()).isNull();
        assertThat(csrdData.getDataResponse()).isNull();
        assertThat(csrdData.getDataUnit()).isNull();
        assertThat(csrdData.getConditionalOrAlternativeDp()).isNull();
        assertThat(csrdData.getCaseStudy()).isNull();
        assertThat(csrdData.getAdditionalInformation()).isNull();
        assertThat(csrdData.getComment()).isNull();
    }
}
