package com.example.parabella_csrd_db.model.mithril;

import com.example.parabella_csrd_db.database.maindatabase.model.mithril.EsrsTopic;
import org.junit.jupiter.api.Test;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * Unit tests for the EsrsTopic entity class.
 */
class EsrsTopicTest {

    @Test
    void testEsrsTopicSettersAndGetters() {
        // Arrange
        EsrsTopic esrsTopic = new EsrsTopic();

        // Act
        esrsTopic.setId(1L);
        esrsTopic.setArea("Environmental");
        esrsTopic.setEsrsCode("E1");
        esrsTopic.setTopic("Climate Change");
        esrsTopic.setSubtopic("GHG Emissions");
        esrsTopic.setSubSubTopic("Scope 1 Emissions");

        // Assert
        assertThat(esrsTopic.getId()).isEqualTo(1L);
        assertThat(esrsTopic.getArea()).isEqualTo("Environmental");
        assertThat(esrsTopic.getEsrsCode()).isEqualTo("E1");
        assertThat(esrsTopic.getTopic()).isEqualTo("Climate Change");
        assertThat(esrsTopic.getSubtopic()).isEqualTo("GHG Emissions");
        assertThat(esrsTopic.getSubSubTopic()).isEqualTo("Scope 1 Emissions");
    }

    @Test
    void testDefaultConstructor() {
        // Arrange & Act
        EsrsTopic esrsTopic = new EsrsTopic();

        // Assert
        assertThat(esrsTopic).isNotNull();
        assertThat(esrsTopic.getId()).isNull();
        assertThat(esrsTopic.getArea()).isNull();
        assertThat(esrsTopic.getEsrsCode()).isNull();
        assertThat(esrsTopic.getTopic()).isNull();
        assertThat(esrsTopic.getSubtopic()).isNull();
        assertThat(esrsTopic.getSubSubTopic()).isNull();
    }
}
