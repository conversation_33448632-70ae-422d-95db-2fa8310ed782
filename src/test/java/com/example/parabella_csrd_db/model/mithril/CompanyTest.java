package com.example.parabella_csrd_db.model.mithril;

import com.example.parabella_csrd_db.database.maindatabase.model.mithril.*;
import org.junit.jupiter.api.Test;

import java.util.ArrayList;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * Unit tests for the Company entity class.
 */
class CompanyTest {

    @Test
    void testCompanySettersAndGetters() {
        // Arrange
        Company company = new Company();
        CompanyGroup companyGroup = new CompanyGroup();
        companyGroup.setId(1L);
        companyGroup.setCompanyGroupName("Test Group");
        
        Project project = new Project();
        project.setId(1L);
        project.setProjectName("Test Project");
        
        List<ValueChainObject> valueChainObjects = new ArrayList<>();
        ValueChainObject vco = new ValueChainObject();
        vco.setId(1L);
        vco.setName("Test VCO");
        valueChainObjects.add(vco);
        
        List<Stakeholder> stakeholders = new ArrayList<>();
        Stakeholder stakeholder = new Stakeholder();
        stakeholder.setId(1L);
        stakeholder.setName("Test Stakeholder");
        stakeholders.add(stakeholder);

        // Act
        company.setId(1L);
        company.setCompanyName("Test Company");
        company.setAddress("123 Test St");
        company.setVat("VAT123");
        company.setNumEmployees("100");
        company.setRevenues(1000000.0);
        company.setIndustry("Technology");
        company.setIsSubCompany(false);
        company.setCompanyGroup(companyGroup);
        company.setProject(project);
        company.setValueChainObjects(valueChainObjects);
        company.setStakeholders(stakeholders);

        // Assert
        assertThat(company.getId()).isEqualTo(1L);
        assertThat(company.getCompanyName()).isEqualTo("Test Company");
        assertThat(company.getAddress()).isEqualTo("123 Test St");
        assertThat(company.getVat()).isEqualTo("VAT123");
        assertThat(company.getNumEmployees()).isEqualTo("100");
        assertThat(company.getRevenues()).isEqualTo(1000000.0);
        assertThat(company.getIndustry()).isEqualTo("Technology");
        assertThat(company.getIsSubCompany()).isFalse();
        assertThat(company.getCompanyGroup()).isEqualTo(companyGroup);
        assertThat(company.getProject()).isEqualTo(project);
        assertThat(company.getValueChainObjects()).hasSize(1);
        assertThat(company.getValueChainObjects().get(0).getName()).isEqualTo("Test VCO");
        assertThat(company.getStakeholders()).hasSize(1);
        assertThat(company.getStakeholders().get(0).getName()).isEqualTo("Test Stakeholder");
    }

    @Test
    void testDefaultConstructor() {
        // Arrange & Act
        Company company = new Company();

        // Assert
        assertThat(company).isNotNull();
        assertThat(company.getId()).isNull();
        assertThat(company.getCompanyName()).isNull();
        assertThat(company.getAddress()).isNull();
        assertThat(company.getVat()).isNull();
        assertThat(company.getNumEmployees()).isNull();
        assertThat(company.getRevenues()).isNull();
        assertThat(company.getIndustry()).isNull();
        assertThat(company.getIsSubCompany()).isNull();
        assertThat(company.getCompanyGroup()).isNull();
        assertThat(company.getProject()).isNull();
        assertThat(company.getValueChainObjects()).isNull();
        assertThat(company.getStakeholders()).isNull();
    }
}
