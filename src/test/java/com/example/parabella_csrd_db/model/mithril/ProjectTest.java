package com.example.parabella_csrd_db.model.mithril;

import com.example.parabella_csrd_db.database.maindatabase.model.authentication.User;
import com.example.parabella_csrd_db.database.maindatabase.model.mithril.Project;
import com.example.parabella_csrd_db.database.maindatabase.model.mithril.Stakeholder;
import com.example.parabella_csrd_db.database.maindatabase.model.notification.Notification;
import org.junit.jupiter.api.Test;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * Unit tests for the Project entity class.
 */
class ProjectTest {

    @Test
    void testProjectSettersAndGetters() {
        // Arrange
        Project project = new Project();
        User user = new User();
        user.setId(1L);
        user.setUsername("testuser");
        
        LocalDateTime createdAt = LocalDateTime.now();
        
        List<Stakeholder> stakeholders = new ArrayList<>();
        Stakeholder stakeholder = new Stakeholder();
        stakeholder.setId(1L);
        stakeholder.setName("Test Stakeholder");
        stakeholders.add(stakeholder);
        
        List<Notification> notifications = new ArrayList<>();
        Notification notification = new Notification();
        notification.setId(1L);
        notification.setMessage("Test Notification");
        notifications.add(notification);

        // Act
        project.setId(1L);
        project.setUser(user);
        project.setProjectName("Test Project");
        project.setProjectDescription("Test Description");
        project.setCreatedAt(createdAt);
        project.setProjectType("company");
        project.setCompanyId(1L);
        project.setCompanyGroupId(null);
        project.setStakeholders(stakeholders);
        project.setNotifications(notifications);

        // Assert
        assertThat(project.getId()).isEqualTo(1L);
        assertThat(project.getUser()).isEqualTo(user);
        assertThat(project.getProjectName()).isEqualTo("Test Project");
        assertThat(project.getProjectDescription()).isEqualTo("Test Description");
        assertThat(project.getCreatedAt()).isEqualTo(createdAt);
        assertThat(project.getProjectType()).isEqualTo("company");
        assertThat(project.getCompanyId()).isEqualTo(1L);
        assertThat(project.getCompanyGroupId()).isNull();
        assertThat(project.getStakeholders()).hasSize(1);
        assertThat(project.getStakeholders().get(0).getName()).isEqualTo("Test Stakeholder");
        assertThat(project.getNotifications()).hasSize(1);
        assertThat(project.getNotifications().get(0).getMessage()).isEqualTo("Test Notification");
    }

    @Test
    void testDefaultConstructor() {
        // Arrange & Act
        Project project = new Project();

        // Assert
        assertThat(project).isNotNull();
        assertThat(project.getId()).isNull();
        assertThat(project.getUser()).isNull();
        assertThat(project.getProjectName()).isNull();
        assertThat(project.getProjectDescription()).isNull();
        assertThat(project.getCreatedAt()).isNull();
        assertThat(project.getProjectType()).isNull();
        assertThat(project.getCompanyId()).isNull();
        assertThat(project.getCompanyGroupId()).isNull();
        assertThat(project.getStakeholders()).isNull();
        assertThat(project.getNotifications()).isNull();
    }

    @Test
    void testPrePersist() {
        // Arrange
        Project project = new Project();
        LocalDateTime before = LocalDateTime.now();

        // Act
        project.onCreate();
        LocalDateTime after = LocalDateTime.now();

        // Assert
        assertThat(project.getCreatedAt()).isNotNull();
        assertThat(project.getCreatedAt()).isAfterOrEqualTo(before);
        assertThat(project.getCreatedAt()).isBeforeOrEqualTo(after);
    }
}
