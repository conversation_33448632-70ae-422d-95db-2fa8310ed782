//package com.example.parabella_csrd_db.model.mithril;
//
//import com.example.parabella_csrd_db.database.maindatabase.model.mithril.Company;
//import com.example.parabella_csrd_db.database.maindatabase.model.mithril.EsrsTopic;
//import com.example.parabella_csrd_db.database.maindatabase.model.mithril.EsrsTopicSelection;
//import com.example.parabella_csrd_db.database.maindatabase.model.mithril.Stakeholder;
//import org.junit.jupiter.api.Test;
//
//import java.math.BigDecimal;
//
//import static org.assertj.core.api.Assertions.assertThat;
//
///**
// * Unit tests for the EsrsTopicSelection entity class.
// */
//class EsrsTopicSelectionTest {
//
//    @Test
//    void testEsrsTopicSelectionSettersAndGetters() {
//        // Arrange
//        EsrsTopicSelection selection = new EsrsTopicSelection();
//        EsrsTopic esrsTopic = new EsrsTopic();
//        esrsTopic.setId(1L);
//        esrsTopic.setEsrsCode("E1");
//
//        Stakeholder stakeholder = new Stakeholder();
//        stakeholder.setId(1L);
//        stakeholder.setName("Test Stakeholder");
//
//        Company company = new Company();
//        company.setId(1L);
//        company.setCompanyName("Test Company");
//
//        // Act
//        selection.setId(1L);
//        selection.setEsrsTopic(esrsTopic);
//        selection.setStakeholder(stakeholder);
//        selection.setRelevant(true);
//        selection.setReasonIrrelevance(null); // Not needed since it's relevant
//        selection.setCompany(company);
//        selection.setFinalImpactMaterialityActualImpact(new BigDecimal("4.5"));
//        selection.setFinalFinancialMaterialityActualImpact(new BigDecimal("3.5"));
//        selection.setFinalImpactMaterialityPotentialImpact(new BigDecimal("4.0"));
//        selection.setFinalFinancialMaterialityPotentialImpact(new BigDecimal("3.0"));
//
//        // Assert
//        assertThat(selection.getId()).isEqualTo(1L);
//        assertThat(selection.getEsrsTopic()).isEqualTo(esrsTopic);
//        assertThat(selection.getEsrsTopic().getEsrsCode()).isEqualTo("E1");
//        assertThat(selection.getStakeholder()).isEqualTo(stakeholder);
//        assertThat(selection.getStakeholder().getName()).isEqualTo("Test Stakeholder");
//        assertThat(selection.getRelevant()).isTrue();
//        assertThat(selection.getReasonIrrelevance()).isNull();
//        assertThat(selection.getCompany()).isEqualTo(company);
//        assertThat(selection.getCompany().getCompanyName()).isEqualTo("Test Company");
//        assertThat(selection.getFinalImpactMaterialityActualImpact()).isEqualTo(new BigDecimal("4.5"));
//        assertThat(selection.getFinalFinancialMaterialityActualImpact()).isEqualTo(new BigDecimal("3.5"));
//        assertThat(selection.getFinalImpactMaterialityPotentialImpact()).isEqualTo(new BigDecimal("4.0"));
//        assertThat(selection.getFinalFinancialMaterialityPotentialImpact()).isEqualTo(new BigDecimal("3.0"));
//    }
//
//    @Test
//    void testIrrelevantTopicWithReason() {
//        // Arrange
//        EsrsTopicSelection selection = new EsrsTopicSelection();
//
//        // Act
//        selection.setRelevant(false);
//        selection.setReasonIrrelevance("Not applicable to our business model");
//
//        // Assert
//        assertThat(selection.getRelevant()).isFalse();
//        assertThat(selection.getReasonIrrelevance()).isEqualTo("Not applicable to our business model");
//    }
//
//    @Test
//    void testDefaultConstructor() {
//        // Arrange & Act
//        EsrsTopicSelection selection = new EsrsTopicSelection();
//
//        // Assert
//        assertThat(selection).isNotNull();
//        assertThat(selection.getId()).isNull();
//        assertThat(selection.getEsrsTopic()).isNull();
//        assertThat(selection.getStakeholder()).isNull();
//        assertThat(selection.getRelevant()).isNull();
//        assertThat(selection.getReasonIrrelevance()).isNull();
//        assertThat(selection.getCompany()).isNull();
//        assertThat(selection.getFinalImpactMaterialityActualImpact()).isNull();
//        assertThat(selection.getFinalFinancialMaterialityActualImpact()).isNull();
//        assertThat(selection.getFinalImpactMaterialityPotentialImpact()).isNull();
//        assertThat(selection.getFinalFinancialMaterialityPotentialImpact()).isNull();
//    }
//}
