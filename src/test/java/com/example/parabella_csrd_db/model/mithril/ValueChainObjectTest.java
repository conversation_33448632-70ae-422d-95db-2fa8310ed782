package com.example.parabella_csrd_db.model.mithril;

import com.example.parabella_csrd_db.database.maindatabase.model.mithril.Company;
import com.example.parabella_csrd_db.database.maindatabase.model.mithril.ValueChainObject;
import org.junit.jupiter.api.Test;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * Unit tests for the ValueChainObject entity class.
 */
class ValueChainObjectTest {

    @Test
    void testValueChainObjectSettersAndGetters() {
        // Arrange
        ValueChainObject valueChainObject = new ValueChainObject();
        Company company = new Company();
        company.setId(1L);
        company.setCompanyName("Test Company");

        // Act
        valueChainObject.setId(1L);
        valueChainObject.setName("Test Value Chain Object");
        valueChainObject.setIndustry("Manufacturing");
        valueChainObject.setCompany(company);

        // Assert
        assertThat(valueChainObject.getId()).isEqualTo(1L);
        assertThat(valueChainObject.getName()).isEqualTo("Test Value Chain Object");
        assertThat(valueChainObject.getIndustry()).isEqualTo("Manufacturing");
        assertThat(valueChainObject.getCompany()).isEqualTo(company);
        assertThat(valueChainObject.getCompany().getCompanyName()).isEqualTo("Test Company");
    }

    @Test
    void testDefaultConstructor() {
        // Arrange & Act
        ValueChainObject valueChainObject = new ValueChainObject();

        // Assert
        assertThat(valueChainObject).isNotNull();
        assertThat(valueChainObject.getId()).isNull();
        assertThat(valueChainObject.getName()).isNull();
        assertThat(valueChainObject.getIndustry()).isNull();
        assertThat(valueChainObject.getCompany()).isNull();
    }
}
