package com.example.parabella_csrd_db.model.mithril;

import com.example.parabella_csrd_db.database.maindatabase.model.mithril.*;
import org.junit.jupiter.api.Test;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * Unit tests for the Iro entity class.
 */
class IroTest {

    @Test
    void testIroSettersAndGetters() {
        // Arrange
        Iro iro = new Iro();
        EsrsTopicSelection esrsTopicSelection = new EsrsTopicSelection();
        esrsTopicSelection.setId(1L);
        
        Stakeholder stakeholder = new Stakeholder();
        stakeholder.setId(1L);
        stakeholder.setName("Test Stakeholder");
        
        Company company = new Company();
        company.setId(1L);
        company.setCompanyName("Test Company");
        
        IroEvaluation iroEvaluation = new IroEvaluation();
        iroEvaluation.setId(1L);

        // Act
        iro.setId(1L);
        iro.setName("Test IRO");
        iro.setIroType("negative");
        iro.setEsrsTopicSelection(esrsTopicSelection);
        iro.setStakeholder(stakeholder);
        iro.setCompany(company);
        iro.setIroEvaluation(iroEvaluation);

        // Assert
        assertThat(iro.getId()).isEqualTo(1L);
        assertThat(iro.getName()).isEqualTo("Test IRO");
        assertThat(iro.getIroType()).isEqualTo("negative");
        assertThat(iro.getEsrsTopicSelection()).isEqualTo(esrsTopicSelection);
        assertThat(iro.getStakeholder()).isEqualTo(stakeholder);
        assertThat(iro.getStakeholder().getName()).isEqualTo("Test Stakeholder");
        assertThat(iro.getCompany()).isEqualTo(company);
        assertThat(iro.getCompany().getCompanyName()).isEqualTo("Test Company");
        assertThat(iro.getIroEvaluation()).isEqualTo(iroEvaluation);
    }

    @Test
    void testDefaultConstructor() {
        // Arrange & Act
        Iro iro = new Iro();

        // Assert
        assertThat(iro).isNotNull();
        assertThat(iro.getId()).isNull();
        assertThat(iro.getName()).isNull();
        assertThat(iro.getIroType()).isNull();
        assertThat(iro.getEsrsTopicSelection()).isNull();
        assertThat(iro.getStakeholder()).isNull();
        assertThat(iro.getCompany()).isNull();
        assertThat(iro.getIroEvaluation()).isNull();
    }
}
