//package com.example.parabella_csrd_db.model.mithril;
//
//import com.example.parabella_csrd_db.database.maindatabase.model.mithril.*;
//import org.junit.jupiter.api.Test;
//
//import java.util.ArrayList;
//import java.util.List;
//
//import static org.assertj.core.api.Assertions.assertThat;
//
///**
// * Unit tests for the Stakeholder entity class.
// */
//class StakeholderTest {
//
//    @Test
//    void testStakeholderSettersAndGetters() {
//        // Arrange
//        Stakeholder stakeholder = new Stakeholder();
//        Project project = new Project();
//        project.setId(1L);
//        project.setProjectName("Test Project");
//
//        Company company = new Company();
//        company.setId(1L);
//        company.setCompanyName("Test Company");
//
//        List<ValueChainObject> valueChainObjects = new ArrayList<>();
//        ValueChainObject vco = new ValueChainObject();
//        vco.setId(1L);
//        vco.setName("Test VCO");
//        valueChainObjects.add(vco);
//
//        List<EsrsTopic> esrsTopics = new ArrayList<>();
//        EsrsTopic topic = new EsrsTopic();
//        topic.setId(1L);
//        topic.setEsrsCode("E1");
//        esrsTopics.add(topic);
//
//        // Act
//        stakeholder.setId(1L);
//        stakeholder.setProject(project);
//        stakeholder.setName("John Doe");
//        stakeholder.setEmail("<EMAIL>");
//        stakeholder.setRole("Manager");
//        stakeholder.setStakeholderType("Internal");
//        stakeholder.setCompany(company);
//        stakeholder.setToken("token123");
//        stakeholder.setStatus(StakeholderStatus.INVITED);
//        stakeholder.setCompletedDatapoints(5);
//        stakeholder.setTotalDatapoints(10);
//        stakeholder.setIs_responsible(true);
//        stakeholder.setValueChainObjects(valueChainObjects);
//        stakeholder.setEsrsTopics(esrsTopics);
//
//        // Assert
//        assertThat(stakeholder.getId()).isEqualTo(1L);
//        assertThat(stakeholder.getProject()).isEqualTo(project);
//        assertThat(stakeholder.getProject().getProjectName()).isEqualTo("Test Project");
//        assertThat(stakeholder.getName()).isEqualTo("John Doe");
//        assertThat(stakeholder.getEmail()).isEqualTo("<EMAIL>");
//        assertThat(stakeholder.getRole()).isEqualTo("Manager");
//        assertThat(stakeholder.getStakeholderType()).isEqualTo("Internal");
//        assertThat(stakeholder.getCompany()).isEqualTo(company);
//        assertThat(stakeholder.getCompany().getCompanyName()).isEqualTo("Test Company");
//        assertThat(stakeholder.getToken()).isEqualTo("token123");
//        assertThat(stakeholder.getStatus()).isEqualTo(StakeholderStatus.INVITED);
//        assertThat(stakeholder.getCompletedDatapoints()).isEqualTo(5);
//        assertThat(stakeholder.getTotalDatapoints()).isEqualTo(10);
//        assertThat(stakeholder.getIs_responsible()).isTrue();
//        assertThat(stakeholder.getValueChainObjects()).hasSize(1);
//        assertThat(stakeholder.getValueChainObjects().get(0).getName()).isEqualTo("Test VCO");
//        assertThat(stakeholder.getEsrsTopics()).hasSize(1);
//        assertThat(stakeholder.getEsrsTopics().get(0).getEsrsCode()).isEqualTo("E1");
//    }
//
//    @Test
//    void testDefaultConstructor() {
//        // Arrange & Act
//        Stakeholder stakeholder = new Stakeholder();
//
//        // Assert
//        assertThat(stakeholder).isNotNull();
//        assertThat(stakeholder.getId()).isNull();
//        assertThat(stakeholder.getProject()).isNull();
//        assertThat(stakeholder.getName()).isNull();
//        assertThat(stakeholder.getEmail()).isNull();
//        assertThat(stakeholder.getRole()).isNull();
//        assertThat(stakeholder.getStakeholderType()).isNull();
//        assertThat(stakeholder.getCompany()).isNull();
//        assertThat(stakeholder.getToken()).isNull();
//        assertThat(stakeholder.getStatus()).isEqualTo(StakeholderStatus.INVITED); // Default value
//        assertThat(stakeholder.getCompletedDatapoints()).isNull();
//        assertThat(stakeholder.getTotalDatapoints()).isNull();
//        assertThat(stakeholder.getIs_responsible()).isNull();
//        assertThat(stakeholder.getValueChainObjects()).isNull();
//        assertThat(stakeholder.getEsrsTopics()).isNull();
//    }
//
//    @Test
//    void testStakeholderStatusEnum() {
//        // Assert
//        assertThat(StakeholderStatus.INVITED.name()).isEqualTo("INVITED");
//        assertThat(StakeholderStatus.ACTIVE.name()).isEqualTo("ACTIVE");
//        assertThat(StakeholderStatus.COMPLETED.name()).isEqualTo("COMPLETED");
//    }
//}
