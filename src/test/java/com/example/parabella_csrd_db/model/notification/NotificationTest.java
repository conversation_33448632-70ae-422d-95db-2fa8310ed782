package com.example.parabella_csrd_db.model.notification;

import com.example.parabella_csrd_db.database.maindatabase.model.mithril.Project;
import com.example.parabella_csrd_db.database.maindatabase.model.notification.Notification;
import org.junit.jupiter.api.Test;

import java.time.LocalDateTime;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * Unit tests for the Notification entity class.
 */
class NotificationTest {

    @Test
    void testNotificationSettersAndGetters() {
        // Arrange
        Notification notification = new Notification();
        Project project = new Project();
        project.setId(1L);
        project.setProjectName("Test Project");
        
        LocalDateTime createdAt = LocalDateTime.now();
        LocalDateTime readAt = createdAt.plusHours(1);

        // Act
        notification.setId(1L);
        notification.setMessage("Test notification message");
        notification.setRead(false);
        notification.setCreatedAt(createdAt);
        notification.setReadAt(readAt);
        notification.setProject(project);

        // Assert
        assertThat(notification.getId()).isEqualTo(1L);
        assertThat(notification.getMessage()).isEqualTo("Test notification message");
        assertThat(notification.isRead()).isFalse();
        assertThat(notification.getCreatedAt()).isEqualTo(createdAt);
        assertThat(notification.getReadAt()).isEqualTo(readAt);
        assertThat(notification.getProject()).isEqualTo(project);
        assertThat(notification.getProject().getProjectName()).isEqualTo("Test Project");
    }

    @Test
    void testDefaultConstructor() {
        // Arrange & Act
        Notification notification = new Notification();

        // Assert
        assertThat(notification).isNotNull();
        assertThat(notification.getId()).isNull();
        assertThat(notification.getMessage()).isNull();
        assertThat(notification.isRead()).isFalse(); // Default is false
        assertThat(notification.getCreatedAt()).isNull();
        assertThat(notification.getReadAt()).isNull();
        assertThat(notification.getProject()).isNull();
    }

    @Test
    void testParameterizedConstructor() {
        // Arrange
        Project project = new Project();
        project.setId(1L);
        project.setProjectName("Test Project");
        
        LocalDateTime before = LocalDateTime.now();

        // Act
        Notification notification = new Notification("Test message", project);
        
        LocalDateTime after = LocalDateTime.now();

        // Assert
        assertThat(notification.getMessage()).isEqualTo("Test message");
        assertThat(notification.getProject()).isEqualTo(project);
        assertThat(notification.isRead()).isFalse();
        assertThat(notification.getCreatedAt()).isNotNull();
        assertThat(notification.getCreatedAt()).isAfterOrEqualTo(before);
        assertThat(notification.getCreatedAt()).isBeforeOrEqualTo(after);
        assertThat(notification.getReadAt()).isNull();
    }
}
