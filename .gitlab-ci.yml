## .gitlab-ci.yml (Parent Pipeline)
stages:
  - dispatch

workflow:
  rules:
    # Run pipeline for pushes/merges to staging and main branches
    - if: '$CI_COMMIT_BRANCH == "staging" || $CI_COMMIT_BRANCH == "main"'
    # Run pipeline for merge requests targeting staging or main
    - if: '$CI_PIPELINE_SOURCE == "merge_request_event" && ($CI_MERGE_REQUEST_TARGET_BRANCH_NAME == "staging" || $CI_MERGE_REQUEST_TARGET_BRANCH_NAME == "main")'
    # Run pipeline for tags (optional, for specific release tagging)
    - if: '$CI_COMMIT_TAG'
    # Don't run for other branches/pushes unless explicitly defined in jobs
    - when: never # Default rule if none above match

#trigger_frontend:
#  stage: dispatch
#  trigger:
#    include: 'frontend.gitlab-ci.yml'
#    strategy: depend # Wait for the child pipeline to finish
#  rules:
#    # Trigger ONLY IF frontend files changed AND it's a staging/main branch push/merge
#    - if: '($CI_COMMIT_BRANCH == "staging" || $CI_COMMIT_BRANCH == "main" || $CI_PIPELINE_SOURCE == "merge_request_event")'
#      changes:
#        paths:
#          - "parabella_frontend_nowa/parabella_elessar/**/*" # Use ** for recursive
#        compare_to: 'refs/heads/main' # Compare changes against main (adjust if needed)
#      when: always # Trigger if conditions met
#    - when: never # Don't trigger otherwise


trigger_backend:
  stage: dispatch
  trigger:
    include: 'backend.gitlab-ci.yml'
    strategy: depend
  rules:
    # Trigger ONLY IF backend files changed AND it's a staging/main branch push/merge
    - if: '($CI_COMMIT_BRANCH == "staging" || $CI_COMMIT_BRANCH == "main" || $CI_PIPELINE_SOURCE == "merge_request_event")'
      changes:
        paths:
          - "src/**/*"
          - "build.gradle"
          - "gradlew"
          - "gradle/**/*"
          - "Dockerfile" # Include Dockerfile changes
        compare_to: 'refs/heads/main'
      when: always
    - when: never