# env.yaml  (no leading “env_vars:” block)
SPRING_PROFILES_ACTIVE: "staging"
SPRING_DATASOURCE_CSRD_URL: "${SPRING_DATASOURCE_CSRD_URL}"
SPRING_DATASOURCE_CSRD_USERNAME: "${SPRING_DATASOURCE_CSRD_USERNAME}"
SPRING_DATASOURCE_CSRD_PASSWORD: "$SPRING_DATASOURCE_CSRD_PASSWORD"
SPRING_DATASOURCE_VECTORDB_URL: "${SPRING_DATASOURCE_VECTORDB_URL}"
SPRING_DATASOURCE_VECTORDB_USERNAME: "${SPRING_DATASOURCE_VECTORDB_USERNAME}"
SPRING_DATASOURCE_VECTORDB_PASSWORD: "${SPRING_DATASOURCE_VECTORDB_PASSWORD}"
JWT_SECRET: "${JWT_SECRET}"
SPRING_MAIL_USERNAME: "${SPRING_MAIL_USERNAME}"
SPRING_MAIL_PASSWORD: "${SPRING_MAIL_PASSWORD}"
OPENAI_API_KEY: "${OPENAI_API_KEY}"
APP_FRONTEND_URL: "${APP_FRONTEND_URL}"
