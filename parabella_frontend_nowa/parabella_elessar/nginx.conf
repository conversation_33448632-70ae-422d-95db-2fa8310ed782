server {
    listen 80;
    server_name _;
    gzip on;
    # Root directory for the application
    root   /usr/share/nginx/html;
    index  index.html index.htm;

    # Handle client-side routing
    location / {
        try_files $uri $uri/ /index.html;
    }

    # Optional: Serve static assets directly
    location /static/ {
        expires 1y;
        add_header Cache-Control "public";
    }
}
