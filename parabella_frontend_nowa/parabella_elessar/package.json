{"name": "parabella_elessar", "private": true, "version": "0.0.1", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint src --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "sass": "sass ./src/ui_components/assets/scss:./src/assets/css/", "sass-min": "sass ./src/ui_components/assets/scss:./src/assets/css/ --style compressed", "preview": "vite preview"}, "dependencies": {"@emotion/react": "^11.11.4", "@emotion/styled": "^11.11.5", "@fullcalendar/daygrid": "^6.1.14", "@fullcalendar/interaction": "^6.1.14", "@fullcalendar/react": "^6.1.14", "@fullcalendar/timegrid": "^6.1.14", "@mui/icons-material": "^5.15.19", "@mui/material": "^5.15.19", "@mui/styled-engine-sc": "^6.0.0-alpha.18", "@mui/x-tree-view": "^7.6.1", "@reduxjs/toolkit": "^1.9.7", "@simonwep/pickr": "^1.9.1", "@types/leaflet": "^1.9.12", "@types/node": "^20.14.2", "@types/react-datepicker": "^6.2.0", "@types/react-dragula": "^1.1.3", "@types/react-flatpickr": "^3.8.11", "@types/react-helmet": "^6.1.11", "@types/react-leaflet": "^3.0.0", "@types/react-simple-maps": "^3.0.4", "@types/react-table": "^7.7.20", "@vitejs/plugin-react-swc": "^3.7.0", "axios": "^1.7.2", "bootstrap": "^5.3.3", "chart.js": "^4.4.3", "dragula": "^3.7.3", "echarts": "^5.5.0", "echarts-for-react": "^3.0.2", "file-saver": "^2.0.5", "filepond": "^4.31.1", "filepond-plugin-image-exif-orientation": "^1.0.11", "filepond-plugin-image-preview": "^4.6.12", "formik": "^2.4.6", "framer-motion": "^11.11.9", "gridjs-react": "^6.1.1", "jwt-decode": "^4.0.0", "leaflet": "^1.9.4", "lucide-react": "^0.436.0", "moment": "^2.30.1", "nanoid": "^5.0.7", "notistack": "^3.0.1", "pdfjs-dist": "^5.2.133", "plotly.js": "^2.35.2", "rc-slider": "^10.6.2", "react": "^18.3.1", "react-apexcharts": "^1.4.1", "react-beautiful-dnd": "^13.1.1", "react-bootstrap": "^2.10.2", "react-bootstrap-icons": "^1.11.6", "react-calendar": "^5.0.0", "react-chartjs-2": "^5.2.0", "react-circular-progressbar": "^2.1.0", "react-datepicker": "^6.9.0", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "^18.3.1", "react-draggable": "^4.4.6", "react-dragula": "^1.1.17", "react-dropdown-select": "^4.11.2", "react-dropzone": "^14.3.8", "react-filepond": "^7.1.2", "react-flatpickr": "^3.10.13", "react-flow-renderer": "^10.3.17", "react-helmet": "^6.1.0", "react-helmet-async": "^2.0.5", "react-icons": "^5.3.0", "react-joyride": "^2.8.2", "react-leaflet": "^4.2.1", "react-number-format": "^5.4.0", "react-organizational-chart": "^2.2.1", "react-pdf": "^9.2.1", "react-perfect-scrollbar": "^1.5.8", "react-plotly.js": "^2.6.0", "react-range": "^1.8.14", "react-range-slider-input": "^3.0.7", "react-redux": "^8.1.3", "react-router-dom": "^6.23.1", "react-select": "^5.8.0", "react-simple-maps": "^3.0.0", "react-table": "^7.8.0", "react-tabulator": "^0.19.0", "react-tag-input-component": "^2.0.2", "react-textarea-autosize": "^8.5.9", "react-toastify": "^10.0.5", "react-transition-group": "^4.4.5", "react-uploader": "^3.43.0", "react-wizard-primitive": "^2.5.0", "recharts": "^2.12.7", "redux": "^4.2.1", "redux-thunk": "^2.4.2", "rodal": "^2.1.0", "sass": "^1.77.4", "simplebar": "^6.2.6", "simplebar-react": "^3.2.5", "suneditor": "^2.46.3", "suneditor-react": "^3.6.1", "sweetalert2": "^11.11.0", "swiper": "^11.1.4", "xlsx": "^0.18.5", "yet-another-react-lightbox": "^3.19.0", "yup": "^1.4.0"}, "devDependencies": {"@types/file-saver": "^2.0.7", "@types/react": "^18.2.66", "@types/react-beautiful-dnd": "^13.1.8", "@types/react-dom": "^18.2.22", "@types/react-plotly.js": "^2.6.3", "@typescript-eslint/eslint-plugin": "^7.2.0", "@typescript-eslint/parser": "^7.2.0", "@vitejs/plugin-react": "^4.2.1", "eslint": "^8.57.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.6", "typescript": "^5.4.5", "vite": "^5.2.12"}}