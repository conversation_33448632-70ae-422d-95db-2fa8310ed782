import {FC, Fragment} from 'react';
import {Provider} from 'react-redux';
import {Outlet} from 'react-router-dom';
import {Helmet, HelmetProvider} from 'react-helmet-async';
import store from "./ui_components/templateLogic/redux/store.tsx";
import Header from "./ui_components/layout/layoutcomponent/header.tsx";
import Sidebar from "./ui_components/layout/layoutcomponent/sidebar.tsx";


interface ComponentProps {
}

const App: FC<ComponentProps> = () => {
    return (
        <Fragment>
            <Provider store={store}>
                <HelmetProvider>
                    <Helmet
                        htmlAttributes={{
                            lang: "en",
                            dir: "ltr",
                            "data-nav-layout": "vertical",
                            "data-theme-mode": "light",
                            "data-header-styles": "light",
                            "data-menu-styles": "light",
                            "data-vertical-style": "overlay",
                        }}
                    >
                        <body className=''></body>
                    </Helmet>
                </HelmetProvider>
                <div className="page">
                    <Header/>
                    <Sidebar/>
                    <div className="main-content app-content">
                        <div className="main-container container-fluid">

                                <Outlet/>


                        </div>
                    </div>
                </div>

            </Provider>
        </Fragment>
    );
};

export default App;
