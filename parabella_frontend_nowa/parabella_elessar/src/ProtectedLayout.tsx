// src/ProtectedLayout.tsx
import { ProjectProvider } from "./ui_components/components/DmaModule/context_module/ProjectContext.tsx";
import { CompanyProvider } from "./ui_components/components/DmaModule/context_module/CompanyContext.tsx";
import { CsrdProjectProvider } from "./ui_components/components/CSRD_Module/context/CsrdProjectProvider.tsx";
import App from './App';

/**
 * This component acts as a wrapper for the main App layout.
 * It bundles all the data providers that should only be active
 * for authenticated users inside the main application layout.
 */
const ProtectedLayout = () => {
    return (
        <ProjectProvider>
            <CompanyProvider>
                <CsrdProjectProvider>
                    <App />
                </CsrdProjectProvider>
            </CompanyProvider>
        </ProjectProvider>
    );
};

export default ProtectedLayout;