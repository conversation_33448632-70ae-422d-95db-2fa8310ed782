// src/SettingsPage.tsx (ENHANCED)

import React, { useState } from 'react';
import {
    PersonSquare,
    Palette,
    ShieldLock,
    Key,
    Lock,
    BoxArrowUpRight,
    People,
    Diagram3,
    Plugin,
    Braces,
    CodeSlash,
    CreditCard,
    Receipt
} from 'react-bootstrap-icons';
import UserManagement from './UserManagement'; // Assuming path is correct
import RoleManagement from './RoleManagement'; // Assuming path is correct
import './settings.css';
import IntegrationsManagement from "./IntegrationsManagement.tsx";
import Profile from "../Profile.tsx"; // We will create/update this file

type SettingsView = 'profile' | 'userManagement' | 'roleManagement' | 'integrations';

const Settings: React.FC = () => {

    const [activeView, setActiveView] = useState<SettingsView>('roleManagement');


    const renderContent = () => {
        switch (activeView) {
            case 'userManagement':
                return <UserManagement />;
            case 'roleManagement':

                return <RoleManagement />;
            case 'integrations':
                return <IntegrationsManagement />;
            case 'profile':
                return <Profile/>;
            default:
                return <h2 className="placeholder-content">Page Not Found</h2>;
        }
    };

    const navSections = [
        {
            title: "My Account",
            items: [
                { key: 'profile', icon: <PersonSquare size={18} />, label: 'Profile', enabled: true },
                { key: 'appearance', icon: <Palette size={18} />, label: 'Appearance', enabled: false },
            ]
        },
        {
            title: "Security",
            items: [
                { key: 'security', icon: <ShieldLock size={18} />, label: 'Security', enabled: false },
                { key: 'password', icon: <Key size={18} />, label: 'Password', enabled: false },
                { key: '2fa', icon: <Lock size={18} />, label: '2FA', enabled: false },
                { key: 'sso', icon: <BoxArrowUpRight size={18} />, label: 'SSO', enabled: false },
            ]
        },
        {
            title: "Organization",
            items: [
                { key: 'userManagement', icon: <People size={18} />, label: 'User Management', enabled: true },
                { key: 'roleManagement', icon: <Diagram3 size={18} />, label: 'Role Management', enabled: true },
            ]
        },
        {
            title: "Developer",
            items: [
                { key: 'integrations', icon: <Plugin size={18} />, label: 'Integrations', enabled: true },
                { key: 'webhooks', icon: <Braces size={18} />, label: 'Webhooks', enabled: false },
                { key: 'apiKeys', icon: <CodeSlash size={18} />, label: 'API Keys', enabled: false },
            ]
        },
        {
            title: "Billing",
            items: [
                { key: 'billing', icon: <CreditCard size={18} />, label: 'Billing', enabled: false },
                { key: 'invoices', icon: <Receipt size={18} />, label: 'Invoices', enabled: false },
            ]
        }
    ];

    return (
        <div className="settings-page-container">
            <nav className="settings-nav">
                <h2 className="settings-nav-header">Settings</h2>
                <ul className="settings-nav-list">
                    {navSections.map(section => (
                        <React.Fragment key={section.title}>
                            <li className="settings-nav-section-title">{section.title}</li>
                            {section.items.map(item => (
                                <li
                                    key={item.key}
                                    className={`settings-nav-item ${activeView === item.key ? 'active' : ''} ${!item.enabled ? 'disabled' : ''}`}
                                    onClick={() => item.enabled && setActiveView(item.key as SettingsView)}
                                >
                                    {item.icon}
                                    <span>{item.label}</span>
                                </li>
                            ))}
                        </React.Fragment>
                    ))}
                </ul>
            </nav>
            <main className="settings-content">
                {renderContent()}
            </main>
        </div>
    );
};

export default Settings;