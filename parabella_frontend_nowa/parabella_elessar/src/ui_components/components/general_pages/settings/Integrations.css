.integrations-container {
    padding: 2rem;
    height: 100%;
    box-sizing: border-box;
}

.integrations-header {
    margin-bottom: 2.5rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #e0e0e0;
}

.integrations-header h1 {
    font-size: 1.75rem;
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.integrations-header p {
    color: #6c757d;
    font-size: 1rem;
}

.integrations-list {
    display: grid;
    gap: 1.5rem;
    grid-template-columns: 1fr; /* Single column on small screens */
}

/* On larger screens, you might want a two-column layout */
@media (min-width: 1200px) {
    .integrations-list {
        grid-template-columns: 1fr 1fr;
    }
}

.integration-card {
    background-color: #ffffff;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    padding: 1.5rem;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    transition: box-shadow 0.2s ease-in-out, border-color 0.2s ease-in-out;
}

.integration-card:hover {
    border-color: #c7c7c7;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

.integration-card.disabled {
    opacity: 0.6;
    background-color: #f8f9fa;
}

.integration-card-header {
    display: flex;
    align-items: center;
    margin-bottom: 1.5rem;
}

.integration-logo {
    height: 40px;
    width: auto;
    margin-right: 1.5rem;
    flex-shrink: 0;
}

.placeholder-logo {
    height: 40px;
    width: 40px;
    background-color: #e9ecef;
    border-radius: 6px;
    margin-right: 1.5rem;
    flex-shrink: 0;
}

.integration-info h3 {
    margin: 0 0 0.25rem 0;
    font-size: 1.1rem;
    font-weight: 600;
}

.integration-description {
    margin: 0;
    color: #6c757d;
    font-size: 0.9rem;
}

.integration-actions {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    gap: 1rem;
}

.status-badge {
    font-size: 0.8rem;
    font-weight: 500;
    padding: 0.25rem 0.6rem;
    border-radius: 12px;
    color: #333;
}

.status-badge.connected {
    background-color: #d1fae5; /* Light Green */
    color: #065f46; /* Dark Green */
}

.status-badge.disconnected {
    background-color: #e5e7eb; /* Light Gray */
    color: #4b5563; /* Dark Gray */
}

.integration-button {
    padding: 0.5rem 1rem;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    background-color: #ffffff;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.2s;
}

.integration-button:hover:not(:disabled) {
    background-color: #f9fafb;
}

.integration-button.disconnect {
    color: #b91c1c; /* Dark Red */
    border-color: #fecaca; /* Light Red */
}

.integration-button.disconnect:hover {
    background-color: #fee2e2;
}

.integration-button:disabled {
    cursor: not-allowed;
    color: #6c757d;
}