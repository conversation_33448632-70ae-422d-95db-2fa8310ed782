import React, { useState } from 'react';
import './Integrations.css';
import {imagesData} from "../../../templateLogic/commonimages.tsx";


const IntegrationsManagement: React.FC = () => {
    // State to track if the Sphera integration is connected
    const [isSpheraConnected, setIsSpheraConnected] = useState(false);

    // Mock function to handle connection logic
    const handleSpheraConnect = () => {
        // In a real app, this would open a modal, redirect to OAuth, etc.
        // For this example, we'll just toggle the state.
        console.log(`Setting Sphera connection to: ${!isSpheraConnected}`);
        setIsSpheraConnected(!isSpheraConnected);
    };

    return (
        <div className="integrations-container">
            <div className="integrations-header">
                <h1>Integrations</h1>
                <p>Connect your favorite tools to streamline your sustainability reporting workflow.</p>
            </div>

            <div className="integrations-list">
                {/* --- Sphera Integration Card --- */}
                <div className="integration-card">
                    <div className="integration-card-header">
                        <img
                            src={imagesData('sphera')}
                            alt="Company Group"
                            style={{width: '100px', height: '50px', objectFit: 'cover'}}
                        />
                        <div className="integration-info">
                            <h3>Sphera</h3>
                            <p className="integration-description">Sync sustainability & ESG data from the Sphera platform.</p>
                        </div>
                    </div>
                    <div className="integration-actions">
                        <span className={`status-badge ${isSpheraConnected ? 'connected' : 'disconnected'}`}>
                            {isSpheraConnected ? 'Connected' : 'Not Connected'}
                        </span>
                        <button
                            onClick={handleSpheraConnect}
                            className={`integration-button ${isSpheraConnected ? 'disconnect' : 'connect'}`}
                        >
                            {isSpheraConnected ? 'Manage' : 'Connect'}
                        </button>
                    </div>
                </div>

                {/* --- Placeholder for future integrations --- */}
                <div className="integration-card disabled">
                    <div className="integration-card-header">
                        {/* You can use a generic icon or the logo */}
                        <div className="integration-logo placeholder-logo"></div>
                        <div className="integration-info">
                            <h3>Microsoft 365</h3>
                            <p className="integration-description">Import data directly from Excel and other sources.</p>
                        </div>
                    </div>
                    <div className="integration-actions">
                        <span className="status-badge">Coming Soon</span>
                        <button className="integration-button" disabled>Connect</button>
                    </div>
                </div>

            </div>
        </div>
    );
};

export default IntegrationsManagement;