// src/components/UserManagement.tsx (ENHANCED)

import React, { useState, useEffect, useCallback } from 'react';
import { getUsers, getRoles, inviteUser, updateUserRole, deleteUser } from './settingsApi'; // Assuming API functions exist
import { Role } from './types'; // Assuming types exist
import { Spinner, Alert, Table, Button, Modal, Form, Tooltip, OverlayTrigger, Badge } from 'react-bootstrap';
import { PersonPlusFill, Pencil, Trash } from 'react-bootstrap-icons';
import {User} from "../../../../services/authService.ts";

const UserManagement: React.FC = () => {
    const [users, setUsers] = useState<User[]>([]);
    const [roles, setRoles] = useState<Role[]>([]);
    const [isLoading, setIsLoading] = useState<boolean>(true);
    const [isSubmitting, setIsSubmitting] = useState<boolean>(false);
    const [error, setError] = useState<string | null>(null);

    const [showInviteModal, setShowInviteModal] = useState(false);
    const [editingUser, setEditingUser] = useState<User | null>(null);
    const [userToDelete, setUserToDelete] = useState<User | null>(null);

    const [inviteEmail, setInviteEmail] = useState('');
    const [selectedRoleId, setSelectedRoleId] = useState<number | string>('');

    const fetchData = useCallback(async () => {
        setIsLoading(true);
        setError(null);
        try {
            const [usersData, rolesData] = await Promise.all([getUsers(), getRoles()]);
            setUsers(usersData);
            setRoles(rolesData);
            if (rolesData.length > 0 && !selectedRoleId) {
                setSelectedRoleId(rolesData[0].id);
            }
        } catch (err) {
            setError('Failed to load user data. Please try again later.');
        } finally {
            setIsLoading(false);
        }
    }, [selectedRoleId]);

    useEffect(() => {
        fetchData();
    }, [fetchData]);

    const handleAction = async (action: () => Promise<any>, errorMessage: string) => {
        setIsSubmitting(true);
        try {
            await action();
            await fetchData();
        } catch (err) {
            setError(errorMessage);
        } finally {
            setIsSubmitting(false);
        }
    };

    const handleInvite = () => {
        if (!inviteEmail || !selectedRoleId) return;
        handleAction(() => inviteUser(inviteEmail, +selectedRoleId), 'Failed to invite user.');
        setShowInviteModal(false);
        setInviteEmail('');
    };

    const handleUpdateUser = () => {
        if (!editingUser || !selectedRoleId) return;
        handleAction(() => updateUserRole(editingUser.id, +selectedRoleId), 'Failed to update user role.');
        setEditingUser(null);
    };

    const handleDeleteUser = () => {
        if (!userToDelete) return;
        handleAction(() => deleteUser(userToDelete.id), 'Failed to remove user.');
        setUserToDelete(null);
    };

    const openEditModal = (user: User) => {
        const role = roles.find(r => r.name === user.roleName);
        setSelectedRoleId(role ? role.id : '');
        setEditingUser(user);
    };

    if (isLoading) return <div className="centered-spinner"><Spinner animation="border" /></div>;

    return (
        <div className="settings-view-container">
            <div className="view-header">
                <h2 className="view-title">User Management</h2>
                <Button variant="primary" onClick={() => setShowInviteModal(true)}>
                    <PersonPlusFill className="me-2" />Invite User
                </Button>
            </div>

            {error && <Alert variant="danger" onClose={() => setError(null)} dismissible>{error}</Alert>}

            <div className="table-responsive">
                <Table hover className="modern-table">
                    <thead>
                    <tr>
                        <th>Name</th>
                        <th>Email</th>
                        <th>Role</th>
                        <th className="text-center">Actions</th>
                    </tr>
                    </thead>
                    <tbody>
                    {users.map((user) => (
                        <tr key={user.id}>
                            <td>{user.username}</td>
                            <td>{user.email}</td>
                            <td><Badge pill bg="light" text="dark" className="role-badge">{user.roleName || 'No Role'}</Badge></td>
                            <td className="text-center action-buttons">
                                <OverlayTrigger overlay={<Tooltip>Edit Role</Tooltip>}>
                                    <Button variant="link" className="icon-button" onClick={() => openEditModal(user)}>
                                        <Pencil />
                                    </Button>
                                </OverlayTrigger>
                                <OverlayTrigger overlay={<Tooltip>Remove User</Tooltip>}>
                                    <Button variant="link" className="icon-button-danger" onClick={() => setUserToDelete(user)}>
                                        <Trash />
                                    </Button>
                                </OverlayTrigger>
                            </td>
                        </tr>
                    ))}
                    </tbody>
                </Table>
            </div>

            <Modal centered show={showInviteModal} onHide={() => setShowInviteModal(false)}>
                <Modal.Header closeButton><Modal.Title>Invite New User</Modal.Title></Modal.Header>
                <Modal.Body>
                    <Form.Group className="mb-3">
                        <Form.Label>Email Address</Form.Label>
                        <Form.Control type="email" value={inviteEmail} onChange={e => setInviteEmail(e.target.value)} placeholder="<EMAIL>" autoFocus />
                    </Form.Group>
                    <Form.Group>
                        <Form.Label>Assign Role</Form.Label>
                        <Form.Select value={selectedRoleId} onChange={e => setSelectedRoleId(e.target.value)} disabled={roles.length === 0}>
                            {roles.length === 0 ? <option>No roles available</option> : roles.map(role => <option key={role.id} value={role.id}>{role.name}</option>)}
                        </Form.Select>
                    </Form.Group>
                </Modal.Body>
                <Modal.Footer>
                    <Button variant="secondary" onClick={() => setShowInviteModal(false)}>Cancel</Button>
                    <Button variant="primary" onClick={handleInvite} disabled={isSubmitting || !inviteEmail || !selectedRoleId}>
                        {isSubmitting ? 'Sending...' : 'Send Invite'}
                    </Button>
                </Modal.Footer>
            </Modal>

            <Modal centered show={!!editingUser} onHide={() => setEditingUser(null)}>
                <Modal.Header closeButton><Modal.Title>Edit User: {editingUser?.username}</Modal.Title></Modal.Header>
                <Modal.Body>
                    <p><strong>Email:</strong> {editingUser?.email}</p>
                    <Form.Group>
                        <Form.Label>Change Role</Form.Label>
                        <Form.Select value={selectedRoleId} onChange={e => setSelectedRoleId(e.target.value)}>
                            {roles.map(role => <option key={role.id} value={role.id}>{role.name}</option>)}
                        </Form.Select>
                    </Form.Group>
                </Modal.Body>
                <Modal.Footer>
                    <Button variant="secondary" onClick={() => setEditingUser(null)}>Cancel</Button>
                    <Button variant="primary" onClick={handleUpdateUser} disabled={isSubmitting}>{isSubmitting ? 'Saving...' : 'Save Changes'}</Button>
                </Modal.Footer>
            </Modal>

            <Modal centered show={!!userToDelete} onHide={() => setUserToDelete(null)}>
                <Modal.Header closeButton><Modal.Title>Confirm Removal</Modal.Title></Modal.Header>
                <Modal.Body>Are you sure you want to remove <strong>{userToDelete?.username}</strong> from the organization?</Modal.Body>
                <Modal.Footer>
                    <Button variant="secondary" onClick={() => setUserToDelete(null)}>Cancel</Button>
                    <Button variant="danger" onClick={handleDeleteUser} disabled={isSubmitting}>{isSubmitting ? 'Removing...' : 'Remove User'}</Button>
                </Modal.Footer>
            </Modal>
        </div>
    );
};

export default UserManagement;