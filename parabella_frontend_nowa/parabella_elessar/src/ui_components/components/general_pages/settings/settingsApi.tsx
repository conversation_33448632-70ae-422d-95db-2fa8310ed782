// src/api/settingsApi.ts (UPDATED)

import { Role, Permission } from './types';

import {User} from "../../../../services/authService.ts";
import api from '../../DmaModule/context_module/api.ts';

// --- User Management ---
export const getUsers = async (): Promise<User[]> => {
    const { data } = await api.get('/users');
    return data;
};

// NEW
export const inviteUser = async (email: string, roleId: number): Promise<User> => {
    const { data } = await api.post('/users/invite', { email, roleId });
    return data;
}

// NEW
export const updateUserRole = async (userId: number, roleId: number): Promise<User> => {
    const { data } = await api.put(`/users/${userId}/role`, { roleId });
    return data;
}

// NEW
export const deleteUser = async (userId: number): Promise<void> => {
    await api.delete(`/users/${userId}`);
}

// --- Role Management ---
export const getRoles = async (): Promise<Role[]> => {
    const { data } = await api.get('/role-management/roles');
    return data;
};

// NEW
export const createRole = async (name: string, permissionIds: number[]): Promise<Role> => {
    const { data } = await api.post('/role-management/roles', { name, permissionIds });
    return data;
}

export const getPermissions = async (): Promise<Permission[]> => {
    const { data } = await api.get('/role-management/permissions');
    return data;
};

// UPDATED to send only permissionIds for a single role
export const updateRolePermissions = async (
    { roleId, permissionIds }: { roleId: number; permissionIds: Set<number> }
): Promise<Role> => {
    const { data } = await api.put(`/role-management/roles/${roleId}`, {
        permissionIds: Array.from(permissionIds)
    });
    return data;
};

// NEW
export const deleteRole = async (roleId: number): Promise<void> => {
    await api.delete(`/role-management/roles/${roleId}`);
}