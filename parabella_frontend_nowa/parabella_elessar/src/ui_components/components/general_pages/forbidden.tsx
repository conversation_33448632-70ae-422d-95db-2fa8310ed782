
import { Container, But<PERSON> } from 'react-bootstrap';
import { <PERSON> } from 'react-router-dom';

const Forbidden = () => {
    return (
        <Container className="text-center mt-5">
            <h1>403 - Access Denied</h1>
            <p className="lead">
                Sorry, you do not have the necessary permissions to view this page.
            </p>
            <Link to={`${import.meta.env.BASE_URL}dashboard/dashboard`}>
                <Button variant="primary">Go to Dashboard</Button>
            </Link>
        </Container>
    );
};

export default Forbidden;