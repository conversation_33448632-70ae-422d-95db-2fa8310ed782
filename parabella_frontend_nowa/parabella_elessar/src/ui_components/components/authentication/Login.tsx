import React, { useState, Fragment } from 'react';
import { useNavi<PERSON>, Link } from 'react-router-dom';
import { <PERSON>, Alert, <PERSON>, Button, Col, Container, InputGroup } from 'react-bootstrap';
import { Helmet, HelmetProvider } from 'react-helmet-async';
import axios from 'axios';
import { imagesData } from '../../templateLogic/commonimages';
import {API_BASE_URL} from "../../../config/APIEndpoints.ts";

const API_URL = API_BASE_URL + '/auth';

const Login: React.FC = () => {
    const [username, setUsername] = useState('');
    const [password, setPassword] = useState('');
    const [totpCode, setTotpCode] = useState('');
    const [error, setError] = useState<string | null>(null);

    // For initial 2FA setup
    const [twoFactorSetupRequired, setTwoFactorSetupRequired] = useState(false);
    const [qrCodeUrl, setQrCodeUrl] = useState<string | null>(null);
    const [twoFactorVerificationCode, setTwoFactorVerificationCode] = useState('');

    // If user already has 2FA enabled
    const [isTwoFactorEnabled, setIsTwoFactorEnabled] = useState(false);

    const [setupUsername, setSetupUsername] = useState<string | null>(null);

    const navigate = useNavigate();

    const handleInitialLogin = async () => {
        try {
            const response = await axios.post(`${API_URL}/signin`, { username, password });
            // If we get here without catch, it means we got a JWT directly (no TOTP needed)
            localStorage.setItem('user', JSON.stringify(response.data));
            navigate(`${import.meta.env.BASE_URL}mithril/CreateNewProjectDMA`);
        } catch (err: any) {
            console.error('Initial login failed:', err);
            if (err.response && err.response.data && err.response.data.message) {
                const msg = err.response.data.message.toLowerCase();
                if (msg.includes('2fa setup required')) {
                    // 2FA setup phase
                    const qrUrlFromResponse = err.response.data.qrCodeUrl;
                    const userFromResponse = err.response.data.username;
                    setQrCodeUrl(qrUrlFromResponse || null);
                    setSetupUsername(userFromResponse || null);
                    setTwoFactorSetupRequired(true);
                    setError('2FA setup required. Scan the QR code below and enter a TOTP code to enable 2FA.');
                } else if (msg.includes('invalid or missing totp code')) {
                    // 2FA already enabled, need TOTP code
                    setIsTwoFactorEnabled(true);
                    setError('2FA is enabled. Please provide your TOTP code.');
                } else {
                    setError('Invalid username or password');
                }
            } else {
                setError('Invalid username or password');
            }
        }
    };

    const handleTwoFactorLogin = async () => {
        try {
            const response = await axios.post(`${API_URL}/signin`, { username, password, totpCode });
            localStorage.setItem('user', JSON.stringify(response.data));
            navigate(`${import.meta.env.BASE_URL}mithril/CreateNewProjectDMA`);
        } catch (err: any) {
            console.error('2FA login failed:', err);
            setError('Invalid credentials or 2FA code');
        }
    };

    const handle2FASetupVerification = async () => {
        if (!setupUsername) {
            setError('No username available for 2FA setup verification.');
            return;
        }

        try {
            const response = await axios.post(`${API_URL}/verify2fa`, {
                username: setupUsername,
                code: twoFactorVerificationCode
            });

            if (response.data.message && response.data.message.toLowerCase().includes('2fa verified')) {
                setIsTwoFactorEnabled(true);
                setTwoFactorSetupRequired(false);
                setError('2FA enabled successfully! Please login again with your TOTP code.');
            } else {
                setError('Invalid TOTP code for verification. Please try again.');
            }
        } catch (err: any) {
            console.error('2FA setup verification failed:', err);
            setError('Failed to verify TOTP code. Please try again.');
        }
    };

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        setError(null);

        if (twoFactorSetupRequired) {
            // 2FA setup verification
            if (!twoFactorVerificationCode) {
                setError('Please enter the TOTP code from your authenticator app.');
                return;
            }
            handle2FASetupVerification();
        } else if (isTwoFactorEnabled) {
            // Login with TOTP code
            if (!username || !password || !totpCode) {
                setError('Username, password, and 2FA code are required');
                return;
            }
            handleTwoFactorLogin();
        } else {
            // Initial login (username/password only)
            if (!username || !password) {
                setError('Username and password are required');
                return;
            }
            handleInitialLogin();
        }
    };

    return (
        <Fragment>
            <HelmetProvider>
                <Helmet>
                    <body className='login-page'></body>
                </Helmet>
            </HelmetProvider>
            <Container fluid className="vh-100">
                <Row className="h-100">
                    <Col md={6} className="d-flex align-items-center justify-content-center bg-light">
                        <div className="p-4" style={{ maxWidth: '400px', width: '100%' }}>
                            <img src={imagesData('parabella_logo')} className="mb-4" alt="logo" style={{ width: '170px' }} />
                            <h2 className="mb-4 font-weight-bold">Sign in to your account</h2>
                            {error && (
                                <Alert variant="danger" onClose={() => setError(null)} dismissible>{error}</Alert>
                            )}
                            <Form onSubmit={handleSubmit}>
                                {!twoFactorSetupRequired && (
                                    <>
                                        <Form.Group className="mb-3" controlId="formBasicUsername">
                                            <Form.Label>Username</Form.Label>
                                            <InputGroup>
                                                <InputGroup.Text style={{backgroundColor: 'white'}}>
                                                    <i className="bi bi-person"></i>
                                                </InputGroup.Text>
                                                <Form.Control
                                                    type="text"
                                                    placeholder="Enter your username"
                                                    value={username}
                                                    onChange={(e) => setUsername(e.target.value)}
                                                    className="form-control"
                                                />
                                            </InputGroup>
                                        </Form.Group>

                                        <Form.Group className="mb-3" controlId="formBasicPassword">
                                            <Form.Label>Password</Form.Label>
                                            <InputGroup>
                                                <InputGroup.Text style={{ backgroundColor: 'white' }}>
                                                    <i className="bi bi-lock"></i>
                                                </InputGroup.Text>
                                                <Form.Control
                                                    type="password"
                                                    placeholder="Enter your password"
                                                    value={password}
                                                    onChange={(e) => setPassword(e.target.value)}
                                                    className="form-control"
                                                />
                                            </InputGroup>
                                        </Form.Group>
                                    </>
                                )}

                                {twoFactorSetupRequired && qrCodeUrl && (
                                    <div className="mb-3 text-center">
                                        <p>Scan this QR code with your authenticator app:</p>
                                        <img
                                            src={`https://api.qrserver.com/v1/create-qr-code/?data=${encodeURIComponent(qrCodeUrl)}`}
                                            alt="TOTP QR Code"
                                            style={{ marginBottom: '1rem' }}
                                        />
                                        <p>After scanning, enter a TOTP code from your app to verify 2FA setup:</p>
                                        <Form.Group className="mb-3" controlId="formBasicTOTPSetup">
                                            <Form.Label>Verification Code (TOTP)</Form.Label>
                                            <InputGroup>
                                                <InputGroup.Text style={{ backgroundColor: 'white' }}>
                                                    <i className="bi bi-shield-lock-fill"></i>
                                                </InputGroup.Text>
                                                <Form.Control
                                                    type="text"
                                                    placeholder="Enter the TOTP code"
                                                    value={twoFactorVerificationCode}
                                                    onChange={(e) => setTwoFactorVerificationCode(e.target.value)}
                                                    className="form-control"
                                                />
                                            </InputGroup>
                                        </Form.Group>
                                    </div>
                                )}

                                {isTwoFactorEnabled && !twoFactorSetupRequired && (
                                    <Form.Group className="mb-3" controlId="formBasicTOTP">
                                        <Form.Label>2FA Code (TOTP)</Form.Label>
                                        <InputGroup>
                                            <InputGroup.Text style={{ backgroundColor: 'white' }}>
                                                <i className="bi bi-shield-lock-fill"></i>
                                            </InputGroup.Text>
                                            <Form.Control
                                                type="text"
                                                placeholder="Enter your 2FA code"
                                                value={totpCode}
                                                onChange={(e) => setTotpCode(e.target.value)}
                                                className="form-control"
                                            />
                                        </InputGroup>
                                    </Form.Group>
                                )}

                                <Button variant="primary" type="submit" className="w-100">
                                    {twoFactorSetupRequired ? 'Verify 2FA Setup' : 'Sign in'}
                                </Button>
                            </Form>
                            <div className="mt-4 text-center">
                                <Link to={`${import.meta.env.BASE_URL}pages/authentication/forgotpassword`} className="d-block mb-2">Forgot your password?</Link>
                                <p className="primary">Don't have an account? <Link className="secondary" to={`${import.meta.env.BASE_URL}authentication/register`}>Sign up</Link></p>
                            </div>
                        </div>
                    </Col>
                    <Col md={6} className="d-none d-md-block" style={{ backgroundImage: `url(${imagesData('login_background')})`, backgroundSize: 'cover', backgroundPosition: 'center'}}>
                    </Col>
                </Row>
            </Container>
        </Fragment>
    );
};

export default Login;
