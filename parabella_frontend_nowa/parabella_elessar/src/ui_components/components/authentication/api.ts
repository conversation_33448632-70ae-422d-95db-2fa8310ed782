// src/config/api.ts

import axios from 'axios';

import {getAuthToken} from "../../../services/authService.ts";
import {API_BASE_URL} from "../../../config/APIEndpoints.ts";



const api = axios.create({
    baseURL: API_BASE_URL,
});

// Add a request interceptor to include the Authorization header
api.interceptors.request.use(
    (config) => {
        const token = getAuthToken();
        if (token) {
            config.headers['Authorization'] = `Bearer ${token}`;
        }
        return config;
    },
    (error) => {
        return Promise.reject(error);
    }
);

export default api;
