import React, { useEffect, useState, useRef } from 'react';
import { Badge, Card, Col, Form, OverlayTrigger, Row, Tooltip, Button } from 'react-bootstrap';
import { IroDto } from '../../mainuser_navigation_module/04_ESRS_Selection/IroService';
import { IroEvaluation, EsrsTopic } from '../../utilities/types';
import {
    calculateFinancialMaterialityPotential,
    calculateImpactMaterialityActual,
    calculateImpactMaterialityPotential,
} from '../../utilities/calculations';
import { Info } from 'lucide-react';
import { fieldDescriptions } from '../../static/constants';
import chatbotAxios from '../../../../../config/chatbotAPIConfig';
import "../../mainuser_navigation_module/05_ImpactFinancialAnalysis/TopicDetailsForm.css";

interface StakeholderIroEvaluationFormProps {
    iroDto: IroDto;
    evaluation: IroEvaluation;
    onEvaluationChange: (iroId: number, updatedData: Partial<IroEvaluation>) => void;
    isLocked: boolean;
    topic: EsrsTopic;
}

const StakeholderIroEvaluationForm: React.FC<StakeholderIroEvaluationFormProps> = ({
                                                                                       iroDto,
                                                                                       evaluation,
                                                                                       onEvaluationChange,
                                                                                       isLocked,
                                                                                       topic,
                                                                                   }) => {
    const isReadOnly = isLocked;
    const [formData, setFormData] = useState<IroEvaluation>(() => ({
        iroId: iroDto.id!,
        companyId: iroDto.companyId || 0,
        actualPotentialImpact: '',
        affectedArea: '',
        description: '',
        effect: '',
        connection: '',
        scale: 1,
        scope: 1,
        irreversibility: 1,
        probability: 0.2,
        financialMaterialityActualImpact: 1,
        timeSpan: 0.2,
        impactMaterialityActualImpact: '',
        impactMaterialityPotentialImpact: '',
        financialMaterialityPotentialImpact: '',
        resultMaterialityAssessment: '',
        lastModifiedBy: '',
        lastModifiedAt: '',
        is_locked: false, // Ensure default for is_locked
        ...evaluation,
    }));

    const [loadingDescriptionAI, setLoadingDescriptionAI] = useState(false);
    const [loadingEffectAI, setLoadingEffectAI] = useState(false);

    // To prevent initial calculation run if evaluation prop hasn't been fully processed
    const isInitialMount = useRef(true);

    useEffect(() => {
        setFormData(prev => ({
            ...prev,
            ...evaluation,
            iroId: iroDto.id!,
            companyId: iroDto.companyId || 0,
        }));
        // After the first update from props, it's no longer the initial mount for this specific evaluation
        if (evaluation && Object.keys(evaluation).length > 0) {
            isInitialMount.current = false;
        }
    }, [evaluation, iroDto.id, iroDto.companyId]);


    useEffect(() => {
        // Avoid running calculation on initial mount if critical formData isn't ready,
        // or if it's locked (no calculations needed)
        if (isInitialMount.current || !formData.iroId || isReadOnly) {
            // If it was the initial mount and formData is now populated, allow next run
            if (formData.iroId) isInitialMount.current = false;
            return;
        }

        const effectiveIrreversibility =
            iroDto.iroType === 'negative' ? (formData.irreversibility || 1) : 0;

        const effectiveProbability =
            formData.actualPotentialImpact === 'actual'
                ? 1
                : (formData.probability || 0.2);

        const inputs = {
            scope: formData.scope || 1,
            scale: formData.scale || 1,
            irreversibility: effectiveIrreversibility,
            probability: effectiveProbability,
            financialImpact: formData.financialMaterialityActualImpact || 1,
            timeSpan: formData.timeSpan || 0.2,
        };

        const isPositiveImpact = iroDto.iroType === 'positive';

        const actualImpact = calculateImpactMaterialityActual(inputs, isPositiveImpact);
        const impactPotential = calculateImpactMaterialityPotential(actualImpact, inputs.probability);
        const financialPotential = calculateFinancialMaterialityPotential(
            inputs.financialImpact,
            inputs.timeSpan
        );

        const relevanceResult =
            parseFloat(impactPotential.toString()) >= 2.5 || parseFloat(financialPotential.toString()) >= 2.5
                ? 'Relevant'
                : 'Not Relevant';

        const newCalculatedFields: Partial<IroEvaluation> = {
            impactMaterialityActualImpact: actualImpact.toFixed(2),
            impactMaterialityPotentialImpact: impactPotential.toFixed(2),
            financialMaterialityPotentialImpact: financialPotential.toFixed(2),
            resultMaterialityAssessment: relevanceResult,
        };

        // Check if any calculated field has actually changed to prevent unnecessary updates
        const hasChanged =
            formData.impactMaterialityActualImpact !== newCalculatedFields.impactMaterialityActualImpact ||
            formData.impactMaterialityPotentialImpact !== newCalculatedFields.impactMaterialityPotentialImpact ||
            formData.financialMaterialityPotentialImpact !== newCalculatedFields.financialMaterialityPotentialImpact ||
            formData.resultMaterialityAssessment !== newCalculatedFields.resultMaterialityAssessment;

        if (hasChanged) {
            setFormData((prev) => ({
                ...prev,
                ...newCalculatedFields,
            }));
            // Only propagate if not read-only (already checked by `isReadOnly` at the start of effect)
            onEvaluationChange(iroDto.id!, newCalculatedFields);
        }

    }, [
        formData.scope,
        formData.scale,
        formData.irreversibility,
        formData.probability,
        formData.financialMaterialityActualImpact,
        formData.timeSpan,
        formData.actualPotentialImpact,
        iroDto.iroType,
        // Minimal dependencies that trigger re-calculation directly.
        // `formData.iroId` and `iroDto.id` are for ensuring context.
        // `onEvaluationChange` should be stable from parent.
        // `isReadOnly` controls if propagation happens.
        // The calculated fields (e.g., formData.impactMaterialityActualImpact) are NOT direct dependencies here;
        // their changes are a RESULT of this effect. The `hasChanged` check handles loop prevention.
        formData.iroId, // From local state, should be stable after init
        iroDto.id,      // From props, stable per IRO
        onEvaluationChange,
        isReadOnly
    ]);


    const handleFieldChange = (field: keyof IroEvaluation, value: any) => {
        if (isReadOnly) return;
        // When a user changes a field, update local state immediately
        // and propagate this specific change to the parent.
        // The calculation useEffect will then pick up this change.
        const updatedField = { [field]: value };
        setFormData(prev => ({ ...prev, ...updatedField }));
        onEvaluationChange(iroDto.id!, updatedField);
    };

    const handleAutoFillDescription = async () => {
        if (isReadOnly || !formData.actualPotentialImpact || !formData.affectedArea) return;
        setLoadingDescriptionAI(true);
        try {
            const payload = {
                actualPotentialImpact: formData.actualPotentialImpact,
                affectedArea: formData.affectedArea,
                mainTopic: topic.topic,
                subtopic: topic.subtopic,
                subSubTopic: topic.subSubTopic,
                generationType: 'DESCRIPTION',
            };
            const response = await chatbotAxios.post('/api/chat/generateDescriptionEffect', payload);
            const { reply } = response.data;
            handleFieldChange('description', reply || ''); // Use handleFieldChange to ensure propagation
        } catch (err) {
            console.error('Error generating AI description:', err);
        } finally {
            setLoadingDescriptionAI(false);
        }
    };

    const handleAutoFillEffect = async () => {
        if (isReadOnly || !formData.actualPotentialImpact || !formData.affectedArea) return;
        setLoadingEffectAI(true);
        try {
            const payload = {
                actualPotentialImpact: formData.actualPotentialImpact,
                affectedArea: formData.affectedArea,
                mainTopic: topic.topic,
                subtopic: topic.subtopic,
                subSubTopic: topic.subSubTopic,
                generationType: 'EFFECT',
            };
            const response = await chatbotAxios.post('/api/chat/generateDescriptionEffect', payload);
            const { reply } = response.data;
            handleFieldChange('effect', reply || ''); // Use handleFieldChange to ensure propagation
        } catch (err) {
            console.error('Error generating AI effect:', err);
        } finally {
            setLoadingEffectAI(false);
        }
    };

    const renderInfoIcon = (fieldKey: string) => (
        <OverlayTrigger
            placement="right"
            overlay={<Tooltip id={`tooltip-${fieldKey}-${iroDto.id}`}>{fieldDescriptions[fieldKey]}</Tooltip>}
        >
            <Info size={16} className="ms-1 info-icon" />
        </OverlayTrigger>
    );

    const possibleValues = {
        Scale: ['minimal', 'low', 'medium', 'high', 'absolute'],
        Scope: ['limited', 'concentrated', 'medium', 'widespread', 'global'],
        Irreversibility: ['easily', 'moderately', 'difficult', 'practically', 'absolutely'],
        Probability: ['Very Unlikely', 'Unlikely', 'Possible', 'Likely', 'Almost Certain'],
        Impact: ['minimal', 'low', 'medium', 'high', 'absolute'],
        Time: ['unlikely long-term', 'likely long-term', 'mid-term', 'likely short-term', 'very short-term'],
    };

    const renderSlider = (
        field: keyof IroEvaluation,
        min: number,
        max: number,
        step: number,
        value: number | undefined | string, // value can be string from toFixed(2)
        labels: string[],
        isProbabilityOrTime = false
    ) => {
        const numValue = typeof value === 'string' ? parseFloat(value) : value;
        const stepsCount = (max - min) / step + 1;
        const ticks: JSX.Element[] = [];
        for (let i = 0; i < stepsCount; i++) {
            const pos = (i / (stepsCount - 1)) * 100;
            ticks.push(<span key={i} className="tick" style={{ left: `${pos}%` }} />);
        }
        const valOrDefault = numValue === undefined || isNaN(numValue) ? (isProbabilityOrTime ? min : 1) : numValue;
        const displayedValueIndex = isProbabilityOrTime
            ? Math.round((valOrDefault - min) / step)
            : Math.max(0, Math.min(labels.length - 1, valOrDefault - 1)); // Ensure index is within bounds
        const displayedValue = labels[displayedValueIndex] ?? labels[0];

        return isReadOnly ? (
            <Form.Control type="text" readOnly plaintext value={displayedValue} />
        ) : (
            <div className="form-range-container">
                <input
                    type="range"
                    className="form-range"
                    min={min}
                    max={max}
                    step={step}
                    value={valOrDefault}
                    onChange={(e) => handleFieldChange(field, Number(e.target.value))}
                />
                <div className="ticks-container">{ticks}</div>
                <div className="range-label">{displayedValue}</div>
            </div>
        );
    };

    // Fallback for topic if it's somehow undefined/empty
    const safeTopic = topic && Object.keys(topic).length > 0 ? topic : { topic: "N/A", subtopic: "N/A", subSubTopic: "N/A" };


    return (
        // ... (rest of your JSX remains the same)
        // Ensure unique keys for tooltips if multiple forms are on the page, e.g., by appending iroDto.id
        // The renderInfoIcon already does this by including iroDto.id in the tooltip id
        // The form structure provided in the previous answer for StakeholderIroEvaluationForm is suitable
        <div className="topic-details-card">
            <Card.Body className="topic-details-body">
                <h5 className="section-heading">General Information for IRO: {iroDto.name}</h5>
                <p>
                    <Badge bg={iroDto.iroType === 'positive' ? 'success' : (iroDto.iroType === 'negative' ? 'danger' : 'secondary')}>
                        Type: {iroDto.iroType?.toUpperCase()}
                    </Badge>
                </p>
                <Form>
                    <Row className="mb-3">
                        <Col md={6}>
                            <Form.Group>
                                <Form.Label>
                                    Affected Area {renderInfoIcon('affectedArea')}
                                </Form.Label>
                                {isReadOnly ? (
                                    <Form.Control readOnly plaintext value={formData.affectedArea || 'N/A'} />
                                ) : (
                                    <Form.Select
                                        value={formData.affectedArea || ''}
                                        onChange={(e) => handleFieldChange('affectedArea', e.target.value)}
                                    >
                                        <option value="">Select</option>
                                        <option value="own operation">Own Operation</option>
                                        <option value="upstream value chain">Upstream Value Chain</option>
                                        <option value="downstream value chain">Downstream Value Chain</option>
                                    </Form.Select>
                                )}
                            </Form.Group>
                        </Col>
                        <Col md={6}>
                            <Form.Group>
                                <Form.Label>
                                    Impact Type (Actual/Potential) {renderInfoIcon('actualPotentialImpact')}
                                </Form.Label>
                                {isReadOnly ? (
                                    <Form.Control readOnly plaintext value={formData.actualPotentialImpact || 'N/A'} />
                                ) : (
                                    <Form.Select
                                        value={formData.actualPotentialImpact || ''}
                                        onChange={(e) => handleFieldChange('actualPotentialImpact', e.target.value)}
                                        disabled={!formData.affectedArea}
                                    >
                                        <option value="">Select</option>
                                        <option value="actual">Actual Impact</option>
                                        <option value="chance">Potential Impact (Risk/Opportunity)</option>
                                    </Form.Select>
                                )}
                            </Form.Group>
                        </Col>
                    </Row>

                    <Row className="mb-3">
                        <Col md={6}>
                            <Form.Group style={{ position: 'relative' }}>
                                <Form.Label>Description {renderInfoIcon('description')}</Form.Label>
                                {isReadOnly ? (
                                    <Form.Control as="textarea" rows={3} readOnly plaintext value={formData.description || 'N/A'} />
                                ) : (
                                    <>
                                        <Form.Control
                                            as="textarea"
                                            rows={3}
                                            style={{ paddingRight: '35px' }}
                                            value={formData.description || ''}
                                            onChange={(e) => handleFieldChange('description', e.target.value)}
                                            disabled={!formData.affectedArea || !formData.actualPotentialImpact}
                                        />
                                        <Button
                                            className="ask-ai-btn"
                                            variant="white"
                                            size="sm"
                                            style={{ top: '1.8rem', right: '0.2rem' }}
                                            title="Generate Description with AI"
                                            onClick={handleAutoFillDescription}
                                            disabled={isReadOnly || !formData.affectedArea || !formData.actualPotentialImpact || loadingDescriptionAI}
                                        >
                                            {loadingDescriptionAI ? <i className="bi bi-hourglass-split"></i> : <i className="bi bi-stars"></i>}
                                        </Button>
                                    </>
                                )}
                            </Form.Group>
                        </Col>
                        <Col md={6}>
                            <Form.Group style={{ position: 'relative' }}>
                                <Form.Label>Effect {renderInfoIcon('effect')}</Form.Label>
                                {isReadOnly ? (
                                    <Form.Control as="textarea" rows={3} readOnly plaintext value={formData.effect || 'N/A'} />
                                ) : (
                                    <>
                                        <Form.Control
                                            as="textarea"
                                            rows={3}
                                            style={{ paddingRight: '35px' }}
                                            value={formData.effect || ''}
                                            onChange={(e) => handleFieldChange('effect', e.target.value)}
                                            disabled={!formData.affectedArea || !formData.actualPotentialImpact}
                                        />
                                        <Button
                                            className="ask-ai-btn"
                                            variant="white"
                                            size="sm"
                                            style={{ top: '1.8rem', right: '0.2rem' }}
                                            title="Generate Effect with AI"
                                            onClick={handleAutoFillEffect}
                                            disabled={isReadOnly || !formData.affectedArea || !formData.actualPotentialImpact || loadingEffectAI}
                                        >
                                            {loadingEffectAI ? <i className="bi bi-hourglass-split"></i> : <i className="bi bi-stars"></i>}
                                        </Button>
                                    </>
                                )}
                            </Form.Group>
                        </Col>
                    </Row>

                    <Row className="mb-3">
                        <Col md={6}>
                            <Form.Group>
                                <Form.Label>Connection {renderInfoIcon('connection')}</Form.Label>
                                {isReadOnly ? (
                                    <Form.Control readOnly plaintext value={formData.connection || 'N/A'} />
                                ) : (
                                    <Form.Select
                                        value={formData.connection || ''}
                                        onChange={(e) => handleFieldChange('connection', e.target.value)}
                                        disabled={!formData.affectedArea || !formData.actualPotentialImpact}
                                    >
                                        <option value="">Select</option>
                                        <option value="caused">Caused</option>
                                        <option value="linked">Linked</option>
                                        <option value="contributed">Contributed</option>
                                    </Form.Select>
                                )}
                            </Form.Group>
                        </Col>
                    </Row>
                </Form>

                <h5 className="mt-4 section-heading">Impact Materiality</h5>
                <Form>
                    <Row className="mb-3">
                        <Col md={6}>
                            <Form.Group><Form.Label>Scale {renderInfoIcon('scale')}</Form.Label>
                                {renderSlider('scale', 1, 5, 1, formData.scale, possibleValues.Scale)}
                            </Form.Group>
                        </Col>
                        <Col md={6}>
                            <Form.Group><Form.Label>Scope {renderInfoIcon('scope')}</Form.Label>
                                {renderSlider('scope', 1, 5, 1, formData.scope, possibleValues.Scope)}
                            </Form.Group>
                        </Col>
                    </Row>

                    {iroDto.iroType === 'negative' && (
                        <Row className="mb-3">
                            <Col md={6}>
                                <Form.Group><Form.Label>Irreversibility {renderInfoIcon('irreversibility')}</Form.Label>
                                    {renderSlider('irreversibility', 1, 5, 1, formData.irreversibility, possibleValues.Irreversibility)}
                                </Form.Group>
                            </Col>
                        </Row>
                    )}

                    {formData.actualPotentialImpact === 'chance' && (
                        <Row className="mb-3">
                            <Col md={6}>
                                <Form.Group><Form.Label>Probability {renderInfoIcon('probability')}</Form.Label>
                                    {renderSlider('probability', 0.2, 1, 0.2, formData.probability, possibleValues.Probability, true)}
                                </Form.Group>
                            </Col>
                        </Row>
                    )}
                </Form>

                <h5 className="mt-4 section-heading">Financial Materiality</h5>
                <Form>
                    <Row className="mb-3">
                        <Col md={6}>
                            <Form.Group><Form.Label>Financial Materiality Actual Impact {renderInfoIcon('financialMaterialityActualImpact')}</Form.Label>
                                {renderSlider('financialMaterialityActualImpact', 1, 5, 1, formData.financialMaterialityActualImpact, possibleValues.Impact)}
                            </Form.Group>
                        </Col>
                        <Col md={6}>
                            <Form.Group><Form.Label>Time Span {renderInfoIcon('timeSpan')}</Form.Label>
                                {renderSlider('timeSpan', 0.2, 1, 0.2, formData.timeSpan, possibleValues.Time, true)}
                            </Form.Group>
                        </Col>
                    </Row>
                </Form>

                <Card className="mt-4 results-card">
                    <Card.Body>
                        <h5 className="mb-3 results-heading">Calculated Results</h5>
                        <Row className="mb-2">
                            <Col md={6}><Form.Label className="fw-normal">Impact Materiality Actual:</Form.Label></Col>
                            <Col md={6} className="results-field">{formData.impactMaterialityActualImpact || 'N/A'}</Col>
                        </Row>
                        <Row className="mb-2">
                            <Col md={6}><Form.Label className="fw-normal">Impact Materiality Potential:</Form.Label></Col>
                            <Col md={6} className="results-field">{formData.impactMaterialityPotentialImpact || 'N/A'}</Col>
                        </Row>
                        <Row className="mb-2">
                            <Col md={6}><Form.Label className="fw-normal">Financial Materiality Potential:</Form.Label></Col>
                            <Col md={6} className="results-field">{formData.financialMaterialityPotentialImpact || 'N/A'}</Col>
                        </Row>
                        <Row className="mb-0">
                            <Col md={6}><Form.Label className="fw-bold">Overall Materiality Assessment:</Form.Label></Col>
                            <Col md={6} className="results-field">
                                <Badge bg={formData.resultMaterialityAssessment === 'Relevant' ? 'danger' : 'secondary'} className="fs-6">
                                    {formData.resultMaterialityAssessment || 'N/A'}
                                </Badge>
                            </Col>
                        </Row>
                    </Card.Body>
                </Card>
            </Card.Body>
        </div>
    );
};

export default StakeholderIroEvaluationForm;