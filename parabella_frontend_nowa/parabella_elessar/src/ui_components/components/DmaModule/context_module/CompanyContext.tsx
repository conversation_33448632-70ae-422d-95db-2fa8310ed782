import React, {
    create<PERSON>ontext,
    Dispatch,
    ReactNode,
    SetStateAction,
    useCallback,
    useContext,
    useMemo,
    useState
} from 'react';
import {
    AnalysisData,
    Company,
    CompanyGroup,
    EsrsTopic,
    IroEvaluation,
    EsrsTopicSelection,
    Stakeholder,
    ValueChainObject
} from '../utilities/types';
import ProgressUtils from '../utilities/ProgressUtils';
import {useProjectContext} from "./ProjectContext.tsx";

interface CompanyContextProps {
    // Existing state
    companies: Company[];
    setCompanies: Dispatch<SetStateAction<Company[]>>;
    groupInfo: CompanyGroup;
    setGroupInfo: Dispatch<SetStateAction<CompanyGroup>>;
    reportingType: string;
    setReportingType: Dispatch<SetStateAction<string>>;
    loading: boolean;
    setLoading: Dispatch<SetStateAction<boolean>>;
    error: string | null;
    setError: Dispatch<SetStateAction<string | null>>;
    analysisData: AnalysisData;
    setAnalysisData: Dispatch<SetStateAction<AnalysisData>>;

    // New state for synchronized data
    selectedCompanyId: number | null;
    topics: EsrsTopic[];
    topicSelections: { [key: string]: EsrsTopicSelection };
    topicDetails: { [key: number]: IroEvaluation };
    valueChainObjects: { [key: number]: ValueChainObject[] };
    stakeholders: { [key: number]: Stakeholder[] };

    // Actions
    setSelectedCompanyId: (id: number | null) => void;
    updateAnalysisData: (newData: AnalysisData) => void;

    // Data fetching and updating functions
    fetchCompanyData: (companyId: number) => Promise<void>;
    updateTopicSelection: (selection: EsrsTopicSelection) => Promise<void>;
    updateTopicDetails: (details: IroEvaluation) => Promise<void>;
    updateValueChainObject: (object: ValueChainObject) => Promise<void>;
    updateStakeholder: (stakeholder: Stakeholder) => Promise<void>;

    // Utility functions
    getTopicById: (topicId: number) => EsrsTopic | undefined;
    getStakeholderById: (stakeholderId: number) => Stakeholder | undefined;
}

const CompanyContext = createContext<CompanyContextProps | undefined>(undefined);

export const CompanyProvider: React.FC<{ children: ReactNode }> = ({children}) => {
    // Existing state
    const {currentProject} = useProjectContext();

    const [companies, setCompanies] = useState<Company[]>([]);
    const [groupInfo, setGroupInfo] = useState<CompanyGroup>({
        id: undefined,
        companyGroupName: '',
        companyGroupAddress: '',
        companyGroupVAT: '',
        numEmployees: '',
        revenues: undefined,
        industry: '',
        subCompanies: companies
    });
    const [reportingType, setReportingType] = useState<string>('');
    const [loading, setLoading] = useState<boolean>(false);
    const [error, setError] = useState<string | null>(null);
    const [analysisData, setAnalysisData] = useState<AnalysisData>({});

    // New state
    const [selectedCompanyId, setSelectedCompanyId] = useState<number | null>(null);
    const [topics, setTopics] = useState<EsrsTopic[]>([]);
    const [topicSelections, setTopicSelections] = useState<{ [key: string]: EsrsTopicSelection }>({});
    const [topicDetails, setTopicDetails] = useState<{ [key: number]: IroEvaluation }>({});
    const [valueChainObjects, setValueChainObjects] = useState<{ [key: number]: ValueChainObject[] }>({});
    const [stakeholders, setStakeholders] = useState<{ [key: number]: Stakeholder[] }>({});


    // Utility functions
    const getTopicById = useCallback((topicId: number) => {
        return topics.find(topic => topic.id === topicId);
    }, [topics]);

    const getStakeholderById = useCallback((stakeholderId: number) => {
        const allStakeholders = Object.values(stakeholders).flat();
        return allStakeholders.find(s => s.id === stakeholderId);
    }, [stakeholders]);

    // Data fetching function
    const fetchCompanyData = useCallback(async (companyId: number) => {
        setLoading(true);
        try {
            const [
                fetchedTopics,
                fetchedSelections,
                fetchedDetails,
                fetchedVCOs,
                fetchedStakeholders
            ] = await Promise.all([
                ProgressUtils.fetchAllEsrsTopics(),
                ProgressUtils.fetchEsrsTopicSelectionsByCompanyId(companyId),
                ProgressUtils.fetchEsrsTopicDetailsByCompanyId(companyId),
                ProgressUtils.fetchValueChainObjects(companyId),
                ProgressUtils.fetchStakeholdersByCompanyId(companyId)
            ]);

            setTopics(fetchedTopics);

            // Convert arrays to mapped objects
            const selectionsMap = fetchedSelections.reduce((acc, selection) => {
                const key = `${selection.area}_${selection.topic}_${selection.subtopic}`;
                acc[key] = selection;
                return acc;
            }, {} as { [key: string]: EsrsTopicSelection });

            const detailsMap = fetchedDetails.reduce((acc, detail) => {
                acc[detail.esrsTopicId] = detail;
                return acc;
            }, {} as { [key: number]: IroEvaluation });

            setTopicSelections(selectionsMap);
            setTopicDetails(detailsMap);
            setValueChainObjects({[companyId]: fetchedVCOs});
            setStakeholders({[companyId]: fetchedStakeholders});

        } catch (err) {
            setError(err instanceof Error ? err.message : 'An error occurred while fetching data');
        } finally {
            setLoading(false);
        }
    }, []);

    // Update functions
    const updateTopicSelection = async (selection: EsrsTopicSelection) => {
        try {
            const updatedSelection = selection.id
                ? await ProgressUtils.updateEsrsTopicSelection(selection.id, selection)
                : await ProgressUtils.createEsrsTopicSelection(selection);

            setTopicSelections(prev => ({
                ...prev,
                [`${selection.area}_${selection.topic}_${selection.subtopic}`]: updatedSelection
            }));
        } catch (error) {
            setError('Failed to update topic selection');
            throw error;
        }
    };

    const updateTopicDetails = async (details: IroEvaluation) => {
        try {
            const updatedDetails = await ProgressUtils.createOrUpdateEsrsTopicDetails(details);
            setTopicDetails(prev => ({
                ...prev,
                [details.esrsTopicId]: updatedDetails
            }));
        } catch (error) {
            setError('Failed to update topic details');
            throw error;
        }
    };

    const updateValueChainObject = async (object: ValueChainObject) => {
        try {
            const updatedObject = object.id
                ? await ProgressUtils.updateValueChainObject(object)
                : await ProgressUtils.createValueChainObject(object);

            setValueChainObjects(prev => ({
                ...prev,
                [object.companyId!]: [
                    ...(prev[object.companyId!] || []).filter(obj => obj.id !== object.id),
                    updatedObject
                ]
            }));
        } catch (error) {
            setError('Failed to update value chain object');
            throw error;
        }
    };

    const updateStakeholder = async (stakeholder: Stakeholder) => {
        try {
            const updatedStakeholder = stakeholder.id
                ? await ProgressUtils.updateStakeholder(stakeholder.id, stakeholder)
                : await ProgressUtils.createStakeholder(stakeholder);

            setStakeholders(prev => ({
                ...prev,
                [stakeholder.companyId!]: [
                    ...(prev[stakeholder.companyId!] || []).filter(s => s.id !== stakeholder.id),
                    updatedStakeholder
                ]
            }));
        } catch (error) {
            setError('Failed to update stakeholder');
            throw error;
        }
    };

    const updateAnalysisData = useCallback((newData: AnalysisData) => {
        setAnalysisData(prevData => ({...prevData, ...newData}));
    }, []);

    const value = useMemo(
        () => ({
            // Existing state
            companies,
            setCompanies,
            groupInfo,
            setGroupInfo,
            reportingType,
            setReportingType,
            loading,
            setLoading,
            error,
            setError,
            analysisData,
            setAnalysisData,

            // New state and functions
            selectedCompanyId,
            setSelectedCompanyId,
            topics,
            topicSelections,
            topicDetails,
            valueChainObjects,
            stakeholders,
            updateAnalysisData,
            fetchCompanyData,
            updateTopicSelection,
            updateTopicDetails,
            updateValueChainObject,
            updateStakeholder,
            getTopicById,
            getStakeholderById,
        }),
        [
            companies, groupInfo, reportingType, loading, error, analysisData,
            selectedCompanyId, topics, topicSelections, topicDetails,
            valueChainObjects, stakeholders, updateAnalysisData, fetchCompanyData,
            getTopicById, getStakeholderById
        ]
    );

    return <CompanyContext.Provider value={value}>{children}</CompanyContext.Provider>;
};

export const useCompanyContext = () => {
    const context = useContext(CompanyContext);
    if (!context) {
        throw new Error('useCompanyContext must be used within a CompanyProvider');
    }
    return context;
};