import {<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>} from 'react-bootstrap';
import {Stakeholder} from '../../utilities/types';
import {FaBuilding, FaEnvelope, FaPencilAlt, FaTrash} from 'react-icons/fa';

interface StakeholderTileProps {
    stakeholder: Stakeholder;
    onEdit: () => void;
    onDelete: () => void;
    companyName: string | null;
}

const StakeholderTile: React.FC<StakeholderTileProps> = ({stakeholder, onEdit, onDelete, companyName}) => {
    // Example logic for status if needed, can be customized:
    const relevanceBadge = stakeholder.is_responsible
        ? <Badge bg="secondary" className="fw-normal">Responsible</Badge>
        : <Badge bg="light" text="dark" className="border fw-normal">Other</Badge>;

    return (
        <Card className="my-modern-card h-80 stakeholder-tile">
            <Card.Body className="d-flex flex-column">
                <div className="d-flex justify-content-between align-items-start mb-2">
                    <h5 className="card-title fw-semibold mb-0" style={{color: '#333'}}>
                        {stakeholder.name || 'Unnamed Stakeholder'}
                    </h5>
                    {relevanceBadge}
                </div>

                <hr className="my-2"/>

                <div className="text-muted mb-2" style={{fontSize: '0.9rem'}}>
                    <FaEnvelope className="me-1 text-muted"/>
                    <strong>Email:</strong> {stakeholder.email || 'N/A'}
                </div>
                <div className="text-muted mb-3" style={{fontSize: '0.9rem'}}>
                    <FaBuilding className="me-1 text-muted"/>
                    <strong>Organization:</strong> {companyName || 'N/A'}
                </div>

                <div className="d-flex justify-content-end mt-auto gap-3">
                    <Button variant="light" className="p-0 text-decoration-none link-button d-flex align-items-center"
                            onClick={onEdit}>
                        <FaPencilAlt className="me-1" size={14}/>
                    </Button>
                    <Button variant="light"
                            className="p-0 text-decoration-none link-button text-danger d-flex align-items-center"
                            onClick={onDelete}>
                        <FaTrash className="me-1" size={14}/>
                    </Button>
                </div>
            </Card.Body>
        </Card>
    );
};

export default StakeholderTile;
