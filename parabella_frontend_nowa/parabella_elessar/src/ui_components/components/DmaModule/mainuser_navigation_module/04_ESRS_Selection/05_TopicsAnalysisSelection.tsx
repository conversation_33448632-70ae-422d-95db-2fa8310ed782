import React, { useEffect, useRef, useState } from "react";
import Joyride, { Step } from "react-joyride";
import { <PERSON><PERSON>, <PERSON><PERSON>, Col, Container, Form, Nav, Navbar, Row, Tab } from "react-bootstrap";
import { EsrsTopic, EsrsTopicSelection, Stakeholder } from "../../utilities/types";
import "../css/TopicSelectionAnalysis.css";
import { useCompanyContext } from "../../context_module/CompanyContext";
import { useProjectContext } from "../../context_module/ProjectContext";
import { useNavigate } from "react-router-dom";
import ProgressUtils from "../../utilities/ProgressUtils";
import { getCurrentUser } from "../../../../../services/authService";
import api from "../../context_module/api";
import chatbotAxios from "../../../../../config/chatbotAPIConfig.ts";

import IroDefinitions, { IroItem } from "./IroDefinitions";
import IroService from "./IroService";

interface TopicSelectionAnalysisProps {
    openChatbot: () => void;
    activeKey: string;
}

const TopicSelectionAnalysis: React.FC<TopicSelectionAnalysisProps> = ({ openChatbot, activeKey }) => {
    const { companies } = useCompanyContext();
    const { currentProject } = useProjectContext();
    const navigate = useNavigate();

    // States for topics, selections, stakeholder, etc.
    const [topics, setTopics] = useState<EsrsTopic[]>([]);
    const [topicSelections, setTopicSelections] = useState<{ [topicId: number]: EsrsTopicSelection }>({});
    const [selectedCompanyId, setSelectedCompanyId] = useState<number | null>(null);
    const [companyStakeholders, setCompanyStakeholders] = useState<Stakeholder[]>([]);
    const [error, setError] = useState<string | null>(null);
    const [responseMessage, setResponseMessage] = useState<string | null>(null);
    const [responseVariant, setResponseVariant] = useState<"success" | "danger" | null>(null);
    const [activeTab, setActiveTab] = useState<string>("Environmental");
    const [validationErrors, setValidationErrors] = useState<string[]>([]);
    const [emailStatus, setEmailStatus] = useState<{ success: boolean; message: string } | null>(null);
    const [showErrors, setShowErrors] = useState(false);

    // Chatbot
    const [chatMessages, setChatMessages] = useState<{ sender: "user" | "ai"; text: string }[]>([]);
    const [loadingChatResponse, setLoadingChatResponse] = useState<boolean>(false);
    const chatContainerRef = useRef<HTMLDivElement | null>(null);

    // Reason for irrelevance extension logic
    const [showExtensionPrompt, setShowExtensionPrompt] = useState<{ [topicId: number]: boolean }>({});
    const [extensionText, setExtensionText] = useState<{ [topicId: number]: string }>({});

    // IRO Data: key => array of IroItem (simply keyed by esrsTopicId, e.g. "42")
    const [iroData, setIroData] = useState<{ [key: string]: IroItem[] }>({});

    // ======== Joyride Setup ========
    const [runTour, setRunTour] = useState(false);

    const steps: Step[] = [
        {
            target: '[data-tour="subtopic-desc"]',
            content: "This highlights the subtopic or sub‐subtopic. Click the icon for AI insights.",
            placement: "bottom",
        },
        {
            target: '[data-tour="relevance-toggle"]',
            content: "Use this toggle to mark if this subtopic is relevant for reporting.",
            placement: "left",
        },
        {
            target: '[data-tour="irrelevance-reason"]',
            content: "If not relevant, add a short reason here so it’s excluded from reports.",
            placement: "top",
        },
    ];

    // Start the tour
    const handleStartTour = () => setRunTour(true);

    // Scroll chatbot container on new messages
    useEffect(() => {
        if (chatContainerRef.current) {
            chatContainerRef.current.scrollTop = chatContainerRef.current.scrollHeight;
        }
    }, [chatMessages]);

    // Auto-select the project’s company if applicable
    useEffect(() => {
        if (currentProject?.companyId && selectedCompanyId !== currentProject.companyId) {
            setSelectedCompanyId(currentProject.companyId);
        }
    }, [currentProject, selectedCompanyId, companies]);

    // Preselect first company if none is set
    useEffect(() => {
        if (companies.length > 0 && !selectedCompanyId) {
            setSelectedCompanyId(companies[0].id);
        }
    }, [companies, selectedCompanyId]);

    // Whenever company is changed, fetch new data
    useEffect(() => {
        if (selectedCompanyId) {
            setTopicSelections({});
            setError(null);
            fetchTopicsAndSelections();
            fetchAllIrosData();
        }
    }, [selectedCompanyId, companies]);

    // Clear validation errors automatically after 6s
    useEffect(() => {
        let timer: NodeJS.Timeout;
        if (validationErrors.length > 0) {
            setShowErrors(true);
            timer = setTimeout(() => {
                setShowErrors(false);
                setValidationErrors([]);
            }, 6000);
        }
        return () => {
            if (timer) clearTimeout(timer);
        };
    }, [validationErrors]);

    // ===================== Data fetches =====================
    const fetchTopicsAndSelections = async () => {
        try {
            // 1) Get all topics from backend
            const fetchedTopics = await ProgressUtils.fetchAllEsrsTopics();
            setTopics(fetchedTopics);

            // 2) Get existing selections
            const selections = await ProgressUtils.fetchEsrsTopicSelectionsByCompanyId(selectedCompanyId!);
            const byTopicId = selections.reduce((acc, selection) => {
                acc[selection.esrsTopicId] = {
                    ...selection,
                    relevant: selection.relevant ?? false,
                };
                return acc;
            }, {} as { [topicId: number]: EsrsTopicSelection });
            setTopicSelections(byTopicId);

            // 3) Stakeholders
            const fetchedStakeholders = await ProgressUtils.fetchStakeholdersByCompanyId(selectedCompanyId!);
            setCompanyStakeholders(fetchedStakeholders);
        } catch (err) {
            console.error("Error fetching topics or selections:", err);
            setError("Failed to fetch topics or selections.");
        }
    };

    /**
     *  Fetch all IROs and store them as `iroData["42"] = [...]` for `esrsTopicId=42`.
     */
    const fetchAllIrosData = async () => {
        try {
            const allIros = await IroService.getAllIros();
            const newIroData: { [key: string]: IroItem[] } = {};

            allIros.forEach((dto) => {
                // We ignore subSubTopic in the key
                const key = String(dto.esrsTopicId);
                if (!newIroData[key]) {
                    newIroData[key] = [];
                }
                // Insert if not already present
                if (!newIroData[key].some((existing) => existing.id === dto.id)) {
                    newIroData[key].push({
                        id: dto.id!,
                        label: dto.name,
                        stakeholderId: dto.stakeholderId,
                        esrsTopicId: dto.esrsTopicId,
                        companyId: dto.companyId!,
                        iroType: dto.iroType,
                        subSubTopic: dto.subSubTopic?.trim() || "",
                    });
                }
            });
            setIroData(newIroData);
        } catch (err) {
            console.error("Error fetching all IROs:", err);
        }
    };

    // ===================== Grouping topics =====================
    // Just group by area, then group by "ESRSCode - topic," storing each row
    const groupTopics = (all: EsrsTopic[]) => {
        return all.reduce((acc, t) => {
            if (!acc[t.area]) {
                acc[t.area] = {};
            }
            const mainKey = `${t.esrsCode} - ${t.topic}`;
            if (!acc[t.area][mainKey]) {
                acc[t.area][mainKey] = [];
            }
            acc[t.area][mainKey].push(t);
            return acc;
        }, {} as { [area: string]: { [mainTopic: string]: EsrsTopic[] } });
    };
    const groupedTopics = groupTopics(topics);

    // ===================== Selections logic =====================
    const handleSaveSingleTopicSelection = async (selection: EsrsTopicSelection) => {
        if (!selectedCompanyId) return;
        try {
            if (selection.id) {
                // If it already has an ID, update
                await ProgressUtils.updateEsrsTopicSelection(selection.id, selection);
            } else {
                // Otherwise create
                const { id, ...rest } = selection;
                const created = await ProgressUtils.createEsrsTopicSelection(rest);
                setTopicSelections((prev) => ({
                    ...prev,
                    [created.esrsTopicId]: created,
                }));
            }
        } catch (error) {
            console.error("Error saving single topic selection:", error);
            setResponseVariant("danger");
        }
    };

    /**
     * Called when user toggles relevance or changes reason. Always references
     * that row's unique .id from the DB (no duplication).
     */
    const handleInputChange = (topicId: number, field: keyof EsrsTopicSelection, value: any) => {
        setTopicSelections((prev) => {
            const updated = {
                ...prev,
                [topicId]: {
                    ...prev[topicId],
                    esrsTopicId: topicId,
                    companyId: selectedCompanyId!,
                    [field]: value,
                },
            };
            handleSaveSingleTopicSelection(updated[topicId]);
            return updated;
        });
    };

    // ===================== Validate + Save all =====================
    const validateSelections = () => {
        const errors: string[] = [];
        Object.values(topicSelections).forEach((sel, idx) => {
            if (!sel.relevant && (!sel.reasonIrrelevance || !sel.reasonIrrelevance.trim())) {
                errors.push(`Row ${idx + 1}: Reason for irrelevance not provided`);
            }
        });
        setValidationErrors(errors);
        return errors.length === 0;
    };

    const handleSaveAllTopics = async (): Promise<boolean> => {
        if (!selectedCompanyId) {
            setResponseMessage("Please select a company first.");
            setResponseVariant("danger");
            return false;
        }
        try {
            // Save all existing selections
            const allForThisCompany = Object.values(topicSelections).filter(
                (s) => s.companyId === selectedCompanyId
            );
            for (const sel of allForThisCompany) {
                if (sel.id) {
                    await ProgressUtils.updateEsrsTopicSelection(sel.id, sel);
                } else {
                    const { id, ...newSel } = sel;
                    await ProgressUtils.createEsrsTopicSelection(newSel);
                }
            }

            // If user created IROs for a topic with no selection, create selection automatically
            for (const key in iroData) {
                const intTopicId = parseInt(key, 10);
                if (!topicSelections[intTopicId] && iroData[key].length > 0) {
                    const newSelection = {
                        esrsTopicId: intTopicId,
                        companyId: selectedCompanyId,
                        relevant: true,
                        reasonIrrelevance: "",
                    };
                    const created = await ProgressUtils.createEsrsTopicSelection(newSelection);
                    setTopicSelections((prev) => ({ ...prev, [created.esrsTopicId]: created }));
                }
            }

            await fetchTopicsAndSelections();
            return true;
        } catch (error) {
            console.error("Error saving topics:", error);
            setResponseMessage("Failed to save topics.");
            setResponseVariant("danger");
            return false;
        }
    };

    // ===================== Stakeholder emails =====================
    const handleSendEmails = async () => {
        if (validateSelections()) {
            try {
                const currentUser = getCurrentUser();
                if (!currentUser || !currentProject) {
                    console.error("No user or project selected.");
                    setEmailStatus({ success: false, message: "No user or project selected." });
                    return;
                }
                const uniqueStakeholderIds = new Set<number>();
                Object.values(iroData).forEach((arr) => {
                    arr.forEach((iro) => {
                        if (iro.stakeholderId) {
                            uniqueStakeholderIds.add(iro.stakeholderId);
                        }
                    });
                });
                const stakeholdersToNotify = companyStakeholders
                    .filter((s) => uniqueStakeholderIds.has(s.id) && !s.is_responsible)
                    .map((s) => ({
                        id: s.id,
                        name: s.name,
                        email: s.email || s.name,
                        type: s.stakeholderType,
                        companyName: companies.find((c) => c.id === selectedCompanyId)?.companyName,
                        projectId: currentProject.id,
                    }));

                await ProgressUtils.sendStakeholderEmails(stakeholdersToNotify);
                setEmailStatus({ success: true, message: "Notifications sent successfully." });
                setResponseMessage("Email notifications sent successfully.");
                setResponseVariant("success");
            } catch (error) {
                console.error("Error sending notifications:", error);
                setEmailStatus({ success: false, message: "Failed to send notifications." });
                setResponseMessage("Failed to send email notifications.");
                setResponseVariant("danger");
            }
        } else {
            setResponseMessage("Please fill out all required fields before sending email notifications.");
            setResponseVariant("danger");
        }
    };

    // ===================== Nav Buttons =====================
    const handleNext = async () => {
        if (validateSelections()) {
            const saveSuccess = await handleSaveAllTopics();
            if (saveSuccess) {
                navigate(`${import.meta.env.BASE_URL}mithril/mithril?tab=ImpactFinancialAnalysis`);
            }
        } else {
            setResponseMessage("Please complete all required fields before proceeding.");
            setResponseVariant("danger");
        }
    };

    const handleBack = async () => {
        await handleSaveAllTopics();
        navigate(`${import.meta.env.BASE_URL}mithril/mithril?tab=StakeholderAnalysis`);
    };

    // ===================== AI Logic =====================
    const generateReasonForIrrelevance = async (
        topicId: number,
        mainTopic: string,
        rowObj: EsrsTopic
    ) => {
        const companyName =
            companies.find((c) => c.id === selectedCompanyId)?.companyName || "The Company";
        const companyIndustry =
            companies.find((c) => c.id === selectedCompanyId)?.industry || "Industry";

        const requestBody = {
            companyName,
            esrsCriteria: mainTopic,
            esrsKpi: rowObj.subtopic,
            companyIndustry,
        };
        try {
            const resp = await api.post("/chat/generate-irrelevance-reason", requestBody);
            const reason = resp.data.reply;
            handleInputChange(topicId, "reasonIrrelevance", reason);
        } catch (err) {
            console.error("Error generating reason:", err);
        }
    };

    const handleAskAI = async (
        mainTopic: string,
        subtopicName: string,
        subSubs: (string | { id: number; name: string })[]
    ) => {
        setLoadingChatResponse(true);
        // If subSubs are objects, extract their names
        const subSubsText = subSubs
            .map((x) => (typeof x === "string" ? x : x.name))
            .filter(Boolean)
            .join(", ");

        const prompt = `Please analyze and provide insights on the following ESRS Criteria under CSRD (5-6 sentences):
${mainTopic}
Subtopic: ${subtopicName}
Sub-Subtopics: ${subSubsText};`;

        try {
            const { data } = await chatbotAxios.post("/api/chat/send", { message: prompt });
            const reply = data.reply || "I'm sorry, I didn't understand.";
            setChatMessages((prev) => [...prev, { sender: "ai", text: reply }]);
            setTimeout(() => {
                openChatbot();
                setLoadingChatResponse(false);
            }, 1000);
        } catch (error) {
            console.error("Error sending to AI:", error);
            setChatMessages((prev) => [...prev, { sender: "ai", text: "Failed to get response." }]);
            setTimeout(() => {
                openChatbot();
                setLoadingChatResponse(false);
            }, 1000);
        }
    };

    const toggleExtensionPrompt = (topicId: number) => {
        setShowExtensionPrompt((prev) => ({
            ...prev,
            [topicId]: !prev[topicId],
        }));
    };

    const handleExtendReason = async (topicId: number, currentText: string) => {
        const userPrompt = extensionText[topicId] || "";
        if (!userPrompt.trim()) return;
        try {
            const { data } = await api.post("/chat/extend-irrelevance-reason", {
                currentText,
                userPrompt,
            });
            const extended = data.reply;
            handleInputChange(topicId, "reasonIrrelevance", extended);
            setShowExtensionPrompt((prev) => ({ ...prev, [topicId]: false }));
            setExtensionText((prev) => ({ ...prev, [topicId]: "" }));
        } catch (error) {
            console.error("Error extending reason:", error);
        }
    };

    // ===================== Child callback for IROs =====================
    const handleIroChange = (key: string, updated: IroItem[]) => {
        setIroData((prev) => ({
            ...prev,
            [key]: updated,
        }));
    };

    // ===================== RENDER =====================
    return (
        <Container className="mt-3">
            <Joyride
                steps={steps}
                run={runTour}
                continuous={true}
                scrollToFirstStep={true}
                showProgress={true}
                showSkipButton={true}
                callback={(data) => {
                    if (data.action === "close" || data.status === "finished") {
                        setRunTour(false);
                    }
                }}
                styles={{ options: { zIndex: 9999 } }}
            />

            {loadingChatResponse && (
                <div className="loading-overlay">
                    <div className="loading-animation-container">
                        <div className="cube-grid">
                            <div className="cube cube1"></div>
                            <div className="cube cube2"></div>
                            <div className="cube cube3"></div>
                            <div className="cube cube4"></div>
                            <div className="cube cube5"></div>
                            <div className="cube cube6"></div>
                            <div className="cube cube7"></div>
                            <div className="cube cube8"></div>
                            <div className="cube cube9"></div>
                        </div>
                        <p>Processing AI Insights...</p>
                    </div>
                </div>
            )}

            <Row className="mb-3 align-items-center">
                <Col>
                    <h2 className="mb-0">Topic Selection Analysis</h2>
                </Col>
                <Col xs="auto" className="text-end">
                    <Button variant="outline-secondary" onClick={handleStartTour}>
                        <i className="bi bi-question-diamond-fill me-1"></i> Start Walkthrough
                    </Button>
                </Col>
            </Row>

            <p>
                <strong>Instructions:</strong> Review each topic (row) and select if it is relevant. If not relevant,
                provide a reason. Changes are saved automatically.
            </p>

            <Navbar expand="lg" className="mb-3 align-items-center">
                <Navbar.Toggle aria-controls="navbar-nav" />
                <Navbar.Collapse id="navbar-nav" className="align-items-center">
                    <Nav
                        variant="tabs"
                        activeKey={activeTab}
                        onSelect={(k) => k && setActiveTab(k)}
                        className="left-tabs align-items-center tab-style-2 nav-justified d-sm-flex d-block"
                    >
                        <Nav.Item>
                            <Nav.Link eventKey="Environmental">Environmental</Nav.Link>
                        </Nav.Item>
                        <Nav.Item>
                            <Nav.Link eventKey="Social">Social</Nav.Link>
                        </Nav.Item>
                        <Nav.Item>
                            <Nav.Link eventKey="Governance">Governance</Nav.Link>
                        </Nav.Item>
                    </Nav>
                    <Nav className="ms-auto align-items-center right-tabs tab-style-2 nav-justified d-sm-flex d-block" variant="tabs">
                        <Nav.Item>
                            <Nav.Link onClick={handleSendEmails} disabled={!selectedCompanyId}>
                                Send Stakeholder Mail <i className="bi bi-envelope-fill me-1"></i>
                            </Nav.Link>
                        </Nav.Item>
                        <Nav.Item>
                            <Form.Select
                                value={selectedCompanyId || ""}
                                onChange={(e) => setSelectedCompanyId(Number(e.target.value))}
                                className="company-select"
                            >
                                {companies.map((company) => (
                                    <option key={company.id} value={company.id}>
                                        {company.companyName}
                                    </option>
                                ))}
                            </Form.Select>
                        </Nav.Item>
                    </Nav>
                </Navbar.Collapse>
            </Navbar>

            <Row className="mb-4">
                <Col>
                    {responseMessage && (
                        <Alert variant={responseVariant || "info"} onClose={() => setResponseMessage(null)} dismissible>
                            {responseMessage}
                            {validationErrors.length > 0 && (
                                <div>
                                    {validationErrors.map((err, i) => (
                                        <li key={i}>{err}</li>
                                    ))}
                                </div>
                            )}
                        </Alert>
                    )}
                </Col>
            </Row>

            <Row className="justify-content-center my-1">
                <Col md={12}>
                    <Tab.Container activeKey={activeTab}>
                        <Tab.Content>
                            {["Environmental", "Social", "Governance"].map((area) => {
                                const areaTopics = groupedTopics[area] || {};
                                return (
                                    <Tab.Pane key={area} eventKey={area}>
                                        {Object.entries(areaTopics).map(([mainTopic, rows]) => (
                                            <div key={mainTopic} className="mb-4">
                                                <h4 className="mb-3">{mainTopic}</h4>
                                                {rows.map((row) => {
                                                    // Each row has a unique ID from DB
                                                    const selection = topicSelections[row.id] || { relevant: false };
                                                    const isRelevant = selection.relevant;
                                                    const reason = selection.reasonIrrelevance || "";

                                                    return (
                                                        <div
                                                            key={row.id}
                                                            className="subtopic-card card mb-4 p-3 border-0 shadow-sm"
                                                            style={{ borderRadius: "8px" }}
                                                        >
                                                            <div className="d-flex justify-content-between align-items-center mb-2">
                                                                <div className="d-flex flex-column">
                                                                    <small className="text-muted" style={{ fontWeight: 600 }}>
                                                                        {/* Show "Subtopic + Sub-Subtopic" if present */}
                                                                        {row.subSubTopic ? "Subtopic + Sub-Subtopic:" : "Subtopic:"}
                                                                    </small>
                                                                    <div className="d-flex align-items-center" data-tour="subtopic-desc">
                                                                        <i className="bi bi-folder2-fill me-2" />
                                                                        <strong style={{ fontSize: "1.15rem" }}>
                                                                            {row.subtopic}
                                                                            {row.subSubTopic ? ` – ${row.subSubTopic}` : ""}
                                                                        </strong>
                                                                        <Button
                                                                            variant="link"
                                                                            className="p-0 text-secondary icon-button ms-3"
                                                                            onClick={() =>
                                                                                handleAskAI(
                                                                                    mainTopic,
                                                                                    row.subtopic,
                                                                                    row.subSubTopic ? [row.subSubTopic] : []
                                                                                )
                                                                            }
                                                                        >
                                                                            <i className="bi bi-question-circle"></i>
                                                                        </Button>
                                                                    </div>
                                                                </div>
                                                                <div className="d-flex align-items-center" data-tour="relevance-toggle">
                                                                    <small className="me-2" style={{ fontWeight: 600 }}>
                                                                        Relevance:
                                                                    </small>
                                                                    <div className="toggle-switch d-inline-block">
                                                                        <input
                                                                            type="checkbox"
                                                                            id={`relevance-toggle-${row.id}`}
                                                                            checked={isRelevant}
                                                                            onChange={(e) =>
                                                                                handleInputChange(row.id, "relevant", e.target.checked)
                                                                            }
                                                                        />
                                                                        <label
                                                                            className="toggle-slider"
                                                                            htmlFor={`relevance-toggle-${row.id}`}
                                                                        />
                                                                    </div>
                                                                </div>
                                                            </div>

                                                            <div className="card-body p-0">
                                                                {isRelevant ? (
                                                                    <IroDefinitions
                                                                        storageKey={String(row.id)}
                                                                        existingIros={iroData[String(row.id)] || []}
                                                                        onIroChange={handleIroChange}
                                                                        stakeholders={companyStakeholders}
                                                                        isRelevant={isRelevant}
                                                                        esrsTopicId={row.id}
                                                                        subSubTopic={row.subSubTopic || ""}
                                                                        companyId={selectedCompanyId!}
                                                                    />
                                                                ) : (
                                                                    <div className="mt-3 pt-3 border-top">
                                                                        <div
                                                                            className="mb-2"
                                                                            style={{ fontWeight: 600 }}
                                                                        >
                                                                            Reason for Irrelevance
                                                                        </div>
                                                                        <div className="position-relative">
                                                                            <Form.Control
                                                                                as="textarea"
                                                                                rows={4}
                                                                                value={reason}
                                                                                data-tour="irrelevance-reason"
                                                                                onChange={(e) =>
                                                                                    handleInputChange(row.id, "reasonIrrelevance", e.target.value)
                                                                                }
                                                                                placeholder="Explain why this subtopic is irrelevant..."
                                                                            />
                                                                            <Button
                                                                                variant="link"
                                                                                className="icon-button position-absolute top-0 end-0 mt-1 me-5"
                                                                                style={{ fontSize: "1.1rem" }}
                                                                                onClick={() =>
                                                                                    generateReasonForIrrelevance(row.id, mainTopic, row)
                                                                                }
                                                                            >
                                                                                <i className="bi bi-stars"></i>
                                                                            </Button>
                                                                            <Button
                                                                                variant="link"
                                                                                className="icon-button position-absolute top-0 end-0 mt-1 me-1"
                                                                                style={{ fontSize: "1.1rem" }}
                                                                                onClick={() => toggleExtensionPrompt(row.id)}
                                                                            >
                                                                                <i className="bi bi-pencil-square"></i>
                                                                            </Button>
                                                                            {showExtensionPrompt[row.id] && (
                                                                                <div className="border p-2 mt-2 rounded bg-light">
                                                                                    <Form.Control
                                                                                        as="textarea"
                                                                                        rows={2}
                                                                                        value={extensionText[row.id] || ""}
                                                                                        onChange={(e) =>
                                                                                            setExtensionText((prev) => ({
                                                                                                ...prev,
                                                                                                [row.id]: e.target.value,
                                                                                            }))
                                                                                        }
                                                                                        placeholder="Refine the reason..."
                                                                                    />
                                                                                    <Button
                                                                                        variant="primary"
                                                                                        size="sm"
                                                                                        className="mt-2"
                                                                                        onClick={() =>
                                                                                            handleExtendReason(row.id, reason)
                                                                                        }
                                                                                    >
                                                                                        Extend Reason
                                                                                    </Button>
                                                                                </div>
                                                                            )}
                                                                        </div>
                                                                    </div>
                                                                )}
                                                            </div>
                                                        </div>
                                                    );
                                                })}
                                            </div>
                                        ))}
                                    </Tab.Pane>
                                );
                            })}
                        </Tab.Content>
                    </Tab.Container>
                </Col>
            </Row>

            <Row className="mt-4">
                <Col className="d-flex justify-content-between">
                    <Button variant="light" onClick={handleBack}>
                        <i className="bi bi-arrow-left"></i>
                    </Button>
                    <Button variant="outline-primary" onClick={handleNext}>
                        <i className="bi bi-arrow-right"></i>
                    </Button>
                </Col>
            </Row>
        </Container>
    );
};

export default TopicSelectionAnalysis;
