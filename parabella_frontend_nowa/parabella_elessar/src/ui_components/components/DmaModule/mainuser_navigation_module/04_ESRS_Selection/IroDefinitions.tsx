import React, { useEffect, useState } from "react";
import { Button, Dropdown, Form, Table } from "react-bootstrap";
import { Stakeholder } from "../../utilities/types";
import "../css/TopicSelectionAnalysis.css";
import IroService, { IroDto } from "./IroService";

export interface IroItem {
    id: string | number;
    label: string;
    stakeholderId?: number;
    esrsTopicId?: number;
    companyId: number;
    // Updated to reflect the new types
    iroType?: 'impact' | 'financial' | string;
    subSubTopic?: string;
}

interface IroDefinitionsProps {
    storageKey: string;
    existingIros: IroItem[];
    onIroChange: (key: string, updated: IroItem[]) => void;
    stakeholders: Stakeholder[];
    isRelevant: boolean;
    esrsTopicId?: number;
    subSubTopic?: string;
    companyId: number;
}

interface IroErrors {
    label?: string;
    stakeholderId?: string;
    iroType?: string;
}

const IroDefinitions: React.FC<IroDefinitionsProps> = ({
                                                           storageKey,
                                                           existingIros,
                                                           onIroChange,
                                                           stakeholders,
                                                           isRelevant,
                                                           esrsTopicId,
                                                           subSubTopic,
                                                           companyId,
                                                       }) => {
    const [iros, setIros] = useState<IroItem[]>(existingIros);
    const [errors, setErrors] = useState<{ [iroId: string]: IroErrors }>({});

    useEffect(() => {
        setIros(existingIros);
        const newErrors: { [iroId: string]: IroErrors } = {};
        existingIros.forEach((iro) => {
            newErrors[iro.id] = validateIro(iro);
        });
        setErrors(newErrors);
    }, [existingIros]);

    // Convert local item to IroDto
    const toIroDto = (item: IroItem): IroDto => ({
        id: typeof item.id === "number" ? item.id : undefined,
        name: item.label,
        esrsTopicId: item.esrsTopicId ?? esrsTopicId,
        stakeholderId: item.stakeholderId,
        companyId,
        iroType: item.iroType,
        subSubTopic: item.subSubTopic,
    });

    // Convert IroDto to local item
    const fromIroDto = (dto: IroDto): IroItem => ({
        id: dto.id ?? Math.random().toString(36).substring(2),
        label: dto.name,
        esrsTopicId: dto.esrsTopicId,
        stakeholderId: dto.stakeholderId,
        companyId,
        iroType: dto.iroType,
        subSubTopic: dto.subSubTopic,
    });

    const validateIro = (iro: IroItem): IroErrors => {
        const rowErrors: IroErrors = {};
        if (!iro.label || !iro.label.trim()) {
            rowErrors.label = "IRO Name is required";
        }
        if (!iro.stakeholderId) {
            rowErrors.stakeholderId = "Stakeholder is required";
        }
        if (!iro.iroType) {
            rowErrors.iroType = "Type is required";
        }
        return rowErrors;
    };

    // CREATE a blank IRO
    const handleAddIro = async () => {
        if (!esrsTopicId) {
            console.warn("No esrsTopicId provided - can't create IRO!");
            return;
        }
        try {
            const blankDto: IroDto = {
                name: "",
                esrsTopicId,
                companyId,
                subSubTopic,
                iroType: 'impact', // Default to 'impact'
            };
            const created = await IroService.createIro(blankDto);
            // Create empty IROEvaluation after IRO is created
            const emptyEvaluation = {
                iroId: created.id,
                actualPotentialImpact: "",
                affectedArea: "",
                description: "",
                effect: "",
                connection: "",
                scale: 0,
                scope: 0,
                irreversibility: 0,
                probability: 0,
                impactMaterialityActualImpact: "",
                impactMaterialityPotentialImpact: "",
                basisOfAssessmentOfFinancialImpact: "",
                financialMaterialityActualImpact: 0,
                financialMaterialityPotentialImpact: "",
                timeSpan: 0,
                resultMaterialityAssessment: "",
            };
            await IroService.saveEvaluation(emptyEvaluation, "");

            const newItem = fromIroDto(created);
            const updatedList = [...iros, newItem];
            setIros(updatedList);
            onIroChange(storageKey, updatedList);
            setErrors((prev) => ({
                ...prev,
                [String(newItem.id)]: validateIro(newItem),
            }));
        } catch (err) {
            console.error("Error creating IRO:", err);
        }
    };

    // DELETE an IRO and its corresponding IROEvaluation
    const handleRemoveIro = async (id: string | number) => {
        const found = iros.find((x) => x.id === id);
        if (!found) return;
        if (typeof found.id === "number") {
            try {
                await IroService.deleteIro(found.id);
                await IroService.deleteEvaluation(found.id);
            } catch (err) {
                console.error("Error deleting IRO:", err);
            }
        }
        const updated = iros.filter((x) => x.id !== id);
        setIros(updated);
        onIroChange(storageKey, updated);
        setErrors((prev) => {
            const { [String(id)]: _, ...rest } = prev;
            return rest;
        });
    };

    // UPDATE or CREATE on field change
    const handleChange = async (id: string | number, field: keyof IroItem, value: any) => {
        const updatedList = iros.map((iro) => (iro.id === id ? { ...iro, [field]: value } : iro));
        setIros(updatedList);
        onIroChange(storageKey, updatedList);
        const changed = updatedList.find((x) => x.id === id);
        if (!changed) return;

        const rowErrors = validateIro(changed);
        setErrors((prev) => ({
            ...prev,
            [String(id)]: rowErrors,
        }));

        const dto = toIroDto(changed);
        try {
            if (dto.id) {
                const updatedDto = await IroService.updateIro(dto.id, dto);
                const updatedItem = fromIroDto(updatedDto);
                const finalList = updatedList.map((i) => (i.id === id ? updatedItem : i));
                setIros(finalList);
                onIroChange(storageKey, finalList);
            } else {
                const createdDto = await IroService.createIro(dto);
                const createdItem = fromIroDto(createdDto);
                const finalList = updatedList.map((i) => (i.id === id ? createdItem : i));
                setIros(finalList);
                onIroChange(storageKey, finalList);
            }
        } catch (err) {
            console.error("Error saving IRO:", err);
        }
    };

    if (!isRelevant) {
        return <div className="text-muted">Not relevant — no IROs needed.</div>;
    }

    return (
        <div className="iro-container">
            {iros.length > 0 && (
                <Table borderless hover responsive className="iro-table mb-2 align-middle">
                    <thead className="iro-thead">
                    <tr>
                        <th className="iro-col-name">IRO Name</th>
                        <th className="iro-col-stakeholder">Responsible Stakeholder</th>
                        <th className="iro-col-impact">Type</th>
                        <th className="iro-col-remove" />
                    </tr>
                    </thead>
                    <tbody>
                    {iros.map((iro) => {
                        const rowErr = errors[iro.id] || {};
                        const hasError = Object.keys(rowErr).length > 0;
                        return (
                            <tr key={iro.id} className={hasError ? "table-danger" : ""}>
                                <td>
                                    <Form.Control
                                        type="text"
                                        placeholder="Enter IRO name"
                                        value={iro.label}
                                        onChange={(e) => handleChange(iro.id, "label", e.target.value)}
                                        isInvalid={!!rowErr.label}
                                    />
                                    {rowErr.label && <div className="text-danger small">{rowErr.label}</div>}
                                </td>
                                <td style={{ position: "relative" }}>
                                    <Dropdown>
                                        <Dropdown.Toggle
                                            className="icon-button stakeholder-dropdown w-100 text-start"
                                            id={`stakeholder-dropdown-${iro.id}`}
                                            variant={rowErr.stakeholderId ? "outline-danger" : "outline-secondary"}
                                        >
                                            {iro.stakeholderId
                                                ? stakeholders.find((s) => s.id === iro.stakeholderId)?.name ||
                                                "Select..."
                                                : "Select..."}
                                        </Dropdown.Toggle>
                                        <Dropdown.Menu
                                            align="end"
                                            className="stakeholder-dropdown-menu"
                                        >
                                            {stakeholders.map((s) => (
                                                <Dropdown.Item
                                                    key={s.id}
                                                    onClick={() =>
                                                        handleChange(iro.id, "stakeholderId", s.id)
                                                    }
                                                >
                                                    {s.name}
                                                </Dropdown.Item>
                                            ))}
                                        </Dropdown.Menu>
                                    </Dropdown>
                                    {rowErr.stakeholderId && (
                                        <div className="text-danger small mt-1">
                                            {rowErr.stakeholderId}
                                        </div>
                                    )}
                                </td>
                                <td>
                                    <div className="d-flex align-items-center gap-2">
                                        <Form.Check
                                            inline
                                            label="Impact"
                                            type="radio"
                                            name={`iroType-${iro.id}`}
                                            id={`iroType-impact-${iro.id}`}
                                            checked={iro.iroType === "impact"}
                                            onChange={() => handleChange(iro.id, "iroType", "impact")}
                                            isInvalid={!!rowErr.iroType}
                                        />
                                        <Form.Check
                                            inline
                                            label="Financial"
                                            type="radio"
                                            name={`iroType-${iro.id}`}
                                            id={`iroType-financial-${iro.id}`}
                                            checked={iro.iroType === "financial"}
                                            onChange={() => handleChange(iro.id, "iroType", "financial")}
                                            isInvalid={!!rowErr.iroType}
                                        />
                                    </div>
                                    {rowErr.iroType && (
                                        <div className="text-danger small">{rowErr.iroType}</div>
                                    )}
                                </td>
                                <td className="text-center">
                                    <Button
                                        variant="link"
                                        className="icon-button iro-remove-btn"
                                        onClick={() => handleRemoveIro(iro.id)}
                                    >
                                        <i className="bi bi-trash3"></i>
                                    </Button>
                                </td>
                            </tr>
                        );
                    })}
                    </tbody>
                </Table>
            )}
            <Button variant="primary" onClick={handleAddIro} className="btn-add-iro">
                <i className="bi bi-plus-circle me-1"></i> ADD IRO
            </Button>
        </div>
    );
};

export default IroDefinitions;