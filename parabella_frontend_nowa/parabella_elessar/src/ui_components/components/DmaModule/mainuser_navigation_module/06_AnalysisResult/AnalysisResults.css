/* ==========================================================================
   Global Variables & Base Styles
   ========================================================================== */
:root {
    --primary-color: #003366;
    --primary-hover: #002244;
    --primary-light: #dde8f1; /* Light blue for hovers/backgrounds */
    --primary-dark: #001a33;  /* For text on light backgrounds */
    --secondary-color: #6c757d;
    --accent-green: #6a994e;
    --light-green: #a7c957;
    --bg-color: #f4f7f9;
    --card-bg: #ffffff;
    --text-color: #212529;
    --text-muted: #6c757d;
    --border-color: #e9ecef;
    --card-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.05), 0 2px 4px -2px rgba(0, 0, 0, 0.05);
    --card-shadow-hover: 0 10px 15px -3px rgba(0, 0, 0, 0.07), 0 4px 6px -4px rgba(0, 0, 0, 0.07);
    --border-radius: 0.75rem; /* 12px */
    --font-family: 'Inter', -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
}

.analysis-results-container {
    background-color: var(--bg-color);
    font-family: var(--font-family);
    color: var(--text-color);
}

.page-instructions {
    font-size: 1rem;
    line-height: 1.5;
    color: var(--text-muted);
    max-width: 800px;
}

/* ==========================================================================
   Top Info Bar & Controls
   ========================================================================== */
.info-bar {
    background-color: var(--card-bg);
    border-radius: var(--border-radius);
    padding: 1rem 1.5rem;
    box-shadow: var(--card-shadow);
    border: 1px solid var(--border-color);
}

.company-select {
    max-width: 300px;
    border-radius: 8px;
    border-color: var(--border-color);
    font-weight: 500;
}
.company-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(0, 51, 102, 0.2);
}

.button-wrapper {
    display: flex;
    gap: 0.75rem;
}

/* ==========================================================================
   Summary Cards & Donut Charts
   ========================================================================== */
.summary-card {
    border: none;
    border-radius: var(--border-radius);
    background: var(--card-bg);
    box-shadow: var(--card-shadow);
    transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
    height: 100%;
}
.summary-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--card-shadow-hover);
}
.summary-card .card-body {
    display: flex;
    align-items: center;
    gap: 1.25rem;
    padding: 1.25rem;
}

.donut-wrapper {
    flex-shrink: 0;
}

.metrics-content {
    min-width: 0;
}
.metric-title {
    color: var(--text-muted);
    font-size: 0.875rem;
    font-weight: 500;
    margin-bottom: 0.25rem;
}
.metric-value {
    color: var(--primary-color);
    font-size: 1.75rem;
    font-weight: 700;
}
.metric-subtext {
    color: var(--text-muted);
    font-size: 0.875rem;
}

/* ==========================================================================
   Main Chart & Table Cards
   ========================================================================== */
.chart-card {
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    background: var(--card-bg);
    box-shadow: var(--card-shadow);
    overflow: hidden;
}
.chart-card .card-header {
    background-color: #fafbfd;
    border-bottom: 1px solid var(--border-color);
    padding: 1rem 1.5rem;
}
.chart-card .card-title {
    color: var(--primary-color);
    font-size: 1.1rem;
    font-weight: 600;
    margin: 0;
}
.chart-card .card-body {
    padding: 1.5rem;
}

/* High Materiality Topics List */
.impact-financial-card-text {
    font-size: 0.9rem;
    color: var(--text-muted);
}
.topic-list-wrapper {
    overflow-y: auto;
    flex-grow: 1;
    margin-right: -1rem; /* Hide scrollbar visually */
    padding-right: 1rem;
}
.high-materiality-list .list-group-item {
    border: none;
    padding: 0.75rem 0.25rem;
    border-bottom: 1px solid var(--border-color);
    font-size: 0.9rem;
}
.high-materiality-list .list-group-item:last-child {
    border-bottom: none;
}
.topic-name {
    font-weight: 500;
    color: var(--text-color);
}
.badge.bg-primary-light {
    background-color: var(--primary-light) !important;
    color: var(--primary-dark) !important;
    font-weight: 600;
}


/* Table Enhancements */
.table-data-points {
    font-size: 0.9rem;
    margin-bottom: 0;
}
.table-data-points thead tr {
    border-bottom: 2px solid var(--primary-color);
}
.table-data-points th {
    font-weight: 600;
    color: var(--primary-color);
    background-color: #f8f9fa;
}
.table-data-points tbody tr:hover {
    background-color: var(--primary-light);
}
.table-data-points td, .table-data-points th {
    padding: 0.85rem 1rem;
    vertical-align: middle;
}
.badge.bg-secondary-soft {
    background-color: #e9ecef;
    color: #495057;
}

/* ==========================================================================
   Export Modal
   ========================================================================== */
.export-modal .modal-content {
    border-radius: var(--border-radius);
    border: none;
    box-shadow: 0 25px 50px -12px rgba(0,0,0,0.25);
}
.export-modal .modal-header {
    background: var(--primary-color);
    color: #fff;
    border-bottom: none;
    padding: 1rem 1.5rem;
}
.export-modal .modal-header .btn-close {
    filter: invert(1) grayscale(100%) brightness(200%);
}
.export-modal .modal-title {
    font-weight: 600;
    font-size: 1.2rem;
}
.export-modal .modal-body {
    padding: 2rem;
}
.modal-intro-text {
    font-size: 1rem;
    color: var(--text-muted);
}
.export-form .form-label {
    font-weight: 600;
    color: var(--text-color);
    font-size: 0.95rem;
}
.export-form .form-check-label {
    text-transform: capitalize;
    font-size: 0.9rem;
    color: #333;
}
.form-check-input:checked {
    background-color: var(--primary-color) !important;
    border-color: var(--primary-color) !important;
}
.form-check-input:focus {
    border-color: var(--primary-hover) !important;
    box-shadow: 0 0 0 0.2rem rgba(0, 51, 102, 0.25) !important;
}
.export-modal .modal-footer {
    background-color: #f8f9fa;
    border-top: 1px solid var(--border-color);
    padding: 1rem 1.5rem;
}