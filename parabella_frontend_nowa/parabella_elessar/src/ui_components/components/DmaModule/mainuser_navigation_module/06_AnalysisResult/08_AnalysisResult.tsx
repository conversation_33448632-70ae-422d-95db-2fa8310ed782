import React, { useEffect, useMemo, useState } from 'react';
import { <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, Col, Container, Form, ListGroup, Modal, Row, Table } from 'react-bootstrap';
import Plot from 'react-plotly.js';
import { saveAs } from 'file-saver';
import * as XLSX from 'xlsx';
import { useProjectContext } from "../../context_module/ProjectContext";
import { useLocation, useNavigate } from "react-router-dom";
import ProgressUtils from "../../utilities/ProgressUtils";
import { Company, EsrsTopic, EsrsTopicSelection } from '../../utilities/types';
import "./AnalysisResults.css";
import { useCompanyContext } from "../../context_module/CompanyContext.tsx";

// Define the props for AnalysisResult
interface AnalysisResultProps {
    activeKey: string; // This prop will be passed from Mithril.tsx
}

// Define the structure for our data points for easier typing
interface RelevantDataPoint extends EsrsTopic {
    x: number;
    y: number;
    name: string;
    finalRelevance?: string | null;
}

const AnalysisResult: React.FC<AnalysisResultProps> = ({ activeKey }) => {
    const [showModal, setShowModal] = useState(false);
    const [selectedFields, setSelectedFields] = useState<string[]>([
        "esrsCode", "topic", "subtopic", "subSubTopic",
        "impactMaterialityActualImpact", "financialMaterialityActualImpact",
        "resultMaterialityAssessment"
    ]);
    const [dataName, setDataName] = useState<string>('dma_analysis_results');
    const [error, setError] = useState<string | null>(null);

    const { companies } = useCompanyContext();
    const { currentProject } = useProjectContext();
    const [selectedCompanyId, setSelectedCompanyId] = useState<number | null>(null);

    const [topics, setTopics] = useState<EsrsTopic[]>([]);
    const [selectionByTopicId, setSelectionByTopicId] = useState<{ [topicId: number]: EsrsTopicSelection }>({});

    const navigate = useNavigate();
    const location = useLocation();

    // Hardcoded totals
    const TOTAL_TOPICS = 91;
    const ENVIRONMENTAL_TOTAL = 30;
    const SOCIAL_TOTAL = 54;
    const GOVERNANCE_TOTAL = 7;
    const MATERIALITY_THRESHOLD = 2.0; // UPDATED THRESHOLD

    const isComponentActive = activeKey === 'AnalysisResult';

    useEffect(() => {
        if (companies.length > 0 && !selectedCompanyId) {
            setSelectedCompanyId(companies[0].id);
        }
    }, [companies, selectedCompanyId]);

    useEffect(() => {
        if (selectedCompanyId && isComponentActive) {
            fetchDataForSelectedCompany(selectedCompanyId);
        }
    }, [location.key, selectedCompanyId, isComponentActive]);

    useEffect(() => {
        if (currentProject?.companyId && selectedCompanyId !== currentProject.companyId) {
            setSelectedCompanyId(currentProject.companyId);
        }
    }, [currentProject, selectedCompanyId]);

    const fetchDataForSelectedCompany = async (companyId: number) => {
        try {
            setError(null);
            const allTopics = await ProgressUtils.fetchAllEsrsTopics();
            setTopics(allTopics);

            const selections = await ProgressUtils.fetchEsrsTopicSelectionsByCompanyId(companyId);
            const selMap = selections.reduce((acc, sel) => {
                acc[sel.esrsTopicId] = sel;
                return acc;
            }, {} as { [topicId: number]: EsrsTopicSelection });
            setSelectionByTopicId(selMap);
        } catch (err) {
            console.error("Error fetching data:", err);
            setError("Failed to fetch data. Please try again.");
        }
    };

    const relevantDataPoints = useMemo((): RelevantDataPoint[] => {
        return topics
            .map((t) => {
                const sel = selectionByTopicId[t.id];
                if (!sel || !sel.relevant) return null;

                const xVal = sel.finalImpactMaterialityActualImpact ?? 0;
                const yVal = sel.finalFinancialMaterialityActualImpact ?? 0;

                return {
                    ...t,
                    x: xVal,
                    y: yVal,
                    name: t.subSubTopic || t.subtopic || t.topic,
                    finalRelevance: sel.finalRelevance,
                };
            })
            .filter((item): item is RelevantDataPoint => item !== null);
    }, [selectionByTopicId, topics]);

    const highValueTopics = useMemo(() => {
        return relevantDataPoints.filter(
            p => p.x >= MATERIALITY_THRESHOLD && p.y >= MATERIALITY_THRESHOLD
        ).sort((a,b) => (b.x + b.y) - (a.x + a.y)); // Sort by combined score
    }, [relevantDataPoints]);

    const totalRelevant = relevantDataPoints.length;
    const relevantEnvironmental = relevantDataPoints.filter(d => d.area === 'Environmental').length;
    const relevantSocial = relevantDataPoints.filter(d => d.area === 'Social').length;
    const relevantGovernance = relevantDataPoints.filter(d => d.area === 'Governance').length;

    const handleExport = (format: string) => {
        const dataToExport = relevantDataPoints.map(item => {
            const rowObj: Record<string, any> = {};
            selectedFields.forEach(field => {
                const key = field as keyof RelevantDataPoint;
                if (key === 'finalImpactMaterialityActualImpact') {
                    rowObj[field] = item.x;
                } else if (key === 'finalFinancialMaterialityActualImpact') {
                    rowObj[field] = item.y;
                } else {
                    rowObj[field] = item[key];
                }
            });
            return rowObj;
        });

        if (format === 'csv') {
            const csv = [
                selectedFields.join(','),
                ...dataToExport.map(row => selectedFields.map(field => `"${row[field] ?? ""}"`).join(',')),
            ].join('\n');
            const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
            saveAs(blob, `${dataName}.csv`);
        } else if (format === 'json') {
            const blob = new Blob([JSON.stringify(dataToExport, null, 2)], { type: 'application/json' });
            saveAs(blob, `${dataName}.json`);
        } else if (format === 'excel') {
            const worksheet = XLSX.utils.json_to_sheet(dataToExport);
            const workbook = { Sheets: { 'data': worksheet }, SheetNames: ['data'] };
            const excelBuffer = XLSX.write(workbook, { bookType: 'xlsx', type: 'array' });
            const blob = new Blob([excelBuffer], { type: 'application/octet-stream' });
            saveAs(blob, `${dataName}.xlsx`);
        }
        setShowModal(false);
    };

    const handleFieldSelection = (field: string) => {
        setSelectedFields(prev =>
            prev.includes(field) ? prev.filter(f => f !== field) : [...prev, field]
        );
    };

    if (error) {
        return <Alert variant="danger" className="m-4">{error}</Alert>;
    }

    return (
        <Container className="analysis-results-container p-4">
            <Row className="mb-4">
                <Col>
                    <h2>DMA Analysis Results</h2>
                    <p className="page-instructions">
                        Review the final results of your double materiality assessment. The charts visualize topic relevance and materiality, while the table provides detailed data points for your reporting.
                    </p>
                </Col>
            </Row>

            <Row className="mb-4 align-items-center info-bar">
                <Col xs={12} md={8} className="d-flex align-items-center gap-3">
                    <h6 className="mb-0 fw-bold" style={{ color: '#003366' }}>Selected Company:</h6>
                    <Form.Select
                        value={selectedCompanyId || ''}
                        onChange={(e) => setSelectedCompanyId(Number(e.target.value))}
                        className="company-select"
                        aria-label="Select Company"
                    >
                        {companies.map((company: Company) => (
                            <option key={company.id} value={company.id}>{company.companyName}</option>
                        ))}
                    </Form.Select>
                </Col>
                <Col xs={12} md={4} className="d-flex justify-content-md-end mt-3 mt-md-0 button-wrapper">
                    <Button variant="outline-secondary" disabled onClick={() => setShowModal(true)} >
                        <i className="bi bi-download me-2" ></i>Export
                    </Button>
                    <Button variant="outline-secondary" disabled>
                        <i className="bi bi-share-fill me-2"></i>Share
                    </Button>
                </Col>
            </Row>

            {selectedCompanyId ? (
                <>
                    <Row className="g-4">
                        {[
                            { title: 'Total Relevant Topics', relevant: totalRelevant, total: TOTAL_TOPICS, color: '#003366' },
                            { title: 'Environmental', relevant: relevantEnvironmental, total: ENVIRONMENTAL_TOTAL, color: '#6a994e' },
                            { title: 'Social', relevant: relevantSocial, total: SOCIAL_TOTAL, color: '#a7c957' },
                            { title: 'Governance', relevant: relevantGovernance, total: GOVERNANCE_TOTAL, color: '#c5d8a2' }
                        ].map(item => (
                            <Col md={6} lg={3} key={item.title}>
                                <Card className="summary-card">
                                    <Card.Body>
                                        <div className="donut-wrapper">
                                            <Plot
                                                data={[{
                                                    values: [item.relevant, item.total - item.relevant],
                                                    labels: ['Relevant', 'Not Relevant'],
                                                    type: 'pie', hole: 0.7,
                                                    marker: { colors: [item.color, '#e9ecef'], line: { width: 2, color: '#fff' } },
                                                    textinfo: 'none', hoverinfo: 'label+value', pull: 0.02,
                                                }]}
                                                layout={{ width: 90, height: 90, margin: { l: 0, r: 0, t: 0, b: 0 }, showlegend: false, paper_bgcolor: 'transparent' }}
                                                config={{ displayModeBar: false }}
                                            />
                                        </div>
                                        <div className="metrics-content">
                                            <h6 className="metric-title">{item.title}</h6>
                                            <h4 className="metric-value">{item.relevant}</h4>
                                            <div className="metric-subtext">{item.relevant} out of {item.total}</div>
                                        </div>
                                    </Card.Body>
                                </Card>
                            </Col>
                        ))}
                    </Row>

                    <Row className="g-4 mt-4">
                        <Col xl={8}>
                            <Card className="chart-card h-100">
                                <Card.Header>
                                    <Card.Title>Double Materiality Heatmap</Card.Title>
                                </Card.Header>
                                <Card.Body>
                                    <Plot
                                        data={[{
                                            x: relevantDataPoints.map(p => p.x),
                                            y: relevantDataPoints.map(p => p.y),
                                            text: relevantDataPoints.map(p => p.name),
                                            type: 'scatter', mode: 'markers',
                                            marker: { size: 15, opacity: 0.85, color: 'var(--primary-color)', line: { color: '#fff', width: 1 } },
                                            hovertemplate: '<b>%{text}</b><br>Impact Materiality: %{x}<br>Financial Materiality: %{y}<extra></extra>',
                                        }]}
                                        layout={{
                                            autosize: true,
                                            xaxis: { title: 'Impact Materiality (Final)', range: [0, 5], gridcolor: 'var(--border-color)', zeroline: false },
                                            yaxis: { title: 'Financial Materiality (Final)', range: [0, 5], gridcolor: 'var(--border-color)', zeroline: false },
                                            hovermode: 'closest',
                                            paper_bgcolor: '#fff', plot_bgcolor: '#fff',
                                            font: { family: 'var(--font-family)' },
                                            margin: { t: 40, b: 50, l: 60, r: 20 },
                                            shapes: [
                                                { type: 'rect', xref: 'x', yref: 'y', x0: MATERIALITY_THRESHOLD, y0: MATERIALITY_THRESHOLD, x1: 5, y1: 5, fillcolor: 'rgba(106, 153, 78, 0.2)', line: { width: 0 } },
                                                { type: 'rect', xref: 'x', yref: 'y', x0: MATERIALITY_THRESHOLD, y0: 0, x1: 5, y1: MATERIALITY_THRESHOLD, fillcolor: 'rgba(106, 153, 78, 0.1)', line: { width: 0 } },
                                                { type: 'rect', xref: 'x', yref: 'y', x0: 0, y0: MATERIALITY_THRESHOLD, x1: MATERIALITY_THRESHOLD, y1: 5, fillcolor: 'rgba(106, 153, 78, 0.1)', line: { width: 0 } },
                                                { type: 'line', x0: MATERIALITY_THRESHOLD, y0: 0, x1: MATERIALITY_THRESHOLD, y1: 5, line: { color: 'var(--accent-green)', width: 2, dash: 'dash' } },
                                                { type: 'line', x0: 0, y0: MATERIALITY_THRESHOLD, x1: 5, y1: MATERIALITY_THRESHOLD, line: { color: 'var(--accent-green)', width: 2, dash: 'dash' } },
                                            ],
                                            annotations: [{ x: 4.9, y: 4.9, xref: 'x', yref: 'y', text: 'High Priority', showarrow: false, font: { color: 'var(--accent-green)', size: 14, family: 'var(--font-family)' }, align: 'right', xanchor: 'right', yanchor: 'top' }],
                                        }}
                                        config={{ responsive: true, displayModeBar: false }}
                                        style={{ width: '100%', height: '100%', minHeight: '450px' }}
                                    />
                                </Card.Body>
                            </Card>
                        </Col>

                        <Col xl={4}>
                            <Card className="chart-card h-100">
                                <Card.Header>
                                    <Card.Title>High-Priority Topics</Card.Title>
                                </Card.Header>
                                <Card.Body className="d-flex flex-column">
                                    <p className="impact-financial-card-text mb-3">
                                        Topics where both Impact and Financial materiality are {`>= ${MATERIALITY_THRESHOLD}`}. These are key for reporting.
                                    </p>
                                    <div className="topic-list-wrapper">
                                        {highValueTopics.length > 0 ? (
                                            <ListGroup variant="flush" className="high-materiality-list">
                                                {highValueTopics.map(topic => (
                                                    <ListGroup.Item key={topic.id} className="d-flex justify-content-between align-items-start">
                                                        <div className="topic-name me-2">{topic.name}</div>
                                                        <Badge bg="primary-light" text="primary-dark" pill>{`I: ${topic.x} | F: ${topic.y}`}</Badge>
                                                    </ListGroup.Item>
                                                ))}
                                            </ListGroup>
                                        ) : (
                                            <div className="text-center text-muted mt-4">No topics in the high-priority quadrant.</div>
                                        )}
                                    </div>
                                </Card.Body>
                            </Card>
                        </Col>
                    </Row>

                    <Row className="mt-4">
                        <Col>
                            <Card className="chart-card">
                                <Card.Header>
                                    <Card.Title>All Relevant Data Points</Card.Title>
                                </Card.Header>
                                <Card.Body>
                                    <div className="table-responsive">
                                        <Table className="table-data-points">
                                            <thead>
                                            <tr>
                                                <th>ESRS Code</th>
                                                <th>Topic</th>
                                                <th>Subtopic</th>
                                                <th>SubSubtopic</th>
                                                <th>Final Impact</th>
                                                <th>Final Financial</th>
                                                <th>Final Relevance</th>
                                            </tr>
                                            </thead>
                                            <tbody>
                                            {relevantDataPoints.map((item) => (
                                                <tr key={item.id}>
                                                    <td>{item.esrsCode}</td>
                                                    <td>{item.topic}</td>
                                                    <td>{item.subtopic}</td>
                                                    <td>{item.subSubTopic || "No Sub-Sub-Topic"}</td>
                                                    <td>{item.x.toFixed(2)}</td>
                                                    <td>{item.y.toFixed(2)}</td>
                                                    <td>
                                                        <Badge pill bg={item.finalRelevance === 'Material' ? 'success' : 'secondary-soft'}>
                                                            {item.finalRelevance}
                                                        </Badge>
                                                    </td>
                                                </tr>
                                            ))}
                                            </tbody>
                                        </Table>
                                    </div>
                                </Card.Body>
                            </Card>
                        </Col>
                    </Row>
                </>
            ) : (
                <Alert variant="info" className="m-4">Please select a company to view the analysis results.</Alert>
            )}

            <Row className="my-4">
                <Col className="d-flex justify-content-between">
                    <Button variant="light" onClick={() => navigate(`${import.meta.env.BASE_URL}mithril/mithril?tab=ImpactFinancialAnalysis`)}>
                        <i className="bi bi-arrow-left me-2"></i>Back
                    </Button>

                </Col>
            </Row>

            <Modal show={showModal} onHide={() => setShowModal(false)} size="lg" centered className="export-modal">
                <Modal.Header closeButton>
                    <Modal.Title><i className="bi bi-box-arrow-down me-3"></i>Export Analysis Data</Modal.Title>
                </Modal.Header>
                <Modal.Body>
                    <p className="modal-intro-text mb-4">
                        Configure and export your dataset for external analysis or reporting.
                    </p>
                    <Form>
                        <Form.Group controlId="dataName" className="mb-4">
                            <Form.Label className="fw-bold">File Name</Form.Label>
                            <Form.Control type="text" value={dataName} onChange={e => setDataName(e.target.value)} />
                        </Form.Group>

                        <Form.Group>
                            <Form.Label className="fw-bold d-block mb-3">Select Fields to Export</Form.Label>
                            <Row>
                                {[ "esrsCode", "topic", "subtopic", "subSubTopic", "scale", "scope", "irreversibility", "impactMaterialityActualImpact", "financialMaterialityActualImpact", "resultMaterialityAssessment"].map((field) => (
                                    <Col md={6} key={field}>
                                        <Form.Check
                                            type="switch"
                                            id={`switch-${field}`}
                                            label={field.replace(/([A-Z])/g, ' $1').replace(/^./, (str) => str.toUpperCase())}
                                            checked={selectedFields.includes(field)}
                                            onChange={() => handleFieldSelection(field)}
                                            className="mb-2"
                                        />
                                    </Col>
                                ))}
                            </Row>
                        </Form.Group>
                    </Form>
                </Modal.Body>
                <Modal.Footer>
                    <Button variant="outline-secondary" onClick={() => setShowModal(false)}>Cancel</Button>
                    <div className="export-modal-buttons d-flex gap-2">
                        <Button variant="success" onClick={() => handleExport('excel')}>
                            <i className="bi bi-file-earmark-excel me-2"></i> Excel
                        </Button>
                        <Button variant="info" onClick={() => handleExport('csv')}>
                            <i className="bi bi-file-earmark-spreadsheet me-2"></i> CSV
                        </Button>
                        <Button variant="dark" onClick={() => handleExport('json')}>
                            <i className="bi bi-file-earmark-code me-2"></i> JSON
                        </Button>
                    </div>
                </Modal.Footer>
            </Modal>
        </Container>
    );
};

export default AnalysisResult;