import React, { useEffect, useMemo, useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, Col, Container, Form, Modal, Row, Table } from 'react-bootstrap';
import Plot from 'react-plotly.js';
import { saveAs } from 'file-saver';
import * as XLSX from 'xlsx';
import { useProjectContext } from "../../context_module/ProjectContext";
import { useLocation, useNavigate } from "react-router-dom";
import ProgressUtils from "../../utilities/ProgressUtils";
import { Company, EsrsTopic, EsrsTopicSelection } from '../../utilities/types';
import "./AnalysisResults.css";
import { useCompanyContext } from "../../context_module/CompanyContext.tsx";

// Define the props for AnalysisResult
interface AnalysisResultProps {
    activeKey: string; // This prop will be passed from Mithril.tsx
}


const AnalysisResult: React.FC<AnalysisResultProps> = ({ activeKey }) => { // Destructure activeKey from props
    const [showModal, setShowModal] = useState(false);
    const [selectedFields, setSelectedFields] = useState<string[]>([
        "esrsCode",
        "topic",
        "subtopic",
        "subSubTopic",

        // The final fields or placeholders:
        "impactMaterialityActualImpact",
        "impactMaterialityPotentialImpact",
        "financialMaterialityPotentialImpact",
        "resultMaterialityAssessment"
    ]);
    const [dataName, setDataName] = useState<string>('data');
    const [error, setError] = useState<string | null>(null);

    const { companies } = useCompanyContext();
    const { currentProject } = useProjectContext();
    const [selectedCompanyId, setSelectedCompanyId] = useState<number | null>(null);

    // All ESRS topics
    const [topics, setTopics] = useState<EsrsTopic[]>([]);
    // Quick lookup: topic.id -> topic
    const [topicById, setTopicById] = useState<{ [id: number]: EsrsTopic }>({});
    // Quick lookup: topic.id -> EsrsTopicSelection (which includes your final fields)
    const [selectionByTopicId, setSelectionByTopicId] = useState<{ [topicId: number]: EsrsTopicSelection }>({});

    const navigate = useNavigate();
    const location = useLocation();

    // Hardcoded totals
    const TOTAL_TOPICS = 91;
    const ENVIRONMENTAL_TOTAL = 30;
    const SOCIAL_TOTAL = 54;
    const GOVERNANCE_TOTAL = 7;

    // Determine if THIS component is the active one based on the activeKey prop
    const isComponentActive = activeKey === 'AnalysisResult';

    // Preselect the first company if available
    useEffect(() => {
        if (companies.length > 0 && !selectedCompanyId) {
            setSelectedCompanyId(companies[0].id);
        }
    }, [companies, selectedCompanyId]);

    // Whenever selectedCompanyId changes, fetch data
    useEffect(() => {
        if (selectedCompanyId && isComponentActive) {
            fetchDataForSelectedCompany(selectedCompanyId);
        }
    }, [location.key, selectedCompanyId,isComponentActive]);

    // If the project has a different company, override
    useEffect(() => {
        if (currentProject?.companyId && selectedCompanyId !== currentProject.companyId) {
            setSelectedCompanyId(currentProject.companyId);
        }
    }, [currentProject, selectedCompanyId]);

    /**
     * Just fetch all topics, then fetch all esrsTopicSelections for that company.
     * We'll store the final fields from the selection (like finalImpactMaterialityActualImpact).
     */
    const fetchDataForSelectedCompany = async (companyId: number) => {
        try {
            // 1) Fetch all ESRS topics
            const allTopics = await ProgressUtils.fetchAllEsrsTopics();
            setTopics(allTopics);

            const byIdTemp = allTopics.reduce((acc, t) => {
                acc[t.id] = t;
                return acc;
            }, {} as { [id: number]: EsrsTopic });
            setTopicById(byIdTemp);

            // 2) Fetch all esrsTopicSelections for the company
            const selections = await ProgressUtils.fetchEsrsTopicSelectionsByCompanyId(companyId);

            // We'll build a map: topicId -> selection
            const selMap: { [topicId: number]: EsrsTopicSelection } = {};
            selections.forEach(sel => {
                selMap[sel.esrsTopicId] = sel;
            });
            setSelectionByTopicId(selMap);
        } catch (err) {
            console.error("Error fetching data:", err);
            setError("Failed to fetch data. Please try again.");
        }
    };

    /**
     * We only want to display "relevant" topics, i.e. topicSelection.relevant === true
     * Then we read the finalImpactMaterialityActualImpact & finalFinancialMaterialityActualImpact
     * (or whichever fields you created) to plot them in the heatmap.
     */
    const getRelevantDataPoints = () => {
        return topics
            .map((t) => {
                const sel = selectionByTopicId[t.id];
                if (!sel) return null; // no selection => skip

                // If selection is relevant
                if (!sel.relevant) return null;

                // We interpret x = finalImpactMaterialityActualImpact
                // and y = finalFinancialMaterialityActualImpact
                // (Or if you have finalPotential... adjust as needed)
                const xVal = sel.finalImpactMaterialityActualImpact ?? 0;
                const yVal = sel.finalFinancialMaterialityActualImpact ?? 0;

                return {
                    ...t,
                    // For convenience
                    x: xVal,
                    y: yVal,
                    name: t.subSubTopic || t.subtopic || t.topic,
                    // Also keep finalRelevance if you want
                    finalRelevance: sel.finalRelevance,
                };
            })
            .filter(Boolean) as any[]; // Remove nulls
    };

    // relevantDataPoints for the donut charts & heatmap
    const relevantDataPoints = useMemo(() => getRelevantDataPoints(), [selectionByTopicId, topics]);

    // Summaries
    const totalRelevant = relevantDataPoints.length;
    const relevantEnvironmental = relevantDataPoints.filter(d => d.area === 'Environmental').length;
    const relevantSocial = relevantDataPoints.filter(d => d.area === 'Social').length;
    const relevantGovernance = relevantDataPoints.filter(d => d.area === 'Governance').length;

    // Export logic
    const handleExport = (format: string) => {
        // We'll just use relevantDataPoints as the dataset
        const filteredData = relevantDataPoints.map(item => {
            const rowObj: Record<string, any> = {};
            selectedFields.forEach(field => {
                // field might not exist on "item"
                rowObj[field] = item[field as keyof typeof item];
            });
            return rowObj;
        });

        switch (format) {
            case 'csv':
                exportCSV(filteredData);
                break;
            case 'json':
                exportJSON(filteredData);
                break;
            case 'excel':
                exportExcel(filteredData);
                break;
            default:
                break;
        }
    };

    const exportCSV = (data: any[]) => {
        const csv = [
            selectedFields.join(','),
            ...data.map(row => selectedFields.map(field => row[field] ?? "").join(',')),
        ].join('\n');
        const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
        saveAs(blob, `${dataName}.csv`);
    };

    const exportJSON = (data: any[]) => {
        const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
        saveAs(blob, `${dataName}.json`);
    };

    const exportExcel = (data: any[]) => {
        const worksheet = XLSX.utils.json_to_sheet(data);
        const workbook = { Sheets: { 'data': worksheet }, SheetNames: ['data'] };
        const excelBuffer = XLSX.write(workbook, { bookType: 'xlsx', type: 'array' });
        const blob = new Blob([excelBuffer], { type: 'application/octet-stream' });
        saveAs(blob, `${dataName}.xlsx`);
    };

    const handleFieldSelection = (field: string) => {
        setSelectedFields(prev =>
            prev.includes(field) ? prev.filter(f => f !== field) : [...prev, field]
        );
    };

    if (error) {
        return <div className="alert alert-danger mt-5" role="alert">{error}</div>;
    }

    return (
        <Container fluid className="dashboard-container">
            <Row className="mb-4">
                <Col>
                    <h2 >DMA Analysis Results</h2>
                    <p className="page-instructions">
                        <strong>Instructions:</strong> Review the final results of your double materiality assessment
                        below.
                        The donut charts display how many topics are relevant out of the total, while the heatmap helps
                        you identify topics high in both impact and financial materiality.

                    </p>
                </Col>
            </Row>

            <Row className="mb-4 align-items-center info-bar">
                <Col xs={12} md={8} className="d-flex align-items-center gap-3">
                    <h6 className="mb-0 fw-bold" style={{ color: '#003457' }}>Selected Company:</h6>
                    <Form.Control
                        as="select"
                        value={selectedCompanyId || ''}
                        onChange={(e) => setSelectedCompanyId(Number(e.target.value))}
                        className="company-select"
                    >
                        {companies.map((company: Company) => (
                            <option key={company.id} value={company.id}>
                                {company.companyName}
                            </option>
                        ))}
                    </Form.Control>
                </Col>
                <Col xs={12} md={4} className="d-flex justify-content-md-end mt-3 mt-md-0 ">
                    <div className="button-wrapper">
                        <Button
                            variant="outline-secondarry"
                            onClick={() => setShowModal(true)}
                            disabled={!selectedCompanyId}
                        >
                            <i className="bi bi-download me-2"></i>Export
                        </Button>
                        <Button variant="outline-secorndafry" disabled>
                            <i className="bi bi-share-fill me-2"></i>Share
                        </Button>

                    </div>
                </Col>
            </Row>

            {selectedCompanyId ? (
                <>
                    {/* Summary Row with Donut Charts */}
                    <Row className="summary-row g-4">
                        <Col md={3}>
                            <Card className="summary-card chart-item">
                                <Card.Body className="summary-card-body">
                                    <div className="donut-wrapper">
                                        <Plot
                                            data={[
                                                {
                                                    labels: ['Relevant', 'Not Relevant'],
                                                    values: [totalRelevant, TOTAL_TOPICS - totalRelevant],
                                                    type: 'pie',
                                                    hole: 0.7,
                                                    marker: {
                                                        colors: ['#002D57', '#e9edf2'],
                                                        line: { width: 2, color: '#fff' },
                                                    },
                                                    textinfo: 'none',
                                                    hoverinfo: 'none',
                                                    pull: 0.005,
                                                }
                                            ]}
                                            layout={{
                                                width: 80,
                                                height: 80,
                                                margin: { l: 0, r: 0, t: 0, b: 0 },
                                                showlegend: false,
                                                paper_bgcolor: 'transparent',
                                                plot_bgcolor: 'transparent',
                                                font: {
                                                    family: 'Inter, sans-serif',
                                                    size: 12,
                                                },
                                            }}
                                            config={{ displayModeBar: false }}
                                        />
                                    </div>
                                    <div className="metrics-content">
                                        <h6 className="metric-title">Total Relevant Topics</h6>
                                        <h4 className="metric-value">{totalRelevant}</h4>
                                        <div className="metric-subtext">
                                            {totalRelevant} out of {TOTAL_TOPICS}
                                        </div>
                                    </div>
                                </Card.Body>
                            </Card>
                        </Col>

                        <Col md={3}>
                            <Card className="summary-card chart-item">
                                <Card.Body className="summary-card-body">
                                    <div className="donut-wrapper">
                                        <Plot
                                            data={[
                                                {
                                                    labels: ['Relevant', 'Not Relevant'],
                                                    values: [relevantEnvironmental, ENVIRONMENTAL_TOTAL - relevantEnvironmental],
                                                    type: 'pie',
                                                    hole: 0.7,
                                                    marker: {
                                                        colors: ['#7E9F14', '#e9edf2'],
                                                        line: { width: 2, color: '#fff' },
                                                    },
                                                    textinfo: 'none',
                                                    hoverinfo: 'none',
                                                    pull: 0.005,
                                                }
                                            ]}
                                            layout={{
                                                width: 80,
                                                height: 80,
                                                margin: { l: 0, r: 0, t: 0, b: 0 },
                                                showlegend: false,
                                                paper_bgcolor: 'transparent',
                                                plot_bgcolor: 'transparent',
                                                font: {
                                                    family: 'Inter, sans-serif',
                                                    size: 12,
                                                },
                                            }}
                                            config={{ displayModeBar: false }}
                                        />
                                    </div>
                                    <div className="metrics-content">
                                        <h6 className="metric-title">Environmental</h6>
                                        <h4 className="metric-value">{relevantEnvironmental}</h4>
                                        <div className="metric-subtext">
                                            {relevantEnvironmental} out of {ENVIRONMENTAL_TOTAL}
                                        </div>
                                    </div>
                                </Card.Body>
                            </Card>
                        </Col>

                        <Col md={3}>
                            <Card className="summary-card chart-item">
                                <Card.Body className="summary-card-body">
                                    <div className="donut-wrapper">
                                        <Plot
                                            data={[
                                                {
                                                    labels: ['Relevant', 'Not Relevant'],
                                                    values: [relevantSocial, SOCIAL_TOTAL - relevantSocial],
                                                    type: 'pie',
                                                    hole: 0.7,
                                                    marker: {
                                                        colors: ['#A2B86B', '#e9edf2'],
                                                        line: { width: 2, color: '#fff' },
                                                    },
                                                    textinfo: 'none',
                                                    hoverinfo: 'none',
                                                    pull: 0.005,
                                                }
                                            ]}
                                            layout={{
                                                width: 80,
                                                height: 80,
                                                margin: { l: 0, r: 0, t: 0, b: 0 },
                                                showlegend: false,
                                                paper_bgcolor: 'transparent',
                                                plot_bgcolor: 'transparent',
                                                font: {
                                                    family: 'Inter, sans-serif',
                                                    size: 12,
                                                },
                                            }}
                                            config={{ displayModeBar: false }}
                                        />
                                    </div>
                                    <div className="metrics-content">
                                        <h6 className="metric-title">Social</h6>
                                        <h4 className="metric-value">{relevantSocial}</h4>
                                        <div className="metric-subtext">
                                            {relevantSocial} out of {SOCIAL_TOTAL}
                                        </div>
                                    </div>
                                </Card.Body>
                            </Card>
                        </Col>

                        <Col md={3}>
                            <Card className="summary-card chart-item">
                                <Card.Body className="summary-card-body">
                                    <div className="donut-wrapper">
                                        <Plot
                                            data={[
                                                {
                                                    labels: ['Relevant', 'Not Relevant'],
                                                    values: [relevantGovernance, GOVERNANCE_TOTAL - relevantGovernance],
                                                    type: 'pie',
                                                    hole: 0.7,
                                                    marker: {
                                                        colors: ['#C5D8A2', '#e9edf2'],
                                                        line: { width: 2, color: '#fff' },
                                                    },
                                                    textinfo: 'none',
                                                    hoverinfo: 'none',
                                                    pull: 0.005,
                                                }
                                            ]}
                                            layout={{
                                                width: 80,
                                                height: 80,
                                                margin: { l: 0, r: 0, t: 0, b: 0 },
                                                showlegend: false,
                                                paper_bgcolor: 'transparent',
                                                plot_bgcolor: 'transparent',
                                                font: {
                                                    family: 'Inter, sans-serif',
                                                    size: 12,
                                                },
                                            }}
                                            config={{ displayModeBar: false }}
                                        />
                                    </div>
                                    <div className="metrics-content">
                                        <h6 className="metric-title">Governance</h6>
                                        <h4 className="metric-value">{relevantGovernance}</h4>
                                        <div className="metric-subtext">
                                            {relevantGovernance} out of {GOVERNANCE_TOTAL}
                                        </div>
                                    </div>
                                </Card.Body>
                            </Card>
                        </Col>
                    </Row>

                    {/* Heatmap */}
                    <Row className="second-row g-4 mt-4">
                        <Col md={8} className="heatmap-col">
                            <Card className="chart-card heatmap-card chart-item">
                                <Card.Header className="chart-card-header">
                                    <Card.Title className="chart-card-title">Heatmap of Materiality Results</Card.Title>
                                </Card.Header>
                                <Card.Body className="chart-card-body">
                                    <Plot
                                        data={[
                                            {
                                                x: relevantDataPoints.map(point => point.x),
                                                y: relevantDataPoints.map(point => point.y),
                                                text: relevantDataPoints.map(point => point.name),
                                                type: 'scatter',
                                                mode: 'markers',
                                                marker: {
                                                    size: 15,
                                                    opacity: 0.85,
                                                    color: '#002D57',
                                                    line: {
                                                        color: '#fff',
                                                        width: 1,
                                                    },
                                                },
                                                hovertemplate:
                                                    '<b>%{text}</b><br>' +
                                                    'Impact Materiality: %{x}<br>' +
                                                    'Financial Materiality: %{y}<extra></extra>',
                                            },
                                        ]}
                                        layout={{
                                            autosize: true,
                                            title: {
                                                text: 'Double Materiality Assessment Heatmap',
                                                font: {
                                                    size: 16,
                                                    family: 'Inter, sans-serif',
                                                    color: '#002D57'
                                                },
                                            },
                                            xaxis: {
                                                title: {
                                                    text: 'Impact (Final Field)',
                                                    font: { family: 'Inter, sans-serif' },
                                                },
                                                range: [0, 5],
                                                gridcolor: '#e5e5e5',
                                            },
                                            yaxis: {
                                                title: {
                                                    text: 'Financial (Final Field)',
                                                    font: { family: 'Inter, sans-serif' },
                                                },
                                                range: [0, 5],
                                                gridcolor: '#e5e5e5',
                                            },
                                            hovermode: 'closest',
                                            paper_bgcolor: '#fff',
                                            plot_bgcolor: '#fff',
                                            font: { family: 'Inter, sans-serif' },
                                            shapes: [
                                                {
                                                    type: 'rect',
                                                    xref: 'x',
                                                    yref: 'y',
                                                    x0: 2.5,
                                                    y0: 2.5,
                                                    x1: 5,
                                                    y1: 5,
                                                    fillcolor: 'rgba(126,159,20,0.2)',
                                                    line: { width: 0 },
                                                },
                                                {
                                                    type: 'line',
                                                    x0: 2.5,
                                                    y0: 0,
                                                    x1: 2.5,
                                                    y1: 5,
                                                    line: {
                                                        color: '#7E9F14',
                                                        width: 2,
                                                        dash: 'dashdot',
                                                    },
                                                },
                                                {
                                                    type: 'line',
                                                    x0: 0,
                                                    y0: 2.5,
                                                    x1: 5,
                                                    y1: 2.5,
                                                    line: {
                                                        color: '#7E9F14',
                                                        width: 2,
                                                        dash: 'dashdot',
                                                    },
                                                },
                                                {
                                                    type: 'rect',
                                                    xref: 'x',
                                                    yref: 'y',
                                                    x0: 2.5,
                                                    y0: 0,
                                                    x1: 5,
                                                    y1: 2.5,
                                                    fillcolor: 'rgba(126,159,20,0.1)',
                                                    line: { width: 0 },
                                                },
                                                {
                                                    type: 'rect',
                                                    xref: 'x',
                                                    yref: 'y',
                                                    x0: 0,
                                                    y0: 2.5,
                                                    x1: 2.5,
                                                    y1: 5,
                                                    fillcolor: 'rgba(126,159,20,0.1)',
                                                    line: { width: 0 },
                                                },
                                            ],
                                            annotations: [
                                                {
                                                    x: 3.75,
                                                    y: 5,
                                                    xref: 'x',
                                                    yref: 'y',
                                                    text: 'High Impact & Financial Materiality',
                                                    showarrow: false,
                                                    font: { color: '#7E9F14', size: 12, family: 'Inter, sans-serif' },
                                                },
                                            ],
                                            margin: { t: 70, b: 50, l: 50, r: 50 },
                                        }}
                                        config={{ responsive: true, displayModeBar: false }}
                                        style={{ width: '100%', height: '500px' }}
                                    />
                                </Card.Body>
                            </Card>
                        </Col>

                        <Col md={4} className="impact-financial-topics-col">
                            <Card className="chart-card impact-financial-card chart-item h-100">
                                <Card.Header className="chart-card-header">
                                    <Card.Title className="chart-card-title">High Impact & Financial Topics</Card.Title>
                                </Card.Header>
                                <Card.Body className="chart-card-body">
                                    <p className="impact-financial-card-text">
                                        Topics with high final impact & financial values appear in the
                                        top-right quadrant of the heatmap. Focus on these for your CSRD reporting.
                                    </p>
                                </Card.Body>
                            </Card>
                        </Col>
                    </Row>

                    {/* Table Row */}
                    <Row className="table-row mt-4">
                        <Col>
                            <Card className="chart-card table-card chart-item">
                                <Card.Header className="chart-card-header">
                                    <Card.Title className="chart-card-title">Significant Data Points</Card.Title>
                                </Card.Header>
                                <Card.Body className="chart-card-body">
                                    <div className="table-responsive">
                                        <Table className="table table-hover align-middle table-data-points">
                                            <thead className="table-light">
                                            <tr>
                                                <th>ESRS Code</th>
                                                <th>Topic</th>
                                                <th>Subtopic</th>
                                                <th>Sub-Sub-Topic</th>
                                                {/* If you don’t want scale/scope, remove them */}

                                                <th>Impact Materiality (Final)</th>
                                                <th>Financial Materiality (Final)</th>
                                                <th>Final Relevance</th>
                                            </tr>
                                            </thead>
                                            <tbody>
                                            {relevantDataPoints.map((item, index) => (
                                                <tr key={index}>
                                                    <td>{item.esrsCode}</td>
                                                    <td>{item.topic}</td>
                                                    <td>{item.subtopic}</td>
                                                    <td>{item.subSubTopic}</td>

                                                    {/* Show final fields */}
                                                    <td>{item.x /* or item.finalImpactMaterialityActualImpact */}</td>
                                                    <td>{item.y /* or item.finalFinancialMaterialityActualImpact */}</td>
                                                    <td>{item.finalRelevance}</td>
                                                </tr>
                                            ))}
                                            </tbody>
                                        </Table>
                                    </div>
                                </Card.Body>
                            </Card>
                        </Col>
                    </Row>
                </>
            ) : (
                <Row className="mt-4">
                    <Col>
                        <Alert variant="info">
                            Please select a company to view the analysis results.
                        </Alert>
                    </Col>
                </Row>
            )}

            <Row className="mb-4 mt-4">
                <Col className="d-flex justify-content-between">
                    <Button
                        variant="light"
                        onClick={() => navigate(`${import.meta.env.BASE_URL}mithril/mithril?tab=ImpactFinancialAnalysis`)}
                    >
                        <i className="bi bi-arrow-left"></i> Back
                    </Button>
                    <Button
                        variant="primary"
                        onClick={() => navigate(`${import.meta.env.BASE_URL}mithril/mithril?tab=CompanyGroupSelection`)}
                    >
                        Next <i className="bi bi-arrow-right"></i>
                    </Button>
                </Col>
            </Row>

            {/* Export Modal */}
            <Modal show={showModal} onHide={() => setShowModal(false)} size="lg" className="export-modal">
                <Modal.Header closeButton className="export-modal-header">
                    <Modal.Title>
                        <div className="d-flex align-items-center">
                            <i className="bi bi-box-arrow-down fs-4 me-2"></i>
                            <span>Export Your Analysis Data</span>
                        </div>
                    </Modal.Title>
                </Modal.Header>
                <Modal.Body className="export-modal-body">
                    <p className="modal-intro-text mb-4">
                        Prepare your dataset for external analysis or reporting. Choose the fields and the format best suited for your workflow.
                    </p>

                    <div className="export-steps mb-4">
                        <div className="export-step d-flex align-items-start mb-3">
                            <div className="export-step-icon me-3">
                                <i className="bi bi-pencil"></i>
                            </div>
                            <div>
                                <strong>Set a Name:</strong> Give your data export a meaningful name for future reference.
                            </div>
                        </div>
                        <div className="export-step d-flex align-items-start mb-3">
                            <div className="export-step-icon me-3">
                                <i className="bi bi-ui-checks"></i>
                            </div>
                            <div>
                                <strong>Choose Fields:</strong> Select which data points you want to include in the export.
                            </div>
                        </div>
                        <div className="export-step d-flex align-items-start mb-3">
                            <div className="export-step-icon me-3">
                                <i className="bi bi-file-earmark-arrow-down"></i>
                            </div>
                            <div>
                                <strong>Select Format:</strong> Export your data as CSV, JSON, or Excel.
                            </div>
                        </div>
                    </div>

                    <Form className="export-form">
                        <Form.Group controlId="dataName" className="mb-4">
                            <Form.Label className="fw-bold">Data Name</Form.Label>
                            <Form.Control
                                type="text"
                                value={dataName}
                                onChange={e => setDataName(e.target.value)}
                                placeholder="e.g., Q4_2024_Analysis_Report"
                            />
                            <Form.Text className="text-muted">
                                This name will be used as the file name for your exported data.
                            </Form.Text>
                        </Form.Group>

                        <Form.Group>
                            <Form.Label className="fw-bold d-block mb-3">Select Fields to Export</Form.Label>
                            <Row>
                                <Col md={6}>
                                    <h6 className="fw-semibold mb-2">Result Fields</h6>
                                    {["impactMaterialityPotentialImpact", "financialMaterialityPotentialImpact", "resultMaterialityAssessment"].map((field) => (
                                        <Form.Check
                                            key={field}
                                            type="switch"
                                            id={`switch-${field}`}
                                            label={field}
                                            checked={selectedFields.includes(field)}
                                            onChange={() => handleFieldSelection(field)}
                                            className="mb-2"
                                        />
                                    ))}
                                </Col>
                                <Col md={6}>
                                    <h6 className="fw-semibold mb-2">Additional Fields</h6>
                                    {["esrsCode", "topic", "subtopic", "subSubTopic", "scale", "scope", "irreversibility", "impactMaterialityActualImpact"].map((field) => (
                                        <Form.Check
                                            key={field}
                                            type="switch"
                                            id={`switch-${field}`}
                                            label={field}
                                            checked={selectedFields.includes(field)}
                                            onChange={() => handleFieldSelection(field)}
                                            className="mb-2"
                                        />
                                    ))}
                                </Col>
                            </Row>
                        </Form.Group>
                    </Form>
                </Modal.Body>
                <Modal.Footer className="export-modal-footer">
                    <div className="export-modal-buttons d-flex gap-2">
                        <Button variant="primary" onClick={() => handleExport('csv')} disabled>
                            <i className="bi bi-file-earmark-spreadsheet me-2"></i> CSV
                        </Button>
                        <Button variant="primary" onClick={() => handleExport('json')} disabled>
                            <i className="bi bi-file-earmark-code me-2"></i> JSON
                        </Button>
                        <Button variant="primary" onClick={() => handleExport('excel')} disabled>
                            <i className="bi bi-file-earmark-excel me-2"></i> Excel
                        </Button>
                    </div>
                </Modal.Footer>
            </Modal>
        </Container>
    );
};

export default AnalysisResult;
