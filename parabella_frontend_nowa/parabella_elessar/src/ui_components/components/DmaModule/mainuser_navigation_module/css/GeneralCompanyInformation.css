/* GeneralCompanyInformation.css */

.company-group-tree {
    text-align: center;
}

.tree-node {
    display: inline-block;
    padding: 15px;
    background-color: #fff;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    transition: transform 0.3s;
}

.tree-node:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.node-logo {
    width: 50px;
    height: 50px;
    margin-bottom: 10px;
}

.tree-node h5,
.tree-node h6 {
    margin: 0;
    font-size: 16px;
    color: #333;
}

footer {
    border-top: 1px solid #dee2e6;
    padding-top: 20px;
}

footer .btn {
    min-width: 120px;
}
