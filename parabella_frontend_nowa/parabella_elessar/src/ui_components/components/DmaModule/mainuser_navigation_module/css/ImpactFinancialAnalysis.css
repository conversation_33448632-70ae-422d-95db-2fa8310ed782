/* --- IMPACT & FINANCIAL ANALYSIS STYLES --- */

/* 1. CSS Variables for Consistency */
:root {
    --color-primary: #7E9F14;
    --color-primary-light: #f2f6e7;
    --color-primary-dark: #5a720f;
    --color-secondary: #132b40;
    --color-text: #212529;
    --color-text-muted: #6c757d;
    --color-background: #f8f9fa;
    --color-surface: #ffffff;
    --color-border: #dee2e6;
    --color-success: #28a745;
    --color-warning: #ffc107;
    --color-danger: #dc3545;
    --color-gray-300: #ced4da;
    --border-radius: 0.5rem;
    --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 12px rgba(0, 0, 0, 0.08);
}

/* 2. Page Level Styles */
.impact-financial-analysis-page {
    background-color: var(--color-background);
    padding: 2rem;
}

.page-title {
    font-weight: 700;
    color: var(--color-secondary);
}

.page-instructions {
    color: var(--color-text-muted);
    max-width: 800px;
}

.info-button {
    display: flex;
    align-items: center;
}

/* 3. Control Bar (Tabs & Company Selector) */
.control-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: var(--color-surface);
    padding: 0.75rem 1rem;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-sm);
    margin-bottom: 2rem;
}

.control-bar .nav-pills .nav-link {
    color: var(--color-text-muted);
    font-weight: 500;
    border-radius: 0.375rem;
}

.control-bar .nav-pills .nav-link.active {
    background-color: var(--color-primary);
    color: white;
}

.company-selector-wrapper {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.company-label {
    margin-bottom: 0;
    font-weight: 500;
    color: var(--color-text-muted);
}

.company-select {
    min-width: 220px;
    font-weight: 500;
}

/* 4. Main Content Area */
.analysis-content-wrapper {
    width: 100%;
}

.topic-list-container {
    background-color: var(--color-surface);
    border-radius: var(--border-radius);
    padding: 0.5rem;
    box-shadow: var(--shadow-sm);
    height: 100%;
}

.details-container {
    height: 100%;
}

/* 5. DataPointsList (Left Column) */
.topic-accordion-item {
    border: none;
    background-color: transparent;
}

.topic-accordion-header {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--color-secondary);
    background-color: transparent !important;
    box-shadow: none !important;
    border: none !important;
    padding: 1rem 1.25rem;
}

.topic-accordion-header::after {
    display: none; /* Remove default bootstrap icon */
}

.accordion-chevron {
    transition: transform 0.2s ease-in-out;
}

.topic-accordion-header:not(.collapsed) .accordion-chevron {
    transform: rotate(180deg);
}

.topic-accordion-body {
    padding: 0 1.25rem 1rem;
}

.topic-card {
    border: 1px solid var(--color-border);
    border-radius: var(--border-radius);
    margin-bottom: 1rem;
    padding: 1rem;
    cursor: pointer;
    transition: all 0.2s ease;
    position: relative;
    overflow: hidden;
}

.topic-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
    border-color: var(--color-primary);
}

.topic-card.selected {
    border-width: 2px;
    border-color: var(--color-primary);
    background-color: var(--color-primary-light);
}

.topic-card-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 1rem;
}

.topic-label {
    font-weight: 600;
    color: var(--color-secondary);
    margin-bottom: 0;
    padding-right: 0.5rem;
}

.status-badge {
    font-weight: 500;
    font-size: 0.75rem;
}
.status-badge.status-open { background-color: #e9ecef; color: #495057; }
.status-badge.status-in-progress { background-color: #fff3cd; color: #856404; }
.status-badge.status-done { background-color: #d4edda; color: #155724; }

.topic-card-body {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.materiality-score {
    font-size: 0.9rem;
    color: var(--color-text-muted);
    display: flex;
    align-items: center;
}
.materiality-score strong {
    color: var(--color-text);
}

.topic-card-footer .relevance-indicator {
    font-size: 0.8rem;
    font-weight: 600;
    padding: 0.2rem 0.5rem;
    border-radius: 99px;
    display: inline-flex;
    align-items: center;
}

.relevance-indicator.relevant {
    color: var(--color-success);
    background-color: #d4edda;
}
.relevance-indicator.not-relevant {
    color: var(--color-danger);
    background-color: #f8d7da;
}

/* 6. IroContainer (Right Column) */
.status-overview-card {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-sm);
    margin-bottom: 2rem;
}

.overview-title {
    font-weight: 600;
    color: var(--color-secondary);
}
.overview-subtitle {
    color: var(--color-text-muted);
    font-size: 0.9rem;
}

.status-dot {
    display: inline-block;
    width: 10px;
    height: 10px;
    border-radius: 50%;
    vertical-align: middle;
}
.status-dot.done { background-color: var(--color-success); }
.status-dot.remaining { background-color: var(--color-gray-300); }


.iro-list-header {
    display: flex;
    padding: 0.75rem 1rem;
    background-color: #f1f3f5;
    border-radius: var(--border-radius) var(--border-radius) 0 0;
    font-size: 0.8rem;
    font-weight: 600;
    color: var(--color-text-muted);
    gap: 1rem;
}

.iro-accordion {
    --bs-accordion-border-width: 0;
}

.iro-list-item {
    border: 1px solid var(--color-border);
    border-top: none;
}
.iro-list-item:last-of-type {
    border-radius: 0 0 var(--border-radius) var(--border-radius);
}

.iro-list-item-header {
    --bs-accordion-btn-padding-x: 1rem;
    --bs-accordion-btn-padding-y: 1rem;
    --bs-accordion-btn-focus-box-shadow: none;
    --bs-accordion-active-bg: var(--color-primary-light);
    --bs-accordion-active-color: var(--color-secondary);
    background-color: var(--color-surface);
    font-size: 0.9rem;
}

.iro-list-item-header .accordion-button {
    display: flex;
    gap: 1rem;
    width: 100%;
}

.iro-list-item-header::after { display: none; }
.iro-list-item-header .accordion-chevron { margin-left: auto; }
.iro-list-item-header:not(.collapsed) .accordion-chevron { transform: rotate(180deg); }

/* Column Widths for IRO list */
.iro-col-name { flex: 3; font-weight: 600; }
.iro-col-stakeholder { flex: 2; }
.iro-col-edited { flex: 2; }
.iro-col-relevance { flex: 1.5; text-align: center; }
.iro-col-actions { flex: 1; text-align: right; }

.iro-accordion .accordion-body {
    padding: 1.5rem;
    background-color: #fdfdfd;
}

/* 7. Placeholder Alerts */
.placeholder-alert {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    text-align: center;
    height: 100%;
    min-height: 400px;
    background-color: var(--color-surface);
    border: 2px dashed var(--color-border);
    border-radius: var(--border-radius);
    padding: 2rem;
    color: var(--color-text-muted);
}
.placeholder-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
}