import React, { useEffect, useState, useRef } from 'react';
import { Badge, Card, Col, Form, OverlayTrigger, Row, Tooltip, Button } from 'react-bootstrap';
import { IroEvaluation } from '../../utilities/types';
import { Eye, Info, Pencil } from 'lucide-react';
import { fieldDescriptions } from '../../static/constants';
import chatbotAxios from '../../../../../config/chatbotAPIConfig';
import './TopicDetailsForm.css';

interface IroDetailsFormProps {
    iroId: number;
    details: IroEvaluation & { iroType?: string };
    mode: 'view' | 'edit';
    onSaveIro: (updatedIro: IroEvaluation, stakeholderName: string) => Promise<void>;
    handleInputChange: (iroId: number, updatedData: Partial<IroEvaluation>) => void;
    handleUserInputChange: (iroId: number, updatedData: Partial<IroEvaluation>) => void;
    stakeholderName: string;
    // mainTopic, subtopic, subSubTopic are no longer used by AI calls in this version
}

const IroDetailsForm: React.FC<IroDetailsFormProps> = ({
                                                           iroId,
                                                           details,
                                                           mode,
                                                           onSaveIro,
                                                           handleInputChange,
                                                           handleUserInputChange,
                                                           stakeholderName,
                                                       }) => {
    const isEditMode = mode === 'edit';
    const [formData, setFormData] = useState<IroEvaluation & { iroType?: string }>(details);
    const [autoSaveStatus, setAutoSaveStatus] = useState<'idle' | 'saving' | 'saved'>('idle');
    const autoSaveTimerRef = useRef<NodeJS.Timeout | null>(null);
    const [hasUserInput, setHasUserInput] = useState(false);
    const [loadingDescriptionAI, setLoadingDescriptionAI] = useState(false);

    useEffect(() => {
        setFormData(details);
    }, [iroId, details]);

    // Debounced auto-save logic
    useEffect(() => {
        if (!isEditMode || !hasUserInput || !formData || !formData.iroId) {
            return;
        }

        setAutoSaveStatus('saving');
        if (autoSaveTimerRef.current) clearTimeout(autoSaveTimerRef.current);

        autoSaveTimerRef.current = setTimeout(async () => {
            try {
                await onSaveIro(formData, stakeholderName);
                setAutoSaveStatus('saved');
                setHasUserInput(false);
            } catch (err) {
                console.error('AutoSave failed:', err);
            }
        }, 1000);

        return () => {
            if (autoSaveTimerRef.current) clearTimeout(autoSaveTimerRef.current);
        };
    }, [formData, isEditMode, onSaveIro, iroId, stakeholderName, hasUserInput]);

    // Recalculation logic based on the new UI
    useEffect(() => {
        if (!isEditMode) return;

        let updatedValues: Partial<IroEvaluation> = {};
        const threshold = 2; // Materiality threshold

        if (formData.iroType === 'impact') {
            const scale = formData.scale || 1;
            const scope = formData.scope || 1;
            // Irreversibility is only counted for negative impacts
            const irreversibility = formData.positiveNegativeImpact === 'negative' ? (formData.irreversibility || 1) : 0;

            let finalScore = (scale + scope + (irreversibility > 0 ? irreversibility : 0)) / (irreversibility > 0 ? 3 : 2);
            let finalRelevance = finalScore >= threshold ? 'Relevant' : 'Not Relevant';

            // Special rule for human rights: if 'yes', it's automatically max score and relevant
            if (formData.humanRightsImpact === 'yes') {
                finalScore = 5.0;
                finalRelevance = 'Relevant';
            }

            updatedValues = {
                impactMaterialityPotentialImpact: finalScore.toFixed(2),
                resultMaterialityAssessment: finalRelevance,
            };

        } else if (formData.iroType === 'financial') {
            const financialEffect = formData.financialMaterialityActualImpact || 1;
            const probability = formData.probability || 0.2; // Use the new probability slider value

            const finalScore = financialEffect * probability;
            const finalRelevance = finalScore >= threshold ? 'Relevant' : 'Not Relevant';

            updatedValues = {
                financialMaterialityPotentialImpact: finalScore.toFixed(2),
                resultMaterialityAssessment: finalRelevance,
            };
        }

        setFormData((prev) => ({ ...prev, ...updatedValues }));
        handleInputChange(iroId, updatedValues);

    }, [
        isEditMode, iroId, handleInputChange, formData.iroType,
        // Impact dependencies
        formData.scale, formData.scope, formData.irreversibility, formData.positiveNegativeImpact, formData.humanRightsImpact,
        // Financial dependencies
        formData.financialMaterialityActualImpact, formData.probability
    ]);

    const handleFieldChange = (field: keyof IroEvaluation, value: any) => {
        if (!isEditMode) return;
        setFormData((prev) => ({ ...prev, [field]: value }));
        handleUserInputChange(iroId, { [field]: value });
        setHasUserInput(true);
    };

    const handleAutoFillDescription = async () => {
        setLoadingDescriptionAI(true);
        try {
            const payload = {
                riskChance: formData.riskOpportunity || formData.positiveNegativeImpact,
                affectedArea: formData.affectedArea,
                generationType: 'DESCRIPTION',
            };
            const response = await chatbotAxios.post('/api/chat/generateDescriptionEffect', payload);
            handleFieldChange('description', response.data.reply || '');
        } catch (err) {
            console.error('Error generating AI description:', err);
        } finally {
            setLoadingDescriptionAI(false);
        }
    };

    const renderInfoIcon = (fieldKey: string) => (
        <OverlayTrigger placement="right" overlay={<Tooltip id={`tooltip-${fieldKey}`}>{fieldDescriptions[fieldKey] || fieldKey}</Tooltip>}>
            <Info size={16} className="ms-1 info-icon" />
        </OverlayTrigger>
    );

    const possibleValues = {
        Scale: ['minimal', 'low', 'medium', 'high', 'absolute'],
        Scope: ['limited', 'concentrated', 'medium', 'widespread', 'global'],
        Irreversibility: ['easily', 'moderately', 'difficult', 'practically', 'absolutely'],
        FinancialEffect: ['minimal', 'low', 'medium', 'high', 'absolute'],
        Probability: ['unlikely long-term', 'likely long-term', 'mid-term', 'likely short-term', 'very short-term'],
    };

    const renderSlider = (field: keyof IroEvaluation, min: number, max: number, step: number, value: number | undefined, labels: string[]) => {
        const displayedValueIndex = Math.round(((value ?? min) - min) / step);
        const displayedValue = labels[displayedValueIndex] ?? labels[0];
        return isEditMode ? (
            <div className="form-range-container">
                <input type="range" className="form-range" min={min} max={max} step={step} value={value ?? min} onChange={(e) => handleFieldChange(field, Number(e.target.value))}/>
                <div className="range-label">{displayedValue}</div>
            </div>
        ) : (<Form.Control type="text" readOnly plaintext value={displayedValue} />);
    };

    // Main render function
    return (
        <Card className="mb-4 topic-details-card">
            <Card.Header className={`d-flex justify-content-between align-items-center ${isEditMode ? 'bg-warning' : 'bg-light'} topic-details-header`}>
                <Badge bg={isEditMode ? 'warning' : 'secondary'} className="fs-6 mode-badge">
                    {isEditMode ? <><Pencil size={16} className="me-1" />Edit Mode</> : <><Eye size={16} className="me-1" />View Mode</>}
                </Badge>
                {isEditMode && <Badge bg={autoSaveStatus === 'saving' ? 'info' : 'success'} className="fs-6">{autoSaveStatus === 'saving' ? 'Saving...' : 'Saved'}</Badge>}
            </Card.Header>

            <Card.Body className="topic-details-body">
                {/* FINANCIAL IRO FORM */}
                {formData.iroType === 'financial' && (
                    <Form>
                        <h5 className="section-heading">General Information</h5>
                        <Row className="mb-3">
                            <Col md={6}><Form.Group><Form.Label>Risk/Opportunity {renderInfoIcon('riskOpportunity')}</Form.Label>{isEditMode ? <Form.Select value={formData.riskOpportunity || ''} onChange={(e) => handleFieldChange('riskOpportunity', e.target.value)}><option value="">Select</option><option value="risk">Risk</option><option value="opportunity">Opportunity</option></Form.Select> : <Form.Control readOnly plaintext value={formData.riskOpportunity || 'N/A'}/>}</Form.Group></Col>
                            <Col md={6}><Form.Group><Form.Label>Time Horizon {renderInfoIcon('timeHorizon')}</Form.Label>{isEditMode ? <Form.Select value={formData.timeHorizon || ''} onChange={(e) => handleFieldChange('timeHorizon', e.target.value)}><option value="">Select</option><option value="short-term">Short-term</option><option value="medium-term">Medium-term</option><option value="long-term">Long-term</option></Form.Select> : <Form.Control readOnly plaintext value={formData.timeHorizon || 'N/A'}/>}</Form.Group></Col>
                        </Row>
                        <Row className="mb-3">
                            <Col md={12}><Form.Group style={{ position: 'relative' }}><Form.Label>Description {renderInfoIcon('description')}</Form.Label>{isEditMode ? <><Form.Control as="textarea" rows={3} value={formData.description || ''} onChange={(e) => handleFieldChange('description', e.target.value)}/><Button className="ask-ai-btn" variant="white" size="sm" style={{ top: '1.8rem', right: '0.2rem' }} title="Generate with AI" onClick={handleAutoFillDescription} disabled={loadingDescriptionAI}>{loadingDescriptionAI ? <i className="bi bi-hourglass-split"></i> : <i className="bi bi-stars"></i>}</Button></> : <Form.Control as="textarea" rows={3} readOnly plaintext value={formData.description || 'N/A'}/>}</Form.Group></Col>
                        </Row>
                        <Row className="mb-3">
                            <Col md={6}><Form.Group><Form.Label>Direct or indirect impact on the value chain {renderInfoIcon('directIndirectImpact')}</Form.Label>{isEditMode ? <Form.Select value={formData.directIndirectImpact || ''} onChange={(e) => handleFieldChange('directIndirectImpact', e.target.value)}><option value="">Select</option><option value="direct">Direct</option><option value="indirect">Indirect</option></Form.Select> : <Form.Control readOnly plaintext value={formData.directIndirectImpact || 'N/A'}/>}</Form.Group></Col>
                            <Col md={6}><Form.Group><Form.Label>Affected Area {renderInfoIcon('affectedArea')}</Form.Label>{isEditMode ? <Form.Select value={formData.affectedArea || ''} onChange={(e) => handleFieldChange('affectedArea', e.target.value)}><option value="">Select</option><option value="own operation">Own Operation</option><option value="upstream value chain">Upstream Value Chain</option><option value="downstream value chain">Downstream Value Chain</option></Form.Select> : <Form.Control readOnly plaintext value={formData.affectedArea || 'N/A'}/>}</Form.Group></Col>
                        </Row>

                        <h5 className="mt-4 section-heading">Financial Materiality</h5>
                        <Row>
                            <Col md={6}><Form.Group><Form.Label>Financial Effect {renderInfoIcon('financialMaterialityActualImpact')}</Form.Label>{renderSlider('financialMaterialityActualImpact', 1, 5, 1, formData.financialMaterialityActualImpact, possibleValues.FinancialEffect)}</Form.Group></Col>
                            <Col md={6}><Form.Group><Form.Label>Probability {renderInfoIcon('probability')}</Form.Label>{renderSlider('probability', 0.2, 1, 0.2, formData.probability, possibleValues.Probability)}</Form.Group></Col>
                        </Row>
                    </Form>
                )}

                {/* IMPACT IRO FORM */}
                {formData.iroType === 'impact' && (
                    <Form>
                        <h5 className="section-heading">General Information</h5>
                        <Row className="mb-3">
                            <Col md={6}><Form.Group><Form.Label>Positive/Negative Impact {renderInfoIcon('positiveNegativeImpact')}</Form.Label>{isEditMode ? <Form.Select value={formData.positiveNegativeImpact || ''} onChange={(e) => handleFieldChange('positiveNegativeImpact', e.target.value)}><option value="">Select</option><option value="positive">Positive</option><option value="negative">Negative</option></Form.Select> : <Form.Control readOnly plaintext value={formData.positiveNegativeImpact || 'N/A'}/>}</Form.Group></Col>
                            <Col md={6}><Form.Group><Form.Label>Impact Type {renderInfoIcon('actualPotentialImpact')}</Form.Label>{isEditMode ? <Form.Select value={formData.actualPotentialImpact || ''} onChange={(e) => handleFieldChange('actualPotentialImpact', e.target.value)}><option value="">Select</option><option value="actual">Actual</option><option value="potential">Potential</option></Form.Select> : <Form.Control readOnly plaintext value={formData.actualPotentialImpact || 'N/A'}/>}</Form.Group></Col>
                        </Row>
                        <Row className="mb-3">
                            <Col md={12}><Form.Group style={{ position: 'relative' }}><Form.Label>Description {renderInfoIcon('description')}</Form.Label>{isEditMode ? <><Form.Control as="textarea" rows={3} value={formData.description || ''} onChange={(e) => handleFieldChange('description', e.target.value)}/><Button className="ask-ai-btn" variant="white" size="sm" style={{ top: '1.8rem', right: '0.2rem' }} title="Generate with AI" onClick={handleAutoFillDescription} disabled={loadingDescriptionAI}>{loadingDescriptionAI ? <i className="bi bi-hourglass-split"></i> : <i className="bi bi-stars"></i>}</Button></> : <Form.Control as="textarea" rows={3} readOnly plaintext value={formData.description || 'N/A'}/>}</Form.Group></Col>
                        </Row>
                        <Row className="mb-3">
                            <Col md={6}><Form.Group><Form.Label>Time Horizon {renderInfoIcon('timeHorizon')}</Form.Label>{isEditMode ? <Form.Select value={formData.timeHorizon || ''} onChange={(e) => handleFieldChange('timeHorizon', e.target.value)}><option value="">Select</option><option value="short-term">Short-term</option><option value="medium-term">Medium-term</option><option value="long-term">Long-term</option></Form.Select> : <Form.Control readOnly plaintext value={formData.timeHorizon || 'N/A'}/>}</Form.Group></Col>
                            <Col md={6}><Form.Group><Form.Label>Is there a negative impact on human rights? {renderInfoIcon('humanRightsImpact')}</Form.Label>{isEditMode ? <Form.Select value={formData.humanRightsImpact || ''} onChange={(e) => handleFieldChange('humanRightsImpact', e.target.value)}><option value="">Select</option><option value="yes">Yes</option><option value="no">No</option></Form.Select> : <Form.Control readOnly plaintext value={formData.humanRightsImpact || 'N/A'}/>}</Form.Group></Col>
                        </Row>
                        <Row className="mb-3">
                            <Col md={6}><Form.Group><Form.Label>Direct or indirect impact on the value chain {renderInfoIcon('directIndirectImpact')}</Form.Label>{isEditMode ? <Form.Select value={formData.directIndirectImpact || ''} onChange={(e) => handleFieldChange('directIndirectImpact', e.target.value)}><option value="">Select</option><option value="direct">Direct</option><option value="indirect">Indirect</option></Form.Select> : <Form.Control readOnly plaintext value={formData.directIndirectImpact || 'N/A'}/>}</Form.Group></Col>
                            <Col md={6}><Form.Group><Form.Label>Affected Area {renderInfoIcon('affectedArea')}</Form.Label>{isEditMode ? <Form.Select value={formData.affectedArea || ''} onChange={(e) => handleFieldChange('affectedArea', e.target.value)}><option value="">Select</option><option value="own operation">Own Operation</option><option value="upstream value chain">Upstream Value Chain</option><option value="downstream value chain">Downstream Value Chain</option></Form.Select> : <Form.Control readOnly plaintext value={formData.affectedArea || 'N/A'}/>}</Form.Group></Col>
                        </Row>

                        <h5 className="mt-4 section-heading">Impact Materiality</h5>
                        <Row>
                            <Col md={6}><Form.Group><Form.Label>Scale {renderInfoIcon('scale')}</Form.Label>{renderSlider('scale', 1, 5, 1, formData.scale, possibleValues.Scale)}</Form.Group></Col>
                            <Col md={6}><Form.Group><Form.Label>Scope {renderInfoIcon('scope')}</Form.Label>{renderSlider('scope', 1, 5, 1, formData.scope, possibleValues.Scope)}</Form.Group></Col>
                        </Row>
                        {formData.positiveNegativeImpact === 'negative' &&
                            <Row className='mt-3'>
                                <Col md={6}><Form.Group><Form.Label>Irreversibility {renderInfoIcon('irreversibility')}</Form.Label>{renderSlider('irreversibility', 1, 5, 1, formData.irreversibility, possibleValues.Irreversibility)}</Form.Group></Col>
                            </Row>
                        }
                    </Form>
                )}

                {/* RESULTS CARD (COMMON TO BOTH) */}
                <Card className="mt-5 results-card">
                    <Card.Body>
                        <h5 className="mb-3 results-heading">Results</h5>
                        <Row>
                            <Col md={6}>
                                <Form.Label>{formData.iroType === 'impact' ? 'Impact Materiality' : 'Financial Materiality'}</Form.Label>
                                <div className="results-field">
                                    {formData.iroType === 'impact' ? formData.impactMaterialityPotentialImpact : formData.financialMaterialityPotentialImpact || 'N/A'}
                                </div>
                            </Col>
                            <Col md={6}>
                                <Form.Label>Materiality Assessment Result {renderInfoIcon('resultMaterialityAssessment')}</Form.Label>
                                <div className="results-field">
                                    <Badge bg={formData.resultMaterialityAssessment === 'Relevant' ? 'success' : 'secondary'} className="fs-6">
                                        {formData.resultMaterialityAssessment || 'Not Assessed'}
                                    </Badge>
                                </div>
                            </Col>
                        </Row>
                    </Card.Body>
                </Card>
            </Card.Body>
        </Card>
    );
};

export default IroDetailsForm;