import  { useEffect, useState, useMemo, useRef } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON>, Card, Col, Container, Form, Row, Nav, Offcanvas } from 'react-bootstrap';
import { useCompanyContext } from '../../context_module/CompanyContext';
import { useProjectContext } from '../../context_module/ProjectContext';
import { useNavigate } from "react-router-dom";

import DataPointsList from './DataPointsList';
import IroContainer from './IroContainer';

import ProgressUtils from "../../utilities/ProgressUtils";
import IroService, { IroDto } from "../04_ESRS_Selection/IroService";
import { EsrsTopic, IroEvaluation, EsrsTopicSelection, Stakeholder } from '../../utilities/types';


// Define the props for ImpactFinancialAnalysis
interface ImpactFinancialAnalysisProps {
    activeKey: string; // This prop is passed from Mithril.tsx
    // Add other props if your parent passes them
}

function ImpactFinancialAnalysis({ activeKey }: ImpactFinancialAnalysisProps) {
    const { companies } = useCompanyContext();
    const { currentProject } = useProjectContext();
    const navigate = useNavigate();

    const [topics, setTopics] = useState<EsrsTopic[]>([]);
    const [error, setError] = useState<string | null>(null);

    // Topic selections (relevance etc.)
    const [topicSelections, setTopicSelections] = useState<Record<number, EsrsTopicSelection>>({});
    const [selectedCompanyId, setSelectedCompanyId] = useState<number | null>(null);

    // We'll keep two separate states:
    // - iroDtos: the base IRO objects (IroDto) – created via IRO endpoints.
    // - iroEvaluations: the evaluations corresponding to each IRO.
    const [iroDtos, setIroDtos] = useState<IroDto[]>([]);
    const [iroEvaluations, setIroEvaluations] = useState<IroEvaluation[]>([]);

    // NEW: store fetched stakeholders
    const [stakeholders, setStakeholders] = useState<Stakeholder[]>([]);

    const [selectedTopicId, setSelectedTopicId] = useState<number | null>(null);
    const [activeTab, setActiveTab] = useState<string>('Environmental');

    const intervalRef = useRef<NodeJS.Timeout | null>(null);

    // NEW: State to control the offcanvas for calculation explanation
    const [showCalculationCanvas, setShowCalculationCanvas] = useState(false);


    const isComponentActive = activeKey === 'ImpactFinancialAnalysis';
    // Preselect the first company if none chosen
    useEffect(() => {
        if (companies.length > 0 && !selectedCompanyId) {
            setSelectedCompanyId(companies[0].id!);
        }
    }, [companies, selectedCompanyId]);

    // Override with project company if provided
    useEffect(() => {
        if (currentProject?.projectType === "company" && currentProject?.companyId) {
            setSelectedCompanyId(currentProject.companyId);
        }
    }, [currentProject]);

    useEffect(() => {
        if (selectedCompanyId && isComponentActive) {
            fetchDataForSelectedCompany(selectedCompanyId);
            if (intervalRef.current) clearInterval(intervalRef.current);
        }
        return () => {
            if (intervalRef.current) clearInterval(intervalRef.current);
        };
    }, [selectedCompanyId, isComponentActive]);

    const fetchDataForSelectedCompany = async (companyId: number) => {
        try {
            // 1) Fetch all ESRS topics
            const allTopics = await ProgressUtils.fetchAllEsrsTopics();
            console.log(allTopics);
            setTopics(allTopics);

            // 2) Fetch IROs (IroDto objects) for the company
            const iroDtosFetched = await IroService.getIrosByCompanyId(companyId);
            console.log("Fetched IRO DTOs:", iroDtosFetched);
            setIroDtos(iroDtosFetched);

            // 3) Fetch IRO evaluations for the company
            const evaluations = await IroService.getEvaluationsByCompanyId(companyId);
            console.log("Fetched IRO Evaluations:", evaluations);
            setIroEvaluations(evaluations);

            // 4) Fetch topic selections (relevant flags etc.)
            const selections = await ProgressUtils.fetchEsrsTopicSelectionsByCompanyId(companyId);
            const selectionsMap: Record<number, EsrsTopicSelection> = {};
            selections.forEach(sel => {
                selectionsMap[sel.esrsTopicId] = sel;
            });
            console.log("Fetched topic selections:", selectionsMap);
            setTopicSelections(selectionsMap);

            // 5) Fetch stakeholders
            const fetchedStakeholders = await ProgressUtils.fetchStakeholdersByCompanyId(companyId);
            setStakeholders(fetchedStakeholders);
        } catch (err) {
            console.error('Error fetching data:', err);
            setError('Failed to fetch data. Please try again.');
        }
    };

    // Filter only topics marked as relevant (based on topicSelections) for the active tab
    const topicsForTab = useMemo(() => {
        return topics.filter(t =>
            t.area === activeTab &&
            topicSelections[t.id]?.relevant === true
        );
    }, [topics, activeTab, topicSelections]);

    // Filter IRO DTOs for the selected topic
    const iroDtosForSelectedTopic = useMemo(() => {
        if (!selectedTopicId) return [];
        return iroDtos.filter(iro => iro.esrsTopicId === selectedTopicId);
    }, [iroDtos, selectedTopicId]);

    const handleTopicSelect = (topicId: number) => {
        console.log("Selected topic id:", topicId);
        setSelectedTopicId(topicId);
    };

    const handleNext = () => {
        navigate(`${import.meta.env.BASE_URL}mithril/mithril?tab=AnalysisResult`);
    };

    const handleBack = () => {
        navigate(`${import.meta.env.BASE_URL}mithril/mithril?tab=KPISelection`);
    };

    // When saving an IRO evaluation, we update only the evaluation state.
    // stakeholderName is passed from the corresponding IRODto.
    const handleIroSave = async (updatedIro: IroEvaluation, stakeholderName: string) => {
        try {
            if (updatedIro.iroId) {
                console.log(updatedIro);
                const savedEvaluation = await IroService.saveEvaluation(updatedIro, stakeholderName);
                setIroEvaluations(prev =>
                    prev.map(ev =>
                        ev.iroId === savedEvaluation.iroId
                            ? { ...ev, ...savedEvaluation }
                            : ev
                    )
                );
            }
        } catch (err) {
            console.error('Error saving IROEvaluation:', err);
        }
    };

    return (
        <Container className="mt-4">
            {/* Header row with title and info icon button */}
            <Row className="align-items-center mb-3">
                <Col>
                    <h2>Impact &amp; Financial Analysis</h2>
                </Col>
                <Col xs="auto">
                    <Button variant="info" onClick={() => setShowCalculationCanvas(true)} title="Calculation Explanation">
                        <i className="bi bi-question-circle"></i>
                    </Button>
                </Col>
            </Row>
            <p>
                <strong>Instructions:</strong> In this step you will conduct a double materiality analysis for each relevant
                sub(sub)topic (as selected in Topic Analysis). All sub(sub)topics marked as relevant are listed on the left.
                When you select a topic, all the IROs (base IRO data) for that sub‑subtopic are displayed on the right.
                Expanding an IRO will show its evaluation details.
            </p>

            {error && <Alert variant="danger" className="mt-3">{error}</Alert>}

            {/* Tabs and Company dropdown */}
            <div className="top-bar mb-3" style={{ width: '100%' }}>
                <Row className="align-items-center justify-content-between">
                    <Col>
                        <div className="tabs-container d-flex" style={{ gap: '1.5rem' }}>
                            <Nav
                                variant="tabs"
                                activeKey={activeTab}
                                onSelect={(k) => k && setActiveTab(k)}
                                style={{ borderBottom: 'none' }}
                            >
                                <Nav.Item>
                                    <Nav.Link eventKey="Environmental">Environmental</Nav.Link>
                                </Nav.Item>
                                <Nav.Item>
                                    <Nav.Link eventKey="Social">Social</Nav.Link>
                                </Nav.Item>
                                <Nav.Item>
                                    <Nav.Link eventKey="Governance">Governance</Nav.Link>
                                </Nav.Item>
                            </Nav>
                        </div>
                    </Col>
                    <Col xs="auto">
                        <div className="d-flex align-items-center">
                            <Form.Label htmlFor="company-dropdown" className="mb-0 me-2 fw-bold">
                                Selected Company:
                            </Form.Label>
                            <Form.Control
                                as="select"
                                id="company-dropdown"
                                className="company-select"
                                value={selectedCompanyId || ""}
                                onChange={(e) => setSelectedCompanyId(Number(e.target.value))}
                                style={{ maxWidth: '200px' }}
                            >
                                {companies.map((company) => (
                                    <option key={company.id} value={company.id}>
                                        {company.companyName}
                                    </option>
                                ))}
                            </Form.Control>
                        </div>
                    </Col>
                </Row>
            </div>

            <hr style={{ marginBottom: '2rem' }} />

            {selectedCompanyId ? (
                <Row>
                    {/* Left side: DataPointsList shows relevant topics */}
                    <Col md={5}>
                        <DataPointsList
                            dataPoints={topicsForTab}
                            selectedTopicId={selectedTopicId}
                            onSelect={handleTopicSelect}
                            topicSelections={topicSelections}
                            evaluations={iroEvaluations}
                            iroDtos={iroDtos}
                        />
                    </Col>
                    {/* Right side: IroContainer shows IROs (IroDto) and their evaluations */}
                    <Col md={7} className="mb-4">
                        <Card className="h-100">
                            <Card.Body className="detail-body">
                                {selectedTopicId ? (
                                    <IroContainer
                                        topicId={selectedTopicId}
                                        iroDtos={iroDtosForSelectedTopic}
                                        evaluations={iroEvaluations}
                                        onSaveIro={handleIroSave}

                                        // === PASS STAKEHOLDERS HERE ===
                                        stakeholders={stakeholders}
                                    />
                                ) : (
                                    <Alert variant="info" className="no-selection-alert">
                                        Please select a relevant sub-subtopic to view its IROs.
                                    </Alert>
                                )}
                            </Card.Body>
                        </Card>
                    </Col>
                </Row>
            ) : (
                <Row>
                    <Col>
                        <Alert variant="info" className="no-company-alert">
                            Please select a company to proceed with the analysis.
                        </Alert>
                    </Col>
                </Row>
            )}

            <Row className="mt-4">
                <Col className="d-flex justify-content-between">
                    <Button variant="outline-secondary" onClick={handleBack}>
                        <i className="bi bi-arrow-left"></i> Back
                    </Button>
                    <Button variant="primary" onClick={handleNext} disabled={!selectedCompanyId}>
                        Next <i className="bi bi-arrow-right"></i>
                    </Button>
                </Col>
            </Row>

            {/* Offcanvas for calculation explanation */}
            <Offcanvas
                show={showCalculationCanvas}
                onHide={() => setShowCalculationCanvas(false)}
                placement="end"
            >
                <Offcanvas.Header closeButton>
                    <Offcanvas.Title>Calculation Explanation</Offcanvas.Title>
                </Offcanvas.Header>
                <Offcanvas.Body>
                    <div className="calc-explanation p-3">
                        <h4 className="mb-3">Calculation Explanation</h4>
                        <div className="formula-section">
                            <div className="formula bg-light p-3 rounded shadow-sm mb-3">
                                <h5 className="text-primary">Positive Impact</h5>
                                <p className="mb-1">Actual Impact = (Scale + Scope) / 2</p>
                                <p>Potential Impact = Actual Impact × Probability</p>
                            </div>
                            <div className="formula bg-light p-3 rounded shadow-sm">
                                <h5 className="text-danger">Negative Impact</h5>
                                <p className="mb-1">Actual Impact = Scale + Scope + Irreversibility</p>
                                <p>Potential Impact = (Scale + Scope + Irreversibility) × Probability</p>
                            </div>
                        </div>
                        <hr/>
                        <p>
                            This methodology differentiates between positive and negative impacts. For positive impacts, irreversibility is excluded from the calculation;
                            for negative impacts, it is incorporated to reflect the difficulty of reversing the impact.
                        </p>
                    </div>
                </Offcanvas.Body>
            </Offcanvas>
        </Container>
    );
}

export default ImpactFinancialAnalysis;
