.accordion-item-custom {
    border: none;
    background-color: transparent;
}


.section-title {
    font-size: 1rem;
    margin: 0;
}

.data-point-card {
    border: 1px solid #dee2e6;
    border-radius: 8px;
    transition: box-shadow 0.2s ease, border-color 0.2s ease, background-color 0.2s ease;
    padding: 1rem;
    margin-bottom: 1rem;
    background-color: #fff;
    cursor: pointer;
    position: relative;

}

.data-point-card:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.selected-card {
    border: 2px solid #7E9F14 !important;;

}

.card-title {
    font-weight: 600;
    margin-bottom: 0;
}

.status-badge {
    font-size: 0.85rem;
    background: #fff3cd;
    color: #856404;
    border: 1px solid #ffeeba;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    white-space: nowrap;
}

.card-divider {
    border: none;
    border-top: 1px solid #dee2e6;
    margin: 0.5rem 0;
}

.info-line {
    font-size: 0.9rem;
    margin-top: 0.25rem;
    color: #333;
}

.card-bottom {
    margin-top: 2rem;
}

.edit-link {
    font-size: 0.9rem;
    color: #132b40;
    font-weight: 500;
    text-decoration: none;
    white-space: nowrap;
}

.edit-link:hover {
    text-decoration: underline;
}

.relevance-container {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.relevance-badge {
    display: inline-block;
    font-size: 0.85rem;
    font-weight: 500;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    white-space: nowrap;
}

.relevance-badge--green {
    background-color: #e6f4ea;
    color: #2e7d32;!important;
}

.relevance-badge--red {
    background-color: #fdecea;
    color: #d32f2f;!important;
}

.relevance-badge--gray {
    background-color: #f2f2f2;
    color: #6c757d;
}


