import React from 'react';
import { Accordion, Card, Badge } from 'react-bootstrap';
import { EsrsTopic, EsrsTopicSelection } from '../../utilities/types';
import { IroEvaluation } from '../../utilities/types';
import { IroDto } from '../04_ESRS_Selection/IroService';
import "./DataPointsList.css";
import ProgressUtils from "../../utilities/ProgressUtils";

interface DataPointsListProps {
    dataPoints: EsrsTopic[];
    onSelect: (id: number) => void;
    selectedTopicId: number | null;
    topicSelections?: Record<number, EsrsTopicSelection>;
    evaluations: IroEvaluation[];
    iroDtos: IroDto[];
}

const DataPointsList: React.FC<DataPointsListProps> = ({
                                                           dataPoints,
                                                           onSelect,
                                                           selectedTopicId,
                                                           topicSelections = {},
                                                           evaluations,
                                                           iroDtos
                                                       }) => {
    // Helper to determine if an evaluation is complete
    const isEvaluationComplete = (evaluation: IroEvaluation): boolean => {
        return Boolean(
            evaluation.affectedArea &&
            evaluation.description &&
            evaluation.effect &&
            evaluation.connection &&
            evaluation.scale !== undefined &&
            evaluation.scope !== undefined &&
            evaluation.irreversibility !== undefined &&
            evaluation.probability !== undefined &&
            evaluation.financialMaterialityActualImpact !== undefined &&
            evaluation.timeSpan !== undefined &&
            evaluation.impactMaterialityActualImpact &&
            evaluation.impactMaterialityPotentialImpact &&
            evaluation.financialMaterialityPotentialImpact &&
            evaluation.resultMaterialityAssessment
        );
    };

    // Group topics by "[esrsCode] - [topic]"
    const groupMap: Record<string, EsrsTopic[]> = {};
    dataPoints.forEach(t => {
        const key = `${t.esrsCode} - ${t.topic}`;
        if (!groupMap[key]) {
            groupMap[key] = [];
        }
        groupMap[key].push(t);
    });

    // For each topic, calculate separate final values and determine overall status:
    const calculateTopicStats = (topicId: number) => {
        // Get IRO DTOs for this topic
        const topicIros = iroDtos.filter(iro => iro.esrsTopicId === topicId);

        // Separate IROs by type
        const impactIros = topicIros.filter(iro => iro.iroType === 'impact');
        const financialIros = topicIros.filter(iro => iro.iroType === 'financial');

        // Find corresponding evaluations for each type
        const impactEvaluations = impactIros
            .map(iro => evaluations.find(ev => ev.iroId === iro.id))
            .filter(ev => ev) as IroEvaluation[];

        const financialEvaluations = financialIros
            .map(iro => evaluations.find(ev => ev.iroId === iro.id))
            .filter(ev => ev) as IroEvaluation[];

        // Calculate Final Impact Value
        let finalImpactValue = 'N/A';
        if (impactEvaluations.length > 0) {
            const sumImpact = impactEvaluations.reduce((sum, ev) => {
                const val = parseFloat(ev.impactMaterialityPotentialImpact || '0');
                return sum + (isNaN(val) ? 0 : val);
            }, 0);
            finalImpactValue = (sumImpact / impactEvaluations.length).toFixed(2);
        }

        // Calculate Final Finance Value
        let finalFinancialValue = 'N/A';
        if (financialEvaluations.length > 0) {
            const sumFinancial = financialEvaluations.reduce((sum, ev) => {
                const val = parseFloat(ev.financialMaterialityPotentialImpact || '0');
                return sum + (isNaN(val) ? 0 : val);
            }, 0);
            finalFinancialValue = (sumFinancial / financialEvaluations.length).toFixed(2);
        }

        // Decide final relevance based on threshold
        const threshold = 2;
        const finalRelevance =
            parseFloat(finalImpactValue) >= threshold || parseFloat(finalFinancialValue) >= threshold
                ? 'Relevant'
                : 'Not Relevant';

        // Determine status based on all IROs for the topic
        const allIrosForTopicCount = topicIros.length;
        const allEvaluationsForTopic = topicIros.map(iro => evaluations.find(e => e.iroId === iro.id)).filter(Boolean);
        const allComplete = allIrosForTopicCount > 0 && allEvaluationsForTopic.length === allIrosForTopicCount && allEvaluationsForTopic.every(ev => ev && isEvaluationComplete(ev));

        const status = allComplete ? 'DONE' : (allEvaluationsForTopic.length > 0 ? 'IN PROGRESS' : 'OPEN');

        // Save these final values to the database if we have a selection ID
        const sel = topicSelections[topicId];
        if (sel && sel.id) {
            const updatedSelection: EsrsTopicSelection = {
                ...sel,
                finalImpactMaterialityActualImpact: parseFloat(finalImpactValue) || null,
                finalFinancialMaterialityActualImpact: parseFloat(finalFinancialValue) || null,
                finalRelevance
            };
            ProgressUtils.updateEsrsTopicSelection(sel.id, updatedSelection)
                .catch(err => console.error('Failed to update final values for topic selection:', err));
        }

        return { avgImpact: finalImpactValue, avgFinancial: finalFinancialValue, finalRelevance, status };
    };

    return (
        <Accordion defaultActiveKey="">
            {Object.entries(groupMap).map(([groupName, topicsArray], idx) => {
                const eventKey = `group-${idx}`;
                return (
                    <Accordion.Item eventKey={eventKey} key={eventKey}>
                        <Accordion.Header>
                            <h5 className="mb-0 fw-bold section-title">{groupName}</h5>
                        </Accordion.Header>
                        <Accordion.Body>
                            {topicsArray.map(topic => {
                                const isSelected = selectedTopicId === topic.id;
                                const topicLabel = topic.subSubTopic
                                    ? ` ${topic.subSubTopic}`
                                    : topic.subtopic
                                        ? ` ${topic.subtopic}`
                                        : topic.topic;

                                const { avgImpact, avgFinancial, finalRelevance, status } =
                                    calculateTopicStats(topic.id!);

                                return (
                                    <Card
                                        key={topic.id}
                                        className={`data-point-card ${isSelected ? 'selected-card' : ''}`}
                                        onClick={() => onSelect(topic.id!)}
                                    >
                                        <div className="card-header d-flex justify-content-between align-items-center">
                                            <h5>{topicLabel}</h5>
                                            <Badge
                                                bg={
                                                    status === 'OPEN' ? 'secondary'
                                                        : status === 'IN PROGRESS' ? 'warning'
                                                            : 'success'
                                                }
                                            >
                                                {status}
                                            </Badge>
                                        </div>
                                        <hr className="card-divider" />
                                        <div className="info-line">
                                            <strong>Impact Materiality:</strong> {avgImpact}
                                        </div>
                                        <div className="info-line">
                                            <strong>Financial Materiality:</strong> {avgFinancial}
                                        </div>
                                        <div className="card-bottom d-flex justify-content-between align-items-center">
                                            <div className="relevance-container">
                                                <strong>Relevance:</strong> <strong>{finalRelevance}</strong>
                                            </div>
                                        </div>
                                    </Card>
                                );
                            })}
                        </Accordion.Body>
                    </Accordion.Item>
                );
            })}
        </Accordion>
    );
};

export default DataPointsList;