import React, { useState } from 'react';
import { <PERSON>ccordion, <PERSON>, Badge, Row, Button } from 'react-bootstrap';

import { Eye, Pencil } from 'lucide-react';
import IroDetailsForm from './IroDetailsForm';
import { IroEvaluation, Stakeholder } from '../../utilities/types';
import { IroDto } from '../04_ESRS_Selection/IroService';
import {PieChart} from "recharts";

const COLUMN_WIDTHS = {
    name: '170px',
    stakeholder: '150px',
    lastEditedBy: '150px',
    lastEditedAt: '150px',
    relevance: '120px',
    actions: '100px'
};

interface IroContainerProps {
    topicId: number;
    iroDtos: IroDto[];
    evaluations: IroEvaluation[];
    onSaveIro: (updatedIro: IroEvaluation, stakeholderName: string) => Promise<void>;
    /**
     * Stakeholders fetched from the backend.
     * We use them to display the stakeholder's name instead of just ID.
     */
    stakeholders: Stakeholder[];
}

const IroContainer: React.FC<IroContainerProps> = ({
                                                       topicId,
                                                       iroDtos,
                                                       evaluations,
                                                       onSaveIro,
                                                       stakeholders
                                                   }) => {
    // Filter IROs that belong to this topic
    const iroDtosForTopic = iroDtos.filter(iro => iro.esrsTopicId === topicId);

    // Helper: determine if an evaluation is fully complete
    const isEvaluationComplete = (evaluation: IroEvaluation): boolean => {
        return Boolean(
            evaluation.affectedArea &&
            evaluation.description &&
            evaluation.effect &&
            evaluation.connection &&
            evaluation.scale !== undefined &&
            evaluation.scope !== undefined &&
            evaluation.irreversibility !== undefined &&
            evaluation.probability !== undefined &&
            evaluation.financialMaterialityActualImpact !== undefined &&
            evaluation.timeSpan !== undefined &&
            evaluation.impactMaterialityActualImpact &&
            evaluation.impactMaterialityPotentialImpact &&
            evaluation.financialMaterialityPotentialImpact &&
            evaluation.resultMaterialityAssessment
        );
    };

    // Count how many are "done"
    const doneIrosCount = iroDtosForTopic.reduce((count, iroDto) => {
        const matchEval = evaluations.find(ev => ev.iroId === iroDto.id);
        return matchEval && isEvaluationComplete(matchEval)
            ? count + 1
            : count;
    }, 0);

    const totalIros = iroDtosForTopic.length;
    const remaining = totalIros - doneIrosCount;

    // Track each IRO's mode
    const [iroModes, setIroModes] = useState<Record<number, 'view' | 'edit'>>({});

    // Toggle or set the mode (view/edit)
    const handleSetMode = (iroId: number, mode: 'view' | 'edit', e?: React.MouseEvent) => {
        if (mode === 'edit' && e) {
            e.stopPropagation(); // Avoid toggling accordion
        }
        setIroModes(prev => ({
            ...prev,
            [iroId]: mode
        }));
    };

    // Create a map from stakeholderId -> stakeholderName
    const stakeholderMap: Record<number, string> = stakeholders.reduce(
        (acc, st) => {
            if (st.id != null) {
                acc[st.id] = st.name || `Stakeholder #${st.id}`;
            }
            return acc;
        },
        {}
    );

    if (iroDtosForTopic.length === 0) {
        return (
            <p className="text-muted mt-3">
                No IROs found for this topic. You can add new IROs in Topic Analysis.
            </p>
        );
    }

    return (
        <div>
            {/* Overview statistics */}
            <Card className="p-3 mt-3 shadow-sm">
                <Row className="align-items-center">
                    <div style={{ width: COLUMN_WIDTHS.name, textAlign: 'center' }}>
                        <div style={{ maxWidth: 150, margin: '0 auto' }}>
                            <PieChart
                                data={[
                                    { title: 'Done', value: doneIrosCount, color: '#3a9700' },
                                    { title: 'Remaining', value: remaining, color: '#ac3030' }
                                ]}
                                lineWidth={20}
                                paddingAngle={3}
                                rounded
                                animate
                                label={({ dataEntry }) => dataEntry.value}
                                labelStyle={{ fontSize: '6px', fill: '#ffffff' }}
                                style={{ height: '120px' }}
                            />
                        </div>
                    </div>
                    <div style={{ flex: 1, textAlign: 'center' }}>
                        <h6 className="fw-bold">Status Overview</h6>
                        <p className="mb-2">
                            <Badge bg="success" className="me-2">
                                {doneIrosCount} Done
                            </Badge>
                            <Badge bg="secondary">
                                {remaining} Remaining
                            </Badge>
                        </p>
                        <small className="text-muted">
                            {doneIrosCount} of {totalIros} IRO evaluations completed
                        </small>
                    </div>
                </Row>
            </Card>

            {/* Table header for wider screens */}
            <div
                className="d-none d-md-flex w-100 p-2 bg-light border mt-3"
                style={{ fontSize: '0.8rem', fontWeight: 'bold' }}
            >
                <div style={{ width: COLUMN_WIDTHS.name, textAlign: 'center' }}>
                    <i className="bi bi-file-earmark-text me-1"></i> Name
                </div>
                <div style={{ width: COLUMN_WIDTHS.stakeholder, textAlign: 'center' }}>
                    <i className="bi bi-people me-1"></i> Stakeholder
                </div>
                <div style={{ width: COLUMN_WIDTHS.lastEditedBy, textAlign: 'center' }}>
                    <i className="bi bi-person-check me-1"></i> Last Edited By
                </div>
                <div style={{ width: COLUMN_WIDTHS.lastEditedAt, textAlign: 'center' }}>
                    <i className="bi bi-clock-history me-1"></i> Last Edited At
                </div>
                <div style={{ width: COLUMN_WIDTHS.relevance, textAlign: 'center' }}>
                    <i className="bi bi-patch-check me-1"></i> Relevance
                </div>
                <div style={{ width: COLUMN_WIDTHS.actions, textAlign: 'center' }}></div>
            </div>

            <style>{`
                .no-caret .accordion-button::after {
                    display: none !important;
                }
            `}</style>

            <Accordion className="mt-0">
                {iroDtosForTopic.map(iroDto => {
                    // See if we have an existing evaluation
                    const matchEval = evaluations.find(ev => ev.iroId === iroDto.id);

                    // Create or fill an evaluation
                    const evaluation: IroEvaluation = matchEval || {
                        iroId: iroDto.id!,
                        companyId: iroDto.companyId || 0,
                        actualPotentialImpact: '',
                        // We'll read iroType from the IroDto, not from the evaluation
                        // so we won't keep "iroType" in the DB for the evaluation
                        affectedArea: '',
                        description: '',
                        effect: '',
                        connection: '',
                        scale: 1,
                        scope: 1,
                        irreversibility: 1,
                        probability: 0.2,
                        financialMaterialityActualImpact: 1,
                        timeSpan: 0.2,
                        impactMaterialityActualImpact: '',
                        impactMaterialityPotentialImpact: '',
                        financialMaterialityPotentialImpact: '',
                        resultMaterialityAssessment: '',
                        lastModifiedBy: '',
                        lastModifiedAt: ''
                    };

                    // Construct a merged object that includes the IroDto's iroType
                    // This ensures IroDetailsForm sees "iroType" for skipping irreversibility if "positive"
                    const combinedEval = {
                        ...evaluation,
                        iroType: iroDto.iroType || 'positive'
                    };

                    // stakeholderName from the stakeholder map
                    let stakeholderName = 'No Stakeholder';
                    if (iroDto.stakeholderId && stakeholderMap[iroDto.stakeholderId]) {
                        stakeholderName = stakeholderMap[iroDto.stakeholderId];
                    }

                    const currentMode = iroModes[iroDto.id!] || 'view';
                    const finalAssessment = evaluation.resultMaterialityAssessment || 'Not Evaluated';

                    return (
                        <Accordion.Item
                            eventKey={String(iroDto.id)}
                            key={iroDto.id}
                            className="no-caret"
                        >
                            <Accordion.Header>
                                <div
                                    className="d-flex align-items-center"
                                    style={{ fontSize: '0.9rem', width: '100%' }}
                                >
                                    <div
                                        style={{
                                            width: COLUMN_WIDTHS.name,
                                            textAlign: 'center'
                                        }}
                                    >
                                        {iroDto.name || 'Unnamed IRO'}
                                    </div>
                                    <div
                                        style={{
                                            width: COLUMN_WIDTHS.stakeholder,
                                            textAlign: 'center'
                                        }}
                                    >
                                        {stakeholderName}
                                    </div>
                                    <div
                                        style={{
                                            width: COLUMN_WIDTHS.lastEditedBy,
                                            textAlign: 'center'
                                        }}
                                    >
                                        {evaluation.lastModifiedBy || 'N/A'}
                                    </div>
                                    <div
                                        style={{
                                            width: COLUMN_WIDTHS.lastEditedAt,
                                            textAlign: 'center'
                                        }}
                                    >
                                        {evaluation.lastModifiedAt || 'N/A'}
                                    </div>
                                    <div
                                        style={{
                                            width: COLUMN_WIDTHS.relevance,
                                            textAlign: 'center'
                                        }}
                                    >
                                        <Badge
                                            bg={
                                                finalAssessment === 'Relevant'
                                                    ? 'success'
                                                    : 'secondary'
                                            }
                                            style={{ fontSize: '0.75rem' }}
                                        >
                                            {finalAssessment}
                                        </Badge>
                                    </div>
                                    <div
                                        style={{
                                            width: COLUMN_WIDTHS.actions,
                                            textAlign: 'center'
                                        }}
                                    >
                                        <div className="d-flex gap-2 justify-content-center">
                                            <Button
                                                variant={
                                                    currentMode === 'view'
                                                        ? 'primary'
                                                        : 'outline-secondary'
                                                }
                                                size="sm"
                                                onClick={() =>
                                                    handleSetMode(iroDto.id!, 'view')
                                                }
                                                title="View Mode"
                                            >
                                                <Eye size={16} />
                                            </Button>
                                            <Button
                                                variant={
                                                    currentMode === 'edit'
                                                        ? 'primary'
                                                        : 'outline-secondary'
                                                }
                                                size="sm"
                                                onClick={(e) =>
                                                    handleSetMode(iroDto.id!, 'edit', e)
                                                }
                                                title="Edit Mode"
                                            >
                                                <Pencil size={16} />
                                            </Button>
                                        </div>
                                    </div>
                                </div>
                            </Accordion.Header>

                            <Accordion.Body>
                                <Card className="mb-3">
                                    <Card.Body>
                                        <IroDetailsForm
                                            iroId={combinedEval.iroId}
                                            details={combinedEval}
                                            mode={currentMode}
                                            onSaveIro={onSaveIro}
                                            // These two are no‐ops here, since we handle them inside IroDetailsForm
                                            handleInputChange={() => {}}
                                            handleUserInputChange={() => {}}
                                            mainTopic={combinedEval.description || ''}
                                            subtopic={combinedEval.effect || ''}
                                            subSubTopic={combinedEval.connection || ''}
                                            stakeholderName={stakeholderName}
                                        />
                                    </Card.Body>
                                </Card>
                            </Accordion.Body>
                        </Accordion.Item>
                    );
                })}
            </Accordion>
        </div>
    );
};

export default IroContainer;
