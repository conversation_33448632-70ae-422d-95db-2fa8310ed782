import React, { useState } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>, Badge, Row, Button, Col } from 'react-bootstrap';
import { Eye, Pencil, ChevronDown, CheckCircle, List } from 'lucide-react';

import IroDetailsForm from './IroDetailsForm';
import { IroEvaluation, Stakeholder } from '../../utilities/types';
import { IroDto } from '../04_ESRS_Selection/IroService';

const COLUMN_DEFINITIONS = {
    name: { label: 'Name', width: '25%', icon: <List size={14} className="me-1" /> },
    stakeholder: { label: 'Stakeholder', width: '20%', icon: <i className="bi bi-people me-1"></i> },
    lastEditedBy: { label: 'Last Edited By', width: '15%', icon: <i className="bi bi-person-check me-1"></i> },
    lastEditedAt: { label: 'Last Edited At', width: '15%', icon: <i className="bi bi-clock-history me-1"></i> },
    relevance: { label: 'Relevance', width: '15%', icon: <CheckCircle size={14} className="me-1" /> },
    actions: { label: '', width: '10%' }
};

interface IroContainerProps {
    topicId: number;
    iroDtos: IroDto[];
    evaluations: IroEvaluation[];
    onSaveIro: (updatedIro: IroEvaluation, stakeholderName: string) => Promise<void>;
    stakeholders: Stakeholder[];
}

const CircularProgress = ({ percentage, size = 100, strokeWidth = 10 }) => {
    const radius = (size - strokeWidth) / 2;
    const circumference = 2 * Math.PI * radius;
    const offset = circumference - (percentage / 100) * circumference;

    return (
        <svg width={size} height={size} viewBox={`0 0 ${size} ${size}`}>
            <circle
                stroke="#e9ecef"
                fill="transparent"
                strokeWidth={strokeWidth}
                r={radius}
                cx={size / 2}
                cy={size / 2}
            />
            <circle
                stroke="#132b40" // UPDATED: Changed color as per your request
                fill="transparent"
                strokeWidth={strokeWidth}
                strokeLinecap="round"
                r={radius}
                cx={size / 2}
                cy={size / 2}
                strokeDasharray={circumference}
                strokeDashoffset={offset}
                transform={`rotate(-90 ${size / 2} ${size / 2})`}
            />
            <text x="50%" y="50%" textAnchor="middle" dy=".3em" fontSize="1.2em" fontWeight="bold" fill="#343a40">
                {`${Math.round(percentage)}%`}
            </text>
        </svg>
    );
};


const IroContainer: React.FC<IroContainerProps> = ({
                                                       topicId,
                                                       iroDtos,
                                                       evaluations,
                                                       onSaveIro,
                                                       stakeholders
                                                   }) => {
    const iroDtosForTopic = iroDtos.filter(iro => iro.esrsTopicId === topicId);

    const isEvaluationComplete = (evaluation: IroEvaluation, iroType: 'impact' | 'financial' | string): boolean => {
        const commonFieldsComplete = !!(
            evaluation.description
           // evaluation.directIndirectImpact &&
           // evaluation.affectedArea &&
           // evaluation.timeHorizon
        );

        if (!commonFieldsComplete) {
            return false;
        }

        if (iroType === 'impact') {
            return !!(
                evaluation.positiveNegativeImpact &&
                evaluation.actualPotentialImpact &&
                evaluation.humanRightsImpact &&
                evaluation.scale !== undefined &&
                evaluation.scope !== undefined &&
                evaluation.irreversibility !== undefined
            );
        }

        if (iroType === 'financial') {
            return !!(
                evaluation.riskOpportunity &&
                evaluation.financialMaterialityActualImpact !== undefined &&
                evaluation.probability !== undefined
            );
        }

        return false;
    };

    const doneIrosCount = iroDtosForTopic.reduce((count, iroDto) => {
        const matchEval = evaluations.find(ev => ev.iroId === iroDto.id);
        const iroType = iroDto.iroType || 'impact';
        return matchEval && isEvaluationComplete(matchEval, iroType) ? count + 1 : count;
    }, 0);

    const totalIros = iroDtosForTopic.length;
    const remaining = totalIros - doneIrosCount;
    const completionPercentage = totalIros > 0 ? (doneIrosCount / totalIros) * 100 : 0;


    const [iroModes, setIroModes] = useState<Record<number, 'view' | 'edit'>>({});
    const [openAccordionKey, setOpenAccordionKey] = useState<string | null>(null);


    const handleSetMode = (iroId: number, mode: 'view' | 'edit', e?: React.MouseEvent) => {
        if (mode === 'edit' && e) {
            e.stopPropagation();
        }
        setIroModes(prev => ({
            ...prev,
            [iroId]: mode
        }));
    };

    const handleAccordionToggle = (eventKey: string) => {
        setOpenAccordionKey(openAccordionKey === eventKey ? null : eventKey);
    };

    const stakeholderMap: Record<number, string> = stakeholders.reduce(
        (acc, st) => {
            if (st.id != null) {
                acc[st.id] = st.name || `Stakeholder #${st.id}`;
            }
            return acc;
        },
        {}
    );

    if (iroDtosForTopic.length === 0) {
        return (
            <Card className="text-center p-5 border-2 border-dashed">
                <h5 className="text-muted">No IROs Found</h5>
                <p className="text-muted mb-0">You can add new IROs for this topic in the Topic Analysis section.</p>
            </Card>
        );
    }

    return (
        <div className="iro-container-enhanced">
            <Card className="p-3 mb-4 shadow-sm">
                <Row className="align-items-center">
                    <Col xs="auto">
                        <CircularProgress percentage={completionPercentage} />
                    </Col>
                    <Col>
                        <h5 className="card-title fw-bold mb-1">Evaluation Status</h5>
                        <p className="mb-2 text-muted">
                            {doneIrosCount} of {totalIros} IRO evaluations completed
                        </p>
                        <div>
                            <Badge pill bg="success" className="me-2 px-3 py-2">
                                {doneIrosCount} Done
                            </Badge>
                            <Badge pill bg="light" text="dark" className="px-3 py-2">
                                {remaining} Remaining
                            </Badge>
                        </div>
                    </Col>
                </Row>
            </Card>

            <div className="d-none d-md-flex w-100 p-2 bg-light text-secondary small text-uppercase rounded-top">
                {Object.values(COLUMN_DEFINITIONS).map(col => (
                    <div key={col.label} style={{ width: col.width, paddingLeft: '1rem' }} className="fw-bold">
                        {col.icon} {col.label}
                    </div>
                ))}
            </div>

            <style>{`
                .iro-accordion-item {
                    border: 1px solid #dee2e6;
                    border-top: none;
                    transition: all 0.2s ease-in-out;
                }
                .iro-accordion-item:first-of-type {
                   border-top: 1px solid #dee2e6;
                   border-top-left-radius: 0;
                   border-top-right-radius: 0;
                }
                 .iro-accordion-item:last-of-type {
                   border-bottom-left-radius: .375rem;
                   border-bottom-right-radius: .375rem;
                }
                .iro-accordion-header {
                   cursor: pointer;
                   transition: background-color 0.2s;
                }
                 .iro-accordion-header:hover {
                    background-color: #f8f9fa;
                 }

                .iro-accordion-header .accordion-button {
                    background-color: transparent !important;
                    border: none !important;
                    box-shadow: none !important;
                    padding: 1rem;
                    width: 100%;
                }
                .accordion-button::after {
                   display: none;
                }
                .accordion-body {
                    padding: 0;
                }
                .relevance-badge {
                  font-size: 0.8rem;
                  padding: 0.4em 0.8em;
                  font-weight: 500;
                }

            `}</style>

            <Accordion activeKey={openAccordionKey} onSelect={(key) => handleAccordionToggle(key as string)}>
                {iroDtosForTopic.map(iroDto => {
                    const matchEval = evaluations.find(ev => ev.iroId === iroDto.id);

                    const evaluation: IroEvaluation = matchEval || {
                        iroId: iroDto.id!,
                        companyId: iroDto.companyId || 0,
                        description: '',
                        directIndirectImpact: '',
                        affectedArea: '',
                        timeHorizon: '',
                        positiveNegativeImpact: '',
                        actualPotentialImpact: '',
                        humanRightsImpact: '',
                        scale: 1,
                        scope: 1,
                        irreversibility: 1,
                        riskOpportunity: '',
                        financialMaterialityActualImpact: 1,
                        probability: 0.2,
                        impactMaterialityPotentialImpact: '',
                        financialMaterialityPotentialImpact: '',
                        resultMaterialityAssessment: '',
                        lastModifiedBy: '',
                        lastModifiedAt: ''
                    };

                    const combinedEval = { ...evaluation, iroType: iroDto.iroType || 'impact' };
                    const stakeholderName = iroDto.stakeholderId && stakeholderMap[iroDto.stakeholderId] ? stakeholderMap[iroDto.stakeholderId] : 'No Stakeholder';
                    const currentMode = iroModes[iroDto.id!] || 'view';
                    const finalAssessment = evaluation.resultMaterialityAssessment || 'Not Relevant';
                    const isAccordionOpen = openAccordionKey === String(iroDto.id);

                    return (
                        <Card as={Accordion.Item} eventKey={String(iroDto.id)} key={iroDto.id} className="iro-accordion-item">
                            <Card.Header as={Accordion.Header} onClick={() => handleAccordionToggle(String(iroDto.id))} className="p-0 border-0 iro-accordion-header">
                                <div className="d-flex align-items-center w-100 p-3">
                                    <div style={{ width: COLUMN_DEFINITIONS.name.width, paddingRight: '1rem', fontWeight: '500' }}>{iroDto.name || 'Unnamed IRO'}</div>
                                    <div style={{ width: COLUMN_DEFINITIONS.stakeholder.width, paddingRight: '1rem' }} className="text-muted">{stakeholderName}</div>
                                    <div style={{ width: COLUMN_DEFINITIONS.lastEditedBy.width, paddingRight: '1rem' }} className="text-muted">{evaluation.lastModifiedBy || 'System-Seeder'}</div>
                                    <div style={{ width: COLUMN_DEFINITIONS.lastEditedAt.width, paddingRight: '1rem' }} className="text-muted">{evaluation.lastModifiedAt ? new Date(evaluation.lastModifiedAt).toLocaleDateString() : new Date().toLocaleDateString()}</div>
                                    <div style={{ width: COLUMN_DEFINITIONS.relevance.width, paddingRight: '1rem' }}>
                                        <Badge pill bg={finalAssessment === 'Relevant' ? 'success' : 'danger'} text="light" className="relevance-badge">{finalAssessment}</Badge>
                                    </div>
                                    <div style={{ width: COLUMN_DEFINITIONS.actions.width, textAlign: 'center' }} className="d-flex justify-content-end align-items-center">
                                        <Button variant={currentMode === 'view' ? 'primary' : 'outline-secondary'} size="sm" onClick={() => handleSetMode(iroDto.id!, 'view')} title="View Mode" className="me-2"><Eye size={16} /></Button>
                                        <Button variant={currentMode === 'edit' ? 'primary' : 'outline-secondary'} size="sm" onClick={(e) => handleSetMode(iroDto.id!, 'edit', e)} title="Edit Mode"><Pencil size={16} /></Button>
                                        <ChevronDown size={20} className={`ms-2 text-muted transition-transform ${isAccordionOpen ? 'rotate-180' : ''}`}/>
                                    </div>
                                </div>
                            </Card.Header>

                            <Accordion.Body as={Accordion.Collapse} eventKey={String(iroDto.id)}>
                                <div className="p-4 bg-light">
                                    <IroDetailsForm
                                        iroId={combinedEval.iroId}
                                        details={combinedEval}
                                        mode={currentMode}
                                        onSaveIro={onSaveIro}
                                        handleInputChange={() => {}}
                                        handleUserInputChange={() => {}}
                                        mainTopic={combinedEval.description || ''}
                                        subtopic={""}
                                        subSubTopic={""}
                                        stakeholderName={stakeholderName}
                                    />
                                </div>
                            </Accordion.Body>
                        </Card>
                    );
                })}
            </Accordion>
        </div>
    );
};

export default IroContainer;