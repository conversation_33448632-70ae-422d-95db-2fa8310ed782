/* Soft Badge Backgrounds */
.bg-success-soft {
    background-color: rgba(25, 135, 84, 0.1);
}
.bg-danger-soft {
    background-color: rgba(220, 53, 69, 0.1);
}
.bg-secondary-soft {
    background-color: rgba(108, 117, 125, 0.1);
}
.text-success {
    color: #198754 !important;
}
.text-danger {
    color: #dc3545 !important;
}
.text-secondary {
    color: #6c757d !important;
}

/* Custom Accordion Styling */
.iro-accordion .iro-card {
    border-radius: 0.5rem;
    border: 1px solid #e9ecef;
    overflow: hidden;
    transition: box-shadow 0.2s ease-in-out;
}

.iro-accordion .iro-card:hover {
    box-shadow: 0 4px 12px rgba(0,0,0,0.08);
}

.iro-accordion .accordion-button {
    padding: 1rem 1.25rem;
    background-color: #fff;
    border-bottom: 1px solid #e9ecef;
    cursor: pointer;
}

/* Remove the default accordion icon and blue focus shadow */
.iro-accordion .accordion-button::after {
    display: none;
}
.iro-accordion .accordion-button:focus {
    box-shadow: none;
    border-color: #dee2e6;
}
.iro-accordion .accordion-button:not(.collapsed) {
    color: inherit;
    background-color: #f8f9fa;
    box-shadow: none;
}

/* Chevron animation */
.iro-accordion .accordion-chevron {
    transition: transform 0.2s ease-in-out;
}

.iro-accordion .accordion-button:not(.collapsed) .accordion-chevron {
    transform: rotate(180deg);
}

.iro-card-header {
    background-color: white !important; /* Override bootstrap */
}