// Types for analysis components
import {IroItem} from "../mainuser_navigation_module/04_ESRS_Selection/IroDefinitions.tsx";

export interface TopicData {
    id: number; // Changed from 'ID' to 'id' to match standard naming conventions
    subID?: string; // Optional if not always present
    area: string;
    esrsCode: string;
    topic: string;
    subtopic?: string;
    subSubTopic?: string;
}

export interface SavedData {
    scale: string;
    scope: string;
    irreversibility: string;
    resultMaterialityAssessment: string;
    impactMaterialityActualImpact: number;
    impactMaterialityPotentialImpact?: number;
    numericResultMaterialityAssessment?: number;
    financialMaterialityPotentialImpact?: number;
}

export interface CombinedData extends SavedData, TopicData {
}

export interface ChartData {
    x: number | null;
    y: number | null;
    esrsCode: string;
    topic: string;
    subtopic?: string;
    subSubTopic?: string;
    area: string;
    scale: string;
    scope: string;
    irreversibility: string;
}

// Updated types for your entities
export interface ValueChainObject {
    id?: number;
    name: string;
    industry: string;
    companyId?: number; // Reference to the company it belongs to
}

export interface Stakeholder {
    id?: number;
    name: string;
    role: string;
    email: string;
    stakeholderType: string;
    companyId?: number; // Reference to the company
    projectId?: number; // Reference to the project
    token?: string;
    status?: StakeholderStatus;
    completedDatapoints?: number;
    totalDatapoints?: number;
    valueChainObjects: number[]; // Array of ValueChainObject IDs
    esrsTopics?: EsrsTopic[];
    is_responsible: boolean | false;
}

export enum StakeholderStatus {
    INVITED = 'INVITED',
    IN_PROGRESS = 'IN_PROGRESS',
    COMPLETED = 'COMPLETED',
}

export interface Company {
    id?: number;
    companyName: string;
    address: string;
    vat: string;
    numEmployees: string;
    revenues?: number;
    industry: string;
    subCompany?: boolean;
    companyGroupId?: number; // Reference to CompanyGroup if applicable
    valueChainObjects?: ValueChainObject[];
    stakeholders?: Stakeholder[];
    esrsTopics?: EsrsTopic[];
    projectId?: number;
}

export interface CompanyGroup {
    id?: number;
    companyGroupName: string;
    companyGroupAddress: string;
    companyGroupVAT: string;
    numEmployees: string;
    revenues?: number;
    industry: string;
    subCompanies: Company[];

}

export interface EsrsTopic {
    id?: number;
    area: string;
    esrsCode: string;
    topic: string;
    subtopic?: string;
    subSubTopic?: string;
}

export interface EsrsTopicSelection {
    id?: number; // Optional, since new selections won't have an ID yet
    esrsTopicId: number;
    stakeholderId?: number | null;
    stakeholderName?: string;
    relevant: boolean | null;
    reasonIrrelevance: string;
    companyId: number;
    area?: string;
    topic?: string;
    subtopic?: string;
    status?: 'Open' | 'Logged' | 'Edited' | 'Approved';  // <-- NEW
    // NEW FIELDS:
    finalImpactMaterialityActualImpact?: number | null;
    finalFinancialMaterialityActualImpact?: number | null;
    finalRelevance?: string | null;

}

export interface EsrsTopicDetailsDTO {
    id?: number;
    esrsTopicId: number;
    riskChance?: string;
    affectedArea?: string;
    description?: string;
    effect?: string;
    connection?: string;
    scale?: number;
    scope?: number;
    irreversibility?: number;
    probability?: number;
    impactMaterialityActualImpact?: string;
    impactMaterialityPotentialImpact?: string;
    basisOfAssessmentOfFinancialImpact?: string;
    financialMaterialityActualImpact?: number;
    financialMaterialityPotentialImpact?: string;
    timeSpan?: number;
    companyId: number;
    resultMaterialityAssessment?: string;
    lastModifiedBy?: string;
    lastModifiedAt?: string;
    is_locked?: boolean;


}


export interface IroEvaluation {
    id?: number;
    iroId: number;
    companyId: number;

    // --- Common Fields for both IRO types ---
    description?: string;
    directIndirectImpact?: string; // New: 'direct', 'indirect'
    affectedArea?: string;
    timeHorizon?: string; // New: 'short-term', 'medium-term', 'long-term'

    // --- Impact IRO Fields ---
    positiveNegativeImpact?: string; // New: 'positive', 'negative'
    actualPotentialImpact?: string; // Renamed from 'Impact Type'
    humanRightsImpact?: string; // New: 'yes', 'no'
    scale?: number;
    scope?: number;
    irreversibility?: number;

    // --- Financial IRO Fields ---
    riskOpportunity?: string; // New: 'risk', 'opportunity'
    financialMaterialityActualImpact?: number; // Repurposed for 'Financial Effect'
    probability?: number;

    // --- Calculated Result Fields ---
    impactMaterialityPotentialImpact?: string; // Final Impact Score
    financialMaterialityPotentialImpact?: string; // Final Financial Score
    resultMaterialityAssessment?: string;

    // --- Deprecated / Replaced ---
    // The following fields are no longer used in the new UI and can be removed
    // from backend models if they are not needed for other purposes.
    // connection?: string;
    // timeSpan?: number;
    // impactMaterialityActualImpact?: string;

    // --- Metadata ---
    lastModifiedBy?: string;
    lastModifiedAt?: string;
    is_locked?: boolean;
}

export interface TopicDetails {
    id: number; // Changed 'ID' to 'id' for consistency
    subID?: string;
    area: string;
    esrsCode: string;
    topic: string;
    subTopic?: string;
    subSubTopic?: string;
}

export interface AnalysisData {
    [key: string]: any;
}


export interface CompanyGroupEsrsSelectionDTO {
    id?: number;
    companyGroupId: number;
    esrsTopicId: number;
    isRelevantCompanyGroup: boolean;
    reasonIrrelevance?: string;
}

