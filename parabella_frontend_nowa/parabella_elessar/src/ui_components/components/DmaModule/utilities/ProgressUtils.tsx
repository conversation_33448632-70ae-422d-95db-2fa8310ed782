// ProgressUtils.ts


import {
    Company,
    CompanyGroup,
    CompanyGroupEsrsSelectionDTO,
    EsrsTopic,
    IroEvaluation,
    EsrsTopicSelection,
    Stakeholder,
    StakeholderStatus,
    ValueChainObject
} from './types';
import api from "../context_module/api.ts";
import {IroDto} from "../mainuser_navigation_module/04_ESRS_Selection/IroService.ts";


export default class ProgressUtils {
    /**
     * Fetches a company by ID.
     */
    static async fetchCompany(companyId: number): Promise<Company | null> {
        try {
            const response = await api.get(`/companies/${companyId}`);
            console.log("company " + response.data);
            return response.data as Company;
        } catch (error) {
            console.error('Error fetching company:', error);
            return null;
        }
    }

    /**
     * Creates a new company.
     */
    static async createCompany(company: Company): Promise<Company> {
        try {
            const response = await api.post('/companies', company);
            return response.data as Company;
        } catch (error) {
            console.error('Error creating company:', error);
            throw error;
        }
    }

    /**
     * Updates an existing company.
     */
    static async updateCompany(company: Company): Promise<Company> {
        try {
            const response = await api.put(`/companies/${company.id}`, company);
            return response.data as Company;
        } catch (error) {
            console.error('Error updating company:', error);
            throw error;
        }
    }

    /**
     * Fetches a company group by ID.
     */
    static async fetchCompanyGroup(groupId: number): Promise<CompanyGroup | null> {
        try {
            const response = await api.get(`/company-groups/${groupId}`);
            return response.data as CompanyGroup;
        } catch (error) {
            console.error('Error fetching company group:', error);
            return null;
        }
    }

    /**
     * Creates a new company group.
     */
    static async createCompanyGroup(companyGroup: CompanyGroup): Promise<CompanyGroup> {
        try {
            const response = await api.post('/company-groups', companyGroup);
            return response.data as CompanyGroup;
        } catch (error) {
            console.error('Error creating company group:', error);
            throw error;
        }
    }

    /**
     * Updates an existing company group.
     */
    static async updateCompanyGroup(companyGroup: CompanyGroup): Promise<CompanyGroup> {
        try {
            const response = await api.put(`/company-groups/${companyGroup.id}`, companyGroup);
            return response.data as CompanyGroup;
        } catch (error) {
            console.error('Error updating company group:', error);
            throw error;
        }
    }

    /**
     * Fetches companies by company group ID.
     */
    static async fetchCompaniesByGroup(groupId: number): Promise<Company[]> {
        try {
            const response = await api.get(`/companies/by-group/${groupId}`);
            return response.data as Company[];
        } catch (error) {
            console.error('Error fetching companies by group:', error);
            return [];
        }
    }


    /**
     * ValueChain Section
     */


    /**
     * Fetches all ValueChainObjects for a company.
     */
    static async fetchValueChainObjects(companyId: number): Promise<ValueChainObject[]> {
        try {
            const response = await api.get(`/value-chain-objects/by-company/${companyId}`);
            return response.data as ValueChainObject[];
        } catch (error) {
            console.error('Error fetching value chain objects:', error);
            return [];
        }
    }

    /**
     * Creates a new ValueChainObject.
     */
    static async createValueChainObject(vco: ValueChainObject): Promise<ValueChainObject> {
        try {
            const response = await api.post('/value-chain-objects', vco);
            return response.data as ValueChainObject;
        } catch (error) {
            console.error('Error creating value chain object:', error);
            throw error;
        }
    }

    /**
     * Updates an existing ValueChainObject.
     */
    static async updateValueChainObject(vco: ValueChainObject): Promise<ValueChainObject> {
        try {
            const response = await api.put(`/value-chain-objects/${vco.id}`, vco);
            return response.data as ValueChainObject;
        } catch (error) {
            console.error('Error updating value chain object:', error);
            throw error;
        }
    }

    /**
     * Deletes a ValueChainObject.
     */
    static async deleteValueChainObject(vcoId: number): Promise<void> {
        try {
            await api.delete(`/value-chain-objects/${vcoId}`);
        } catch (error) {
            console.error('Error deleting value chain object:', error);
            throw error;
        }
    }

    /**
     * Stakeholder Section
     */

    /**
     * Fetches all Stakeholders for a company.
     */
    static async fetchStakeholdersByCompanyId(companyId: number): Promise<Stakeholder[]> {
        try {
            const response = await api.get(`/stakeholders/company/${companyId}`);
            return response.data as Stakeholder[];
        } catch (error) {
            console.error('Error fetching stakeholders:', error);
            return [];
        }
    }

    /**
     * Creates a new Stakeholder.
     */
    static async createStakeholder(stakeholder: Stakeholder): Promise<Stakeholder> {
        try {
            const response = await api.post('/stakeholders', stakeholder);
            return response.data as Stakeholder;
        } catch (error) {
            console.error('Error creating stakeholder:', error);
            throw error;
        }
    }

    /**
     * Updates an existing Stakeholder.
     */
    static async updateStakeholder(id: number, stakeholder: Stakeholder): Promise<Stakeholder> {
        try {
            const response = await api.put(`/stakeholders/${id}`, stakeholder);
            return response.data as Stakeholder;
        } catch (error) {
            console.error('Error updating stakeholder:', error);
            throw error;
        }
    }

    /**
     * Deletes a Stakeholder.
     */
    static async deleteStakeholder(id: number): Promise<void> {
        try {
            await api.delete(`/stakeholders/${id}`);
        } catch (error) {
            console.error('Error deleting stakeholder:', error);
            throw error;
        }
    }


    /**
     * ESRS Topics Section
     */

    /**
     * Fetches all ESRS Topics.
     */
    static async fetchAllEsrsTopics(): Promise<EsrsTopic[]> {
        try {
            const response = await api.get('/esrs-topics/subtopics');
            return response.data as EsrsTopic[];
        } catch (error) {
            console.error('Error fetching ESRS topics:', error);
            return [];
        }
    }


    /**
     * Fetches EsrsTopicSelections by company ID.
     */
    static async fetchEsrsTopicSelectionsByCompanyId(companyId: number): Promise<EsrsTopicSelection[]> {
        try {
            const response = await api.get(`/esrs-topic-selections/company/${companyId}`);
            return response.data as EsrsTopicSelection[];
        } catch (error) {
            console.error('Error fetching EsrsTopicSelections:', error);
            return [];
        }
    }

    /**
     * Creates a new EsrsTopicSelection.
     */
    static async createEsrsTopicSelection(selection: EsrsTopicSelection): Promise<EsrsTopicSelection> {
        try {
            const response = await api.post('/esrs-topic-selections', selection);
            return response.data as EsrsTopicSelection;
        } catch (error) {
            console.error('Error creating EsrsTopicSelection:', error);
            throw error;
        }
    }

    /**
     * Updates an existing EsrsTopicSelection.
     */
    static async updateEsrsTopicSelection(id: number, selection: EsrsTopicSelection): Promise<EsrsTopicSelection> {
        try {
            const response = await api.put(`/esrs-topic-selections/${id}`, selection);
            return response.data as EsrsTopicSelection;
        } catch (error) {
            console.error('Error updating EsrsTopicSelection:', error);
            throw error;
        }
    }

    /**
     * Deletes an EsrsTopicSelection.
     */
    static async deleteEsrsTopicSelection(id: number): Promise<void> {
        try {
            await api.delete(`/esrs-topic-selections/${id}`);
        } catch (error) {
            console.error('Error deleting EsrsTopicSelection:', error);
            throw error;
        }
    }

    static async fetchEsrsTopicDetails(id: number): Promise<IroEvaluation> {
        try {
            const response = await api.get(`/esrs-topic-details/${id}`);
            return response.data as IroEvaluation;
        } catch (error) {
            console.error('Error fetching ESRS topic details:', error);
            throw error;
        }
    }

    /**
     * Fetches all EsrsTopicDetails for a given company ID.
     */
    static async fetchEsrsTopicDetailsByCompanyId(
        companyId: number
    ): Promise<IroEvaluation[]> {
        try {
            const response = await api.get(`/esrs-topic-details/company/${companyId}`);
            return response.data as IroEvaluation[];
        } catch (error) {
            console.error('Error fetching ESRS topic details:', error);
            throw error;
        }
    }

    static async createEsrsTopicDetails(details: IroEvaluation): Promise<IroEvaluation> {
        try {
            const response = await api.post('/esrs-topic-details', details);
            return response.data as IroEvaluation;
        } catch (error) {
            console.error('Error creating ESRS topic details:', error);
            throw error;
        }
    }

    static async updateEsrsTopicDetails(id: number, details: IroEvaluation): Promise<IroEvaluation> {
        try {
            const response = await api.put(`/esrs-topic-details/${id}`, details);
            return response.data as IroEvaluation;
        } catch (error) {
            console.error('Error updating ESRS topic details:', error);
            throw error;
        }
    }

    // ProgressUtils.ts

    static async createOrUpdateEsrsTopicDetails(details: IroEvaluation, stakeholderName?: string): Promise<IroEvaluation> {
        try {
            console.log(details.is_locked)
            const response = await api.post('/esrs-topic-details', details, {

                params: {

                    stakeholderName: stakeholderName || ''
                }
            });
            return response.data as IroEvaluation;
        } catch (error) {
            console.error('Error creating/updating ESRS topic details:', error);
            throw error;
        }
    }

    /**
     * Fetches EsrsTopicDetails by company ID and EsrsTopic ID.
     */
    static async fetchEsrsTopicDetailsByCompanyIdAndTopicId(
        companyId: number,
        esrsTopicId: number
    ): Promise<IroEvaluation> {
        try {
            const response = await api.get(
                `/esrs-topic-details/company/${companyId}/topic/${esrsTopicId}`
            );
            return response.data as IroEvaluation;
        } catch (error) {
            console.error('Error fetching ESRS topic details:', error);
            throw error;
        }
    }


    /**
     * Sends email notifications to stakeholders
     */
    static async sendStakeholderEmails(stakeholdersToNotify: {
        id: number;
        name: string;
        email: string;
        type: string;
        companyName: string;
        projectId: number;
    }[]): Promise<void> {
        try {
            const response = await api.post('/stakeholders/send-stakeholder-emails', stakeholdersToNotify);
            console.log('Email notifications sent successfully:', response.data);
        } catch (error) {
            console.error('Error sending email notifications:', error);
            throw error;
        }
    }

    /**
     * Fetches a stakeholder by their unique token.
     * This matches the endpoint: GET /api/stakeholders/token/{token}
     */
    static async fetchStakeholderByToken(token: string): Promise<Stakeholder | null> {
        if (!token) {
            console.error('Fetch stakeholder by token: token is null or empty.');
            return null;
        }
        try {
            const response = await api.get(`/stakeholders/token/${token}`);
            return response.data as Stakeholder;
        } catch (error) {
            console.error(`Error fetching stakeholder by token ${token}:`, error);
            return null; // Return null on error to allow UI to handle it
        }
    }

    /**
     * Fetches all IROs assigned to a specific stakeholder.
     * Uses the new endpoint: GET /api/stakeholders/{stakeholderId}/iros
     */
    static async fetchIrosByStakeholderId(stakeholderId: number): Promise<IroDto[]> {
        if (!stakeholderId) {
            console.error('Fetch IROs by stakeholder ID: stakeholderId is null or invalid.');
            return [];
        }
        try {
            const response = await api.get(`/stakeholders/${stakeholderId}/iros`);
            return response.data as IroDto[];
        } catch (error) {
            console.error(`Error fetching IROs for stakeholder ID ${stakeholderId}:`, error);
            return [];
        }
    }

    /**
     * Fetches EsrsTopicSelections relevant to a stakeholder.
     * Uses the new endpoint: GET /api/stakeholders/{stakeholderId}/topic-selections
     * Note: The backend currently returns EsrsTopicSelection entities.
     * Ensure your frontend `EsrsTopicSelection` type matches the structure of these entities
     * or adapt if the backend starts returning a DTO.
     */
    static async fetchEsrsTopicSelectionsByStakeholderId(stakeholderId: number): Promise<EsrsTopicSelection[]> {
        if (!stakeholderId) {
            console.error('Fetch ESRS Topic Selections by stakeholder ID: stakeholderId is null or invalid.');
            return [];
        }
        try {
            // The old endpoint was /esrs-topic-selections/stakeholder/{stakeholderId}
            // The new endpoint from StakeholderController is /stakeholders/{stakeholderId}/topic-selections
            const response = await api.get(`/stakeholders/${stakeholderId}/topic-selections`);
            return response.data as EsrsTopicSelection[];
        } catch (error) {
            console.error('Error fetching EsrsTopicSelections by stakeholder ID:', error);
            return [];
        }
    }

    /**
     * Creates or updates an IRO Evaluation.
     * The backend for IroEvaluationService has `createOrUpdateIroEvaluation`
     * which takes `IroEvaluationDTO` and `stakeholderName`.
     */
    static async saveIroEvaluation(evaluation: IroEvaluation, stakeholderName?: string): Promise<IroEvaluation> {
        try {
            // Ensure the evaluation object matches the backend's IroEvaluationDTO structure if it's different.
            // Particularly, ensure `iroId` is present in the `evaluation` object.
            const response = await api.post('/iroEval', evaluation, {
                params: {
                    stakeholderName: stakeholderName || '' // Pass stakeholderName as a query parameter
                }
            });
            return response.data as IroEvaluation; // Assuming backend returns the saved/updated evaluation
        } catch (error) {
            console.error('Error saving IRO evaluation:', error);
            throw error;
        }
    }

    static async fetchIroEvaluationsByCompanyId(companyId: number): Promise<IroEvaluation[]> {
        try {
            // Assuming your IroEvaluationController uses `/iro-evaluations/company/{companyId}`
            const response = await api.get(`/iroEval/company/${companyId}`);
            return response.data as IroEvaluation[];
        } catch (error) {
            console.error(`Error fetching IRO evaluations for company ID ${companyId}:`, error);
            return [];
        }
    }
    /**
     * Updates the status of a stakeholder.
     */
    static async updateStakeholderStatus(token: string, newStatus: StakeholderStatus): Promise<void> {
        try {
            await api.post(`/stakeholders/update-status/${token}`, JSON.stringify(newStatus), {
                headers: {
                    'Content-Type': 'application/json'
                }
            });
        } catch (error) {
            console.error('Error updating stakeholder status:', error);
            throw error;
        }
    }

    // ProgressUtils.ts

    static async createOrUpdateCompanyGroupEsrsSelection(
        selectionDTO: CompanyGroupEsrsSelectionDTO
    ): Promise<CompanyGroupEsrsSelectionDTO> {
        try {
            const response = await api.post('/company-group-esrs-selections', selectionDTO);
            return response.data as CompanyGroupEsrsSelectionDTO;
        } catch (error) {
            console.error('Error creating/updating company group ESRS selection:', error);
            throw error;
        }
    }

    static async fetchGroupSelectionsByCompanyGroupId(
        companyGroupId: number
    ): Promise<CompanyGroupEsrsSelectionDTO[]> {
        try {
            const response = await api.get(`/company-group-esrs-selections/company-group/${companyGroupId}`);
            return response.data as CompanyGroupEsrsSelectionDTO[];
        } catch (error) {
            console.error('Error fetching group selections by company group ID:', error);
            throw error;
        }
    }


}
