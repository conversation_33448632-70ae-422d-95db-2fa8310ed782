// src/components/CustomPromptOverlay.tsx

import React, { useState } from 'react';
import { Modal, Button, Form, Spinner, Alert, ListGroup } from 'react-bootstrap';
import { Send, Star} from 'lucide-react';
import axios from 'axios';

interface CustomPromptOverlayProps {
    show: boolean;
    handleClose: () => void;
}

const CustomPromptOverlay: React.FC<CustomPromptOverlayProps> = ({ show, handleClose }) => {
    const [prompt, setPrompt] = useState<string>('');
    const [response, setResponse] = useState<string>('');
    const [loading, setLoading] = useState<boolean>(false);
    const [error, setError] = useState<string | null>(null);
    // New state for favourite prompts
    const [favouritePrompts, setFavouritePrompts] = useState<string[]>([]);

    const handleSend = async () => {
        if (!prompt.trim()) {
            setError('Prompt cannot be empty.');
            return;
        }

        setLoading(true);
        setError(null);
        setResponse('');

        try {
            const res = await axios.post('/api/chat/custom-prompt', { prompt });
            setResponse(res.data.aiResponse);
        } catch (err: any) {
            console.error(err);
            setError(err.response?.data?.message || 'An error occurred while fetching the AI response.');
        } finally {
            setLoading(false);
        }
    };

    const handleInputChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
        setPrompt(e.target.value);
    };

    // New: Save current prompt to favourites (if not already saved)
    const handleSaveFavourite = () => {
        const trimmed = prompt.trim();
        if (trimmed && !favouritePrompts.includes(trimmed)) {
            setFavouritePrompts([...favouritePrompts, trimmed]);
        }
    };

    // New: Load a favourite prompt into the textarea when clicked
    const handleLoadFavourite = (fav: string) => {
        setPrompt(fav);
    };

    // New: Remove a prompt from the favourites list
    const handleDeleteFavourite = (fav: string) => {
        setFavouritePrompts(favouritePrompts.filter(item => item !== fav));
    };

    return (
        <Modal show={show} onHide={handleClose} size="lg" backdrop="static" keyboard={false} className="custom-prompt-overlay">
            <Modal.Header closeButton className="border-bottom-0">
                <Modal.Title>Custom Prompt Builder</Modal.Title>
            </Modal.Header>
            <Modal.Body>
                <Form>
                    <Form.Group controlId="customPromptTextarea" className="mb-3">
                        <Form.Label>Edit Your Prompt</Form.Label>
                        <Form.Control
                            as="textarea"
                            rows={10}
                            value={prompt}
                            onChange={handleInputChange}
                            placeholder="Enter your custom prompt here..."
                            className="prompt-textarea"
                        />
                    </Form.Group>
                    <div className="d-flex align-items-center mb-3">
                        <Button variant="primary" onClick={handleSend} disabled={loading} className="me-2 d-flex align-items-center">
                            {loading ? (
                                <>
                                    <Spinner
                                        as="span"
                                        animation="border"
                                        size="sm"
                                        role="status"
                                        aria-hidden="true"
                                        className="me-2"
                                    />
                                    Sending...
                                </>
                            ) : (
                                <>
                                    <Send className="me-2" /> Send Prompt
                                </>
                            )}
                        </Button>
                        <Button variant="secondary" onClick={handleSaveFavourite} disabled={!prompt.trim()} className="d-flex align-items-center">
                            <Star className="me-2" /> Save to Favourites
                        </Button>
                    </div>
                    {error && <Alert variant="danger" className="mt-3">{error}</Alert>}
                    {response && (
                        <Form.Group controlId="aiResponse" className="mt-4">
                            <Form.Label>AI Response</Form.Label>
                            <Form.Control
                                as="textarea"
                                rows={10}
                                value={response}
                                readOnly
                                style={{ backgroundColor: '#f8f9fa' }}
                            />
                        </Form.Group>
                    )}
                </Form>

                {/* New: Favourites Toolbar */}
                {favouritePrompts.length > 0 && (
                    <div className="favourites-toolbar mt-4">
                        <h5>Favourite Prompts</h5>
                        <ListGroup horizontal className="overflow-auto">
                            {favouritePrompts.map((fav, index) => (
                                <ListGroup.Item key={index} className="d-flex align-items-center favourite-prompt-item">
                  <span
                      className="prompt-text flex-grow-1"
                      onClick={() => handleLoadFavourite(fav)}
                      style={{ cursor: 'pointer' }}
                  >
                    {fav.length > 30 ? fav.substring(0, 30) + '...' : fav}
                  </span>
                                    <Button variant="link" className="p-0 ms-2" onClick={() => handleDeleteFavourite(fav)}>
                                        <Star size={16} color="#ffc107" />
                                    </Button>
                                </ListGroup.Item>
                            ))}
                        </ListGroup>
                    </div>
                )}
            </Modal.Body>
            <Modal.Footer className="border-top-0">
                <Button variant="secondary" onClick={handleClose} disabled={loading}>
                    Close
                </Button>
            </Modal.Footer>
        </Modal>
    );
};

export default CustomPromptOverlay;
