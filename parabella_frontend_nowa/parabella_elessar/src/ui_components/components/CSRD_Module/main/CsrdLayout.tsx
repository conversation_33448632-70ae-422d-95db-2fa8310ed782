// src/modules/csrd/components/CsrdLayout.tsx
import React, { useState, useEffect } from 'react';
import { Container, Tab, Nav, Navbar, Card, Modal, Button } from 'react-bootstrap';
import { Link, useLocation, useNavigate } from 'react-router-dom';

// Import your actual page components
import CSRDDashboard from "./dashboard/CsrdDashboard.tsx";
import AiUploadPage from "./dashboard/ai-upload-page.tsx";
import ReportsPage from "./report/ReportsPage.tsx";
import SettingsPage from "./SettingsPage";
import CsrdAnalyticsPage from "./CsrdAnalyticsPage"; // <--- IMPORT NEW PAGE

// Import icons
import { UploadCloud, FileText, Settings as SettingsIcon, Home, BarChart } from 'lucide-react'; // <-- Added BarChart
import { DashboardCustomize } from "@mui/icons-material";

// Import the CSS
import './css/CsrsLayout.css';

const CsrdLayout: React.FC = () => {
    const [activeKey, setActiveKey] = useState('dashboard');
    const [showHomeConfirmModal, setShowHomeConfirmModal] = useState(false);
    const location = useLocation();
    const navigate = useNavigate();

    // Define the steps/tabs including Home
    const csrdSteps = [
        { eventKey: 'home', title: 'Home', Icon: Home, isNavLink: false },
        { eventKey: 'dashboard', title: 'Dashboard', Icon: DashboardCustomize, isNavLink: true },
        { eventKey: 'aiupload', title: 'AI Upload', Icon: UploadCloud, isNavLink: true },
        { eventKey: 'reports', title: 'Reports', Icon: FileText, isNavLink: true },
        { eventKey: 'analytics', title: 'Analytics', Icon: BarChart, isNavLink: true }, // <--- ADDED ANALYTICS STEP
        { eventKey: 'settings', title: 'Settings', Icon: SettingsIcon, isNavLink: true },
    ];

    /**
     * Sync active tab with URL query param (?tab=...)
     */
    useEffect(() => {
        const searchParams = new URLSearchParams(location.search);
        const tabQueryParam = searchParams.get('tab');

        // Find if the tab from the URL is valid *content* tab
        const isValidContentTab = csrdSteps.some(step => step.isNavLink && step.eventKey === tabQueryParam);

        if (tabQueryParam && isValidContentTab) {
            setActiveKey(tabQueryParam);
        } else {
            // Default to 'dashboard' if no valid content tab is specified
            const currentBasePath = location.pathname;
            // Only update URL if it doesn't already specify 'dashboard' or is invalid/missing
            if (!tabQueryParam || !isValidContentTab) {
                // Use replace to avoid adding duplicate history entries
                navigate(`${currentBasePath}?tab=dashboard`, { replace: true });
                setActiveKey('dashboard');
            } else if (!tabQueryParam) { // Handles case where path is correct but no query param
                setActiveKey('dashboard');
            }
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [location.search, location.pathname, navigate]); // Dependencies


    // --- Base Path for Links ---
    const csrdBasePath = `${import.meta.env.BASE_URL || '/'}csrd/main`; // Added fallback for BASE_URL

    // --- Handlers ---
    const handleHomeClick = (e: React.MouseEvent) => {
        e.preventDefault(); // Prevent any default link behavior
        setShowHomeConfirmModal(true);
    };

    const handleLeaveProject = () => {
        setShowHomeConfirmModal(false);
        // Navigate to your main application dashboard or project list
        navigate('/dashboard'); // <--- ADJUST THIS ROUTE if needed
        console.log("Leaving project...");
    };

    const handleCloseModal = () => {
        setShowHomeConfirmModal(false);
    };

    return (
        <div>
            {/* Optional: Include your top-level Navbar here if it exists */}
            {/* <CsrdNavbar /> */}

            {/* 2. Main Content Area with Tab Navigation */}
            <Container fluid className="mt-3">
                {/* Tab.Container manages content based on activeKey (excluding Home) */}
                <Tab.Container id="csrd-tabs" activeKey={activeKey}>

                    {/* Tab Navigation Bar with new styles */}
                    {/* Added csrd-tab-navbar class */}
                    <Navbar expand="lg" className="csrd-tab-navbar">
                        {/* Removed bg="light" as background is handled by CSS */}
                        {/* Removed justify-content-center here, handled in CSS */}
                        <Container fluid>
                            {/* Consider if Toggle is needed if tabs might overflow on small screens */}
                            <Navbar.Toggle aria-controls="csrd-navbar-tabs"/>
                            <Navbar.Collapse id="csrd-navbar-tabs">
                                {/* Added nav-tabs-custom class for CSS targeting */}
                                {/* Use mx-auto to center the nav group if navbar-collapse isn't full width */}
                                <Nav className="nav-tabs-custom" role="tablist">
                                    {csrdSteps.map((step) => (
                                        <Nav.Item key={step.eventKey}>
                                            {step.isNavLink ? (
                                                // Regular NavLink for content tabs
                                                <Nav.Link
                                                    as={Link}
                                                    to={`${csrdBasePath}?tab=${step.eventKey}`}
                                                    eventKey={step.eventKey}
                                                    // active class is handled by Tab.Container + matching eventKey/activeKey
                                                    // className={activeKey === step.eventKey ? 'active' : ''} // Redundant with Tab.Container
                                                >
                                                    <div className="step-content">
                                                        {step.Icon && <step.Icon size={18} />}
                                                        <span className="step-title2">{step.title}</span>
                                                    </div>
                                                </Nav.Link>
                                            ) : (
                                                // Special handling for Home button
                                                <Nav.Link
                                                    href="#" // Keep it looking like a link
                                                    onClick={handleHomeClick} // Trigger modal
                                                    className="home-link" // Class for potential specific styling
                                                    // Do not set eventKey here if it doesn't correspond to a Tab.Pane
                                                >
                                                    <div className="step-content">
                                                        {step.Icon && <step.Icon size={18} />}
                                                        <span className="step-title2">{step.title}</span>
                                                    </div>
                                                </Nav.Link>
                                            )}
                                        </Nav.Item>
                                    ))}
                                </Nav>
                            </Navbar.Collapse>
                        </Container>
                    </Navbar>

                    {/* Tab Content Panes */}
                    <Card className="custom-card border-0 shadow-sm"> {/* Example styling */}
                        <Card.Body className="p-0"> {/* Remove default Card padding */}
                            <Tab.Content>
                                {/* Render content panes based on activeKey */}
                                <Tab.Pane className="p-3" eventKey="dashboard"> {/* Add padding back here */}
                                    {activeKey === 'dashboard' && <CSRDDashboard />}
                                </Tab.Pane>
                                <Tab.Pane className="p-3" eventKey="aiupload">
                                    {activeKey === 'aiupload' && <AiUploadPage />}
                                </Tab.Pane>
                                <Tab.Pane className="p-3" eventKey="reports">
                                    {activeKey === 'reports' && <ReportsPage />}
                                </Tab.Pane>
                                {/* --- ADDED ANALYTICS PANE --- */}
                                <Tab.Pane className="p-3" eventKey="analytics">
                                    {activeKey === 'analytics' && <CsrdAnalyticsPage />}
                                </Tab.Pane>
                                {/* -------------------------- */}
                                <Tab.Pane className="p-3" eventKey="settings">
                                    {activeKey === 'settings' && <SettingsPage />}
                                </Tab.Pane>
                            </Tab.Content>
                        </Card.Body>
                    </Card>

                </Tab.Container>
            </Container>

            {/* Home Confirmation Modal */}
            <Modal show={showHomeConfirmModal} onHide={handleCloseModal} centered className="leave-confirm-modal">
                {/* ... modal content ... */}
                <Modal.Header closeButton>
                    <Modal.Title>Confirm Navigation</Modal.Title>
                </Modal.Header>
                <Modal.Body>
                    Are you sure you want to leave this project and return to the main dashboard?
                </Modal.Body>
                <Modal.Footer>
                    <Button variant="secondary" onClick={handleCloseModal}>
                        Cancel
                    </Button>
                    <Button variant="danger" onClick={handleLeaveProject}>
                        Leave Project
                    </Button>
                </Modal.Footer>
            </Modal>
        </div>
    );
};

export default CsrdLayout;