import React, { useState, useEffect } from 'react';
import { Container, Tab, Nav, Navbar, <PERSON>, Modal, Button } from 'react-bootstrap';
import { Link, useLocation, useNavigate } from 'react-router-dom';

// Import your actual page components
import CSRDDashboard from "./dashboard/CsrdDashboard.tsx"; // For Content & Topic Management
import AiUploadPage from "./dashboard/ai-upload-page.tsx";  // For Data Sources & AI Analysis
import ReportsPage from "./report/ReportsPage.tsx";        // For Report Preview & Export
// import SettingsPage from "./SettingsPage"; // Removed as per new flow
// import CsrdAnalyticsPage from "../../analytics/CsrdAnalyticsPage.tsx"; // Removed as per new flow

// Import icons
import { UploadCloud, FileText, Settings as SettingsIcon, Home, BarChart, Edit3, ClipboardList } from 'lucide-react';
// import { DashboardCustomize } from "@mui/icons-material"; // Kept for DashboardCustomize if preferred

// Import the CSS
import './css/CsrsLayout.css'; // Ensure this path is correct

const CsrdLayout: React.FC = () => {
    const [activeKey, setActiveKey] = useState('data-sources'); // Default to the first main step
    const [showHomeConfirmModal, setShowHomeConfirmModal] = useState(false);
    const location = useLocation();
    const navigate = useNavigate();

    const homeStep = { eventKey: 'home', title: 'Home', Icon: Home };
    const mainCsrdSteps = [
        { eventKey: 'data-sources', title: 'Data Sources & AI Analysis', Icon: UploadCloud, stepNumber: 1 },
        { eventKey: 'content-management', title: 'Content & Topic Management', Icon: Edit3, stepNumber: 2 },
        { eventKey: 'report-preview', title: 'Report Preview & Export', Icon: FileText, stepNumber: 3 },
    ];

    // Determine the index of the currently active main step
    const activeStepIndex = mainCsrdSteps.findIndex(step => step.eventKey === activeKey);

    /**
     * Sync active tab with URL query param (?tab=...)
     */
    useEffect(() => {
        const searchParams = new URLSearchParams(location.search);
        const tabQueryParam = searchParams.get('tab');
        const defaultTab = mainCsrdSteps[0].eventKey; // Default to the first main step

        const isValidContentTab = mainCsrdSteps.some(step => step.eventKey === tabQueryParam);

        if (tabQueryParam && isValidContentTab) {
            setActiveKey(tabQueryParam);
        } else {
            const currentBasePath = location.pathname;
            // If tab is missing, invalid, or not a main content tab, redirect to default
            if (location.search !== `?tab=${defaultTab}`) {
                navigate(`${currentBasePath}?tab=${defaultTab}`, { replace: true });
            }
            setActiveKey(defaultTab);
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [location.search, location.pathname, navigate]); // mainCsrdSteps is stable, not needed in deps

    const csrdBasePath = `${import.meta.env.BASE_URL || '/'}csrd/main`;

    const handleHomeClick = (e: React.MouseEvent) => {
        e.preventDefault();
        setShowHomeConfirmModal(true);
    };

    const handleLeaveProject = () => {
        setShowHomeConfirmModal(false);
        navigate(`${import.meta.env.BASE_URL}csrd/create`);
        console.log("Leaving project...");
    };

    const handleCloseModal = () => {
        setShowHomeConfirmModal(false);
    };

    return (
        <div>
            <Container fluid className="mt-3 csrd-layout-container">
                <Tab.Container id="csrd-tabs" activeKey={activeKey}>
                    <Navbar expand="lg" className="csrd-tab-navbar">
                        <Container fluid>
                            {/* Home Button on the Left */}
                            <Nav className="navbar-nav-home">
                                <Nav.Link
                                    href="#"
                                    onClick={handleHomeClick}
                                    className="home-nav-link"
                                >
                                    <div className="step-content">
                                        {homeStep.Icon && <homeStep.Icon size={20} className="home-icon" />}
                                        <span className="step-title-home">{homeStep.title}</span>
                                    </div>
                                </Nav.Link>
                            </Nav>

                            <Navbar.Toggle aria-controls="csrd-navbar-tabs" />
                            <Navbar.Collapse id="csrd-navbar-tabs" className="justify-content-center">
                                <Nav className="nav-tabs-custom" role="tablist">
                                    {mainCsrdSteps.map((step, index) => {
                                        let stepStatus = 'pending';
                                        if (activeStepIndex > -1) { // Ensure activeKey is found in steps
                                            if (index < activeStepIndex) {
                                                stepStatus = 'completed';
                                            } else if (index === activeStepIndex) {
                                                stepStatus = 'active';
                                            }
                                        } else if (index === 0 && activeKey === step.eventKey) {
                                            //Handles initial load case where activeStepIndex might be computed before activeKey state updates fully
                                            stepStatus = 'active';
                                        }


                                        return (
                                            <Nav.Item key={step.eventKey} className={`step-item step-status-${stepStatus}`}>
                                                <Nav.Link
                                                    as={Link}
                                                    to={`${csrdBasePath}?tab=${step.eventKey}`}
                                                    eventKey={step.eventKey}
                                                    className={activeKey === step.eventKey ? 'active' : ''}
                                                >
                                                    <div className="step-content-main">
                                                        <span className="step-number-badge">{step.stepNumber}</span>
                                                        <div className="step-icon-title-progress">
                                                            <div className="step-icon-title">
                                                                {step.Icon && <step.Icon size={18} className="step-icon" />}
                                                                <span className="step-title-main">{step.title}</span>
                                                            </div>
                                                            <div className="modern-progress-bar-container">
                                                                <div className={`modern-progress-bar-fill status-${stepStatus}`}></div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </Nav.Link>
                                            </Nav.Item>
                                        );
                                    })}
                                </Nav>
                            </Navbar.Collapse>

                            {/* Placeholder for right side to balance the Home link, only on larger screens */}
                            {/* This helps keep the main tabs truly centered if Home link has significant width */}
                            <Nav className="navbar-nav-home-placeholder d-none d-lg-flex" aria-hidden="true">
                                <Nav.Link className="home-nav-link" disabled style={{ visibility: 'hidden' }}>
                                    <div className="step-content">
                                        {homeStep.Icon && <homeStep.Icon size={20} className="home-icon" />}
                                        <span className="step-title-home">{homeStep.title}</span>
                                    </div>
                                </Nav.Link>
                            </Nav>

                        </Container>
                    </Navbar>

                    <Card className="custom-card border-0 shadow-sm">
                        <Card.Body className="p-0">
                            <Tab.Content>
                                <Tab.Pane className="p-3" eventKey="data-sources">
                                    {activeKey === 'data-sources' && <AiUploadPage />}
                                </Tab.Pane>
                                <Tab.Pane className="p-3" eventKey="content-management">
                                    {activeKey === 'content-management' && <CSRDDashboard />}
                                </Tab.Pane>
                                <Tab.Pane className="p-3" eventKey="report-preview">
                                    {activeKey === 'report-preview' && <ReportsPage />}
                                </Tab.Pane>
                            </Tab.Content>
                        </Card.Body>
                    </Card>
                </Tab.Container>
            </Container>

            <Modal show={showHomeConfirmModal} onHide={handleCloseModal} centered className="leave-confirm-modal">
                <Modal.Header closeButton>
                    <Modal.Title>Confirm Navigation</Modal.Title>
                </Modal.Header>
                <Modal.Body>
                    Are you sure you want to leave this project and return to the main dashboard?
                </Modal.Body>
                <Modal.Footer>
                    <Button variant="secondary" onClick={handleCloseModal}>
                        Cancel
                    </Button>
                    <Button variant="danger" onClick={handleLeaveProject}>
                        Leave Project
                    </Button>
                </Modal.Footer>
            </Modal>
        </div>
    );
};

export default CsrdLayout;