// src/modules/csrd/components/company-info-form.tsx (Modified for styling)

import React, { useState, useEffect } from 'react';
// Added Image and adjusted imports if needed
import { Form, Button, Container, Row, Col, Card, Floating<PERSON><PERSON>l, Spinner, Alert, Image } from 'react-bootstrap';
import { Building, Briefcase, BarChart, Users, DollarSign, Send } from 'lucide-react';
import { CompanyInfo } from '../types/csrdTypes'; // Adjust path
import { useCsrdProjectContext } from '../../context/CsrdProjectProvider.tsx';
import { useNavigate } from 'react-router-dom';
import { imagesData } from '../../../../templateLogic/commonimages.tsx'; // Adjust path if needed

// --- Optional: Add some custom CSS for fine-tuning ---
// You could create a CompanyInfoForm.css file and import it
// import './CompanyInfoForm.css';
// Or add styles directly (less maintainable for larger changes)
const controlStyle = {
    paddingTop: '1.625rem', // Increase top padding inside the input to push text down
    paddingBottom: '0.625rem' // Default is usually 0.625rem, keep or adjust
};
// --- End Optional CSS ---


const CompanyInfoForm: React.FC = () => {
    const {
        companyInfo: initialCompanyInfo,
        saveCompanyInfo,
        isLoading: contextLoading,
        error: contextError,
        clearError,
        currentProject
    } = useCsrdProjectContext();
    const navigate = useNavigate();

    const [formData, setFormData] = useState<CompanyInfo>({
        companyName: initialCompanyInfo?.companyName || '',
        revenue: initialCompanyInfo?.revenue || '',
        industry: initialCompanyInfo?.industry || '',
        size: initialCompanyInfo?.size || 'Large Enterprise',
        numberOfEmployees: initialCompanyInfo?.numberOfEmployees || '',
    });
    const [validated, setValidated] = useState(false);
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [submitError, setSubmitError] = useState<string | null>(null);

    useEffect(() => {
        clearError();
        if (!currentProject) {
            console.warn("No CSRD project selected. Redirecting to project creation.");
            navigate(`${import.meta.env.BASE_URL}csrd/create`);
        }
        if (initialCompanyInfo) {
            setFormData({
                companyName: initialCompanyInfo.companyName || '',
                revenue: initialCompanyInfo.revenue || '',
                industry: initialCompanyInfo.industry || '',
                size: initialCompanyInfo.size || 'Large Enterprise',
                numberOfEmployees: initialCompanyInfo.numberOfEmployees || '',
            });
        } else if (currentProject) {
            setFormData({
                companyName: '', revenue: '', industry: '', size: 'Large Enterprise', numberOfEmployees: ''
            });
        }
    }, [currentProject, initialCompanyInfo, navigate, clearError]);

    const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
        const { name, value } = e.target;
        setFormData(prev => ({ ...prev, [name]: value }));
    };

    const handleSubmit = async (event: React.FormEvent<HTMLFormElement>) => {
        event.preventDefault();
        setSubmitError(null);
        clearError();
        const form = event.currentTarget;
        if (form.checkValidity() === false || !currentProject?.id) {
            event.stopPropagation();
            if (!currentProject?.id) {
                setSubmitError("Cannot submit: No active project found.");
            }
            setValidated(true);
        } else {
            setIsSubmitting(true);
            setValidated(true);
            try {
                await saveCompanyInfo(currentProject.id, formData);
                navigate(`${import.meta.env.BASE_URL}csrd/main`);
            } catch (error: any) {
                setSubmitError(error.message || "An unknown error occurred.");
            } finally {
                setIsSubmitting(false);
            }
        }
    };

    const showLoading = contextLoading || isSubmitting;
    const displayError = submitError || contextError;

    return (
        // Reduced top padding/margin potentially if needed, or rely on card body padding
        <Container className="d-flex align-items-center justify-content-center min-vh-100 py-4"> {/* Added py-4 for vertical padding */}
            <Row className="w-100 justify-content-center">
                <Col md={8} lg={6}>
                    {/* Added border radius similar to image */}
                    <Card className="shadow-lg border-0 rounded-3">
                        {/* Removed Card.Header for a cleaner look like the image */}
                        <Card.Body className="p-4 p-md-5"> {/* Standard padding */}

                            {/* Adjusted Image Spacing */}
                            <div className="text-center mb-3"> {/* Reduced margin bottom to mb-3 */}
                                <Image
                                    src={imagesData('csrd_logo')}
                                    alt="CSRD Reporting Logo" // Updated Alt text
                                    fluid
                                    style={{ maxHeight: '60px', width: 'auto' }} // Adjusted height slightly
                                />
                                {/* Optional: Add title below logo if needed */}
                                {/* <h3 className="mt-2">Company Information</h3>
                                <p className="text-muted small">Enter details for: {currentProject?.projectName || 'your project'}</p> */}
                            </div>

                            {displayError && <Alert variant="danger">{displayError}</Alert>}

                            <Form noValidate validated={validated} onSubmit={handleSubmit}>
                                {/* Added style={controlStyle} to Form.Control */}
                                <FloatingLabel controlId="companyName" label={<><Building size={16} className="me-1" /> Company Name</>} className="mb-3">
                                    <Form.Control required type="text" name="companyName" placeholder="Enter company name" value={formData.companyName} onChange={handleChange} disabled={showLoading} style={controlStyle} />
                                    <Form.Control.Feedback type="invalid">Please provide the company name.</Form.Control.Feedback>
                                </FloatingLabel>

                                <FloatingLabel controlId="revenue" label={<><DollarSign size={16} className="me-1" /> Annual Revenue (e.g., 70B EUR)</>} className="mb-3">
                                    <Form.Control type="text" name="revenue" placeholder="Enter annual revenue" value={formData.revenue} onChange={handleChange} disabled={showLoading} style={controlStyle} />
                                </FloatingLabel>

                                <FloatingLabel controlId="industry" label={<><Briefcase size={16} className="me-1" /> Industry / Sector(s)</>} className="mb-3">
                                    <Form.Control as="textarea" name="industry" placeholder="Describe primary industry/sectors" style={{ ...controlStyle, height: '100px' }} value={formData.industry} onChange={handleChange} disabled={showLoading} />
                                </FloatingLabel>

                                <FloatingLabel controlId="size" label={<><BarChart size={16} className="me-1" /> Company Size</>} className="mb-3">
                                    {/* Select doesn't use the text input style, no need for controlStyle here */}
                                    <Form.Select required name="size" value={formData.size} onChange={handleChange} disabled={showLoading} >
                                        <option value="">Select size...</option>
                                        <option value="Small">Small</option>
                                        <option value="Medium">Medium</option>
                                        <option value="Large Enterprise">Large Enterprise</option>
                                    </Form.Select>
                                    <Form.Control.Feedback type="invalid">Please select the company size.</Form.Control.Feedback>
                                </FloatingLabel>

                                <FloatingLabel controlId="numberOfEmployees" label={<><Users size={16} className="me-1" /> Number of Employees</>} className="mb-4">
                                    <Form.Control required type="text" name="numberOfEmployees" placeholder="Enter number of employees" value={formData.numberOfEmployees} onChange={handleChange} disabled={showLoading} style={controlStyle} />
                                    <Form.Control.Feedback type="invalid">Please provide the number of employees.</Form.Control.Feedback>
                                </FloatingLabel>

                                <div className="d-grid">
                                    {/* Removed size="lg", using default/medium size. Changed variant for dark blue */}
                                    <Button type="submit" variant="primary" disabled={showLoading} className="py-2"> {/* Added py-2 for padding */}
                                        {showLoading ? (
                                            <>
                                                <Spinner as="span" animation="border" size="sm" role="status" aria-hidden="true"/>
                                                <span className="ms-2">Saving...</span>
                                            </>
                                        ) : (
                                            <>Proceed to Dashboard <Send size={16} className="ms-2" /></> // Adjusted icon size
                                        )}
                                    </Button>
                                </div>
                            </Form>
                        </Card.Body>
                    </Card>
                </Col>
            </Row>
        </Container>
    );
};

export default CompanyInfoForm;