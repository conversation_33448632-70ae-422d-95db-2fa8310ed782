// src/modules/csrd/components/CreateNewProjectCsrd.tsx

import React, { useEffect, useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON>, Card, Col, Container, Form, Modal, Row, Spinner, Alert } from 'react-bootstrap';
import { useNavigate } from 'react-router-dom';
import { useCsrdProjectContext } from '../../context/CsrdProjectProvider.tsx'; // Use CSRD context
// Use CSRD Project type from context provider's types
import '../../../DmaModule/mainuser_navigation_module/css/CreateNewProjectDMA.css'; // Reusing DMA CSS
import { getCurrentUser } from '../../../../../services/authService.ts';
// Import desired icons from lucide-react
import { Building, PlusCircle, Library } from "lucide-react";
import {imagesData} from "../../../../templateLogic/commonimages.tsx"; // Using Library for Group

const CreateNewProjectCsrd: React.FC = () => {
    const [projectName, setProjectName] = useState('');
    const [projectDescription, setProjectDescription] = useState('');
    // Input selection still uses lowercase for simplicity
    const [projectType, setProjectType] = useState<'company' | 'companyGroup'>('company');
    const [userId, setUserId] = useState<number | null>(null);
    // Local state mainly for form validation errors now
    const [validationErrors, setValidationErrors] = useState<{ [key: string]: string }>({});
    const [loadingSubmit, setLoadingSubmit] = useState(false);
    const [selectedProjectId, setSelectedProjectId] = useState<number | 'new' | null>(null);
    const [showWarningModal, setShowWarningModal] = useState(false);

    // Get functions and state from CSRD context
    const {
        loadUserCsrdProjects,
        userCsrdProjects,
        createCsrdProject,
        selectCsrdProject,
        setCurrentProjectDirectly,
        isLoading: loadingProjects,
        error: contextError, // Use contextError directly for API/loading errors
        clearError // Get clearError from context
    } = useCsrdProjectContext();
    const navigate = useNavigate();

    // --- Effects ---
    useEffect(() => {
        clearError(); // Clear context errors when component mounts
        const currentUser = getCurrentUser();
        if (currentUser?.id) {
            setUserId(currentUser.id);
            loadUserCsrdProjects(currentUser.id);
        } else {
            console.error('No user is logged in.');
            // navigate('/login'); // Optional: Redirect if no user
        }
        setSelectedProjectId(null);
        selectCsrdProject(null); // Clear project selection in context
    }, [loadUserCsrdProjects, selectCsrdProject, navigate, clearError]); // Added clearError dependency

    // --- Validation ---
    const validateFields = () => {
        const newErrors: { [key: string]: string } = {};
        if (!projectName.trim()) newErrors.projectName = 'Project name is required';
        if (!projectDescription.trim()) newErrors.projectDescription = 'Project description is required';
        if (!userId) newErrors.userId = 'User ID is missing'; // Should not happen if useEffect runs correctly
        if (!projectType) newErrors.projectType = 'Please select a project type';
        setValidationErrors(newErrors);
        return Object.keys(newErrors).length === 0;
    };

    // --- Handlers ---
    const handleCreateAndProceed = async () => {
        clearError(); // Clear previous API errors
        if (!validateFields() || !userId) return;

        setLoadingSubmit(true);

        try {
            const projectData = {
                userId, // SECURITY NOTE: Backend should ideally use authenticated user ID
                projectName: projectName.trim(),
                projectDescription: projectDescription.trim(),
                projectType, // Pass 'company' or 'companyGroup' - API layer will map
            };

            const newProject = await createCsrdProject(projectData);

            if (newProject) {
                console.log('Created new CSRD project:', newProject.projectName);
                setCurrentProjectDirectly(newProject); // Set context immediately
                // Navigate to the Company Info form - Ensure path is correct
                navigate(`${import.meta.env.BASE_URL}csrd/companyinform`);
            }
            // If newProject is null, the context provider should have set contextError
        } catch (error) {
            // Catch potential errors not handled by context (should be rare)
            console.error('Unexpected error during project creation flow:', error);
            // Set validation error state as a fallback UI indicator if needed
            setValidationErrors(prev => ({ ...prev, form: 'An unexpected error occurred.' }));
        } finally {
            setLoadingSubmit(false);
        }
    };

    const handleLoadAndProceed = async (projectId: number) => {
        setLoadingSubmit(true);
        clearError(); // Clear previous errors

        try {
            const projectToLoad = userCsrdProjects.find(p => p.id === projectId);
            if (projectToLoad) {
                // Select project - context will handle fetching company info
                await selectCsrdProject(projectToLoad);
                // Navigate to Company Info form after selection attempt
                navigate(`${import.meta.env.BASE_URL}csrd/companyinform`);
            } else {
                // Should not happen if list is up-to-date, but handle defensively
                console.error("Selected project not found in the list.");
                setValidationErrors({ form: 'Selected project could not be found.' }); // Use validationErrors for feedback
            }
        } catch (error) {
            console.error("Error selecting project:", error);
            // Error during selectCsrdProject (e.g., fetching company info failed)
            // The contextError should already be set by the provider.
            // Optionally add specific feedback here if contextError isn't sufficient
            setValidationErrors(prev => ({ ...prev, form: 'Failed to load project details.' }));
        } finally {
            setLoadingSubmit(false);
        }
    };

    const handleSelectProject = (projectId: number | 'new') => {
        setSelectedProjectId(projectId);
        clearError(); // Clear errors on selection change
        setValidationErrors({}); // Clear local validation errors
        if (projectId !== 'new') {
            setProjectName('');
            setProjectDescription('');
            setProjectType('company');
        }
        // Clear context selection immediately - it will be set on 'Next' -> handleLoadAndProceed
        selectCsrdProject(null);
    };

    const handleNext = async () => {
        clearError(); // Clear errors before proceeding

        if (!selectedProjectId) {
            setShowWarningModal(true);
            return;
        }

        if (selectedProjectId === 'new') {
            await handleCreateAndProceed();
        } else {
            await handleLoadAndProceed(selectedProjectId);
        }
    };

    // --- Render Logic ---

    // Helper to display project type string nicely
    const getDisplayProjectType = (type: 'COMPANY' | 'COMPANY_GROUP' | undefined): string => {
        if (type === 'COMPANY_GROUP') return 'Company Group';
        if (type === 'COMPANY') return 'Company';
        return 'Unknown';
    };

    return (
        <Container className="project-container mt-4">
            <h1>Start CSRD Reporting</h1>
            <p>Create a new CSRD project or select an existing one.</p>

            {/* Display context errors OR local form error */}
            {contextError && <Alert variant="danger" onClose={clearError} dismissible>{contextError}</Alert>}
            {validationErrors.form && <Alert variant="danger">{validationErrors.form}</Alert>}


            <Row className="project-grid-wrapper">
                {/* --- Create New Project Card --- */}
                <Col md={4} sm={6} xs={12} className="mb-3">
                    <Card
                        className={`project-tile h-100 ${selectedProjectId === 'new' ? 'selected' : ''}`}
                        onClick={() => handleSelectProject('new')}
                    >
                        <Card.Body className="d-flex flex-column">
                            <div className="d-flex align-items-center mb-3">
                                <PlusCircle size={40} className="me-3 flex-shrink-0" />
                                <h4 className="mb-0">Create New CSRD Project</h4>
                            </div>

                            {selectedProjectId === 'new' && (
                                <Form className="mt-3 flex-grow-1" onSubmit={(e) => e.preventDefault()}> {/* Prevent default form submission */}
                                    <Form.Group controlId="csrdProjectName" className="mb-2">
                                        <Form.Label>Project Name</Form.Label>
                                        <Form.Control
                                            required // HTML5 validation
                                            type="text"
                                            value={projectName}
                                            onChange={(e) => setProjectName(e.target.value)}
                                            placeholder="e.g., CSRD Report 2024"
                                            isInvalid={!!validationErrors.projectName}
                                            onClick={(e) => e.stopPropagation()}
                                        />
                                        <Form.Control.Feedback type="invalid">{validationErrors.projectName}</Form.Control.Feedback>
                                    </Form.Group>
                                    <Form.Group controlId="csrdProjectType" className="mb-2">
                                        <Form.Label>Project Type</Form.Label>
                                        <div>
                                            <Form.Check required inline type="radio" id="csrd-company-radio" name="csrdProjectType" value="company" label="Company" checked={projectType === 'company'} onChange={() => setProjectType('company')} onClick={(e) => e.stopPropagation()} />
                                            <Form.Check required inline type="radio" id="csrd-group-radio" name="csrdProjectType" value="companyGroup" label="Company Group" checked={projectType === 'companyGroup'} onChange={() => setProjectType('companyGroup')} onClick={(e) => e.stopPropagation()} />
                                        </div>
                                        {validationErrors.projectType && <div className="text-danger mt-1 small">{validationErrors.projectType}</div>}
                                    </Form.Group>
                                    <Form.Group controlId="csrdProjectDescription" className="mb-2">
                                        <Form.Label>Description</Form.Label>
                                        <Form.Control
                                            required // HTML5 validation
                                            as="textarea"
                                            rows={2}
                                            value={projectDescription}
                                            onChange={(e) => setProjectDescription(e.target.value)}
                                            placeholder="Brief description (e.g., Annual report scope)"
                                            isInvalid={!!validationErrors.projectDescription}
                                            onClick={(e) => e.stopPropagation()}
                                        />
                                        <Form.Control.Feedback type="invalid">{validationErrors.projectDescription}</Form.Control.Feedback>
                                    </Form.Group>
                                </Form>
                            )}
                            {selectedProjectId !== 'new' && <div className="flex-grow-1"></div>}
                            <p className="text-muted small mt-2 mb-0">Click here to start a new report.</p>
                        </Card.Body>
                    </Card>
                </Col>

                {/* --- Existing Projects --- */}
                {loadingProjects ? (
                    <Col xs={12} className="text-center py-5">
                        <Spinner animation="border" role="status">
                            <span className="visually-hidden">Loading projects...</span>
                        </Spinner>
                    </Col>
                ) : userCsrdProjects.length > 0 ? (
                    userCsrdProjects.map((project) => (
                        <Col md={4} sm={6} xs={12} key={project.id} className="mb-3">
                            <Card
                                className={`project-tile h-100 ${selectedProjectId === project.id ? 'selected' : ''}`}
                                onClick={() => handleSelectProject(project.id)}
                            >
                                <Card.Body className="d-flex flex-column">
                                    {/* Header with Icon and Title */}
                                    <div className="d-flex align-items-center mb-3">
                                        {/* Use different icons based on backend type */}
                                        {project.projectType === 'COMPANY' ? (
                                            <img
                                                src={imagesData('company_small')}
                                                alt="Company"
                                                style={{
                                                    width: '80px',
                                                    height: '80px',
                                                    objectFit: 'cover',
                                                }}
                                            />
                                        ) : (
                                            <img
                                                src={imagesData('company_big')}
                                                alt="Company Group"
                                                style={{
                                                    width: '80px',
                                                    height: '80px',
                                                    objectFit: 'cover',
                                                }}
                                            /> // Using Library for Group
                                        )}
                                        <div className="flex-grow-1">
                                            <Card.Title className="h5 mb-1">{project.projectName}</Card.Title>
                                            {/* Display type using helper function */}
                                            <Badge
                                                bg={project.projectType === 'COMPANY' ? 'secondary' : 'info'}
                                                pill
                                                className="text-capitalize" // Nicely formats 'COMPANY' -> 'Company'
                                            >
                                                {getDisplayProjectType(project.projectType).toLowerCase()}
                                            </Badge>
                                        </div>
                                    </div>

                                    {/* Description */}
                                    <Card.Text className="small text-muted flex-grow-1">
                                        {project.projectDescription || 'No description.'}
                                    </Card.Text>

                                    {/* Last Updated Timestamp */}
                                    <p className="small text-muted mb-0 mt-2">
                                        Last updated: {new Date(project.updatedAt).toLocaleDateString()}
                                    </p>
                                    <div className="d-flex justify-content-end">
                                        <i
                                            className="bi bi-files action-icon me-2"
                                            onClick={(e) => {
                                                e.stopPropagation();

                                            }}
                                        ></i>
                                        <i
                                            className="bi bi-trash action-icon"
                                            onClick={(e) => {
                                                e.stopPropagation();

                                            }}
                                        ></i>
                                    </div>
                                    {/* Remove Copy/Delete placeholders if not implemented */}
                                    {/* <div className="mt-2 text-end"> ... buttons ... </div> */}
                                </Card.Body>
                            </Card>
                        </Col>
                    ))
                ) : (
                    // Show message only if not loading and no context error occurred during load
                    <Col xs={12}>
                        {!loadingProjects && !contextError &&
                            <Alert variant="info">No CSRD projects found. Create your first one!</Alert>}
                    </Col>
                )}
            </Row>

            {/* --- Next Button --- */}
            <div className="d-flex justify-content-end mt-4">
                <Button
                    variant="primary"
                    onClick={handleNext}
                    // Disable if loading submit OR if no project is selected
                    disabled={loadingSubmit || !selectedProjectId}
                    size="lg"
                >
                    {loadingSubmit ? (
                        <>
                            <Spinner as="span" animation="border" size="sm" role="status" aria-hidden="true"/>
                            <span className="ms-2">Processing...</span>
                        </>
                    ) : (
                        'Next'
                    )}
                </Button>
            </div>

            {/* --- Warning Modal --- */}
            <Modal show={showWarningModal} onHide={() => setShowWarningModal(false)} centered>
                <Modal.Header closeButton>
                    <Modal.Title>Selection Required</Modal.Title>
                </Modal.Header>
                <Modal.Body>Please create a new project or select an existing CSRD project to continue.</Modal.Body>
                <Modal.Footer>
                    <Button variant="primary" onClick={() => setShowWarningModal(false)}>OK</Button>
                </Modal.Footer>
            </Modal>
        </Container>
    );
};

export default CreateNewProjectCsrd;