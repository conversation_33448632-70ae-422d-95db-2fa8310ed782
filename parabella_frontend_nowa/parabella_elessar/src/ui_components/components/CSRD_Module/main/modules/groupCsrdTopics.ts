// groupCsrdTopics.ts
import { CsrdTopic } from '../../api/csrdApiTypes.tsx';

export type CategoryType = 'General' | 'Environmental' | 'Social' | 'Governance';

export interface GroupedTopicsByCategory {
    General: CsrdTopic[];
    Environmental: CsrdTopic[];
    Social: CsrdTopic[];
    Governance: CsrdTopic[];
}

export function groupTopicsByCategory(topics: CsrdTopic[]): GroupedTopicsByCategory {
    const result: GroupedTopicsByCategory = {
        General: [],
        Environmental: [],
        Social: [],
        Governance: []
    };

    topics.forEach((topic) => {
        // If topic.code = "ESRS_E2", then the "E" suggests "Environmental"
        // If topic.code = "ESRS_S1", then "S" => "Social"
        // If topic.code = "ESRS_G1", => "Governance"
        // Otherwise => "General"
        // But you may need to parse the "ESRS_E2" carefully:
        if (topic.code.startsWith('ESRS_E')) {
            result.Environmental.push(topic);
        } else if (topic.code.startsWith('ESRS_S')) {
            result.Social.push(topic);
        } else if (topic.code.startsWith('ESRS_G')) {
            result.Governance.push(topic);
        } else {
            // e.g. "ESRS2" might be "General" or "Cross-cutting"
            result.General.push(topic);
        }
    });

    return result;
}
