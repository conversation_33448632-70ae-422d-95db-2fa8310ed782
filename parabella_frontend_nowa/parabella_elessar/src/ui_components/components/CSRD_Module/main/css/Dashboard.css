/* src/modules/csrd/css/Dashboard.css */
/* REVAMPED VERSION - Focus on cleaner design, grey tones, modern stats */

/* --- Base & Variables --- */
:root {
    /* Keep primary for potential future use or specific highlights, but avoid overuse */
    --bs-primary-rgb: 13, 110, 253;
    --bs-secondary-rgb: 108, 117, 125; /* Grey */
    --bs-light-rgb: 248, 249, 250;     /* Light Grey BG */
    --bs-dark-rgb: 33, 37, 41;

    --app-bg-main: #f8f9fa; /* Use Bootstrap light */
    --app-bg-sidebar: #ffffff; /* White sidebar for contrast */
    --app-text-primary: #212529;
    --app-text-secondary: #6c757d;
    --app-border-color: #dee2e6;
    --app-border-color-light: #e9ecef;
    --app-card-shadow: 0 2px 4px rgba(0, 0, 0, 0.05); /* Softer shadow */
    --app-card-shadow-lg: 0 5px 15px rgba(0, 0, 0, 0.08);
    --text-gradient: linear-gradient(135deg, var(--bs-secondary), #343a40); /* Grey/Dark gradient */

    /* Custom Grey Scale (Optional, can use BS classes too) */
    --app-grey-100: #f8f9fa;
    --app-grey-200: #e9ecef;
    --app-grey-300: #dee2e6;
    --app-grey-400: #ced4da;
    --app-grey-500: #adb5bd;
    --app-grey-600: #6c757d; /* BS Secondary */
    --app-grey-700: #495057;
    --app-grey-800: #343a40;
    --app-grey-900: #212529; /* BS Dark */
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    color: var(--app-text-primary);
    background-color: var(--app-bg-main);
    font-size: 0.95rem; /* Slightly larger base font */
}

.text-gradient {
    background: var(--text-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-fill-color: transparent;
}

/* --- Layout & Structure --- */
.esg-command-center-layout {
    height: 100vh;
    overflow: hidden;
}

.main-row-wrapper {
    /* Removed fixed height, rely on flex grow */
}

.esg-sidebar {
    background-color: var(--app-bg-sidebar);
    border-right: 1px solid var(--app-border-color-light) !important;
    height: 100vh; /* Ensure sidebar takes full height */
    overflow-y: auto;
}
.main-content-area {
    background-color: var(--app-bg-main);
    height: 100vh; /* Ensure main content takes full height */
    overflow-y: auto; /* Enable scrolling within main content */
}
.main-content-scroll {
    /* This class is applied to the inner scrollable div in main content */
    height: 100%; /* Ensure it tries to take available space */
}


/* --- Sidebar Specifics --- */
.sidebar-heading {
    font-size: 0.7rem;
    font-weight: 600;
    letter-spacing: 0.05em;
    color: var(--app-grey-600);
}

.company-project-sidebar-info strong {
    font-size: 0.9rem; display: block; word-break: break-word;
    color: var(--app-text-primary); font-weight: 500;
}
.company-project-sidebar-info small.label-sidebar { /* Target specific small */
    font-size: 0.65rem; color: var(--app-text-secondary);
    text-transform: uppercase; font-weight: 600; letter-spacing: 0.5px;
}
.project-name-sidebar, .company-name-sidebar {
    max-width: 180px; white-space: nowrap;
    overflow: hidden; text-overflow: ellipsis;
}

.nav-pills-custom .nav-link.category-nav-link {
    font-weight: 500; color: var(--app-grey-700); padding: 0.6rem 1rem; /* Reduced padding */
    border-radius: 0.3rem; margin-bottom: 0.15rem; /* Tighter spacing */
    transition: background-color 0.15s ease, color 0.15s ease;
}
.nav-pills-custom .nav-link.category-nav-link .category-icon {
    margin-right: 0.6rem;
    color: var(--app-grey-600);
    transition: color 0.15s ease;
}

/* Grey theme for hover/active */
.nav-pills-custom .nav-link.category-nav-link:hover {
    background-color: var(--app-grey-100);
    color: var(--app-grey-900);
}
.nav-pills-custom .nav-link.category-nav-link:hover .category-icon {
    color: var(--app-grey-700);
}

.nav-pills-custom .nav-link.category-nav-link.active,
.nav-pills-custom .show > .nav-link.category-nav-link {
    background-color: var(--app-grey-200); /* Light grey active BG */
    color: var(--app-grey-900); /* Dark text on active */
    box-shadow: none; /* Remove shadow */
}
.nav-pills-custom .nav-link.category-nav-link.active .category-icon {
    color: var(--app-grey-800); /* Darker icon on active */
}

.topic-list-group {
    border-left: 2px solid var(--app-grey-200); /* Thinner, lighter border */
    margin-left: 0.5rem; /* Reduced indent */
    padding-left: 0.5rem !important;
    margin-top: 0.15rem !important;
    margin-bottom: 0.3rem !important;
}
.topic-nav-item {
    font-size: 0.85rem; padding: 0.4rem 0.6rem !important; /* Tighter padding */
    border: none !important; background-color: transparent !important;
    color: var(--app-grey-800); cursor: pointer; border-radius: 0.25rem !important;
    margin-bottom: 1px !important;
    transition: background-color 0.15s ease, color 0.15s ease;
}
.topic-nav-item .topic-item-text-wrapper { display: flex; flex-direction: column; overflow: hidden; }
.topic-nav-item .topic-code { font-weight: 500; font-size: 0.8rem;}
.topic-nav-item .topic-label { font-size: 0.7rem; line-height: 1.3; color: var(--app-grey-600); }
.topic-nav-item .icon-inline { vertical-align: -0.1em; } /* Align icons better */


/* Grey theme for topic list active/hover */
.topic-nav-item:hover { background-color: var(--app-grey-100) !important; }
.topic-nav-item.active {
    background-color: var(--app-grey-200) !important; /* Match category active bg */
    color: var(--app-grey-900) !important; font-weight: 500;
}
.topic-nav-item.active .topic-code { color: var(--app-grey-900); }
.topic-nav-item.active .topic-label { color: var(--app-grey-700); }

.topic-nav-item-placeholder .placeholder { opacity: 1; }

/* --- Main Content Area --- */
/* Modern Stat Cards */
.dashboard-stats-row .modern-stat-card {
    background-color: #ffffff;
    border-radius: 0.5rem; /* Standard radius */
    transition: transform 0.2s ease, box-shadow 0.2s ease;
    box-shadow: var(--app-card-shadow);
    border: 1px solid var(--app-border-color-light); /* Subtle border */
}
.dashboard-stats-row .modern-stat-card:hover {
    transform: translateY(-3px);
    box-shadow: var(--app-card-shadow-lg);
}
.dashboard-stats-row .stat-icon-box {
    width: 48px; height: 48px;
    border-radius: 0.375rem; /* Slightly less rounded than circle */
    display: flex; align-items: center; justify-content: center;
}
/* Icon box colors are set via bg-*-subtle and text-*-emphasis classes in component */

.stat-value { font-size: 1.6rem; color: var(--app-text-primary); }
.stat-title { text-transform: uppercase; font-size: 0.7rem; letter-spacing: 0.4px; font-weight: 500; }
.stat-progress-bar { border-radius: 3px; }
.stat-progress-bar .progress-bar { border-radius: 3px; }


/* Topic Summary Card */
.topic-summary-card {
    background-color: #ffffff;
    border: 1px solid var(--app-border-color-light);
    border-left-width: 5px !important; /* Keep left border for category */
    border-radius: 0.5rem;
    box-shadow: var(--app-card-shadow);
}
/* Category border colors */
.topic-border-general { border-left-color: var(--bs-secondary) !important; }
.topic-border-environmental { border-left-color: var(--bs-success) !important; }
.topic-border-social { border-left-color: var(--bs-warning) !important; }
.topic-border-governance { border-left-color: var(--bs-danger) !important; }

.topic-main-title { color: var(--app-grey-800); font-weight: 600; font-size: 1.4rem; }
.topic-summary-card .ai-assist-button {
    font-weight: 500;
    transition: background-color 0.15s ease;
}
.topic-summary-card .ai-assist-button svg { vertical-align: -0.15em; }

.subtopic-grid-title { color: var(--app-grey-800); font-weight: 500; font-size: 1.2rem; }
.subtopic-grid-title svg { vertical-align: -0.15em; }

/* Subtopic Cards */
.subtopic-card-item {
    transition: all 0.2s ease-in-out;
    border: 1px solid var(--app-border-color-light);
    border-radius: 0.5rem; background-color: #fff;
    box-shadow: none; /* Remove default shadow */
}
.subtopic-card-item:hover {
    transform: translateY(-4px);
    box-shadow: var(--app-card-shadow-lg); /* Add shadow on hover only */
    border-color: var(--app-grey-300);
}
.subtopic-id-label { font-size: 0.9rem; color: var(--app-grey-800); }
.subtopic-title-label { font-size: 0.85rem; line-height: 1.4; color: var(--app-grey-700); }
.three-line-clamp {
    display: -webkit-box; -webkit-line-clamp: 3; -webkit-box-orient: vertical;
    overflow: hidden; text-overflow: ellipsis; min-height: calc(1.4em * 3);
}
.subtopic-coverage-badge {
    font-size: 0.7rem; padding: 0.3em 0.6em;
}
.subtopic-coverage-badge .icon-inline { vertical-align: -0.1em; } /* Adjust icon alignment */
.subtopic-progress-bar { border-radius: 3px; }
.subtopic-progress-bar .progress-bar { border-radius: 3px; }

.view-subtopic-btn { font-weight: 500; font-size: 0.8rem; }
.view-subtopic-btn svg { vertical-align: -0.1em; }


/* Placeholder View */
.placeholder-view {
    background-color: #fff; border: 1px dashed var(--app-grey-300);
    min-height: 300px;
}
.placeholder-view .placeholder-icon { opacity: 1; color: var(--app-grey-500); }
.placeholder-view h4 { color: var(--app-grey-700); font-weight: 500; }
.placeholder-view p { color: var(--app-grey-600); }

/* --- Modal Styling --- */
/* Keep existing advanced modal styling from original CSS */
.modal-dialog.modal-extra-wide { max-width: 95%; }
@media (min-width: 1400px) { .modal-dialog.modal-extra-wide { max-width: 1600px; } }

.modal-fullscreen-xl-down .modal-content {
    height: calc(100vh - 2rem);
    display: flex; flex-direction: column; border: none;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1); border-radius: 0.75rem;
    overflow: hidden;
}
.modal-fullscreen-xl-down .modal-body-splitview { flex-grow: 1; min-height: 0; overflow: hidden; padding: 0; }

.modal-header-custom {
    background-color: var(--app-grey-100); /* Light grey header */
    border-bottom: 1px solid var(--app-border-color-light);
    padding: 0.75rem 1.5rem !important;
}
.modal-title-custom { font-size: 1.1rem; font-weight: 600; }

.modal-footer-custom {
    background-color: var(--app-grey-100);
    border-top: 1px solid var(--app-border-color-light);
    padding: 0.75rem 1.5rem !important;
}

/* Modal Split View Panes */
.modal-body-splitview .datapoints-pane {
    background-color: #ffffff; /* White pane for datapoints */
    border-right: 1px solid var(--app-border-color);
    padding: 1.5rem;
    overflow-y: auto; /* Allow scrolling */
}
.modal-body-splitview .ai-insights-pane {
    width: 400px; /* Slightly narrower */
    flex-shrink: 0;
    background-color: var(--app-bg-main); /* Match main app background */
    padding: 1.5rem;
    display: flex;
    flex-direction: column;
    overflow-y: auto; /* Allow scrolling */
}

/* --- AI Insights & Context (Right Pane in Modal) --- */
/* Reuse styling from original CSS, ensure consistency */
.document-context-section { min-height: 150px; flex-shrink: 0; margin-bottom: 1rem; }
.ai-copilot-section { flex-grow: 1; min-height: 0; display: flex; flex-direction: column; }
.ai-insights-list { flex-grow: 1; overflow-y: auto; }

/* Context Chunk Cards - Refined */
.context-chunk-card {
    background-color: #ffffff;
    border: 1px solid var(--app-border-color-light);
    border-radius: 0.375rem;
    transition: box-shadow 0.15s ease;
    margin-bottom: 0.5rem;
}
.context-chunk-card:hover { box-shadow: var(--app-card-shadow); }
.context-chunk-card .card-body { padding: 0.5rem 0.75rem; } /* Smaller padding */
.context-chunk-text { font-size: 0.78rem; line-height: 1.45; }
.chunk-source-info { font-size: 0.7rem; }
.context-score-badge { font-size: 0.65rem; }

/* AI Insight Cards - Refined */
.ai-insight-card-modern {
    border: 1px solid var(--app-border-color-light);
    border-left-width: 4px;
    border-radius: 0.375rem;
    margin-bottom: 0.75rem !important;
    background-color: #fff;
    transition: box-shadow 0.2s ease;
    overflow: hidden;
}
.ai-insight-card-modern:hover { box-shadow: var(--app-card-shadow); }
.ai-insight-card-modern.type-suggestion-border { border-left-color: var(--bs-info); }
.ai-insight-card-modern.type-reference-border { border-left-color: var(--bs-secondary); }
.ai-insight-card-modern.type-benchmark-border { border-left-color: var(--bs-success); }
.ai-insight-card-modern.type-risk-border { border-left-color: var(--bs-danger); }
.ai-insight-card-modern .card-body { padding: 0.7rem 0.9rem !important; }
.insight-title { font-weight: 600 !important; font-size: 0.85rem !important; }
.insight-content { font-size: 0.8rem; line-height: 1.5; color: var(--app-grey-700); }
.insight-action-button { font-size: 0.75rem !important; }

/* --- Scrollbar styling --- */
/* Keep the existing scrollbar styles */
::-webkit-scrollbar { width: 6px; height: 6px; } /* Slimmer */
::-webkit-scrollbar-track { background: var(--app-grey-100); border-radius: 10px; }
::-webkit-scrollbar-thumb { background: var(--app-grey-300); border-radius: 10px; }
::-webkit-scrollbar-thumb:hover { background: var(--app-grey-400); }

/* Adjustments for specific component states */
.form-control:focus, .form-select:focus {
    border-color: var(--bs-secondary); /* Use secondary for focus outline */
    box-shadow: 0 0 0 0.25rem rgba(var(--bs-secondary-rgb), 0.25);
}

/* Coverage Icon in Modal Datapoints (ensure consistency) */
/* --- Modal General Styling --- */
.modal-dialog.modal-extra-wide { max-width: 95%; }
@media (min-width: 1400px) { .modal-dialog.modal-extra-wide { max-width: 1600px; } }

.modal-fullscreen-xl-down .modal-content {
    height: calc(100vh - 2rem); /* Allow margin */
    display: flex; flex-direction: column; border: none;
    box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.175) !important; /* Stronger shadow for modal */
    border-radius: 0.75rem;
    overflow: hidden; /* Prevent content spill */
}

.modal-header-custom {
    background-color: var(--app-grey-100); /* Light grey */
    border-bottom: 1px solid var(--app-border-color-light);
    padding: 0.75rem 1.5rem !important; /* Consistent padding */
}
.modal-title-custom { font-size: 1.1rem; font-weight: 600; color: var(--app-grey-800); }

.modal-footer-custom {
    background-color: var(--app-grey-100);
    border-top: 1px solid var(--app-border-color-light);
    padding: 0.75rem 1.5rem !important;
}

/* --- Modal Split View Panes --- */
.modal-body-splitview {
    flex-grow: 1;
    min-height: 0; /* Allow shrinking */
    overflow: hidden; /* Prevent parent scroll */
}

.datapoints-pane {
    background-color: #ffffff; /* White background for data entry */
    padding: 1.25rem; /* Standard padding */
    /* Enable scrolling within this pane */
    overflow-y: auto;
    height: 100%;
    position: relative; /* Needed if you have absolute elements inside */
}

.ai-insights-pane {
    width: 400px; /* Fixed width */
    flex-shrink: 0;
    background-color: var(--app-bg-main); /* Match dashboard bg */
    padding: 1.25rem;
    height: 100%;
    overflow-y: auto; /* Enable scrolling */
    border-left: 1px solid var(--app-border-color-light);
    display: flex; /* Ensure flex direction works */
    flex-direction: column;
}

/* --- Datapoint Highlighting --- */
/* This assumes DataPointRenderer.tsx adds this class */
.datapoint-focused {
    border: 1px solid #3a9700 !important; /* Highlight border */
    box-shadow: 0 0 0 3px rgba(58, 151, 0, 0.2) !important; /* Subtle outer glow */
    /* Optional: slightly change background */
    /* background-color: #f2f9ec; */
}
/* Add transition to the DataPointRenderer's main element for smoothness */
.data-point-renderer-card { /* Example class name for the renderer's container */
    transition: border-color 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
}


/* --- Document Context Section --- */
.document-context-section {
    /* Styles defined in the component */
}
.document-context-title { color: var(--app-grey-700); }

.context-chunks-list {
    /* Max height set inline */
    scrollbar-width: thin;
    scrollbar-color: var(--app-grey-300) transparent; /* Subtle scrollbar */
}
.context-chunks-list::-webkit-scrollbar { width: 5px; }
.context-chunks-list::-webkit-scrollbar-track { background: transparent; }
.context-chunks-list::-webkit-scrollbar-thumb { background: var(--app-grey-300); border-radius: 10px;}
.context-chunks-list::-webkit-scrollbar-thumb:hover { background: var(--app-grey-400);}

.context-chunk-card {
    background-color: var(--app-grey-100); /* Subtle background */
    border: 1px solid var(--app-border-color-light);
    border-radius: 0.375rem;
}
.context-chunk-card .card-body { padding: 0.6rem 0.8rem; }
.context-chunk-text { color: var(--app-grey-700); font-size: 0.8rem; line-height: 1.45; }
.chunk-source-info { font-size: 0.7rem; color: var(--app-grey-600); }
.context-score-badge { font-size: 0.65rem; padding: 0.2em 0.4em; font-weight: 500; }
.context-action-button {
    padding: 0.1rem 0.4rem !important;
    font-size: 0.75rem;
    color: var(--app-grey-600);
    text-decoration: none;
}
.context-action-button:hover { background-color: var(--app-grey-200); color: var(--app-grey-800); }

.context-empty-state {
    display: flex; flex-direction: column; align-items: center; text-align: center;
    padding: 1rem; background-color: var(--app-grey-100); border-radius: 0.375rem; color: var(--app-grey-600);
    font-size: 0.85rem; border: 1px dashed var(--app-grey-300);
}
.context-empty-state .icon { margin-bottom: 0.5rem; opacity: 1; }
.context-alert { font-size: 0.8rem !important; padding: 0.5rem 0.8rem; }

/* --- AI Co-Pilot Section --- */
.ai-copilot-section {
    /* Styles defined in the component */
}
.ai-insights-title { color: var(--app-grey-800); }
.ai-context-info { font-size: 0.85rem; color: var(--app-grey-600); }

.ai-insights-list {
    /* Styles for scrollbar same as context-chunks-list */
    scrollbar-width: thin;
    scrollbar-color: var(--app-grey-300) transparent;
}
.ai-insights-list::-webkit-scrollbar { width: 5px; }
.ai-insights-list::-webkit-scrollbar-track { background: transparent; }
.ai-insights-list::-webkit-scrollbar-thumb { background: var(--app-grey-300); border-radius: 10px;}
.ai-insights-list::-webkit-scrollbar-thumb:hover { background: var(--app-grey-400);}

/* Modern AI Card Styling */
.ai-insight-card-modern {
    border: 1px solid var(--app-border-color-light);
    border-left-width: 4px;
    border-radius: 0.375rem;
    margin-bottom: 0.75rem !important;
    background-color: #fff;
    transition: box-shadow 0.15s ease;
    overflow: hidden;
}
.ai-insight-card-modern:hover { box-shadow: var(--app-card-shadow); }

/* Border colors based on type class */
.ai-insight-card-modern.type-suggestion-border { border-left-color: var(--bs-info); }
.ai-insight-card-modern.type-reference-border { border-left-color: var(--bs-secondary); }
.ai-insight-card-modern.type-benchmark-border { border-left-color: var(--bs-success); }
.ai-insight-card-modern.type-risk-border { border-left-color: var(--bs-danger); }
.ai-insight-card-modern.type-warning-border { border-left-color: var(--bs-warning); }
.ai-insight-card-modern.type-info-border { border-left-color: var(--bs-primary); } /* Or keep info as blue? */

.ai-insight-card-modern .card-body { padding: 0.7rem 1rem !important; }
.insight-header { margin-bottom: 0.4rem !important; }
.insight-title-wrapper { flex-grow: 1; overflow: hidden; margin-right: 0.5rem; }
.insight-title { font-weight: 600 !important; font-size: 0.85rem !important; margin-bottom: 0 !important; color: var(--app-grey-800); }
.insight-title-wrapper svg { flex-shrink: 0; margin-top: 1px; }
/* Icon colors are set via text-* classes in getInsightIcon */

.insight-score-badge { font-size: 0.65rem; padding: 0.2em 0.45em; font-weight: 500; }
.insight-content { font-size: 0.82rem; line-height: 1.5; color: var(--app-grey-700); white-space: pre-wrap; }
.insight-footer { margin-top: 0.6rem !important; }
.insight-source { color: var(--app-grey-600) !important; font-size: 0.75rem; }
.insight-actions .insight-action-button {
    font-size: 0.78rem !important;
    padding: 0.25rem 0.6rem !important;
    font-weight: 500;
}
.insight-actions .insight-action-button svg { margin-right: 0.25rem; vertical-align: -0.1em; }
.insight-actions .copy-btn { color: var(--app-grey-700); }
.insight-actions .copy-btn:hover { color: var(--app-grey-900); background-color: var(--app-grey-100); }

/* AI Empty/Sub states */
.ai-empty-sub-state {
    text-align: center; padding: 1.5rem; background-color: var(--app-grey-100);
    border-radius: 0.375rem; border: 1px dashed var(--app-grey-300); color: var(--app-grey-600);
    font-size: 0.85rem;
}
.ai-empty-sub-state .icon { margin-bottom: 0.5rem; opacity: 0.7; }

.ai-empty-state {
    display: flex; flex-direction: column; align-items: center; justify-content: center;
    text-align: center; flex-grow: 1; color: var(--app-grey-500); padding: 2rem;
}
.ai-empty-state .icon { opacity: 0.5; }
.ai-empty-state h6 { font-weight: 500; color: var(--app-grey-600); }
.ai-empty-state p { font-size: 0.9rem; }
/* Define colors for AI badges and borders */
/* src/modules/csrd/css/Dashboard.css */

/* ... your existing styles ... */

:root {
    --ai-doc-color: #198754; /* Bootstrap success text color */
    --ai-doc-bg: #d1e7dd;  /* Bootstrap success-subtle bg color */
    --ai-doc-border: #a3cfbb; /* Bootstrap success-subtle border color */

    --ai-draft-color: #fd7e14; /* Orange, as before */
    --ai-draft-bg: #fff3cd; /* Light yellow/orange */
    --ai-draft-border: #ffc107; /* Bootstrap warning color for border consistency */
}

/* Badge styling */
.badge.bg-ai-doc {
    background-color: var(--ai-doc-bg) !important;
    color: var(--ai-doc-color) !important;
    /* border: 1px solid var(--ai-doc-border); /* Optional: if you want a distinct border on the badge itself */
}

.badge.bg-ai-draft {
    background-color: var(--ai-draft-bg) !important;
    color: var(--ai-draft-color) !important;
    /* border: 1px solid var(--ai-draft-border); /* Optional */
}

.adv-ai-source-badge {
    font-size: 0.7em !important;
    padding: 0.25em 0.5em !important;
    vertical-align: middle; /* Helps with alignment in InputGroup */
    margin-left: 0.25rem; /* Space from previous element in InputGroup */
    margin-right: 0.25rem; /* Space before next element */
    font-weight: 500;
    line-height: normal; /* Ensure text inside badge is not overly spaced */
    display: inline-flex; /* To help center icon and text */
    align-items: center; /* Vertically center icon and text within badge */
}
.adv-ai-source-badge .lucide {
    margin-right: 0.2em; /* Space between icon and text in badge */
}

/* Specific styling for badges in numeric input groups for better centering */
.adv-input-group .adv-ai-source-badge.adv-ai-source-badge-numeric {
    /* Bootstrap's InputGroup uses flex. We can leverage this. */
    /* vertical-align might not be as effective here.
       If it's still off, you might need to wrap the badge in a div
       and apply flex alignment to that wrapper if InputGroup doesn't suffice.
       For now, relying on global vertical-align and InputGroup's flex.
    */
}


.adv-textarea-wrapper textarea.adv-form-control, /* Target TextareaAutosize specifically */
.adv-textarea-wrapper .form-control[as="textarea"] /* Target Bootstrap Form.Control as textarea if not using library */
{
    width: 100% !important; /* Crucial: Make textarea itself take full width of its wrapper */
    min-height: calc(1.5em + 0.75rem + 2px);
    padding: 0.375rem 0.75rem;
    font-size: 1rem;
    font-weight: 400;
    line-height: 1.5;
    color: var(--bs-body-color);
    background-color: var(--bs-body-bg);
    background-clip: padding-box;
    border: 1px solid var(--bs-border-color);
    appearance: none;
    border-radius: var(--bs-border-radius);
    transition: border-color .15s ease-in-out,box-shadow .15s ease-in-out;
    resize: none;
    overflow-y: hidden;
    box-sizing: border-box; /* Ensure padding and border are included in the width */
}
.adv-textarea-wrapper textarea.adv-form-control:focus {
    color: var(--bs-body-color);
    background-color: var(--bs-body-bg);
    border-color: #86b7fe;
    outline: 0;
    box-shadow: 0 0 0 0.25rem rgba(13,110,253,.25);
}


.adv-textarea-actions {
    position: absolute;
    bottom: 8px;
    right: 8px;
    display: flex;
    gap: 4px;
    align-items: center;
    opacity: 0.7;
    transition: opacity 0.2s ease-in-out;
}
.adv-textarea-wrapper:hover .adv-textarea-actions,
.adv-textarea-wrapper:focus-within .adv-textarea-actions {
    opacity: 1;
}

.adv-ai-button, .adv-edit-button {
    border-radius: 0.2rem;
    line-height: 1;
    background-color: rgba(var(--bs-light-rgb), 0.5); /* Make them a bit more subtle */
    border: 1px solid var(--bs-gray-300);
}
.adv-ai-button:hover, .adv-edit-button:hover {
    background-color: var(--bs-light);
    border-color: var(--bs-gray-400);
}

.adv-ai-button .lucide, .adv-edit-button .lucide {
    vertical-align: middle;
}

/* Datapoint Card Border Styling */
.adv-datapoint-card.adv-datapoint-border-ai-doc {
    border-left: 4px solid var(--ai-doc-border) !important;
    /* background-color: #f8f9fa; /* Optional: very subtle bg tint */
}

.adv-datapoint-card.adv-datapoint-border-ai-draft {
    border-left: 4px solid var(--ai-draft-border) !important;
    /* background-color: #fff8e1; /* Optional: very subtle bg tint */
}
/* Ensure scrollbar styling is present */
/* (Keep scrollbar styles from previous CSS) */
::-webkit-scrollbar { width: 6px; height: 6px; }
::-webkit-scrollbar-track { background: var(--app-grey-100); border-radius: 10px; }
::-webkit-scrollbar-thumb { background: var(--app-grey-300); border-radius: 10px; }
::-webkit-scrollbar-thumb:hover { background: var(--app-grey-400); }