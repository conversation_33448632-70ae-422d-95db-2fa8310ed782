/* ./css/AiUploadPage.css */

/* --- General <PERSON> Styles --- */
body { /* Apply to body or a top-level wrapper if this CSS is scoped */
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", <PERSON><PERSON>, sans-serif;
    background-color: #f8f9fc; /* Lighter, modern background, slightly blueish-gray */
    color: #343a40; /* Default text color */
}

.ai-upload-page {
    min-height: 100vh;
    padding-top: 2rem;
    padding-bottom: 2rem;
}

.ai-upload-page h2, .ai-upload-page h4, .ai-upload-page h5, .ai-upload-page h6 {
    color: #1f2937; /* Darker heading color for contrast */
    font-weight: 600; /* Semibold for headings */
}
.ai-upload-page h2 {
    font-size: 1.875rem; /* <PERSON><PERSON><PERSON>'s text-3xl */
    margin-bottom: 0.75rem;
}
.ai-upload-page h4 { /* For "Upload History" */
    font-size: 1.25rem; /* <PERSON><PERSON><PERSON>'s text-xl */
    margin-bottom: 1.25rem; /* More space below section titles */
    color: #374151; /* Slightly softer than h2 */
}
.ai-upload-page p, .ai-upload-page .text-muted {
    color: #6b7280; /* Softer muted text, good for descriptions */
    line-height: 1.6;
}
.ai-upload-page p.small {
    font-size: 0.875rem; /* Tailwind's text-sm */
}


/* --- Dropzone Enhancements --- */
.dropzone-area.modern-dropzone {
    border-radius: 0.75rem; /* Softer radius */
    border: 2px dashed #d1d5db; /* Default border color (Tailwind gray-300) */
    background-color: #ffffff;
    padding: 2.5rem; /* More padding for a spacious feel */
    transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1); /* Smoother transition */
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.03), 0 1px 1px 0 rgba(0, 0, 0, 0.02); /* Very subtle shadow */
}

.dropzone-area.modern-dropzone:hover,
.dropzone-area.modern-dropzone.active { /* Active implies dragging over */
    border-color: #132b40; /* Primary blue (Tailwind blue-500) */
    background-color: #eff6ff; /* Lighter blue background (Tailwind blue-50) */
    transform: translateY(-2px); /* Slight lift effect */
    box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.05), 0 2px 4px 0 rgba(0, 0, 0, 0.03); /* Enhanced shadow on hover/active */
}

.dropzone-area.modern-dropzone.accept {
    border-color: #10b981; /* Green (Tailwind green-500) */
    background-color: #f0fdf4; /* Lighter green (Tailwind green-50) */
}
.dropzone-area.modern-dropzone.accept .dropzone-icon-bg {
    background-color: rgba(16, 185, 129, 0.1) !important;
}
.dropzone-area.modern-dropzone.accept .dropzone-icon-bg svg {
    color: #10b981 !important;
}

.dropzone-area.modern-dropzone.reject {
    border-color: #ef4444; /* Red (Tailwind red-500) */
    background-color: #fef2f2; /* Lighter red (Tailwind red-50) */
}
.dropzone-area.modern-dropzone.reject .dropzone-icon-bg {
    background-color: rgba(239, 68, 68, 0.1) !important;
}
.dropzone-area.modern-dropzone.reject .dropzone-icon-bg svg {
    color: #ef4444 !important;
}

.dropzone-area.modern-dropzone.disabled-dropzone,
.dropzone-area.modern-dropzone.disabled-dropzone:hover {
    border-color: #e5e7eb; /* Tailwind gray-200 */
    background-color: #f3f4f6; /* Tailwind gray-100 */
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}
.dropzone-area.modern-dropzone.disabled-dropzone .dropzone-icon-bg svg {
    color: #9ca3af !important; /* Muted icon for disabled state (Tailwind gray-400) */
}

.dropzone-icon-bg {
    width: 64px; /* Slightly smaller for a sleeker look */
    height: 64px;
    background: rgba(59, 130, 246, 0.08); /* Primary blue tint, slightly less opaque */
    border-radius: 50%;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1.25rem; /* More space below icon */
    transition: background-color 0.25s ease, transform 0.25s ease;
}
.dropzone-icon-bg svg {
    color: #132b40; /* Primary blue */
    width: 32px; /* Adjusted icon size */
    height: 32px;
    stroke-width: 1.5; /* If applicable to your icons */
}

.dropzone-area.modern-dropzone:hover .dropzone-icon-bg {
    transform: scale(1.03); /* Subtle scale on hover */
}

.dropzone-area.modern-dropzone h5 {
    font-size: 1.125rem; /* Tailwind text-lg */
    color: #1f2937; /* Darker text for prominence */
    font-weight: 500; /* Medium weight */
    margin-bottom: 0.375rem;
}
.dropzone-area.modern-dropzone p.text-muted { /* "or click to browse" */
    font-size: 0.9rem;
    color: #4b5563; /* Slightly darker than default muted for better readability here */
}
.dropzone-area.modern-dropzone p.small { /* "(Supports PDF only)" */
    font-size: 0.8rem;
    margin-top: 0.75rem; /* More space above this note */
}

/* --- Uploads Table / Card Enhancements --- */
.card.shadow-sm { /* General card styling for table container and "no files" card */
    border-radius: 0.75rem;
    border: 1px solid #e5e7eb; /* Lighter border (Tailwind gray-200) */
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.04), 0 1px 2px -1px rgba(0, 0, 0, 0.04) !important; /* Refined subtle shadow */
    background-color: #ffffff;
}
/* "No files uploaded" card specific styling */
.card .card-body.text-center.text-muted {
    padding-top: 3rem;
    padding-bottom: 3rem;
}
.card .card-body.text-center.text-muted p {
    font-size: 0.9rem;
}
.card .card-body.text-center.text-muted .opacity-50 {
    color: #9ca3af; /* Tailwind gray-400 */
}


.uploads-table thead th {
    font-weight: 500; /* Medium weight */
    color: #4b5563; /* Darker gray, less muted (Tailwind gray-600) */
    text-transform: none; /* Normal case for a softer look */
    font-size: 0.8rem; /* Slightly smaller, more modern table headers */
    letter-spacing: 0.025em; /* Subtle letter spacing */
    border-bottom: 1px solid #e5e7eb; /* Thinner, lighter bottom border */
    background-color: #f9fafb; /* Very light gray, almost white (Tailwind gray-50) */
    padding: 0.875rem 1.25rem; /* Adjust padding */
}
.uploads-table thead th:first-child { border-top-left-radius: 0.6rem; } /* Match card radius */
.uploads-table thead th:last-child { border-top-right-radius: 0.6rem; }


.uploads-table tbody tr {
    border-bottom: 1px solid #f3f4f6; /* Lighter row separator (Tailwind gray-100) */
    transition: background-color 0.15s ease-in-out;
}
.uploads-table tbody tr:last-child {
    border-bottom: none;
}

.uploads-table tbody tr:hover {
    background-color: #f9fafb; /* Subtle hover (Tailwind gray-50) */
}

.uploads-table td {
    vertical-align: middle;
    padding: 1rem 1.25rem; /* Consistent padding with header */
    font-size: 0.875rem; /* Tailwind text-sm */
    color: #374151; /* Tailwind gray-700 */
    border-top: none;
}
.uploads-table td .fw-medium { /* File name */
    font-weight: 500 !important;
    color: #111827; /* Tailwind gray-900 */
}
.uploads-table td .text-muted { /* FileIcon, Uploaded time */
    color: #6b7280 !important; /* Tailwind gray-500 */
}
.uploads-table td .font-monospace { /* Size, Type badge */
    font-size: 0.8rem;
}

/* ESG Category Badges in Table */
.esg-badge {
    padding: 0.3rem 0.6rem !important;
    font-size: 0.7rem !important;
    font-weight: 500;
    border-radius: 1rem !important; /* Fully rounded pill shape */
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
    line-height: 1; /* Ensure consistent height */
}
.esg-badge svg {
    width: 12px; height: 12px;
    margin-right: 0 !important;
}

/* Action Icons in Table */
.action-icon-btn {
    color: #6b7280; /* Muted gray for icons (Tailwind gray-500) */
    padding: 0.375rem; /* Slightly larger clickable area */
    border-radius: 50%;
    transition: color 0.2s ease, background-color 0.2s ease;
    line-height: 1; /* Prevent layout shifts */
}
.action-icon-btn svg { display: block; } /* Helps with alignment */

.action-icon-btn:hover {
    color: #1f2937; /* Darker on hover (Tailwind gray-800) */
    background-color: #e5e7eb; /* Light gray circular background (Tailwind gray-200) */
}
.action-icon-btn.text-primary:hover { color: #132b40 !important; background-color: #132b40 !important; } /* Tailwind blue-600, blue-100 */
.action-icon-btn.text-warning:hover { color: #d97706 !important; background-color: #fef3c7 !important; } /* Tailwind amber-600, amber-100 */
.action-icon-btn.text-danger:hover { color: #dc2626 !important; background-color: #fee2e2 !important; } /* Tailwind red-600, red-100 */

.action-icon-btn:disabled,
.action-icon-btn[disabled],
.action-icon-btn[style*="cursor: not-allowed"] /* Catch inline disabled styles */
{
    color: #d1d5db !important; /* Tailwind gray-300 */
    background-color: transparent !important;
    opacity: 0.6 !important;
}

/* Status Icons in Table */
.uploads-table td .d-inline-block > .text-success, /* CheckCircle */
.uploads-table td .d-inline-block > .text-danger,  /* XCircle */
.uploads-table td .d-inline-block > .text-muted    /* FileIcon (pending) */
{
    width: 20px; height: 20px;
}
.uploads-table td .spinner-border { /* Loader, Uploading spinner */
    width: 18px !important; height: 18px !important;
    border-width: 0.18em !important;
}
.uploads-table td .text-warning.spinning { /* Loader icon color */
    color: #f59e0b !important; /* Tailwind amber-500 */
}


/* --- Modal Enhancements --- */
.modal-content {
    border-radius: 0.75rem;
    border: none;
    box-shadow: 0 20px 25px -5px rgba(0,0,0,0.1), 0 8px 10px -6px rgba(0,0,0,0.1); /* Softer, more spread shadow */
    overflow: hidden; /* Ensure children conform to border-radius */
}
.modal-header {
    background-color: #f9fafb; /* Tailwind gray-50 */
    border-bottom: 1px solid #e5e7eb; /* Tailwind gray-200 */
    padding: 1rem 1.5rem;
}
.modal-header .modal-title {
    font-size: 1.25rem; /* Tailwind text-xl */
    font-weight: 600;
    color: #111827; /* Tailwind gray-900 */
}
.modal-header .modal-title .fw-normal {
    font-weight: 400 !important;
    font-size: 1rem;
    color: #4b5563; /* Tailwind gray-600 */
}
.modal-body {
    padding: 1.5rem;
    background-color: #f8f9fc; /* Match page background or slightly different for depth */
}
.modal-footer {
    background-color: #f9fafb; /* Tailwind gray-50 */
    border-top: 1px solid #e5e7eb; /* Tailwind gray-200 */
    padding: 1rem 1.5rem;
}
.modal-footer .btn-outline-secondary {
    border-color: #d1d5db; /* Tailwind gray-300 */
    color: #374151; /* Tailwind gray-700 */
}
.modal-footer .btn-outline-secondary:hover {
    background-color: #f3f4f6; /* Tailwind gray-100 */
    border-color: #d1d5db;
    color: #1f2937; /* Tailwind gray-800 */
}


/* Cards within Modal for Data Points */
.modern-card { /* Category card in modal */
    border-radius: 0.5rem;
    border: 1px solid #e5e7eb; /* Tailwind gray-200 */
    box-shadow: 0 1px 2px 0 rgba(0,0,0,0.03); /* Very subtle shadow */
    background-color: #ffffff; /* Ensure card is white */
    overflow: hidden; /* For border-start */
}
.modern-card .card-header {
    background-color: #f9fafb; /* Tailwind gray-50 */
    border-bottom: 1px solid #e5e7eb; /* Tailwind gray-200 */
    padding: 0.75rem 1.25rem;
}
.modern-card .card-header h6 {
    font-size: 0.95rem;
    font-weight: 600;
    color: #1f2937; /* Tailwind gray-800 */
}
.modern-card .card-header .badge { /* Count badge */
    font-size: 0.75rem;
    background-color: #e5e7eb !important; /* Tailwind gray-200 */
    color: #374151 !important; /* Tailwind gray-700 */
}

.data-point-list .list-group-item { /* Individual data point row */
    background-color: #ffffff;
    border-bottom: 1px solid #f3f4f6; /* Tailwind gray-100, very light separator */
    padding: 1.25rem; /* More vertical space */
}
.data-point-list .list-group-item:last-child { border-bottom: none; }

.data-point-list .list-group-item:hover {
    background-color: #f9fafb; /* Subtle hover (Tailwind gray-50) */
}

.data-point-list .list-group-item .fw-medium { /* Metric name */
    color: #111827; /* Tailwind gray-900 */
    font-weight: 500 !important;
    font-size: 0.9rem;
}
.data-point-list .data-value { /* Actual value */
    font-size: 1.375rem !important; /* Tailwind text-xl or 2xl */
    color: #1f2937; /* Tailwind gray-800 */
    font-weight: 600;
    line-height: 1.2;
}
.data-point-list .data-value + .text-muted { /* Unit */
    font-size: 0.8rem;
    font-weight: 400;
    color: #6b7280 !important; /* Tailwind gray-500 */
    position: relative;
    top: -0.1em; /* Align better with large value */
}

.data-point-list .small .text-muted, .data-point-list .small { /* Labels like "Disclosure Ref." */
    color: #4b5563 !important; /* Tailwind gray-600 */
    font-size: 0.75rem;
    font-weight: 500; /* Make labels slightly bolder */
}
.data-point-list .text-dark { /* Values for Disclosure, Source ID */
    color: #374151 !important; /* Tailwind gray-700 */
    font-size: 0.8rem;
}
.data-point-list .badge.font-monospace { /* Source ID value badge */
    padding: 0.25em 0.5em;
    font-size: 0.75rem;
}

/* Icons in Modal List */
.data-point-list .list-group-item svg.me-2 { /* Metric icon, label icons */
    color: #9ca3af; /* Tailwind gray-400 */
    width: 16px; height: 16px; /* Consistent icon size */
}
/* Override specific icon colors if needed, e.g., for metric type */
.data-point-list .list-group-item .d-flex > svg.text-primary { color: #132b40 !important; }
.data-point-list .list-group-item .d-flex > svg.text-success { color: #10b981 !important; }
.data-point-list .list-group-item .d-flex > svg.text-warning { color: #f59e0b !important; }


/* Badges in Modal (Confidence, Chunk, etc.) */
.modal-body .badge[class*="-subtle"] { /* Target Bootstrap's subtle badges */
    border: 1px solid transparent; /* Ensure consistent structure */
    font-weight: 500;
    font-size: 0.7rem;
    padding: 0.3em 0.6em;
}
.modal-body .badge[class*="bg-success-subtle"] { border-color: currentColor; } /* Example for success */
.modal-body .badge[class*="bg-warning-subtle"] { border-color: currentColor; } /* Example for warning */
.modal-body .badge[class*="bg-info-subtle"]   { border-color: currentColor; } /* Example for info */


/* --- Tooltips --- */
.tooltip {
    z-index: 1090 !important; /* Ensure visibility above modal */
    font-size: 0.8rem;
}
.tooltip-inner {
    background-color: #1f2937; /* Dark tooltip (Tailwind gray-800) */
    color: #f9fafb; /* Light text (Tailwind gray-50) */
    padding: 0.4rem 0.8rem;
    border-radius: 0.25rem; /* Tailwind rounded-sm */
    box-shadow: 0 2px 4px rgba(0,0,0,0.15);
}
.tooltip.bs-tooltip-top .tooltip-arrow::before { border-top-color: #1f2937; }
.tooltip.bs-tooltip-bottom .tooltip-arrow::before { border-bottom-color: #1f2937; }
.tooltip.bs-tooltip-start .tooltip-arrow::before { border-left-color: #1f2937; }
.tooltip.bs-tooltip-end .tooltip-arrow::before { border-right-color: #1f2937; }


/* --- General UI element consistency --- */
/* ProgressBar in Table */
.progress { /* Container for progress bar */
    height: 0.6rem !important; /* Slightly thinner, more modern */
    border-radius: 0.3rem;
    background-color: #e5e7eb; /* Tailwind gray-200 */
}
.progress-bar {
    border-radius: 0.3rem;
}
.progress-bar.bg-info { background-color: #132b40 !important; } /* Primary blue for info */
.progress-bar.bg-warning { background-color: #f59e0b !important; } /* Amber for warning */


/* Alert component in page (General errors, etc.) */
.alert {
    border-radius: 0.5rem;
    font-size: 0.9rem;
    padding: 0.8rem 1.25rem;
    border-width: 1px;
    border-style: solid;
}
.alert-danger {
    background-color: #fef2f2; /* Tailwind red-50 */
    border-color: #fecaca; /* Tailwind red-200 */
    color: #991b1b; /* Tailwind red-800 */
}
.alert-danger .alert-link { color: #7f1d1d; } /* Tailwind red-900 */

.alert-warning { /* For "Please select project" */
    background-color: #fffbeb; /* Tailwind amber-50 */
    border-color: #fde68a; /* Tailwind amber-200 */
    color: #92400e; /* Tailwind amber-800 */
}
.alert-warning strong { color: #78350f; } /* Tailwind amber-900 */

.alert-info { /* For "No files uploaded" card's text info */
    background-color: #eff6ff; /* Tailwind blue-50 */
    border-color: #bfdbfe; /* Tailwind blue-200 */
    color: #132b40; /* Tailwind blue-800 */
}
.alert-info svg { color: #132b40; } /* Tailwind blue-600 */

/* Fix for icons if they have flex-shrink, e.g. FileIcon in table */
.uploads-table td .flex-shrink-0 {
    margin-right: 0.5rem; /* Ensure space */
    position: relative;
    top: 1px; /* Fine-tune vertical alignment */
}

/* CSRD Coverage Sidebar - placeholder for consistency */
.csrd-coverage-sidebar .card { /* If this component also uses cards */
    border-radius: 0.75rem;
    border: 1px solid #e5e7eb;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.04), 0 1px 2px -1px rgba(0, 0, 0, 0.04) !important;
    background-color: #ffffff;
}

/* Existing animations (spin, pulse) - kept from original, slightly adjusted pulse color */
@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
.spinning {
    animation: spin 1.2s linear infinite; /* Slightly faster spin */
}

@keyframes pulse {
    0% { transform: scale(1); box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.3); } /* Primary blue with adjusted alpha */
    70% { transform: scale(1.05); box-shadow: 0 0 0 12px rgba(59, 130, 246, 0); }
    100% { transform: scale(1); box-shadow: 0 0 0 0 rgba(59, 130, 246, 0); }
}

.dropzone-icon-bg.pulsing {
    animation: pulse 1.8s infinite cubic-bezier(0.4, 0, 0.2, 1); /* Smoother pulse */
}


/* Ensure this selector is used for the wide modal */
.modal-dialog.wide-modal {
    max-width: 95vw !important; /* Make it almost full viewport width */
    width: 95vw !important;     /* Ensure width is also set */
}

/* Keep these styles for the panes as they were: */
.pdf-pane {
    height: 80vh; /* fixed viewport height for the PDF preview pane */
    background: #e9ecef; /* A light background for the PDF pane */
    position: relative;
}

.pdf-pane iframe {
    width: 80%;
    height: 100%;
    border: none;
}

.data-pane {
    height: 80vh; /* fixed viewport height for the data pane */
    /* overflow-y: auto; is set inline on the component */
}

.pdf-pane.border-end {
    border-right: 1px solid #dee2e6 !important;
}

.modal-body.p-0 {
    padding: 0 !important;
}