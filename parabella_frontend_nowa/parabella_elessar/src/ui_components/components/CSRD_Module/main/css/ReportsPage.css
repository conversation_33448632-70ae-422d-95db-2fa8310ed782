/* Add to CsrsLayout.css or a new ReportsPage.css linked in ReportsPage.tsx */

/* --- Report List Item Enhancements (ReportsPage.tsx) --- */
.modern-card.report-list-item {
    background-color: var(--rdd-card-bg, #ffffff); /* Use variable from ReportDetailDisplay or default */
    border: 1px solid var(--rdd-border-color, #E5E7EB);
    border-radius: 8px;
    box-shadow: var(--rdd-shadow, 0 1px 3px 0 rgba(0, 0, 0, 0.07), 0 1px 2px -1px rgba(0, 0, 0, 0.05));
    transition: box-shadow 0.2s ease-in-out, border-color 0.2s ease-in-out;
}

.modern-card.report-list-item:hover {
    box-shadow: var(--rdd-shadow-md, 0 4px 6px -1px rgba(0, 0, 0, 0.07), 0 2px 4px -2px rgba(0, 0, 0, 0.05));
    border-color: var(--rdd-border-hover-color, #D1D5DB);
}

.report-item-title {
    font-weight: 600;
    color: var(--rdd-text-primary, #1F2937);
    font-size: 0.95rem;
}

.report-item-job-id {
    font-size: 0.75rem;
    color: var(--rdd-text-muted, #6B7280);
}

.report-item-date small {
    font-size: 0.8rem;
    color: var(--rdd-text-secondary, #4B5563);
}

/* Soft Badges for Report List */
.status-badge {
    padding: 0.3em 0.65em;
    font-size: 0.75rem;
    font-weight: 500;
    border-radius: 6px;
    display: inline-flex;
    align-items: center;
}

.badge.bg-success-soft { background-color: #D1FAE5; color: #065F46 !important; }
.badge.bg-info-soft { background-color: #E0F2FE; color: #075985 !important; }
.badge.bg-danger-soft { background-color: #FEE2E2; color: #991B1B !important; }
.badge.bg-secondary-soft { background-color: #F3F4F6; color: #4B5563 !important; }
/* Default BS badges like bg-light, bg-secondary for UNKNOWN will still work */

.report-item-actions .btn-group .btn {
    padding: 0.3rem 0.6rem; /* Smaller buttons */
}
.report-item-actions .btn-group .btn svg {
    width: 16px;
    height: 16px;
}

.report-detail-expanded {
    margin-left: -1rem; /* Offset slightly if detail view is inside a padded parent */
    margin-right: -1rem;
    animation: fadeInReportDetail 0.3s ease-out;
}

@keyframes fadeInReportDetail {
    from { opacity: 0; transform: translateY(-10px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Reports Page Header */
.reports-page-header h2 { /* Assuming CSRD Reports is H2 */
    color: var(--rdd-text-primary, #1F2937);
    font-weight: 700;
}
.reports-page-header .btn-primary { /* Generate New Report button */
    background-color: var(--rdd-primary, #3B82F6) !important;
    border-color: var(--rdd-primary, #3B82F6) !important;
    box-shadow: var(--rdd-shadow, 0 1px 2px 0 rgba(0,0,0,0.05));
}
.reports-page-header .btn-primary:hover {
    background-color: var(--rdd-primary-darker, #2563EB) !important;
    border-color: var(--rdd-primary-darker, #2563EB) !important;
}