/* src/pages/CsrdCoverageAnalysisPage/CsrdCoverageAnalysisPage.css */

.coverage-analysis-page h2 {
    color: #343a40;
}

.stat-card .card-body {
    padding: 1rem 0.8rem; /* Slightly less padding */
}

.stat-card h4 {
    font-size: 1.75rem; /* Larger number */
    font-weight: 600;
    color: #495057;
}

.stat-card .card-subtitle {
    font-size: 0.8rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.category-card .card-title {
    font-size: 0.95rem;
    font-weight: 600;
}

.category-card .card-text {
    font-size: 0.85rem;
}

.category-card .progress-bar {
    font-size: 0.7rem !important; /* Ensure label fits */
    font-weight: 500;
}

.accordion-button:not(.collapsed) {
    color: var(--bs-primary);
    background-color: var(--bs-primary-bg-subtle);
}
.accordion-button:focus {
    box-shadow: 0 0 0 0.25rem rgba(var(--bs-primary-rgb), 0.15);
}

.coverage-table thead th {
    font-weight: 600;
    font-size: 0.85rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    background-color: #f8f9fa;
    border-bottom-width: 1px;
    color: #495057;
}

.coverage-table td {
    vertical-align: middle;
    font-size: 0.9rem;
}

.standard-badge {
    font-size: 0.75rem;
    padding: 0.3em 0.6em;
    font-weight: 500;
}

.info-icon-button {
    color: #6c757d; /* Muted color */
    transition: color 0.2s ease;
}

.info-icon-button:hover {
    color: var(--bs-primary); /* Highlight on hover */
}

/* Filter bar adjustments */
.card-header .form-select,
.card-header .form-control,
.card-header .input-group-text,
.card-header .btn {
    font-size: 0.85rem;
}

/* Tooltip Styling */
.tooltip-inner {
    max-width: 300px; /* Adjust as needed */
    padding: 0.5rem 0.75rem;
    font-size: 0.85rem;
    background-color: #343a40; /* Darker tooltip */
    border-radius: 0.25rem;
}
.tooltip-inner ul {
    padding-left: 1rem; /* Indent list */
}
.tooltip-inner li {
    margin-bottom: 0.2rem;
}