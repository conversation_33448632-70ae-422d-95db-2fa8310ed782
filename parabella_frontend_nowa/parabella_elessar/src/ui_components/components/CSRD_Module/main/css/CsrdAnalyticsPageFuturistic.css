/* src/modules/csrd/components/css/CsrdAnalyticsPageFuturistic.css */

/* --- Base & Variables --- */
:root {
    --futuristic-bg: #0a0f1a; /* Dark blue/black base */
    --futuristic-card-bg: rgba(20, 28, 48, 0.6); /* Semi-transparent dark blue */
    --futuristic-border-color: rgba(56, 77, 130, 0.4); /* Subtle blue border */
    --futuristic-text-primary: #e0e0ff; /* Light lavender/white */
    --futuristic-text-secondary: #a0a8c0; /* Lighter gray/blue */
    --futuristic-text-muted: #707a9a;
    --futuristic-accent-primary: #00f2ff; /* Electric cyan/blue */
    --futuristic-accent-secondary: #8a2be2; /* Blue-violet */
    --futuristic-success: #00ffaa; /* Neon green */
    --futuristic-warning: #ffdd00; /* Cyber yellow */
    --futuristic-danger: #ff4466; /* Neon pink/red */
    --futuristic-font: 'Inter', 'Segoe UI', <PERSON>o, sans-serif; /* Clean sans-serif */
    --futuristic-border-radius: 12px;
    --futuristic-glow-primary: 0 0 15px rgba(0, 242, 255, 0.3); /* Cyan glow */
    --futuristic-glow-secondary: 0 0 12px rgba(138, 43, 226, 0.3); /* Violet glow */
}

.analytics-page-container-futuristic {
    background-color: var(--futuristic-bg);
    color: var(--futuristic-text-primary);
    font-family: var(--futuristic-font);
    padding: 2rem;
    min-height: 100vh;
}

/* --- Futuristic Overrides & New Styles --- */

/* General Card/Module Style */
.futuristic-module {
    background: var(--futuristic-card-bg);
    border: 1px solid var(--futuristic-border-color);
    border-radius: var(--futuristic-border-radius);
    backdrop-filter: blur(10px); /* Glassmorphism */
    -webkit-backdrop-filter: blur(10px);
    margin-bottom: 1.5rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
    overflow: hidden; /* Important for consistent borders/backgrounds */
}

.futuristic-module:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 25px rgba(0, 0, 0, 0.4), var(--futuristic-glow-secondary);
}

.futuristic-module .card-header,
.futuristic-module .card-footer {
    background-color: rgba(30, 40, 68, 0.5); /* Slightly different shade for headers/footers */
    border-bottom: 1px solid var(--futuristic-border-color);
    color: var(--futuristic-text-primary);
    padding: 0.8rem 1.2rem;
    font-weight: 600;
}
.futuristic-module .card-header {
    border-top-left-radius: var(--futuristic-border-radius);
    border-top-right-radius: var(--futuristic-border-radius);
    border-bottom: 1px solid var(--futuristic-border-color);
}

.futuristic-module .card-footer {
    border-top: 1px solid var(--futuristic-border-color);
    border-bottom-left-radius: var(--futuristic-border-radius);
    border-bottom-right-radius: var(--futuristic-border-radius);
}


.futuristic-module .card-body {
    padding: 1.2rem;
    color: var(--futuristic-text-secondary);
}

.futuristic-module .card-title {
    color: var(--futuristic-text-primary);
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.futuristic-module .card-subtitle,
.futuristic-module .text-muted {
    color: var(--futuristic-text-muted) !important; /* Override bootstrap */
}
.futuristic-module .fs-sm {
    font-size: 0.85rem;
}
.futuristic-module .fs-xs {
    font-size: 0.75rem;
}

/* Header & Search */
.futuristic-header h2 {
    color: var(--futuristic-text-primary);
    font-weight: 700;
    text-shadow: var(--futuristic-glow-primary);
}
.futuristic-search .form-control {
    background-color: rgba(255, 255, 255, 0.05);
    border: 1px solid var(--futuristic-border-color);
    color: var(--futuristic-text-primary);
    border-radius: 8px 0 0 8px;
}
.futuristic-search .form-control::placeholder {
    color: var(--futuristic-text-muted);
}
.futuristic-search .form-control:focus {
    background-color: rgba(255, 255, 255, 0.1);
    border-color: var(--futuristic-accent-primary);
    box-shadow: 0 0 0 0.2rem rgba(0, 242, 255, 0.25);
    color: var(--futuristic-text-primary);
}
.futuristic-search .btn-primary {
    background: linear-gradient(90deg, var(--futuristic-accent-primary), var(--futuristic-accent-secondary));
    border: none;
    color: var(--futuristic-bg);
    font-weight: 600;
    border-radius: 0 8px 8px 0;
    transition: filter 0.2s ease;
}
.futuristic-search .btn-primary:hover {
    filter: brightness(1.2);
    box-shadow: var(--futuristic-glow-primary);
}
.futuristic-search .btn-primary:disabled {
    background: var(--futuristic-text-muted);
    opacity: 0.6;
}


/* Stat Bar / Top Metrics */
.stat-bar {
    display: flex;
    gap: 1rem;
    padding: 1rem;
    background: var(--futuristic-card-bg);
    border: 1px solid var(--futuristic-border-color);
    border-radius: var(--futuristic-border-radius);
    backdrop-filter: blur(10px);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    margin-bottom: 1.5rem;
}
.stat-item {
    flex: 1;
    text-align: center;
    padding: 0.8rem;
    border-radius: 10px;
    background: rgba(0, 0, 0, 0.1);
    transition: background 0.2s ease;
}
.stat-item:hover {
    background: rgba(0, 0, 0, 0.2);
}
.stat-item .icon-wrapper {
    margin-bottom: 0.5rem;
    color: var(--futuristic-accent-primary);
}
.stat-item h5 { /* Score */
    font-size: 1.8rem;
    font-weight: 700;
    color: var(--futuristic-text-primary);
    margin-bottom: 0.2rem;
    line-height: 1.1;
}
.stat-item .stat-label {
    font-size: 0.8rem;
    color: var(--futuristic-text-muted);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}
.stat-item .stat-subtext {
    font-size: 0.75rem;
    color: var(--futuristic-text-muted);
    margin-top: 0.3rem;
}
/* Specific Icon Colors */
.stat-item.overall .icon-wrapper { color: var(--futuristic-accent-primary); }
.stat-item.climate .icon-wrapper { color: var(--futuristic-success); }
.stat-item.social .icon-wrapper { color: var(--futuristic-warning); }
.stat-item.governance .icon-wrapper { color: #6495ED; } /* Cornflower Blue */


/* Chart Placeholders (Style the container) */
.futuristic-chart-placeholder {
    border: 1px dashed var(--futuristic-border-color);
    border-radius: var(--futuristic-border-radius);
    padding: 2rem;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 250px; /* Ensure minimum height */
    background: rgba(0, 0, 0, 0.1);
    color: var(--futuristic-text-muted);
    font-style: italic;
}
.futuristic-chart-placeholder svg {
    margin-bottom: 1rem;
    opacity: 0.5;
}

/* Timeline */
.futuristic-timeline .list-group-item {
    background-color: transparent !important;
    border: none !important;
    padding-left: 2rem !important; /* Space for line/dot */
    position: relative;
}
.futuristic-timeline .list-group-item::before { /* Timeline line */
    content: '';
    position: absolute;
    left: 10px;
    top: 0;
    bottom: 0;
    width: 2px;
    background-color: var(--futuristic-border-color);
}
.futuristic-timeline .list-group-item:first-child::before { top: 15px; }
.futuristic-timeline .list-group-item:last-child::before { bottom: auto; height: 15px; }

.futuristic-timeline .timeline-icon {
    position: absolute;
    left: 0px;
    top: 8px; /* Adjust vertical alignment */
    width: 20px; /* Diameter of the dot */
    height: 20px;
    border-radius: 50%;
    background-color: var(--futuristic-bg); /* Background color */
    border: 2px solid var(--futuristic-accent-primary); /* Dot border */
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1;
}
.futuristic-timeline .timeline-icon svg {
    width: 10px;
    height: 10px;
    color: var(--futuristic-accent-primary);
}
/* Different colors for severity/type */
.futuristic-timeline .timeline-icon.medium,
.futuristic-timeline .timeline-icon.alert {
    border-color: var(--futuristic-warning);
}
.futuristic-timeline .timeline-icon.medium svg,
.futuristic-timeline .timeline-icon.alert svg {
    color: var(--futuristic-warning);
}
.futuristic-timeline .timeline-icon.high {
    border-color: var(--futuristic-danger);
}
.futuristic-timeline .timeline-icon.high svg {
    color: var(--futuristic-danger);
}
.futuristic-timeline .timeline-icon.milestone,
.futuristic-timeline .timeline-icon.goal {
    border-color: var(--futuristic-success);
}
.futuristic-timeline .timeline-icon.milestone svg,
.futuristic-timeline .timeline-icon.goal svg {
    color: var(--futuristic-success);
}

.futuristic-timeline .list-group-item p {
    margin-bottom: 0.1rem;
    color: var(--futuristic-text-secondary);
    font-size: 0.9rem;
}
.futuristic-timeline .list-group-item span.fs-xs {
    color: var(--futuristic-text-muted);
}


/* Progress Bars / Pillar Performance */
.progress.progress-style {
    height: 10px !important; /* Override bootstrap */
    border-radius: 10px;
    background-color: rgba(0, 0, 0, 0.3) !important;
}
.progress-bar {
    border-radius: 10px;
    background: linear-gradient(90deg, var(--futuristic-accent-secondary), var(--futuristic-accent-primary)) !important; /* Default gradient */
}
.progress-bar.bg-success {
    background: linear-gradient(90deg, #00a060, var(--futuristic-success)) !important;
}
.progress-bar.bg-warning {
    background: linear-gradient(90deg, #d0a000, var(--futuristic-warning)) !important;
}
.progress-bar.bg-info {
    background: linear-gradient(90deg, #4060d0, #6495ED) !important; /* Cornflower blue gradient */
}
.progress-bar.bg-secondary {
    background: linear-gradient(90deg, #707a9a, #a0a8c0) !important;
}


/* Table Styling */
.futuristic-table .table {
    background-color: transparent;
    color: var(--futuristic-text-secondary);
    border-color: var(--futuristic-border-color);
    margin-bottom: 0; /* Remove default margin */
}

.futuristic-table thead {
    border-bottom: 2px solid var(--futuristic-accent-primary);
}

.futuristic-table th {
    background-color: transparent !important;
    color: var(--futuristic-text-primary);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-size: 0.8rem;
    border: none !important;
    padding: 0.8rem 1rem;
}

.futuristic-table tbody tr {
    border-bottom: 1px solid var(--futuristic-border-color);
    transition: background-color 0.2s ease;
}

.futuristic-table tbody tr:last-child {
    border-bottom: none;
}

.futuristic-table tbody tr:hover {
    background-color: rgba(255, 255, 255, 0.03);
}

.futuristic-table td {
    border: none !important;
    padding: 0.8rem 1rem;
    vertical-align: middle;
}

/* Badges in Table */
.futuristic-table .badge {
    padding: 0.4em 0.6em;
    font-size: 0.75rem;
    font-weight: 600;
    border-radius: 6px;
}
/* Remove transparent suffix */
.futuristic-table .badge.bg-success-light { background-color: rgba(0, 255, 170, 0.15); color: var(--futuristic-success); }
.futuristic-table .badge.bg-warning-light { background-color: rgba(255, 221, 0, 0.15); color: var(--futuristic-warning); }
.futuristic-table .badge.bg-danger-light { background-color: rgba(255, 68, 102, 0.15); color: var(--futuristic-danger); }
.futuristic-table .badge.bg-info-light { background-color: rgba(100, 149, 237, 0.15); color: #87CEFA; } /* Light Sky Blue */
.futuristic-table .badge.bg-secondary-light { background-color: rgba(160, 168, 192, 0.15); color: var(--futuristic-text-secondary); }
.futuristic-table .badge.bg-light-transparent { background-color: rgba(112, 122, 154, 0.3); color: var(--futuristic-text-secondary); } /* Adjust if needed */

/* Trend Icons */
.futuristic-table .lucide-trending-up { color: var(--futuristic-danger); }
.futuristic-table .lucide-trending-down { color: var(--futuristic-success); }

/* Goals List */
.futuristic-goals .list-group-item {
    background-color: transparent !important;
    border-color: var(--futuristic-border-color) !important;
    padding: 0.6rem 1rem;
}
.futuristic-goals .form-check-input {
    background-color: rgba(255,255,255,0.1);
    border-color: var(--futuristic-border-color);
}
.futuristic-goals .form-check-input:checked {
    background-color: var(--futuristic-accent-primary);
    border-color: var(--futuristic-accent-primary);
}
.futuristic-goals .form-check-input:checked ~ span {
    text-decoration: line-through;
    color: var(--futuristic-text-muted);
}
.futuristic-goals .badge { font-size: 0.7rem; }

/* Alerts */
.alert {
    border-radius: var(--futuristic-border-radius);
    border-width: 1px;
    border-style: solid;
}
.alert-danger {
    background-color: rgba(255, 68, 102, 0.1);
    border-color: var(--futuristic-danger);
    color: var(--futuristic-danger);
}
.alert-danger .alert-heading, .alert-danger svg {
    color: var(--futuristic-danger);
}
.alert-info {
    background-color: rgba(0, 242, 255, 0.1);
    border-color: var(--futuristic-accent-primary);
    color: var(--futuristic-accent-primary);
}
.alert-light { /* For 'no data' messages */
    background-color: rgba(255, 255, 255, 0.05);
    border-color: var(--futuristic-border-color);
    color: var(--futuristic-text-secondary);
}

/* Spinner */
.spinner-border {
    color: var(--futuristic-accent-primary);
}