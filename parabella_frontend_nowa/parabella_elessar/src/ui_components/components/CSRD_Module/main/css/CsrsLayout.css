/* General Layout & Navbar Container */
.csrd-layout-container {
    /* Adjust if you need more specific container styles */
}

.csrd-tab-navbar {
    background-color: #ffffff;
    border-radius: 0.75rem; /* 12px */
    padding: 0.5rem 0.75rem; /* 8px 12px */
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    border: none;
    margin-bottom: 1.5rem !important; /* 24px */
}

.csrd-tab-navbar .container-fluid {
    /* Override Bootstrap default padding if needed, e.g., padding-left: 0; padding-right: 0; */
    /* This helps if the placeholder technique for centering needs exact alignment */
}

/* Home Link Styling (Left Aligned) */
.navbar-nav-home {
    flex-shrink: 0; /* Prevent home button from shrinking */
}
.navbar-nav-home-placeholder {
    flex-shrink: 0; /* Match the non-placeholder */
}


.home-nav-link {
    display: flex;
    align-items: center;
    padding: 0.6rem 1rem !important; /* 9.6px 16px */
    color: #5a6470 !important; /* Subtler text color */
    background-color: transparent !important;
    border-radius: 0.5rem; /* 8px */
    transition: background-color 0.2s ease-in-out, color 0.2s ease-in-out;
    border: 1px solid transparent !important; /* Keeps layout stable on hover */
}

.home-nav-link:hover,
.home-nav-link:focus {
    color: #007bff !important; /* Primary color */
    background-color: #e7f3ff !important; /* Very light blue */
    text-decoration: none;
}

.home-nav-link .step-content { /* step-content is the shared class for icon+text div */
    display: flex;
    align-items: center;
    gap: 0.4rem; /* 6.4px */
}
.home-icon {
    /* Specific styling for home icon if needed */
}
.step-title-home {
    font-weight: 500;
}


/* Centered Main Workflow Tabs */
.csrd-tab-navbar .navbar-collapse {
    /* justify-content: center; /* This is already applied from props on Navbar.Collapse */
}

.nav-tabs-custom {
    border-bottom: none;
    flex-wrap: nowrap;
    overflow-x: auto; /* For smaller screens if tabs overflow */
    align-items: stretch; /* Make nav-items take full height of nav-tabs-custom */
}

/* Individual Step Item & Link */
.step-item {
    margin-right: 0.375rem; /* 6px, reduced spacing a bit */
}
.step-item:last-child {
    margin-right: 0;
}

.step-item .nav-link {
    border: none !important;
    border-radius: 0.625rem; /* 10px, slightly more rounded */
    padding: 0.75rem 1rem; /* 12px 16px, adjust for content vertical balance */
    color: #6c757d; /* Default text color (grey) */
    background-color: #f8f9fa; /* Light grey inactive background */
    transition: background-color 0.2s ease-in-out, color 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
    white-space: nowrap;
    display: flex; /* Needed for complex content inside */
    flex-direction: column; /* Align content vertically if needed, but step-content-main handles it */
    justify-content: center; /* Vertical centering of content in the tab */
    min-height: 20px; /* Example fixed height, adjust as needed */
}

.step-item .nav-link:hover,
.step-item .nav-link:focus {
    color: #495057;
    background-color: #e9ecef; /* Slightly darker hover */
}

/* Active Step Tab */
.step-item .nav-link.active {
    color: #ffffff !important;
    background-color: #132b40 !important; /* Bootstrap primary blue */
    font-weight: 500;
    box-shadow: 0 2px 8px rgba(0, 123, 255, 0.3);
}

/* Content within each main step tab */
.step-content-main {
    display: flex;
    align-items: center;
    gap: 0.75rem; /* 12px, space between number badge and rest */
    width: 100%; /* Ensure it takes full width of nav-link */
}

.step-number-badge {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 1.75rem; /* 28px */
    height: 1.75rem; /* 28px */
    border-radius: 50%;
    background-color: rgba(0,0,0,0.08); /* Default badge background */
    color: #495057; /* Default badge text */
    font-size: 0.875rem; /* 14px */
    font-weight: 600;
    flex-shrink: 0; /* Prevent shrinking */
    transition: background-color 0.2s ease, color 0.2s ease;
}
.step-item .nav-link.active .step-number-badge {
    background-color: rgba(255,255,255,0.25);
    color: #ffffff;
}
.step-item.step-status-completed .nav-link .step-number-badge {
    background-color: #28a745; /* Green for completed */
    color: #ffffff;
}


.step-icon-title-progress {
    display: flex;
    flex-direction: column;
    gap: 0.3rem; /* ~5px between title row and progress bar */
    flex-grow: 1; /* Takes remaining space */
    min-width: 150px; /* Adjust based on your titles, helps maintain tab size */
}
.step-icon-title {
    display: flex;
    align-items: center;
    gap: 0.5rem; /* 8px */
}
.step-icon {
    flex-shrink: 0;
}
.step-title-main {
    font-size: 0.875rem; /* 14px */
    font-weight: 500;
    line-height: 1.2;
    /* color will be inherited from nav-link */
}


/* Modern Progress Bar */
.modern-progress-bar-container {
    width: 100%;
    height: 5px; /* Slimmer */
    background-color: rgba(0,0,0,0.08); /* Track color, very light */
    border-radius: 2.5px;
    overflow: hidden; /* Crucial for border-radius on fill */
}
.step-item .nav-link.active .modern-progress-bar-container {
    background-color: rgba(255,255,255,0.2); /* Lighter track for active tab */
}


.modern-progress-bar-fill {
    height: 100%;
    width: 0%; /* Default to 0% */
    background-color: #132b40; /* Default fill, will be overridden */
    border-radius: 2.5px;
    transition: width 0.4s ease-in-out, background-color 0.4s ease-in-out;
}

.modern-progress-bar-fill.status-pending {
    width: 0%; /* Or a very small % like 5% for visual cue */
    background-color: #adb5bd; /* Grey for pending if you show a bit */
}
.modern-progress-bar-fill.status-active {
    width: 100%; /* For active tab, show full progress bar */
    background-color: #ffffff; /* White fill on blue active tab */
}
.step-item .nav-link:not(.active) .modern-progress-bar-fill.status-active {
    background-color: #132b40; /* Blue fill if tab is conceptually active but not the .active Bootstrap class */
}

.modern-progress-bar-fill.status-completed {
    width: 100%;
    background-color: #28a745; /* Green for completed steps on inactive tabs */
}
.step-item .nav-link.active .modern-progress-bar-fill.status-completed {
    background-color: #ffffff; /* If a completed step somehow became .active, make fill white */
}


/* Modal Styles (from original, ensure they are still relevant) */
.leave-confirm-modal .modal-header {
    border-bottom: none;
}
.leave-confirm-modal .modal-footer {
    border-top: none;
}

/* Responsive adjustments for tabs */
@media (max-width: 991.98px) { /* Below lg breakpoint (Bootstrap) */
    .csrd-tab-navbar .navbar-collapse {
        /* When collapsed, tabs might stack or need specific alignment */
        margin-top: 0.5rem;
    }
    .nav-tabs-custom {
        /* Allow wrapping or ensure horizontal scroll works well */
        justify-content: flex-start; /* Align to start when stacked or scrolling */
    }
    .navbar-nav-home-placeholder {
        display: none !important; /* Hide placeholder on mobile */
    }
    .step-item .nav-link {
        min-height: auto; /* Reset min-height for stacked view */
        padding: 0.6rem 0.8rem;
    }
    .step-content-main {
        gap: 0.5rem;
    }
    .step-icon-title-progress {
        min-width: 0; /* Allow shrinking on mobile */
    }
}