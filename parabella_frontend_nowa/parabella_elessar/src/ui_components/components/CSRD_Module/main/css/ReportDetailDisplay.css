/* src/modules/csrd/components/css/ReportDetailDisplay.css */
:root {
    --rdd-bg: #f9fafb; /* Softer background */
    --rdd-card-bg: #ffffff;
    --rdd-primary: #132b40; /* A modern blue */
    --rdd-primary-light: #EFF6FF;
    --rdd-primary-darker: #132b40;
    --rdd-text-primary: #1F2937; /* Darker main text */
    --rdd-text-secondary: #4B5563;
    --rdd-text-muted: #6B7280;
    --rdd-border-color: #E5E7EB; /* Lighter border */
    --rdd-border-hover-color: #D1D5DB;
    --rdd-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.07), 0 1px 2px -1px rgba(0, 0, 0, 0.05);
    --rdd-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.07), 0 2px 4px -2px rgba(0, 0, 0, 0.05);
    --rdd-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.07), 0 4px 6px -4px rgba(0, 0, 0, 0.05);

    --rdd-severity-high: #EF4444;
    --rdd-severity-medium: #F59E0B;
    --rdd-severity-low: #132b40; /* Using primary for low */
    --rdd-severity-high-bg: #FEE2E2;
    --rdd-severity-medium-bg: #FEF3C7;
    --rdd-severity-low-bg: #DBEAFE;
}

.report-detail-container {
    background-color: var(--rdd-bg);
    box-shadow: var(--rdd-shadow-lg);
    border-radius: 12px; /* More rounded */
    border: 1px solid var(--rdd-border-color);
}

.report-title-text {
    font-size: 1.375rem; /* Slightly larger title */
    font-weight: 600;
    color: var(--rdd-text-primary);
}
.report-job-id-badge {
    font-size: 0.75rem;
    font-weight: 500;
    background-color: var(--rdd-border-color) !important;
    color: var(--rdd-text-secondary) !important;
    padding: 0.3em 0.6em;
}

.report-detail-container hr {
    border-top: 1px solid var(--rdd-border-color);
}

/* Buttons in header */
.report-detail-container .btn-outline-primary {
    color: var(--rdd-primary);
    border-color: var(--rdd-primary);
}
.report-detail-container .btn-outline-primary:hover {
    background-color: var(--rdd-primary-light);
    color: var(--rdd-primary-darker);
    border-color: var(--rdd-primary);
}
.report-detail-container .btn-outline-secondary {
    color: var(--rdd-text-secondary);
    border-color: var(--rdd-border-color);
}
.report-detail-container .btn-outline-secondary:hover {
    background-color: #F3F4F6; /* light gray */
    border-color: var(--rdd-border-hover-color);
}


/* --- Column Layout & Scroll --- */
.disclosure-nav-col-detail,
.report-content-col-detail,
.analysis-findings-col-detail {
    max-height: calc(100vh - 250px); /* Adjust based on actual header/footer */
    overflow-y: auto;
    padding-top: 0.25rem;
}

/* Custom Scrollbar Styling */
.disclosure-nav-col-detail::-webkit-scrollbar,
.report-content-col-detail::-webkit-scrollbar,
.analysis-findings-col-detail::-webkit-scrollbar {
    width: 7px;
}
.disclosure-nav-col-detail::-webkit-scrollbar-thumb,
.report-content-col-detail::-webkit-scrollbar-thumb,
.analysis-findings-col-detail::-webkit-scrollbar-thumb {
    background-color: #A0AEC0; /* Tailwind gray-500 */
    border-radius: 10px;
    border: 2px solid var(--rdd-bg); /* Creates padding around thumb */
}
.disclosure-nav-col-detail::-webkit-scrollbar-track,
.report-content-col-detail::-webkit-scrollbar-track,
.analysis-findings-col-detail::-webkit-scrollbar-track {
    background-color: transparent; /* Or var(--rdd-bg) if border on thumb not enough */
    border-radius: 10px;
}


/* --- Left: Disclosure Navigation --- */
.disclosure-nav-detail {
    position: sticky;
    top: 0.25rem; /* Sticky within its column */
}
.disclosure-nav-detail h6 {
    font-size: 0.9rem;
    font-weight: 600;
    color: var(--rdd-text-primary);
    padding: 0.5rem 0.75rem;
    margin-bottom: 0.25rem;
}
.disclosure-nav-detail .list-group {
    padding-right: 5px; /* space for scrollbar if inner container used */
}
.disclosure-nav-detail .list-group-item {
    padding: 0.65rem 0.85rem;
    font-size: 0.875rem;
    border: 1px solid transparent; /* For smooth transition and layout */
    border-left: 3px solid transparent;
    border-radius: 6px;
    margin-bottom: 4px;
    color: var(--rdd-text-secondary);
    transition: background-color 0.15s ease, border-color 0.15s ease, color 0.15s ease;
}
.disclosure-nav-detail .list-group-item:hover {
    background-color: #F3F4F6; /* Tailwind gray-100 */
    border-left-color: var(--rdd-border-hover-color);
    color: var(--rdd-text-primary);
}
.disclosure-nav-detail .list-group-item.active {
    font-weight: 600;
    background-color: var(--rdd-primary-light);
    border-left-color: var(--rdd-primary);
    color: var(--rdd-primary-darker);
    box-shadow: var(--rdd-shadow);
}
.disclosure-nav-detail .list-group-item.active small {
    color: var(--rdd-primary);
}
.disclosure-nav-detail .list-group-item small {
    font-size: 0.75rem;
    color: var(--rdd-text-muted);
    display: block;
    margin-top: 2px;
}


/* --- Middle: Report Content Chunks --- */
.report-content-col-detail {
    padding-left: 1rem;
    padding-right: 1rem;
}
.disclosure-chunk-detail {
    border: 1px solid var(--rdd-border-color);
    border-radius: 8px;
    box-shadow: var(--rdd-shadow);
    transition: border-left-color 0.3s ease, box-shadow 0.3s ease;
    background-color: var(--rdd-card-bg);
}
.disclosure-chunk-detail.active {
    border-left: 4px solid var(--rdd-primary) !important;
    box-shadow: 0 3px 7px rgba(59, 130, 246, 0.2); /* Using primary color for shadow */
}
.disclosure-chunk-detail .card-header {
    background-color: #F9FAFB; /* Tailwind gray-50 */
    font-size: 0.95rem;
    font-weight: 600;
    color: var(--rdd-text-primary);
    padding: 0.75rem 1rem;
    border-bottom: 1px solid var(--rdd-border-color);
    cursor: pointer;
    border-radius: 8px 8px 0 0; /* Match card rounding */
}
.generated-text-display-detail {
    white-space: pre-wrap;
    word-wrap: break-word;
    font-family: "SFMono-Regular", Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
    font-size: 0.875rem; /* Slightly larger */
    line-height: 1.7;
    background-color: var(--rdd-card-bg);
    padding: 1rem;
    color: var(--rdd-text-secondary);
    border-radius: 0 0 8px 8px;
}


/* --- Right: AI Co-pilot Analysis Panel --- */
.analysis-findings-col-detail {
    background-color: #F3F4F6; /* Tailwind gray-100 for the panel background */
    border-radius: 10px;
    padding: 0; /* Padding will be inside for sticky header */
}

.ai-copilot-sticky-header {
    position: sticky;
    top: 0;
    background-color: #F3F4F6; /* Match column background */
    z-index: 10;
    padding: 0.75rem 1rem;
    border-bottom: 1px solid var(--rdd-border-color);
    border-radius: 10px 10px 0 0; /* Match column rounding */
}

.ai-copilot-header-content {
    display: flex;
    align-items: center;
}

.ai-copilot-header-content .copilot-icon {
    color: var(--rdd-primary);
    margin-right: 0.75rem;
}
.ai-copilot-header-content h6 {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 0;
    color: var(--rdd-text-primary);
    display: flex;
    align-items: center;
}
.ai-copilot-header-content .badge {
    font-size: 0.8rem;
    margin-left: 0.5rem;
    padding: 0.3em 0.6em;
    font-weight: 500;
}
/* Ensure badge has correct color if not using bootstrap defaults for bg */
.ai-copilot-header-content .badge.bg-primary {
    background-color: var(--rdd-primary) !important;
    color: white !important;
}
.ai-copilot-header-content .badge.bg-light {
    background-color: var(--rdd-border-color) !important;
    color: var(--rdd-text-secondary) !important;
}

/* Accordion Styling for AI Panel */
.analysis-findings-col-detail .accordion {
    padding: 0.75rem; /* Add padding around accordions */
}
.analysis-findings-col-detail .accordion-item {
    background-color: var(--rdd-card-bg);
    border: 1px solid var(--rdd-border-color) !important;
    border-radius: 8px !important;
    box-shadow: var(--rdd-shadow);
    overflow: hidden;
}
.analysis-findings-col-detail .accordion-item:not(:last-of-type) {
    margin-bottom: 0.75rem !important;
}


.analysis-findings-col-detail .accordion-button {
    padding: 0.85rem 1.1rem;
    font-size: 0.95rem; /* Clearer text */
    font-weight: 500; /* Medium weight */
    color: var(--rdd-text-primary);
    background-color: var(--rdd-card-bg); /* Cleaner */
    box-shadow: none !important; /* Remove bootstrap shadow */
    border-bottom: 1px solid transparent; /* Separator for open state */
    border-radius: 0; /* Handled by accordion-item */
    width: 100%;
    text-align: left;
    transition: background-color 0.15s ease, border-color 0.15s ease;
}

.analysis-findings-col-detail .accordion-button:hover {
    background-color: #F9FAFB; /* Tailwind gray-50 */
}
.analysis-findings-col-detail .accordion-button:focus {
    box-shadow: none; /* Remove focus ring if not desired, or style custom */
    /* outline: 2px solid var(--rdd-primary-light);
    outline-offset: 2px; */
}

.analysis-findings-col-detail .accordion-button:not(.collapsed) {
    color: var(--rdd-primary-darker);
    background-color: var(--rdd-primary-light);
    border-bottom-color: #BFDBFE; /* Lighter blue border for active */
    font-weight: 600;
}
/* Styling for Bootstrap's default chevron */
.analysis-findings-col-detail .accordion-button::after {
    filter: grayscale(1) brightness(1.2); /* Subdue default chevron */
}
.analysis-findings-col-detail .accordion-button:not(.collapsed)::after {
    filter: none; /* Reset filter */
    /* Bootstrap default SVG is black, so primary color for this might need custom SVG or ::before */
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%232563EB'%3e%3cpath fill-rule='evenodd' d='M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z'/%3e%3c/svg%3e");
}


.accordion-button-content {
    display: flex;
    align-items: center;
    flex-grow: 1;
}
.accordion-button-content .icon {
    margin-right: 0.85rem;
    font-size: 1.1rem;
    flex-shrink: 0;
    /* Colors defined in class directly for clarity, or could use variables */
}
.accordion-button-content .icon.text-danger { color: var(--rdd-severity-high) !important; }
.accordion-button-content .icon.text-info { color: var(--rdd-severity-low) !important; } /* Using low for info */
.accordion-button-content .icon.text-primary { color: var(--rdd-primary) !important; }

.accordion-button-content .title {
    font-weight: inherit; /* Inherit from .accordion-button */
}

.accordion-button-badges {
    display: flex;
    align-items: center;
    margin-left: auto;
    padding-left: 0.75rem;
}
.accordion-button-badges .badge {
    font-size: 0.75rem;
    font-weight: 600;
    min-width: 24px; /* Ensure badge width */
    text-align: center;
}
.accordion-button-badges .badge.bg-danger { background-color: var(--rdd-severity-high) !important; color: white !important; }
.accordion-button-badges .badge.bg-info { background-color: var(--rdd-severity-low-bg) !important; color: var(--rdd-severity-low) !important; }
.accordion-button-badges .badge.bg-primary { background-color: var(--rdd-primary-light) !important; color: var(--rdd-primary) !important; }


.analysis-findings-col-detail .accordion-body {
    padding: 0;
    font-size: 0.85rem;
    background-color: var(--rdd-card-bg);
}
.analysis-findings-col-detail .accordion-body .list-group-item {
    padding: 0.8rem 1.1rem;
    border-top: 1px solid var(--rdd-border-color);
    border-radius: 0;
    background-color: transparent;
}
.analysis-findings-col-detail .accordion-body .list-group-item:first-child {
    border-top: none;
}

.analysis-findings-col-detail .accordion-body .finding-title {
    font-weight: 600; /* Bolder finding title */
    font-size: 0.875rem;
    margin-bottom: 0.35rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    color: var(--rdd-text-primary);
}
.analysis-findings-col-detail .accordion-body .finding-description {
    color: var(--rdd-text-secondary);
    margin-bottom: 0.3rem;
    line-height: 1.6;
}
.analysis-findings-col-detail .accordion-body .finding-quote {
    font-style: italic;
    color: var(--rdd-text-muted);
    font-size: 0.8rem;
    padding: 0.3rem 0.6rem;
    border-left: 3px solid var(--rdd-border-color);
    margin-top: 0.4rem;
    background-color: #F9FAFB; /* Tailwind gray-50 */
    display: block;
    border-radius: 0 4px 4px 0;
}
.analysis-findings-col-detail .accordion-body .finding-meta {
    font-size: 0.75rem;
    color: var(--rdd-text-muted);
}
.analysis-findings-col-detail .accordion-body .no-findings-message {
    padding: 1.5rem 1.1rem;
    text-align: center;
    color: var(--rdd-text-muted);
    font-style: italic;
    font-size: 0.9rem;
}

/* Custom Severity Badges (for use in finding-title) */
.severity-badge {
    font-size: 0.75rem;
    font-weight: 600;
    padding: 0.25em 0.6em;
    border-radius: 12px; /* Pill shape */
}
.severity-badge.high { background-color: var(--rdd-severity-high-bg); color: var(--rdd-severity-high); }
.severity-badge.medium { background-color: var(--rdd-severity-medium-bg); color: var(--rdd-severity-medium); }
.severity-badge.low { background-color: var(--rdd-severity-low-bg); color: var(--rdd-severity-low); }
.severity-badge.default { background-color: var(--rdd-border-color); color: var(--rdd-text-secondary); }


/* Responsive adjustments */
@media (max-width: 991.98px) { /* md breakpoint */
    .disclosure-nav-col-detail,
    .report-content-col-detail,
    .analysis-findings-col-detail {
        max-height: none;
        overflow-y: visible;
        margin-bottom: 1.5rem;
    }
    .disclosure-nav-col-detail:last-child,
    .report-content-col-detail:last-child,
    .analysis-findings-col-detail:last-child {
        margin-bottom: 0;
    }
    .disclosure-nav-detail {
        position: static;
        max-height: 380px; /* Scrollable list on mobile */
        overflow-y: auto;
        padding-right: 0;
    }
    .ai-copilot-sticky-header {
        position: static;
        border-radius: 10px 10px 0 0;
    }
}

/* Anomaly Highlighting in Text */
.generated-text-display-detail mark.anomaly-highlight {
    background-color: rgba(245, 158, 11, 0.2); /* Softer yellow/orange (Tailwind amber-400 with opacity) */
    padding: 0.1em 0.2em;
    border-radius: 4px;
    transition: background-color 0.3s ease, box-shadow 0.3s ease;
    cursor: help;
}

.generated-text-display-detail mark.anomaly-highlight.active {
    background-color: rgba(245, 158, 11, 0.5); /* Tailwind amber-500 with more opacity */
    font-weight: 500;
    box-shadow: 0 0 0 2px rgba(245, 158, 11, 0.3);
}


/* Active Finding Item in Right Panel */
.analysis-findings-col-detail .list-group-item.active-finding-item {
    background-color: var(--rdd-primary-light) !important;
    border-left: 3px solid var(--rdd-primary) !important; /* More prominent */
    position: relative; /* For potential future ::before elements */
}
.analysis-findings-col-detail .list-group-item.active-finding-item .finding-title,
.analysis-findings-col-detail .list-group-item.active-finding-item .finding-description,
.analysis-findings-col-detail .list-group-item.active-finding-item .finding-quote {
    color: var(--rdd-primary-darker) !important; /* Darken text for better contrast on light blue */
}
.analysis-findings-col-detail .list-group-item.active-finding-item .finding-quote {
    border-left-color: var(--rdd-primary) !important;
}