/* src/modules/csrd/components/css/ReportDetailDisplay.css */
.report-detail-container {
    background-color: #f8f9fa;
    box-shadow: 0 4px 12px rgba(0,0,0,0.08);
    border-radius: 8px;
    border-color: #e0e5ec !important;
}
.report-title-text {
    font-size: 1.25rem; /* Slightly larger title */
    font-weight: 600;
}
.report-job-id-badge {
    font-size: 0.7rem;
    font-weight: normal;
}


/* --- Column Layout & Scroll --- */
.disclosure-nav-col-detail,
.report-content-col-detail,
.analysis-findings-col-detail {
    max-height: calc(100vh - 240px); /* Adjust based on header/footer height */
    overflow-y: auto;
    padding-top: 0.5rem; /* Reduced top padding for columns */
}

/* Custom Scrollbar Styling */
.disclosure-nav-col-detail::-webkit-scrollbar,
.report-content-col-detail::-webkit-scrollbar,
.analysis-findings-col-detail::-webkit-scrollbar {
    width: 8px;
}
.disclosure-nav-col-detail::-webkit-scrollbar-thumb,
.report-content-col-detail::-webkit-scrollbar-thumb,
.analysis-findings-col-detail::-webkit-scrollbar-thumb {
    background-color: #adb5bd;
    border-radius: 4px;
}
.disclosure-nav-col-detail::-webkit-scrollbar-track,
.report-content-col-detail::-webkit-scrollbar-track,
.analysis-findings-col-detail::-webkit-scrollbar-track {
    background-color: #e9ecef;
    border-radius: 4px;
}


/* --- Left: Disclosure Navigation --- */
.disclosure-nav-detail {
    position: sticky;
    top: 0.5rem; /* Sticky within its column */
    max-height: calc(100vh - 270px);
    overflow-y: auto;
    padding-right: 10px; /* Space for scrollbar to not overlap content */
}
.disclosure-nav-detail h6 {
    font-size: 0.9rem;
    font-weight: 600;
    color: #495057;
    padding-left: 0.8rem; /* Align with list items */
    margin-bottom: 0.5rem;
}
.disclosure-nav-detail .list-group-item {
    padding: 0.6rem 0.8rem;
    font-size: 0.875rem;
    border: none;
    border-left: 4px solid transparent;
    border-radius: 0 4px 4px 0;
    margin-bottom: 3px;
    transition: background-color 0.2s ease, border-left-color 0.2s ease, color 0.2s ease;
}
.disclosure-nav-detail .list-group-item:hover {
    background-color: #e9ecef;
    border-left-color: #ced4da;
}
.disclosure-nav-detail .list-group-item.active {
    font-weight: 600;
    background-color: #e0eaff;
    border-left-color: var(--bs-primary);
    color: var(--bs-primary);
}
.disclosure-nav-detail .list-group-item.active small {
    color: var(--bs-primary);
    opacity: 0.9;
}
.disclosure-nav-detail .list-group-item small {
    font-size: 0.7rem;
    color: #6c757d;
}


/* --- Middle: Report Content Chunks --- */
.report-content-col-detail {
    padding-left: 1rem;
    padding-right: 1rem;
}
.disclosure-chunk-detail {
    border: 1px solid #dee2e6;
    border-radius: 6px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.04);
    transition: border-left-color 0.3s ease, box-shadow 0.3s ease;
    background-color: #fff;
}
.disclosure-chunk-detail.active {
    border-left: 4px solid var(--bs-primary) !important;
    box-shadow: 0 3px 7px rgba(var(--bs-primary-rgb), 0.15);
}
.disclosure-chunk-detail .card-header {
    background-color: #f8f9fa;
    font-size: 0.95rem;
    font-weight: 500;
    padding: 0.6rem 1rem;
    border-bottom: 1px solid #e9ecef;
    cursor: pointer;
}
.generated-text-display-detail {
    white-space: pre-wrap;
    word-wrap: break-word;
    font-family: "SFMono-Regular", Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
    font-size: 0.85rem;
    line-height: 1.65;
    background-color: #fff;
    padding: 0.75rem 1rem 1rem 1rem;
    border-radius: 0 0 5px 5px;
    color: #343a40;
}


/* --- Right: AI Co-pilot Analysis Panel --- */
.analysis-findings-col-detail {
    background-color: #f0f4f8; /* Distinct background for AI panel */
    border-radius: 8px;
    padding: 0.75rem 1rem 1rem 1rem; /* Top padding handled by sticky header */
}

.ai-copilot-header {
    display: flex;
    align-items: center;
    padding-top: 0.5rem; /* Padding for sticky header content */
    padding-bottom: 0.75rem;
    border-bottom: 1px solid #dde2e7;
    margin-bottom: 0; /* Margin will be on accordion */

    position: sticky;
    top: 0; /* Stick to top of .analysis-findings-col-detail */
    background-color: #f0f4f8; /* Match column background */
    z-index: 100; /* Above scrolling accordion */
}
.ai-copilot-header .copilot-icon {
    color: var(--bs-primary);
    margin-right: 0.75rem;
}
.ai-copilot-header h6 {
    font-size: 1.05rem;
    font-weight: 600;
    margin-bottom: 0;
    color: #2c3e50;
}
.ai-copilot-header .badge {
    font-size: 0.75rem;
    vertical-align: middle;
}

/* Accordion Styling for AI Panel */
.analysis-findings-col-detail .accordion-item {
    background-color: #ffffff;
    border:none;
    border-radius: 6px !important; /* Ensure specificity */
    box-shadow: 0 2px 5px rgba(0,0,0,0.06);
    overflow: hidden; /* Important for border-radius on children */
}
.analysis-findings-col-detail .accordion-item.mb-3 { /* From JSX */
    margin-bottom: 0.75rem !important; /* Slightly less margin */
}


.analysis-findings-col-detail .accordion-button {
    padding: 0.8rem 1rem;
    font-size: 0.9rem;
    font-weight: 500;
    color: #343a40;
    background-color: #fff;
    border-bottom: 1px solid #e9ecef;
    box-shadow: none;
    border-radius: 0; /* Handled by accordion-item */
    width: 100%;
    text-align: left;
}
.analysis-findings-col-detail .accordion-button:hover {
    background-color: #f8f9fa;
}
.analysis-findings-col-detail .accordion-button:not(.collapsed) {
    color: var(--bs-primary);
    background-color: #e7f1ff; /* Light primary when open */
    border-bottom-color: #cce0ff; /* Slightly darker border when open */
}
.analysis-findings-col-detail .accordion-button::after { /* Default chevron */
    margin-left: 0; /* Push to far right if badges are also there */
    transition: transform .2s ease-in-out;
}
.analysis-findings-col-detail .accordion-button:not(.collapsed)::after {
    filter: brightness(0) saturate(100%) invert(38%) sepia(99%) saturate(2080%) hue-rotate(204deg) brightness(101%) contrast(101%); /* Primary color for chevron */
}


.accordion-button-content {
    display: flex;
    align-items: center;
    flex-grow: 1;
}
.accordion-button-content .icon {
    margin-right: 0.75rem;
    font-size: 1.1rem;
    flex-shrink: 0;
}
.accordion-button-content .icon.text-danger { color: var(--bs-danger) !important; }
.accordion-button-content .icon.text-info { color: var(--bs-info) !important; }
.accordion-button-content .icon.text-primary { color: var(--bs-primary) !important; }

.accordion-button-content .title {
    font-weight: 500;
}

.accordion-button-badges {
    display: flex;
    align-items: center;
    margin-left: auto; /* Pushes to the right, before default chevron */
    padding-left: 0.75rem; /* Space before chevron */
}
.accordion-button-badges .badge {
    font-size: 0.7rem;
}


.analysis-findings-col-detail .accordion-body {
    padding: 0;
    font-size: 0.825rem;
    background-color: #fff;
}
.analysis-findings-col-detail .accordion-body .list-group-item {
    padding: 0.75rem 1rem;
    border-top: 1px solid #f1f3f5;
    border-radius: 0;
    background-color: transparent; /* Inherit from accordion-body */
}
.analysis-findings-col-detail .accordion-body .list-group-item:first-child {
    border-top: none;
}

.analysis-findings-col-detail .accordion-body .finding-title {
    font-weight: 500;
    font-size: 0.85rem;
    margin-bottom: 0.3rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    color: #212529;
}
.analysis-findings-col-detail .accordion-body .finding-description {
    color: #495057;
    margin-bottom: 0.25rem;
    line-height: 1.5;
}
.analysis-findings-col-detail .accordion-body .finding-quote {
    font-style: italic;
    color: #6c757d;
    font-size: 0.78rem;
    padding: 0.25rem 0.5rem;
    border-left: 2px solid #e9ecef;
    margin-top: 0.3rem;
    background-color: #f8f9fa;
    display: block; /* Ensure it takes block properties */
    border-radius: 0 3px 3px 0;
}
.analysis-findings-col-detail .accordion-body .finding-meta {
    font-size: 0.75rem;
    color: #6c757d;
}
.analysis-findings-col-detail .accordion-body .no-findings-message {
    padding: 1.25rem 1rem;
    text-align: center;
    color: #6c757d;
    font-style: italic;
    font-size: 0.85rem;
}

/* Ensure severity badge text colors are legible */
.badge.bg-danger { color: white !important; }
.badge.bg-warning.text-dark { color: #212529 !important; }
.badge.bg-info.text-dark { color: #212529 !important; }


/* Responsive adjustments */
@media (max-width: 991.98px) { /* md breakpoint */
    .disclosure-nav-col-detail,
    .report-content-col-detail,
    .analysis-findings-col-detail {
        max-height: none; /* Allow natural height on smaller screens */
        overflow-y: visible;
        margin-bottom: 1.5rem;
    }
    .disclosure-nav-detail {
        position: static;
        max-height: 350px; /* Give it a scrollable max height on mobile if list is long */
        overflow-y: auto;
        padding-right: 0;
    }
    .ai-copilot-header {
        position: static; /* No sticky header on smaller screens */
        padding-top: 0; /* Reset padding for non-sticky */
    }
    .analysis-findings-col-detail .accordion {
        margin-top: 0 !important; /* Reset margin if header is not sticky */
    }
}

@media (max-width: 767.98px) { /* sm breakpoint */
    .report-title-text {
        font-size: 1.1rem;
    }
    .disclosure-nav-detail .list-group-item {
        font-size: 0.8rem;
        padding: 0.5rem 0.75rem;
    }
    .disclosure-chunk-detail .card-header {
        font-size: 0.9rem;
        padding: 0.5rem 0.8rem;
    }
    .generated-text-display-detail {
        font-size: 0.8rem;
        padding: 0.75rem;
    }
    .ai-copilot-header h6 { font-size: 1rem; }
    .analysis-findings-col-detail .accordion-button {
        padding: 0.7rem 0.9rem;
        font-size: 0.85rem;
    }
    .analysis-findings-col-detail .accordion-body .list-group-item {
        padding: 0.6rem 0.9rem;
    }

    /* --- Anomaly Highlighting in Text --- */
    .generated-text-display-detail mark.anomaly-highlight {
        background-color: rgba(255, 230, 153, 0.7); /* Softer yellow */
        padding: 0.1em 0; /* Minimal padding to not disrupt flow too much */
        border-radius: 3px;
        transition: background-color 0.3s ease;
        cursor: help; /* Indicate more info on hover (from title attribute) */
    }

    .generated-text-display-detail mark.anomaly-highlight.active {
        background-color: rgba(255, 165, 0, 0.8); /* Brighter orange for active */
        font-weight: 600; /* Make active highlight slightly bolder */
        box-shadow: 0 0 5px rgba(255, 165, 0, 0.5);
    }


    /* --- Active Finding Item in Right Panel --- */
    .analysis-findings-col-detail .list-group-item.active-finding-item {
        background-color: #cfe2ff !important; /* A light blue, bootstrap primary-light often */
        border-left: 3px solid var(--bs-primary);
        font-weight: 500;
    }
    .analysis-findings-col-detail .list-group-item.active-finding-item:hover {
        background-color: #bad3fc !important;
    }

    /* Ensure specificity for accordion body list items when active */
    .analysis-findings-col-detail .accordion-body .list-group-item.active-finding-item {
        /* Styles here will apply if an item within an accordion body gets this class */
        background-color: #e7f1ff !important; /* Lighter blue to fit within accordion */
        border-color: var(--bs-primary) !important; /* Explicitly set border for active item */
    }
}