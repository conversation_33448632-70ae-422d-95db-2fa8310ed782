// src/modules/csrd/pages/ReportsPage.tsx
import React, { useState, useEffect, useCallback } from 'react';
import {
    Container, Row, Col, Button, Table, Alert, Spinner, Dropdown, ButtonGroup, Badge, Card,
} from 'react-bootstrap';
import { Eye, Trash, Download, CheckCircle, AlertTriangle, PlusCircle, ChevronDown, ChevronUp } from "lucide-react";
import { getReportsList, createCsrdReportJob, CSRDGeneratorRequestPayload } from '../../api/csrdApi.ts';
import { ReportListItem, JobStatusApi }  from '../modules/types.ts';
import ReportDetailDisplay from './ReportDetailDisplay.tsx'; // Import the new component

const ReportsPage: React.FC = () => {
    // No useNavigate needed for this approach if not navigating away
    const [reports, setReports] = useState<ReportListItem[]>([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);
    const [isCreatingReport, setIsCreatingReport] = useState(false);

    const [selectedReportId, setSelectedReportId] = useState<string | null>(null); // State for viewing details

    const fetchReports = useCallback(async () => {
        setLoading(true);
        setError(null);
        try {
            const fetchedReports = await getReportsList();
            setReports(fetchedReports);
        } catch (err) {
            console.error("Failed to fetch reports:", err);
            setError(err instanceof Error ? err.message : "An unknown error occurred while fetching reports.");
        } finally {
            setLoading(false);
        }
    }, []);

    useEffect(() => {
        fetchReports();
    }, [fetchReports]);

    const handleCreate = async () => {
        const companyProfileId = prompt("Enter Company Profile ID (e.g., 'BASF' or a test ID):");
        if (!companyProfileId) return;
        const payload: CSRDGeneratorRequestPayload = { company_profile_id: companyProfileId, reporting_year: new Date().getFullYear() - 1, output_format: 'docx' };
        setIsCreatingReport(true);
        setError(null);
        try {
            const jobContext = await createCsrdReportJob(payload);
            alert(`Report generation job submitted: ${jobContext.job_id}. Status: ${jobContext.status}. Refreshing list...`);
            await fetchReports();
        } catch (err) {
            const errorMessage = err instanceof Error ? err.message : "Failed to start report generation.";
            setError(errorMessage);
            alert(`Error: ${errorMessage}`);
        } finally {
            setIsCreatingReport(false);
        }
    };

    const handleToggleView = (jobId: string) => {
        setSelectedReportId(prevId => prevId === jobId ? null : jobId); // Toggle view
    };

    const handleDownload = (r: ReportListItem) => {
        if (r.status === JobStatusApi.COMPLETED) {
            // Ideally, ReportListItem would have report_file_path if available.
            // If not, you'd need to fetch details first.
            alert(`Download would be initiated for job ${r.job_id}. (Actual download URL needed)`);
        } else {
            alert("Report is not yet completed for download.");
        }
    };

    const handleDelete = (r: ReportListItem) => {
        console.log('Delete', r.job_id);
        alert(`Deletion for report ${r.job_id} not yet implemented.`);
    };


    return (
        <Container fluid className="py-4 px-md-4">
            <Row className="align-items-center mb-4">
                <Col xs="auto"><h2 className="m-0">CSRD Reports</h2></Col>
                <Col className="text-end">
                    <Button onClick={handleCreate} variant="primary" disabled={isCreatingReport}>
                        {isCreatingReport ? <Spinner as="span" animation="border" size="sm" className="me-1" /> : <PlusCircle size={18} className="me-1" />}
                        Generate New Report
                    </Button>
                </Col>
            </Row>

            {error && <Alert variant="danger" onClose={() => setError(null)} dismissible>{error}</Alert>}

            {loading && !isCreatingReport ? (
                <div className="text-center py-5"><Spinner animation="border" /><span className="ms-2">Loading reports...</span></div>
            ) : reports.length === 0 && !error ? (
                <Alert variant="info">No reports found. Click "Generate New Report" to start.</Alert>
            ) : (
                <>
                    {reports.map((r) => (
                        <React.Fragment key={r.job_id}>
                            <Card className="mb-2 report-list-item">
                                <Card.Body className="p-2">
                                    <Row className="align-items-center gx-2">
                                        <Col>
                                            <strong>{r.display_name}</strong>
                                            <br/>
                                            <small className="text-muted">{r.job_id}</small>
                                        </Col>
                                        <Col md="2" className="d-none d-md-block">
                                            <small>{new Date(r.generated_at).toLocaleDateString()}</small>
                                        </Col>
                                        <Col xs="auto" md="2">
                                            {r.status === JobStatusApi.COMPLETED && <Badge bg="success"><CheckCircle size={14} className="me-1"/> Completed</Badge>}
                                            {r.status === JobStatusApi.RUNNING && <Badge bg="info" text="dark"><Spinner size="sm" animation="border" className="me-1"/> Running</Badge>}
                                            {r.status === JobStatusApi.PENDING && <Badge bg="light" text="dark"><Spinner size="sm" animation="border" className="me-1"/> Pending</Badge>}
                                            {r.status === JobStatusApi.FAILED && <Badge bg="danger"><AlertTriangle size={14} className="me-1"/> Failed</Badge>}
                                            {r.status === JobStatusApi.UNKNOWN && <Badge bg="secondary">Unknown</Badge>}
                                        </Col>
                                        <Col xs="auto" className="text-end">
                                            <ButtonGroup size="sm">
                                                <Button
                                                    variant="outline-primary"
                                                    onClick={() => handleToggleView(r.job_id)}
                                                    title={selectedReportId === r.job_id ? "Hide Details" : "View Details"}
                                                >
                                                    {selectedReportId === r.job_id ? <ChevronUp size={16}/> : <ChevronDown size={16} />}
                                                    <span className="d-none d-lg-inline ms-1">
                                                        {selectedReportId === r.job_id ? "Hide" : "Details"}
                                                    </span>
                                                </Button>
                                                {/* Optional: Keep dropdown for other actions if needed */}
                                                <Dropdown as={ButtonGroup}>
                                                    <Dropdown.Toggle split variant="outline-secondary" id={`dd-${r.job_id}`} />
                                                    <Dropdown.Menu align="end">
                                                        <Dropdown.Item
                                                            onClick={() => handleDownload(r)}
                                                            disabled={r.status !== JobStatusApi.COMPLETED /* || !r.downloadUrl - add if list has it */}
                                                        >
                                                            <Download size={16} className="me-2" /> Download File
                                                        </Dropdown.Item>
                                                        <Dropdown.Divider />
                                                        <Dropdown.Item onClick={() => handleDelete(r)} className="text-danger">
                                                            <Trash size={16} className="me-2" /> Delete
                                                        </Dropdown.Item>
                                                    </Dropdown.Menu>
                                                </Dropdown>
                                            </ButtonGroup>
                                        </Col>
                                    </Row>
                                </Card.Body>
                            </Card>
                            {/* Conditionally render the ReportDetailDisplay component */}
                            {selectedReportId === r.job_id && (
                                <div className="report-detail-expanded mb-3">
                                    <ReportDetailDisplay jobId={r.job_id} onClose={() => setSelectedReportId(null)} />
                                </div>
                            )}
                        </React.Fragment>
                    ))}
                </>
            )}
        </Container>
    );
};

export default ReportsPage;