import React, { useState, useEffect, useRef, useMemo, useCallback } from 'react';
import {
    Con<PERSON><PERSON>, <PERSON>, <PERSON>, Spin<PERSON>, <PERSON><PERSON>, Card, ListGroup, Badge, Button, Accordion
} from 'react-bootstrap';
import {
    FileText, AlertTriangle, CheckCircle, Search, Repeat, Info, Download, XCircle,
    Bot, AlertOctagon, FileSearch as FileSearchIcon, ChevronDown, ChevronUp
} from 'lucide-react'; // ChevronDown/Up for accordion if not using default
import { getReportDetails } from '../../api/csrdApi.ts';
import { ReportDetails, DisclosureChunkDetail, FindingDetail, JobStatusApi } from '../modules/types.ts';
import '../css/ReportDetailDisplay.css'; // ENSURE THIS PATH IS CORRECT

interface ReportDetailDisplayProps {
    jobId: string;
    onClose?: () => void;
}

const ReportDetailDisplay: React.FC<ReportDetailDisplayProps> = ({ jobId, onClose }) => {
    const [reportDetails, setReportDetails] = useState<ReportDetails | null>(null);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);
    const [activeDisclosureCode, setActiveDisclosureCode] = useState<string | null>(null);
    const [activeHighlightKey, setActiveHighlightKey] = useState<string | null>(null);

    const disclosureRefs = useRef<Record<string, HTMLDivElement | null>>({});
    const contentColRef = useRef<HTMLDivElement>(null);

    useEffect(() => {
        // ... (fetching logic remains the same)
        if (!jobId) {
            setError("Job ID is required to display details.");
            setLoading(false);
            return;
        }
        const fetchDetails = async () => {
            setLoading(true);
            setError(null);
            setReportDetails(null);
            setActiveDisclosureCode(null);
            setActiveHighlightKey(null);
            try {
                const data = await getReportDetails(jobId);
                setReportDetails(data);
                if (data.disclosure_chunks && data.disclosure_chunks.length > 0) {
                    setActiveDisclosureCode(data.disclosure_chunks[0].disclosure_code);
                }
            } catch (err) {
                const errorMessage = err instanceof Error ? err.message : "Failed to fetch report details.";
                setError(errorMessage);
                console.error("Error fetching report details:", err);
            } finally {
                setLoading(false);
            }
        };
        fetchDetails();
    }, [jobId]);

    useEffect(() => {
        // ... (IntersectionObserver logic remains the same)
        if (!reportDetails?.disclosure_chunks || reportDetails.disclosure_chunks.length === 0 || !contentColRef.current) return;
        const observerOptions = {
            root: contentColRef.current,
            rootMargin: "-35% 0px -65% 0px", // Adjusted margins for potentially taller nav items
            threshold: 0.01,
        };
        const observerCallback: IntersectionObserverCallback = (entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const code = entry.target.getAttribute('data-disclosure-code');
                    if (code) setActiveDisclosureCode(code);
                }
            });
        };
        const observer = new IntersectionObserver(observerCallback, observerOptions);
        const currentRefs = disclosureRefs.current;
        Object.values(currentRefs).forEach(el => el && observer.observe(el));
        return () => Object.values(currentRefs).forEach(el => el && observer.unobserve(el));
    }, [reportDetails?.disclosure_chunks, contentColRef.current]);


    const handleDisclosureChunkClick = (code: string) => {
        setActiveDisclosureCode(code);
        const targetElement = disclosureRefs.current[code];
        if (targetElement && contentColRef.current) {
            const containerTop = contentColRef.current.getBoundingClientRect().top;
            const elementTop = targetElement.getBoundingClientRect().top;
            const scrollPosition = elementTop - containerTop + contentColRef.current.scrollTop - (contentColRef.current.clientHeight / 4); // scroll to top quarter
            contentColRef.current.scrollTo({ top: scrollPosition, behavior: 'smooth' });
        }
    };

    const getSeverityBadgePill = (severity?: string | null) => {
        if (!severity) return <span className="severity-badge default">N/A</span>;
        switch (severity.toLowerCase()) {
            case 'high': return <span className="severity-badge high">High</span>;
            case 'medium': return <span className="severity-badge medium">Medium</span>;
            case 'low': return <span className="severity-badge low">Low</span>;
            default: return <span className="severity-badge default">{severity}</span>;
        }
    };

    // --- Anomaly Highlighting Logic (renderHighlightedText, getAnomalyKey etc. remains the same) ---
    const getAnomalyKey = useCallback((disclosureCode: string, anomalyIndex: number) => {
        return `${disclosureCode}-anomaly-${anomalyIndex}`;
    }, []);

    const getAnomaliesForChunk = useCallback((disclosureCode: string): FindingDetail[] => {
        if (!reportDetails) return [];
        return reportDetails.validation_findings?.anomalies?.filter(
            (anomaly) => anomaly.disclosure_code === disclosureCode
        ) || [];
    }, [reportDetails]);

    const renderHighlightedText = useCallback((
        text: string,
        anomaliesForChunk: FindingDetail[],
        currentActiveHighlightKey: string | null,
        disclosureCode: string
    ): React.ReactNode[] => {
        // ... (logic remains the same as in your original code)
        if (!text) return [''];
        if (!anomaliesForChunk || anomaliesForChunk.length === 0) {
            return [text];
        }

        interface QuoteOccurrence {
            startIndex: number;
            endIndex: number;
            quote: string;
            anomaly: FindingDetail;
            originalAnomalyIndex: number;
            occurrenceInTextIndex: number;
        }

        let allOccurrences: QuoteOccurrence[] = [];

        anomaliesForChunk.forEach((anomaly, originalAnomalyIndex) => {
            if (anomaly.text_quote && typeof anomaly.text_quote === 'string' && anomaly.text_quote.trim() !== '') {
                let currentIndex = 0;
                let occurrenceInTextCounter = 0;
                while (currentIndex < text.length) {
                    const startIndex = text.indexOf(anomaly.text_quote, currentIndex);
                    if (startIndex === -1) break;

                    allOccurrences.push({
                        startIndex,
                        endIndex: startIndex + anomaly.text_quote.length,
                        quote: anomaly.text_quote,
                        anomaly,
                        originalAnomalyIndex,
                        occurrenceInTextIndex: occurrenceInTextCounter
                    });
                    currentIndex = startIndex + anomaly.text_quote.length;
                    occurrenceInTextCounter++;
                }
            }
        });

        allOccurrences.sort((a, b) => {
            if (a.startIndex !== b.startIndex) return a.startIndex - b.startIndex;
            return b.quote.length - a.quote.length;
        });

        const nonOverlappingOccurrences: QuoteOccurrence[] = [];
        let lastEndIndex = -1;
        for (const occ of allOccurrences) {
            if (occ.startIndex >= lastEndIndex) {
                nonOverlappingOccurrences.push(occ);
                lastEndIndex = occ.endIndex;
            }
        }

        if (nonOverlappingOccurrences.length === 0) return [text];

        const result: React.ReactNode[] = [];
        let currentTextIndex = 0;
        let keyCounter = 0;

        nonOverlappingOccurrences.forEach(occ => {
            if (occ.startIndex > currentTextIndex) {
                result.push(text.substring(currentTextIndex, occ.startIndex));
            }
            const anomalyKey = getAnomalyKey(disclosureCode, occ.originalAnomalyIndex);
            const highlightElementId = `highlight-${anomalyKey}-${occ.occurrenceInTextIndex}`;
            const isActive = currentActiveHighlightKey === anomalyKey;

            result.push(
                <mark
                    key={`${highlightElementId}-${keyCounter++}`}
                    id={highlightElementId}
                    className={`anomaly-highlight ${isActive ? 'active' : ''}`}
                    title={`${occ.anomaly.anomaly_type || 'Anomaly'}: ${occ.anomaly.description}`}
                >
                    {occ.quote}
                </mark>
            );
            currentTextIndex = occ.endIndex;
        });

        if (currentTextIndex < text.length) {
            result.push(text.substring(currentTextIndex));
        }
        return result;
    }, [getAnomalyKey, activeHighlightKey]);


    const handleAnomalyItemClick = (anomalyKey: string) => {
        // ... (logic remains the same)
        const newActiveKey = activeHighlightKey === anomalyKey ? null : anomalyKey;
        setActiveHighlightKey(newActiveKey);

        if (newActiveKey) {
            const firstHighlightElementId = `highlight-${newActiveKey}-0`;
            const element = document.getElementById(firstHighlightElementId);

            if (element && contentColRef.current) {
                const containerRect = contentColRef.current.getBoundingClientRect();
                const elementRect = element.getBoundingClientRect();
                const offsetTopInContainer = elementRect.top - containerRect.top + contentColRef.current.scrollTop;
                const desiredScrollPosition = offsetTopInContainer - (contentColRef.current.clientHeight / 3);

                contentColRef.current.scrollTo({
                    top: desiredScrollPosition,
                    behavior: 'smooth'
                });
            }
        }
    };


    // --- Filtered Findings for Right Panel (remains the same) ---
    const filteredFindings = useMemo(() => {
        // ... (logic remains the same)
        if (!reportDetails || !activeDisclosureCode) {
            return { anomalies: [], coverage: [], repetitions: [] };
        }
        const { validation_findings } = reportDetails;
        if (!validation_findings) return { anomalies: [], coverage: [], repetitions: [] };

        const filterByCode = (finding: FindingDetail) => finding.disclosure_code === activeDisclosureCode;

        const activeAnomalies = validation_findings.anomalies?.filter(filterByCode) || [];
        const activeCoverage = validation_findings.coverage?.filter(filterByCode) || [];

        let activeRepetitions = validation_findings.repetitions?.filter(f =>
            f.disclosure_code === activeDisclosureCode ||
            (f.sentence1_location?.includes(activeDisclosureCode)) ||
            (f.sentence2_location?.includes(activeDisclosureCode))
        ) || [];

        // If no section-specific repetitions, consider general ones (those without disclosure_code or locations)
        if (activeRepetitions.length === 0 && validation_findings.repetitions) {
            activeRepetitions = validation_findings.repetitions.filter(f =>
                !f.disclosure_code && !f.sentence1_location && !f.sentence2_location
            );
        }
        return { anomalies: activeAnomalies, coverage: activeCoverage, repetitions: activeRepetitions };
    }, [reportDetails, activeDisclosureCode]);


    // --- Render Logic ---
    if (loading) return <div className="text-center py-5"><Spinner animation="border" style={{ color: 'var(--rdd-primary)'}} /> <span className="ms-2 fs-5" style={{ color: 'var(--rdd-text-secondary)'}}>Loading report details...</span></div>;
    if (error) return <Alert variant="danger" className="mt-3 mx-3 shadow-sm">{error}</Alert>;
    if (!reportDetails) return <Alert variant="light" className="mt-3 mx-3 text-center shadow-sm">No details to display for this report.</Alert>;

    // Handling non-viewable states
    if (reportDetails.generation_status !== JobStatusApi.COMPLETED || (reportDetails.generation_status === JobStatusApi.COMPLETED && reportDetails.disclosure_chunks.length === 0)) {
        let statusIcon, statusTitle, statusMessage, variant;
        switch (reportDetails.generation_status) {
            case JobStatusApi.FAILED:
                statusIcon = <AlertTriangle size={28} className="me-2 text-danger" />;
                statusTitle = "Report Generation Failed";
                statusMessage = reportDetails.error_message || "An error occurred during report generation.";
                variant = "danger";
                break;
            case JobStatusApi.PENDING:
            case JobStatusApi.RUNNING:
                statusIcon = <Spinner animation="border" size="sm" className="me-2" style={{ color: 'var(--rdd-primary)'}} />;
                statusTitle = "Report Processing...";
                statusMessage = `The report is currently ${reportDetails.generation_status.toLowerCase()}. Please check back later.`;
                variant = "info";
                break;
            case JobStatusApi.COMPLETED: // but no chunks
            default:
                statusIcon = <Info size={28} className="me-2 text-warning" />;
                statusTitle = "Report Generated - No Sections";
                statusMessage = "The report generation completed, but no individual disclosure sections were processed or found.";
                variant = "warning";
                break;
        }

        return (
            <Card className="m-3 shadow-sm report-detail-container">
                <Card.Body className="p-4">
                    <div className="d-flex justify-content-between align-items-start">
                        <div className="d-flex align-items-center">
                            {statusIcon}
                            <h4 className={`mb-0 text-${variant}`}>{statusTitle}</h4>
                        </div>
                        {onClose && <Button variant="light" size="sm" onClick={onClose}><XCircle size={18}/> <span className="d-none d-sm-inline ms-1">Close</span></Button>}
                    </div>
                    <p className="text-muted mt-2 mb-1"><strong>Job ID:</strong> {reportDetails.job_id}</p>
                    <Alert variant={variant} className="mt-3 mb-0">{statusMessage}</Alert>
                    {reportDetails.generation_status === JobStatusApi.COMPLETED && reportDetails.report_file_path && (
                        <div className="mt-3">
                            <Button variant="primary" size="sm" href={reportDetails.report_file_path} target="_blank" download>
                                <Download size={16} className="me-2" /> Download Full Report ({reportDetails.report_file_path.split('.').pop()?.toUpperCase()})
                            </Button>
                        </div>
                    )}
                </Card.Body>
            </Card>
        );
    }


    return (
        <div className="mt-3 p-lg-4 p-3 report-detail-container">
            <Row className="mb-3 align-items-center">
                <Col>
                    <h3 className="mb-1 report-title-text">
                        Report: {reportDetails.report_file_path?.split('/').pop() || `Job ${reportDetails.job_id.substring(0,13)}...`}
                    </h3>
                    <Badge pill className="report-job-id-badge">{reportDetails.job_id}</Badge>
                </Col>
                {reportDetails.report_file_path && (
                    <Col xs="auto" className="text-end mt-2 mt-md-0">
                        <Button variant="outline-primary" size="sm" href={reportDetails.report_file_path} target="_blank" download>
                            <Download size={16} className="me-2" /> Download ({reportDetails.report_file_path.split('.').pop()?.toUpperCase()})
                        </Button>
                    </Col>
                )}
                {onClose && (
                    <Col xs="auto" className="text-end mt-2 mt-md-0">
                        <Button variant="outline-secondary" size="sm" onClick={onClose}>
                            <XCircle size={16} className="me-1"/> <span className="d-none d-md-inline">Close Details</span>
                        </Button>
                    </Col>
                )}
            </Row>
            <hr className="mt-1 mb-3"/>

            <Row>
                <Col lg={3} md={4} className="disclosure-nav-col-detail mb-3 mb-md-0">
                    <div className="disclosure-nav-detail">
                        <h6>Disclosures ({reportDetails.disclosure_chunks.length})</h6>
                        <ListGroup variant="flush" className="pe-md-2">
                            {reportDetails.disclosure_chunks.map(chunk => (
                                <ListGroup.Item
                                    key={chunk.disclosure_code} action
                                    onClick={(e) => { e.preventDefault(); handleDisclosureChunkClick(chunk.disclosure_code); }}
                                    active={chunk.disclosure_code === activeDisclosureCode}
                                    title={chunk.disclosure_name || chunk.disclosure_code}
                                >
                                    {chunk.disclosure_code}
                                    <small className="d-block text-truncate">{chunk.disclosure_name || 'View Section'}</small>
                                </ListGroup.Item>
                            ))}
                        </ListGroup>
                    </div>
                </Col>

                <Col lg={5} md={8} className="report-content-col-detail mb-3 mb-md-0" ref={contentColRef}>
                    {reportDetails.disclosure_chunks.length === 0 && (
                        <Alert variant="light" className="text-center p-5">
                            <FileText size={40} className="text-muted mb-3"/>
                            <h5>No disclosure content available.</h5>
                            <p className="text-muted">This report does not contain any parsed disclosure sections.</p>
                        </Alert>
                    )}
                    {reportDetails.disclosure_chunks.map((chunk: DisclosureChunkDetail) => {
                        const anomaliesForThisChunk = getAnomaliesForChunk(chunk.disclosure_code);
                        return (
                            <Card
                                key={chunk.disclosure_code}
                                id={`detail-chunk-${chunk.disclosure_code}`}
                                ref={el => disclosureRefs.current[chunk.disclosure_code] = el}
                                className={`mb-3 disclosure-chunk-detail ${chunk.disclosure_code === activeDisclosureCode ? 'active' : ''}`}
                                data-disclosure-code={chunk.disclosure_code}
                            >
                                <Card.Header onClick={() => handleDisclosureChunkClick(chunk.disclosure_code)}>
                                    <strong>{chunk.disclosure_code}</strong> - {chunk.disclosure_name || 'Unnamed Disclosure'}
                                </Card.Header>
                                <Card.Body className="p-0"> {/* Remove Card.Body padding, handled by pre */}
                                    <pre className="generated-text-display-detail">
                                        {renderHighlightedText(
                                            chunk.generated_text,
                                            anomaliesForThisChunk,
                                            activeHighlightKey,
                                            chunk.disclosure_code
                                        )}
                                    </pre>
                                </Card.Body>
                            </Card>
                        );
                    })}
                </Col>

                <Col lg={4} className="analysis-findings-col-detail">
                    <div className="ai-copilot-sticky-header">
                        <div className="ai-copilot-header-content">
                            <Bot size={28} className="copilot-icon" />
                            <h6>
                                AI Co-pilot Analysis
                                {activeDisclosureCode ?
                                    <Badge pill bg="primary" className="ms-2 align-middle">{activeDisclosureCode}</Badge> :
                                    <Badge pill bg="light" text="dark" className="ms-2 align-middle">Select Section</Badge>
                                }
                            </h6>
                        </div>
                    </div>

                    <Accordion defaultActiveKey={['0', '1', '2']} alwaysOpen>
                        <Accordion.Item eventKey="0">
                            <Accordion.Header>
                                <div className="accordion-button-content">
                                    <AlertOctagon size={18} className="icon text-danger" />
                                    <span className="title">Anomalies</span>
                                </div>
                                <div className="accordion-button-badges">
                                    <Badge pill bg="danger">{filteredFindings.anomalies.length}</Badge>
                                </div>
                            </Accordion.Header>
                            <Accordion.Body>
                                {filteredFindings.anomalies.length === 0 ? (
                                    <p className="no-findings-message">No anomalies identified for this section.</p>
                                ) : (
                                    <ListGroup variant="flush">
                                        {filteredFindings.anomalies.map((item, idx) => {
                                            const anomalyKey = getAnomalyKey(activeDisclosureCode!, idx);
                                            return (
                                                <ListGroup.Item
                                                    key={anomalyKey} action
                                                    onClick={() => handleAnomalyItemClick(anomalyKey)}
                                                    className={activeHighlightKey === anomalyKey ? 'active-finding-item' : ''}
                                                >
                                                    <div className="finding-title">
                                                        <span>{item.anomaly_type || 'Anomaly'}</span>
                                                        {getSeverityBadgePill(item.severity)}
                                                    </div>
                                                    <p className="finding-description mb-1">{item.description}</p>
                                                    {item.text_quote && <small className="finding-quote"><em>"{item.text_quote}"</em></small>}
                                                </ListGroup.Item>
                                            );
                                        })}
                                    </ListGroup>
                                )}
                            </Accordion.Body>
                        </Accordion.Item>

                        <Accordion.Item eventKey="1">
                            <Accordion.Header>
                                <div className="accordion-button-content">
                                    <FileSearchIcon size={18} className="icon text-info" />
                                    <span className="title">Coverage Gaps</span>
                                </div>
                                <div className="accordion-button-badges">
                                    <Badge pill bg="info">{filteredFindings.coverage.length}</Badge>
                                </div>
                            </Accordion.Header>
                            <Accordion.Body>
                                {filteredFindings.coverage.length === 0 ? (
                                    <p className="no-findings-message">No coverage gaps identified for this section.</p>
                                ) : (
                                    <ListGroup variant="flush">
                                        {filteredFindings.coverage.map((item, idx) => (
                                            <ListGroup.Item key={`detail-coverage-${idx}`}>
                                                <div className="finding-title">
                                                    <span>{item.concern_type || 'Gap'}</span>
                                                    {getSeverityBadgePill(item.severity)}
                                                </div>
                                                <p className="finding-description mb-0">{item.description}</p>
                                            </ListGroup.Item>
                                        ))}
                                    </ListGroup>
                                )}
                            </Accordion.Body>
                        </Accordion.Item>

                        <Accordion.Item eventKey="2">
                            <Accordion.Header>
                                <div className="accordion-button-content">
                                    <Repeat size={18} className="icon text-primary" />
                                    <span className="title">Repetitions</span>
                                </div>
                                <div className="accordion-button-badges">
                                    <Badge pill bg="primary">{filteredFindings.repetitions.length}</Badge>
                                </div>
                            </Accordion.Header>
                            <Accordion.Body>
                                {filteredFindings.repetitions.length === 0 ? (
                                    <p className="no-findings-message">No significant repetitions found for this section.</p>
                                ) : (
                                    <ListGroup variant="flush">
                                        {filteredFindings.repetitions.map((item, idx) => (
                                            <ListGroup.Item key={`detail-repetition-${idx}`}>
                                                <div className="finding-title">
                                                    <span>{item.type || 'Repetitive'} {item.score && `(Score: ${item.score.toFixed(2)})`}</span>
                                                    {getSeverityBadgePill(item.severity)}
                                                </div>
                                                {item.sentence1_text && <p className="finding-description text-truncate mb-1" title={item.sentence1_text}><em>S1: "{item.sentence1_text}"</em> <small className="finding-meta">({item.sentence1_location || 'Loc1'})</small></p>}
                                                {item.sentence2_text && <p className="finding-description text-truncate mb-0" title={item.sentence2_text}><em>S2: "{item.sentence2_text}"</em> <small className="finding-meta">({item.sentence2_location || 'Loc2'})</small></p>}
                                                {!item.sentence1_text && item.description && <p className="finding-description mb-0">{item.description}</p>}
                                            </ListGroup.Item>
                                        ))}
                                    </ListGroup>
                                )}
                            </Accordion.Body>
                        </Accordion.Item>
                    </Accordion>
                </Col>
            </Row>
        </div>
    );
};

export default ReportDetailDisplay;