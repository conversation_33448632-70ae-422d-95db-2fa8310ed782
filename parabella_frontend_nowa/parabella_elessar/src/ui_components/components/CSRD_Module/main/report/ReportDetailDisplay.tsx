import React, { useState, useEffect, useRef, useMemo, useCallback } from 'react';
import {
    <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, Spin<PERSON>, <PERSON><PERSON>, Card, ListGroup, Badge, Button, Accordion
} from 'react-bootstrap';
import {
    FileText, AlertTriangle, CheckCircle, Search, Repeat, Info, Download, XCircle,
    Bot, AlertOctagon, FileSearch as FileSearchIcon
} from 'lucide-react';
import { getReportDetails } from '../../api/csrdApi.ts';
import { ReportDetails, DisclosureChunkDetail, FindingDetail, JobStatusApi } from '../modules/types.ts';
import '../css/ReportDetailDisplay.css';

interface ReportDetailDisplayProps {
    jobId: string;
    onClose?: () => void;
}

const ReportDetailDisplay: React.FC<ReportDetailDisplayProps> = ({ jobId, onClose }) => {
    const [reportDetails, setReportDetails] = useState<ReportDetails | null>(null);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);
    const [activeDisclosureCode, setActiveDisclosureCode] = useState<string | null>(null);
    const [activeHighlightKey, setActiveHighlightKey] = useState<string | null>(null); // For focused anomaly

    const disclosureRefs = useRef<Record<string, HTMLDivElement | null>>({});
    const mainContainerRef = useRef<HTMLDivElement>(null);
    const contentColRef = useRef<HTMLDivElement>(null); // Ref for the scrollable content column

    // --- Fetching and Basic Setup ---
    useEffect(() => {
        if (!jobId) {
            setError("Job ID is required to display details.");
            setLoading(false);
            return;
        }
        const fetchDetails = async () => {
            setLoading(true);
            setError(null);
            setReportDetails(null);
            setActiveDisclosureCode(null);
            setActiveHighlightKey(null);
            try {
                const data = await getReportDetails(jobId);
                setReportDetails(data);
                if (data.disclosure_chunks && data.disclosure_chunks.length > 0) {
                    setActiveDisclosureCode(data.disclosure_chunks[0].disclosure_code);
                }
            } catch (err) {
                const errorMessage = err instanceof Error ? err.message : "Failed to fetch report details.";
                setError(errorMessage);
                console.error("Error fetching report details:", err);
            } finally {
                setLoading(false);
            }
        };
        fetchDetails();
    }, [jobId]);

    useEffect(() => {
        if (!reportDetails?.disclosure_chunks || reportDetails.disclosure_chunks.length === 0 || !contentColRef.current) return;
        const observerOptions = {
            root: contentColRef.current,
            rootMargin: "-40% 0px -60% 0px",
            threshold: 0.01,
        };
        const observerCallback: IntersectionObserverCallback = (entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const code = entry.target.getAttribute('data-disclosure-code');
                    if (code) setActiveDisclosureCode(code);
                }
            });
        };
        const observer = new IntersectionObserver(observerCallback, observerOptions);
        const currentRefs = disclosureRefs.current;
        Object.values(currentRefs).forEach(el => el && observer.observe(el));
        return () => Object.values(currentRefs).forEach(el => el && observer.unobserve(el));
    }, [reportDetails?.disclosure_chunks, contentColRef.current]);

    const handleDisclosureChunkClick = (code: string) => {
        setActiveDisclosureCode(code);
        const targetElement = disclosureRefs.current[code];
        if (targetElement && contentColRef.current) {
            const containerTop = contentColRef.current.scrollTop;
            const elementTopInContainer = targetElement.offsetTop;
            const desiredScrollPosition = elementTopInContainer - (contentColRef.current.clientHeight / 3);
            contentColRef.current.scrollTo({ top: desiredScrollPosition, behavior: 'smooth' });
        }
    };

    const getSeverityBadge = (severity?: string | null) => {
        if (!severity) return <Badge bg="light" text="dark" pill>N/A</Badge>;
        switch (severity.toLowerCase()) {
            case 'high': return <Badge bg="danger" pill>High</Badge>;
            case 'medium': return <Badge bg="warning" text="dark" pill>Medium</Badge>;
            case 'low': return <Badge bg="info" text="dark" pill>Low</Badge>;
            default: return <Badge bg="secondary" pill>{severity}</Badge>;
        }
    };

    // --- Anomaly Highlighting Logic ---
    const getAnomalyKey = useCallback((disclosureCode: string, anomalyIndex: number) => {
        return `${disclosureCode}-anomaly-${anomalyIndex}`;
    }, []);

    const getAnomaliesForChunk = useCallback((disclosureCode: string): FindingDetail[] => {
        if (!reportDetails) return [];
        // Ensure validation_findings and anomalies exist before filtering
        return reportDetails.validation_findings?.anomalies?.filter(
            (anomaly) => anomaly.disclosure_code === disclosureCode
        ) || [];
    }, [reportDetails]);


    const renderHighlightedText = useCallback((
        text: string,
        anomaliesForChunk: FindingDetail[],
        currentActiveHighlightKey: string | null,
        disclosureCode: string
    ): React.ReactNode[] => {
        if (!text) return [''];
        if (!anomaliesForChunk || anomaliesForChunk.length === 0) {
            return [text];
        }

        interface QuoteOccurrence {
            startIndex: number;
            endIndex: number;
            quote: string;
            anomaly: FindingDetail;
            originalAnomalyIndex: number; // index in `anomaliesForChunk`
            occurrenceInTextIndex: number; // 0th, 1st, etc. occurrence of *this specific quote of this anomaly item* in the text
        }

        let allOccurrences: QuoteOccurrence[] = [];

        anomaliesForChunk.forEach((anomaly, originalAnomalyIndex) => {
            if (anomaly.text_quote && typeof anomaly.text_quote === 'string' && anomaly.text_quote.trim() !== '') {
                let currentIndex = 0;
                let occurrenceInTextCounter = 0;
                while (currentIndex < text.length) {
                    const startIndex = text.indexOf(anomaly.text_quote, currentIndex);
                    if (startIndex === -1) break;

                    allOccurrences.push({
                        startIndex,
                        endIndex: startIndex + anomaly.text_quote.length,
                        quote: anomaly.text_quote,
                        anomaly,
                        originalAnomalyIndex,
                        occurrenceInTextIndex: occurrenceInTextCounter
                    });
                    currentIndex = startIndex + anomaly.text_quote.length;
                    occurrenceInTextCounter++;
                }
            }
        });

        allOccurrences.sort((a, b) => {
            if (a.startIndex !== b.startIndex) return a.startIndex - b.startIndex;
            return b.quote.length - a.quote.length; // Longer matches first if at same start
        });

        const nonOverlappingOccurrences: QuoteOccurrence[] = [];
        let lastEndIndex = -1;
        for (const occ of allOccurrences) {
            if (occ.startIndex >= lastEndIndex) {
                nonOverlappingOccurrences.push(occ);
                lastEndIndex = occ.endIndex;
            }
        }

        if (nonOverlappingOccurrences.length === 0) return [text];

        const result: React.ReactNode[] = [];
        let currentTextIndex = 0;
        let keyCounter = 0;

        nonOverlappingOccurrences.forEach(occ => {
            if (occ.startIndex > currentTextIndex) {
                result.push(text.substring(currentTextIndex, occ.startIndex));
            }
            const anomalyKey = getAnomalyKey(disclosureCode, occ.originalAnomalyIndex);
            const highlightElementId = `highlight-${anomalyKey}-${occ.occurrenceInTextIndex}`;
            const isActive = currentActiveHighlightKey === anomalyKey;

            result.push(
                <mark
                    key={`${highlightElementId}-${keyCounter++}`}
                    id={highlightElementId}
                    className={`anomaly-highlight ${isActive ? 'active' : ''}`}
                    title={`${occ.anomaly.anomaly_type || 'Anomaly'}: ${occ.anomaly.description}`}
                >
                    {occ.quote}
                </mark>
            );
            currentTextIndex = occ.endIndex;
        });

        if (currentTextIndex < text.length) {
            result.push(text.substring(currentTextIndex));
        }
        return result;
    }, [getAnomalyKey]);


    const handleAnomalyItemClick = (anomalyKey: string) => {
        const newActiveKey = activeHighlightKey === anomalyKey ? null : anomalyKey;
        setActiveHighlightKey(newActiveKey);

        if (newActiveKey) { // Only scroll if we are activating a key
            // Scroll to the first occurrence of this anomaly's highlight
            const firstHighlightElementId = `highlight-${newActiveKey}-0`;
            const element = document.getElementById(firstHighlightElementId);

            if (element && contentColRef.current) {
                const containerRect = contentColRef.current.getBoundingClientRect();
                const elementRect = element.getBoundingClientRect();
                const offsetTopInContainer = elementRect.top - containerRect.top + contentColRef.current.scrollTop;
                const desiredScrollPosition = offsetTopInContainer - (contentColRef.current.clientHeight / 3);

                contentColRef.current.scrollTo({
                    top: desiredScrollPosition,
                    behavior: 'smooth'
                });
            }
        }
    };


    // --- Filtered Findings for Right Panel (based on activeDisclosureCode) ---
    const filteredFindings = useMemo(() => {
        if (!reportDetails || !activeDisclosureCode) {
            return { anomalies: [], coverage: [], repetitions: [] };
        }
        const { validation_findings } = reportDetails;
        if (!validation_findings) return { anomalies: [], coverage: [], repetitions: [] };

        const filterByCode = (finding: FindingDetail) => finding.disclosure_code === activeDisclosureCode;

        const activeAnomalies = validation_findings.anomalies?.filter(filterByCode) || [];
        const activeCoverage = validation_findings.coverage?.filter(filterByCode) || [];

        let activeRepetitions = validation_findings.repetitions?.filter(f =>
            f.disclosure_code === activeDisclosureCode ||
            (f.sentence1_location?.includes(activeDisclosureCode)) ||
            (f.sentence2_location?.includes(activeDisclosureCode))
        ) || [];

        if (activeRepetitions.length === 0 && validation_findings.repetitions) {
            activeRepetitions = validation_findings.repetitions.filter(f =>
                !f.disclosure_code && !f.sentence1_location && !f.sentence2_location
            );
        }
        return { anomalies: activeAnomalies, coverage: activeCoverage, repetitions: activeRepetitions };
    }, [reportDetails, activeDisclosureCode]);


    // --- Render Logic ---
    if (loading) return <div className="text-center py-5"><Spinner animation="border" /> <span className="ms-2">Loading report details...</span></div>;
    if (error) return <Alert variant="danger" className="mt-3">{error}</Alert>;
    if (!reportDetails) return <Alert variant="light" className="mt-3 text-center">No details to display for this report.</Alert>;

    if (reportDetails.generation_status === JobStatusApi.FAILED ||
        reportDetails.generation_status === JobStatusApi.PENDING ||
        reportDetails.generation_status === JobStatusApi.RUNNING ||
        (reportDetails.generation_status === JobStatusApi.COMPLETED && reportDetails.disclosure_chunks.length === 0)
    ) {
        // Simplified handling for these states, as per original code structure
        if (reportDetails.generation_status === JobStatusApi.FAILED) {
            return (
                <Alert variant="danger" className="mt-3 report-detail-container">
                    <div className="d-flex justify-content-between align-items-center">
                        <h4><AlertTriangle size={24} className="me-2"/>Report Generation Failed</h4>
                        {onClose && <Button variant="close" onClick={onClose} aria-label="Close" />}
                    </div>
                    <p><strong>Job ID:</strong> {reportDetails.job_id}</p>
                    {reportDetails.error_message && <p><strong>Error:</strong> {reportDetails.error_message}</p>}
                </Alert>
            );
        }
        if (reportDetails.generation_status === JobStatusApi.PENDING || reportDetails.generation_status === JobStatusApi.RUNNING) {
            return (
                <Alert variant="info" className="mt-3 text-center report-detail-container">
                    <div className="d-flex justify-content-between align-items-center">
                        <h4><Spinner animation="border" size="sm" className="me-2"/>Report is Still Processing...</h4>
                        {onClose && <Button variant="close" onClick={onClose} aria-label="Close" />}
                    </div>
                    <p><strong>Job ID:</strong> {reportDetails.job_id}</p>
                    <p>Status: {reportDetails.generation_status}</p>
                </Alert>
            );
        }
        if (reportDetails.generation_status === JobStatusApi.COMPLETED && reportDetails.disclosure_chunks.length === 0) {
            return (
                <div className="mt-3 p-3 border rounded report-detail-container">
                    <div className="d-flex justify-content-between align-items-center mb-2">
                        <h4 className="mb-0">Report: {reportDetails.report_file_path?.split('/').pop() || `Job ${reportDetails.job_id.substring(0,13)}...`}</h4>
                        {onClose && <Button variant="light" size="sm" onClick={onClose}><XCircle size={16}/> Close</Button>}
                    </div>
                    <Badge bg="secondary" className="mb-2">{reportDetails.job_id}</Badge>
                    {reportDetails.report_file_path && (
                        <div className="mb-3">
                            <Button variant="primary" size="sm" href={reportDetails.report_file_path} target="_blank" download>
                                <Download size={16} className="me-2" /> Download Full Report ({reportDetails.report_file_path.split('.').pop()?.toUpperCase()})
                            </Button>
                        </div>
                    )}
                    <Alert variant="warning" className="text-center">
                        <Info size={24} className="me-2"/>
                        The report generation completed, but no individual disclosure sections were found.
                    </Alert>
                </div>
            );
        }
    }

    return (
        <div ref={mainContainerRef} className="mt-3 p-3 border rounded report-detail-container">
            {/* Header */}
            <Row className="mb-3 align-items-center">
                <Col>
                    <h4 className="mb-0 report-title-text">
                        Report: {reportDetails.report_file_path?.split('/').pop() || `Job ${reportDetails.job_id.substring(0,13)}...`}
                    </h4>
                    <Badge bg="secondary" pill className="report-job-id-badge">{reportDetails.job_id}</Badge>
                </Col>
                {reportDetails.report_file_path && (
                    <Col xs="auto" className="text-end">
                        <Button variant="outline-primary" size="sm" href={reportDetails.report_file_path} target="_blank" download>
                            <Download size={16} className="me-2" /> Download ({reportDetails.report_file_path.split('.').pop()?.toUpperCase()})
                        </Button>
                    </Col>
                )}
                {onClose && (
                    <Col xs="auto" className="text-end">
                        <Button variant="outline-secondary" size="sm" onClick={onClose}>
                            <XCircle size={16} className="me-1"/> Close Details
                        </Button>
                    </Col>
                )}
            </Row>
            <hr className="mt-0"/>

            {/* Main Content Grid */}
            <Row>
                {/* Left: Disclosure Navigation */}
                <Col md={3} className="disclosure-nav-col-detail">
                    <div className="disclosure-nav-detail">
                        <h6>Disclosures ({reportDetails.disclosure_chunks.length})</h6>
                        <ListGroup variant="flush">
                            {reportDetails.disclosure_chunks.map(chunk => (
                                <ListGroup.Item
                                    key={chunk.disclosure_code} action
                                    onClick={(e) => { e.preventDefault(); handleDisclosureChunkClick(chunk.disclosure_code); }}
                                    active={chunk.disclosure_code === activeDisclosureCode}
                                    title={chunk.disclosure_name || chunk.disclosure_code}
                                >
                                    {chunk.disclosure_code}
                                    <small className="d-block text-muted text-truncate">{chunk.disclosure_name || 'View Section'}</small>
                                </ListGroup.Item>
                            ))}
                        </ListGroup>
                    </div>
                </Col>

                {/* Middle: Report Content Chunks */}
                <Col md={5} className="report-content-col-detail" ref={contentColRef}>
                    <div className="disclosure-chunks-container-detail">
                        {reportDetails.disclosure_chunks.map((chunk: DisclosureChunkDetail) => {
                            const anomaliesForThisChunk = getAnomaliesForChunk(chunk.disclosure_code);
                            return (
                                <Card
                                    key={chunk.disclosure_code}
                                    id={`detail-chunk-${chunk.disclosure_code}`}
                                    ref={el => disclosureRefs.current[chunk.disclosure_code] = el}
                                    className={`mb-3 disclosure-chunk-detail ${chunk.disclosure_code === activeDisclosureCode ? 'active' : ''}`}
                                    data-disclosure-code={chunk.disclosure_code}
                                >
                                    <Card.Header onClick={() => handleDisclosureChunkClick(chunk.disclosure_code)}>
                                        <strong>{chunk.disclosure_code}</strong> - {chunk.disclosure_name || 'Unnamed Disclosure'}
                                    </Card.Header>
                                    <Card.Body>
                                        <pre className="generated-text-display-detail">
                                            {renderHighlightedText(
                                                chunk.generated_text,
                                                anomaliesForThisChunk,
                                                activeHighlightKey,
                                                chunk.disclosure_code
                                            )}
                                        </pre>
                                    </Card.Body>
                                </Card>
                            );
                        })}
                    </div>
                </Col>

                {/* Right: Analysis Findings (AI Co-pilot) */}
                <Col md={4} className="analysis-findings-col-detail">
                    <div className="ai-copilot-header">
                        <Bot size={28} className="copilot-icon" />
                        <h6>
                            AI Co-pilot Analysis
                            {activeDisclosureCode ?
                                <Badge bg="primary" pill className="ms-2 align-middle">{activeDisclosureCode}</Badge> :
                                <Badge bg="light" text="dark" pill className="ms-2 align-middle">Select Section</Badge>
                            }
                        </h6>
                    </div>

                    <Accordion defaultActiveKey={['0', '1', '2']} alwaysOpen className="mt-3">
                        {/* Anomalies Accordion */}
                        <Accordion.Item eventKey="0" className="mb-3">
                            <Accordion.Header>
                                <div className="accordion-button-content">
                                    <AlertOctagon size={20} className="icon text-danger" />
                                    <span className="title">Anomalies</span>
                                </div>
                                <div className="accordion-button-badges">
                                    <Badge bg="danger" pill>{filteredFindings.anomalies.length}</Badge>
                                </div>
                            </Accordion.Header>
                            <Accordion.Body>
                                {filteredFindings.anomalies.length === 0 ? (
                                    <p className="no-findings-message">No specific anomalies for this section.</p>
                                ) : (
                                    <ListGroup variant="flush">
                                        {filteredFindings.anomalies.map((item, idx) => {
                                            const anomalyKey = getAnomalyKey(activeDisclosureCode!, idx);
                                            return (
                                                <ListGroup.Item
                                                    key={anomalyKey}
                                                    action
                                                    onClick={() => handleAnomalyItemClick(anomalyKey)}
                                                    className={activeHighlightKey === anomalyKey ? 'active-finding-item' : ''}
                                                >
                                                    <div className="finding-title">
                                                        <span>{item.anomaly_type || 'Anomaly'}</span>
                                                        {getSeverityBadge(item.severity)}
                                                    </div>
                                                    <p className="finding-description mb-1">{item.description}</p>
                                                    {item.text_quote && <small className="finding-quote"><em>"{item.text_quote}"</em></small>}
                                                </ListGroup.Item>
                                            );
                                        })}
                                    </ListGroup>
                                )}
                            </Accordion.Body>
                        </Accordion.Item>

                        {/* Coverage Gaps Accordion */}
                        <Accordion.Item eventKey="1" className="mb-3">
                            <Accordion.Header>
                                <div className="accordion-button-content">
                                    <FileSearchIcon size={20} className="icon text-info" />
                                    <span className="title">Coverage Gaps</span>
                                </div>
                                <div className="accordion-button-badges">
                                    <Badge bg="info" text="dark" pill>{filteredFindings.coverage.length}</Badge>
                                </div>
                            </Accordion.Header>
                            <Accordion.Body>
                                {filteredFindings.coverage.length === 0 ? (
                                    <p className="no-findings-message">No specific coverage gaps for this section.</p>
                                ) : (
                                    <ListGroup variant="flush">
                                        {filteredFindings.coverage.map((item, idx) => (
                                            <ListGroup.Item key={`detail-coverage-${idx}`}>
                                                <div className="finding-title">
                                                    <span>{item.concern_type || 'Gap'}</span>
                                                    {getSeverityBadge(item.severity)}
                                                </div>
                                                <p className="finding-description mb-0">{item.description}</p>
                                            </ListGroup.Item>
                                        ))}
                                    </ListGroup>
                                )}
                            </Accordion.Body>
                        </Accordion.Item>

                        {/* Repetitions Accordion */}
                        <Accordion.Item eventKey="2" className="mb-3">
                            <Accordion.Header>
                                <div className="accordion-button-content">
                                    <Repeat size={20} className="icon text-primary" />
                                    <span className="title">Repetitions</span>
                                </div>
                                <div className="accordion-button-badges">
                                    <Badge bg="primary" pill>{filteredFindings.repetitions.length}</Badge>
                                </div>
                            </Accordion.Header>
                            <Accordion.Body>
                                {filteredFindings.repetitions.length === 0 ? (
                                    <p className="no-findings-message">No significant repetitions for this section.</p>
                                ) : (
                                    <ListGroup variant="flush">
                                        {filteredFindings.repetitions.map((item, idx) => (
                                            <ListGroup.Item key={`detail-repetition-${idx}`}>
                                                <div className="finding-title">
                                                    <span>{item.type || 'Repetitive'} {item.score && `(Score: ${item.score.toFixed(2)})`}</span>
                                                    {getSeverityBadge(item.severity)}
                                                </div>
                                                {item.sentence1_text && <p className="finding-description text-truncate mb-1" title={item.sentence1_text}><em>S1: "{item.sentence1_text}"</em> <small className="finding-meta">({item.sentence1_location || 'Loc1'})</small></p>}
                                                {item.sentence2_text && <p className="finding-description text-truncate mb-0" title={item.sentence2_text}><em>S2: "{item.sentence2_text}"</em> <small className="finding-meta">({item.sentence2_location || 'Loc2'})</small></p>}
                                                {!item.sentence1_text && item.description && <p className="finding-description mb-0">{item.description}</p>}
                                            </ListGroup.Item>
                                        ))}
                                    </ListGroup>
                                )}
                            </Accordion.Body>
                        </Accordion.Item>
                    </Accordion>
                </Col>
            </Row>
        </div>
    );
};

export default ReportDetailDisplay;