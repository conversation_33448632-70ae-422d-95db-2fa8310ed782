import React, { useState, useCallback, useEffect } from 'react'; // Import useEffect
import {
    Con<PERSON>er,
    Row,
    Col,
    Card,
    Table,
    Spinner,
    Alert,
    ProgressBar,
    Modal,
    Button,
    ListGroup,
    Tooltip,
    OverlayTrigger,
    Badge,
} from 'react-bootstrap';
import { useDropzone } from 'react-dropzone';
import {
    UploadCloud,
    File as FileIcon,
    CheckCircle,
    XCircle,
    Loader,
    Leaf,
    Users,
    Scale,
    Compass,
    AlertCircle,
    Info,
    BarChart3,
    Hash,
    Percent,
    Sigma,
    DollarSign,
    Eye,
    RefreshCw,
    Trash2,
    FileText,
    Database,
    ListChecks,
    HelpCircle,
    Calendar
} from 'lucide-react';
import './css/AiUploadPage.css'; // Ensure this CSS file exists

// --- Interfaces ---
type CategoryType = 'General' | 'Environmental' | 'Social' | 'Governance';

interface ExtractedDataPoint {
    id: string;
    category: CategoryType;
    metric: string;
    value: string | number;
    unit?: string;
    sourcePage?: number;
    confidence?: number;
    notes?: string;
    disclosure_requirement?: string;
    source_id?: number | string;
    data_source_ids?: (number | string)[];
    source_chunk_index?: number;
}

interface UploadedFile {
    id: number;
    file: File;
    status: 'pending' | 'uploading' | 'processing' | 'completed' | 'error';
    progress: number;
    error?: string;
    uploadDate: Date;
    extractedCategories?: CategoryType[];
    extractedData?: ExtractedDataPoint[];
    backendJobId?: string;
}

interface CategoryStyle {
    name: CategoryType;
    color: string;
    icon: React.ReactNode;
}

const esgCategoryStyles: Record<CategoryType, CategoryStyle> = {
    General:       { name: 'General',       color: '#6c757d', icon: <Compass size={18} /> },
    Environmental: { name: 'Environmental', color: '#198754', icon: <Leaf size={18} /> },
    Social:        { name: 'Social',        color: '#fd7e14', icon: <Users size={18} /> },
    Governance:    { name: 'Governance',    color: '#c32020', icon: <Scale size={18} /> },
};

const API_BASE_URL =  'http://localhost:8000';
const API_UPLOAD_URL = `${API_BASE_URL}/api/v1/jobs/extract`;

interface BackendJobContext {
    job_id: string;
    job_type: string;
    params: Record<string, any>;
    status: 'PENDING' | 'RUNNING' | 'COMPLETED' | 'FAILED';
    current_step?: string | null;
    results?: ExtractedDataPoint[] | null;
    error_message?: string | null;
    intermediate_data?: Record<string, any>;
}

const AiUploadPage: React.FC = () => {
    const [uploadedFiles, setUploadedFiles] = useState<UploadedFile[]>([]);
    const [showDetailsModal, setShowDetailsModal] = useState(false);
    const [selectedFileDataForModal, setSelectedFileDataForModal] = useState<UploadedFile | null>(null);
    const [previewUrl, setPreviewUrl] = useState<string | null>(null);

    // Debug log
    useEffect(() => {
        console.log('--- UploadedFiles State Changed ---', uploadedFiles);
    }, [uploadedFiles]);

    // Create and clean up PDF preview URL
    useEffect(() => {
        if (selectedFileDataForModal) {
            const url = URL.createObjectURL(selectedFileDataForModal.file);
            setPreviewUrl(url);
            return () => {
                URL.revokeObjectURL(url);
                setPreviewUrl(null);
            };
        } else {
            setPreviewUrl(null);
        }
    }, [selectedFileDataForModal]);

    const handleFileUpload = useCallback(async (
        fileToUpload: UploadedFile
    ) => {
        setUploadedFiles(prev =>
            prev.map(f =>
                f.id === fileToUpload.id
                    ? { ...f, status: 'uploading', progress: 0, error: undefined, backendJobId: undefined }
                    : f
            )
        );

        const formData = new FormData();
        formData.append('file', fileToUpload.file);

        try {
            setUploadedFiles(prev =>
                prev.map(f =>
                    f.id === fileToUpload.id ? { ...f, status: 'processing', progress: 50 } : f
                )
            );

            const response = await fetch(API_UPLOAD_URL, { method: 'POST', body: formData });
            let result: BackendJobContext;
            let errorToThrow: Error | null = null;

            try {
                result = await response.json();
            } catch (jsonError) {
                const text = await response.text();
                errorToThrow = new Error(`Non-JSON response: ${text}`);
                result = { status: 'FAILED', error_message: errorToThrow.message } as any;
            }

            if (!response.ok) {
                if (!errorToThrow) {
                    errorToThrow = new Error(result.error_message || `HTTP ${response.status}`);
                }
                throw errorToThrow;
            }

            if (result.status === 'COMPLETED') {
                const resultsData = Array.isArray(result.results) ? result.results : [];
                const categories = Array.from(new Set(resultsData.map(dp => dp.category))).sort() as CategoryType[];

                setUploadedFiles(prev => prev.map(f =>
                    f.id === fileToUpload.id
                        ? {
                            ...f,
                            status: 'completed',
                            progress: 100,
                            extractedCategories: categories,
                            extractedData: resultsData,
                            backendJobId: result.job_id,
                        }
                        : f
                ));
            } else {
                throw new Error(result.error_message || `Job ended with status ${result.status}`);
            }

        } catch (error: any) {
            setUploadedFiles(prev => prev.map(f =>
                f.id === fileToUpload.id
                    ? { ...f, status: 'error', progress: 0, error: error.message, extractedCategories: [], extractedData: [] }
                    : f
            ));
        }
    }, []);

    const onDrop = useCallback((acceptedFiles: File[]) => {
        const newFiles: UploadedFile[] = acceptedFiles.map((file, idx) => ({
            id: Date.now() + idx,
            file,
            status: 'pending',
            progress: 0,
            uploadDate: new Date(),
        }));
        setUploadedFiles(prev => [...newFiles, ...prev].sort((a, b) => b.uploadDate.getTime() - a.uploadDate.getTime()));
        newFiles.forEach(nf => handleFileUpload(nf));
    }, [handleFileUpload]);



    // --- Other handlers and helpers (No changes needed) ---
    const { getRootProps, getInputProps, isDragActive, isDragAccept, isDragReject } = useDropzone({ onDrop, accept: { 'application/pdf': ['.pdf'] } });
    const getFileExtension = (file: File) => { /* ... */ const parts = file.name.split('.'); return parts.length > 1 ? parts[parts.length - 1].toUpperCase() : 'N/A'; };
    const handleRetry = (fileId: number) => { /* ... */ const fileToRetry = uploadedFiles.find((f) => f.id === fileId); if (fileToRetry) { console.log(`Retrying upload for file ID: ${fileId}`); handleFileUpload(fileToRetry, { use_semantic_chunking: true }); } };
    const handleDelete = (fileId: number) => { /* ... */ console.log(`Deleting file ID: ${fileId}`); setUploadedFiles((prev) => prev.filter((f) => f.id !== fileId)); };
    const handleShowDetails = (fileId: number) => { /* ... */ const fileToShow = uploadedFiles.find((f) => f.id === fileId); console.log(`[handleShowDetails] Clicked FE ID: ${fileId}. File found:`, fileToShow); if (fileToShow && fileToShow.status === 'completed') { console.log(`[handleShowDetails] Setting modal data. Data length: ${fileToShow.extractedData?.length}`); setSelectedFileDataForModal(fileToShow); setShowDetailsModal(true); } else if (fileToShow) { console.log("[handleShowDetails] Details not available for status:", fileToShow.status); } else { console.log(`[handleShowDetails] File with FE ID ${fileId} not found.`); } };
    const handleCloseDetails = () => { /* ... */ setShowDetailsModal(false); setSelectedFileDataForModal(null); };
    const getDropzoneStyle = () => { /* ... */ let borderColor = '#dee2e6'; let backgroundColor = '#f8f9fa'; if (isDragAccept) { borderColor = '#198754'; backgroundColor = 'rgba(25, 135, 84, 0.05)'; } if (isDragReject) { borderColor = '#dc3545'; backgroundColor = 'rgba(220, 53, 69, 0.05)'; } if (isDragActive) { borderColor = '#0d6efd'; backgroundColor = 'rgba(13, 110, 253, 0.05)'; } return { borderColor, backgroundColor }; };
    const getStatusIcon = (status: UploadedFile['status'], errorMsg?: string) => { /* ... */ const iconProps = { size: 18 }; let tooltipText = status.charAt(0).toUpperCase() + status.slice(1); let iconElement: React.ReactNode; switch (status) { case 'uploading': iconElement = <Spinner animation="border" size="sm" variant="info" />; tooltipText = "Uploading..."; break; case 'processing': iconElement = <Loader {...iconProps} className="text-warning spinning" />; tooltipText = "Processing..."; break; case 'completed': iconElement = <CheckCircle {...iconProps} className="text-success" />; tooltipText = "Completed"; break; case 'error': iconElement = <XCircle {...iconProps} className="text-danger" />; tooltipText = `Error: ${errorMsg || 'Unknown error'}`; break; default: iconElement = <FileIcon {...iconProps} className="text-muted" />; tooltipText = "Pending"; break; } return (<OverlayTrigger placement="top" overlay={<Tooltip>{tooltipText}</Tooltip>}><span className="d-inline-block">{iconElement}</span></OverlayTrigger>); };
    const getCategoryBadge = (category: CategoryType) => { /* ... */ const style = esgCategoryStyles[category] || esgCategoryStyles.General; return (<OverlayTrigger key={category} placement="top" overlay={<Tooltip>{style.name}</Tooltip>}><span className="esg-badge d-inline-flex align-items-center" style={{ backgroundColor: style.color, color: '#fff', marginRight: '4px', padding: '0.3em 0.6em', fontSize: '0.75rem', borderRadius: '0.25rem', cursor: 'default' }}>{React.cloneElement(style.icon as React.ReactElement, { size: 14 })}<span className="ms-1 d-none d-md-inline">{category}</span></span></OverlayTrigger>); };
    const groupDataByCategory = (data: ExtractedDataPoint[] | undefined): Record<CategoryType, ExtractedDataPoint[]> => { /* ... */ const grouped: Record<CategoryType, ExtractedDataPoint[]> = { Environmental: [], Social: [], Governance: [], General: [] }; if (!data) return grouped; const validCategories = Object.keys(grouped) as CategoryType[]; data.forEach((item) => { if (validCategories.includes(item.category)) { grouped[item.category].push(item); } else { console.warn(`Unexpected category: ${item.category}`); grouped.General.push(item); } }); return grouped; };
    const renderDataPointIcon = (unit?: string, metric?: string) => { /* ... */ const iconProps = { size: 16, className: "text-muted me-2 flex-shrink-0" }; if (unit) { const lowerUnit = unit.toLowerCase(); if (lowerUnit.includes('%')) return <Percent {...iconProps} className="text-primary me-2 flex-shrink-0" />; if (lowerUnit.includes('co2') || lowerUnit.includes('ghg') || lowerUnit.includes('kg') || lowerUnit.includes('tonne')) return <Sigma {...iconProps} className="text-success me-2 flex-shrink-0" />; if (lowerUnit.includes('eur') || lowerUnit.includes('usd') || lowerUnit.includes('gbp') || lowerUnit.includes('$')) return <DollarSign {...iconProps} className="text-warning me-2 flex-shrink-0" />; if (lowerUnit.includes('mwh') || lowerUnit.includes('kwh') || lowerUnit.includes('gj')) return <Sigma {...iconProps} className="text-info me-2 flex-shrink-0" />; if (lowerUnit.includes('m³') || lowerUnit.includes('liter')) return <Hash {...iconProps} className="text-info me-2 flex-shrink-0" />; if (lowerUnit.includes('hour') || lowerUnit.includes('day')) return <Calendar {...iconProps} className="text-secondary me-2 flex-shrink-0" />; } if (metric?.toLowerCase().includes('number of') || metric?.toLowerCase().includes('count')) return <Hash {...iconProps} className="text-secondary me-2 flex-shrink-0" />; if (metric?.toLowerCase().includes('ratio') || metric?.toLowerCase().includes('rate')) return <Percent {...iconProps} className="text-primary me-2 flex-shrink-0" />; return <BarChart3 {...iconProps} />; };


    // --- Render Method (No changes needed in JSX structure) ---
    return (
        <>
            {/* ... Container, Header, Dropzone, Table, Modal JSX ... */}
            <Container fluid className="py-4 px-lg-5 ai-upload-page">
                {/* Header */}
                <Row className="justify-content-center mb-4">
                    <h2>AI Document Analysis</h2>
                    <p>Upload your sustainability reports (PDF). Our AI will analyze them for CSRD-relevant data points.</p>
                </Row>

                <Row className="justify-content-center">
                    {/* Dropzone Area */}
                    <Col md={11} lg={10} xl={9} className="mb-4">
                        <div {...getRootProps()} className={`dropzone-area modern-dropzone text-center p-4 p-lg-5 border border-2 border-dashed rounded-3 transition-all ${isDragActive ? 'active' : ''} ${isDragAccept ? 'accept' : ''} ${isDragReject ? 'reject' : ''}`} style={getDropzoneStyle()}>
                            <input {...getInputProps()} />
                            <div className="dropzone-content">
                                <div className={`dropzone-icon-bg d-inline-flex align-items-center justify-content-center rounded-circle mb-3 ${isDragActive ? 'pulsing' : ''}`} style={{ width: '80px', height: '80px', background: 'rgba(13, 110, 253, 0.1)' }}>
                                    <UploadCloud size={40} strokeWidth={1.5} className="text-primary" />
                                </div>
                                {isDragActive ? (isDragAccept ? <h5 className="mt-2 mb-1">Drop files here</h5> : <h5 className="mt-2 mb-1 text-danger">Invalid file type</h5>)
                                    : (<><h5 className="mt-2 mb-1">Drag & drop PDF files</h5><p className="text-muted mb-0">or click to browse</p></>)}
                                <p className="small text-muted mt-2 mb-0">(Supports PDF only)</p>
                            </div>
                            {isDragReject && <Alert variant="danger" className="mt-3 small py-1 px-2 mb-0">Only PDF files accepted.</Alert>}
                        </div>
                    </Col>

                    {/* Uploads Table */}
                    <Col md={12}>
                        <h4 className="mb-3 mt-2">Upload History</h4>
                        {uploadedFiles.length === 0 ? (
                            <Card className="shadow-sm border-0"><Card.Body className="text-center text-muted py-5"><Info size={40} className="mb-3" /><p>No files uploaded yet.</p></Card.Body></Card>
                        ) : (
                            <Card className="shadow-sm border-0">
                                <Card.Body className="p-0">
                                    <Table responsive hover className="mb-0 uploads-table align-middle table-modern">
                                        <thead>
                                        <tr>
                                            <th style={{ width: '5%', paddingLeft: '1.25rem' }} className="text-center">Status</th>
                                            <th>File Name</th>
                                            <th style={{ width: '8%' }}>Type</th>
                                            <th style={{ width: '10%' }}>Size</th>
                                            <th style={{ width: '12%' }}>Uploaded</th>
                                            <th style={{ width: '15%' }}>Info</th>
                                            <th>ESG Categories Found</th>
                                            <th style={{ width: '10%' }} className="text-center pe-3">Actions</th>
                                        </tr>
                                        </thead>
                                        <tbody>
                                        {uploadedFiles.map((f) => ( // Map over state variable
                                            <tr key={f.id}>
                                                <td className="text-center" style={{ paddingLeft: '1.25rem' }}>{getStatusIcon(f.status, f.error)}</td>
                                                <td className="text-break fw-medium"><FileIcon size={16} className="me-2 text-muted flex-shrink-0"/>{f.file.name}</td>
                                                <td><span className="badge bg-secondary bg-opacity-10 text-secondary-emphasis fw-normal font-monospace">{getFileExtension(f.file)}</span></td>
                                                <td className='font-monospace'>{(f.file.size / (1024 * 1024)).toFixed(2)} MB</td>
                                                <td className="text-muted small">{f.uploadDate.toLocaleDateString()} {f.uploadDate.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}</td>
                                                <td>
                                                    {['uploading', 'processing'].includes(f.status) && <ProgressBar now={f.status === 'uploading' ? 25 : 75} variant={f.status === 'processing' ? 'warning' : 'info'} animated style={{ height: '8px' }} className="small" />}
                                                    {f.status === 'completed' && <span className="text-success small d-inline-flex align-items-center"><CheckCircle size={14} className="me-1"/> Completed</span>}
                                                    {f.status === 'error' && <OverlayTrigger placement="top" overlay={<Tooltip>{f.error || 'Unknown error'}</Tooltip>}><span className="text-danger small d-inline-flex align-items-center" style={{cursor: 'help'}}><AlertCircle size={14} className="me-1"/> Error</span></OverlayTrigger>}
                                                    {f.status === 'pending' && <span className="text-muted small">Pending...</span>}
                                                </td>
                                                <td>
                                                    {/* Check f.extractedCategories which should be set correctly now */}
                                                    {f.status === 'completed' && f.extractedCategories && f.extractedCategories.length > 0 ? <div className="d-flex flex-wrap gap-1">{f.extractedCategories.map(cat => getCategoryBadge(cat))}</div>
                                                        : f.status === 'completed' ? <span className="text-muted small fst-italic">None found</span>
                                                            : ['error', 'pending'].includes(f.status) ? <span className="text-muted small">-</span>
                                                                : <Spinner animation="border" size="sm" variant="secondary" style={{width: '1rem', height: '1rem', borderWidth: '0.15em'}}/>}
                                                </td>
                                                <td className="text-center pe-3">
                                                    {/* Check f.extractedData which should be set correctly now */}
                                                    <OverlayTrigger placement="top" overlay={<Tooltip>View Extracted Data</Tooltip>}>
                                                         <span className="d-inline-block">
                                                            <Button variant="link" className="p-0 me-2 text-primary action-icon-btn" onClick={() => handleShowDetails(f.id)} disabled={f.status !== 'completed' || !f.extractedData || f.extractedData.length === 0} style={f.status !== 'completed' || !f.extractedData || f.extractedData.length === 0 ? { pointerEvents: 'none' } : {}}><Eye size={18} /></Button>
                                                         </span>
                                                    </OverlayTrigger>
                                                    {f.status === 'error' && (<OverlayTrigger placement="top" overlay={<Tooltip>Retry Upload</Tooltip>}><Button variant="link" className="p-0 me-2 text-warning action-icon-btn" onClick={() => handleRetry(f.id)}><RefreshCw size={18} /></Button></OverlayTrigger>)}
                                                    <OverlayTrigger placement="top" overlay={<Tooltip>Delete Upload</Tooltip>}><Button variant="link" className="p-0 text-danger action-icon-btn" onClick={() => handleDelete(f.id)}><Trash2 size={18} /></Button></OverlayTrigger>
                                                </td>
                                            </tr>
                                        ))}
                                        </tbody>
                                    </Table>
                                </Card.Body>
                            </Card>
                        )}
                    </Col>
                </Row>
            </Container>

            {/* Modal JSX (Should work correctly if state is updated) */}
            {/* ... */}
            <Modal show={showDetailsModal} onHide={handleCloseDetails} size="xl" centered scrollable  className="wide-modal"   /* ➟ see CSS below */>
                <Modal.Header closeButton>
                    <Modal.Title className="d-flex align-items-center fs-5">
                        <FileIcon size={20} className="me-2 text-primary" />
                        Extracted Data: <span className="fw-normal ms-1 text-muted">{selectedFileDataForModal?.file.name}</span>
                    </Modal.Title>
                </Modal.Header>
                <Modal.Body className="p-4 bg-light">
                    <Row>
                        {/* PDF Preview Column */}
                        <Col xl={4} lg={5} className="border-end pdf-pane">
                            {previewUrl ? (
                                <iframe
                                    src={previewUrl}
                                    title="PDF preview"
                                    className="w-100 h-100 border-0"
                                />
                            ) : (
                                <div className="d-flex justify-content-center align-items-center h-100 text-muted">
                                    No preview
                                </div>
                            )}
                        </Col>
                        {/* Extracted Data Column */}
                        <Col xl={8} lg={7} className="data-pane overflow-auto p-4" style={{ maxHeight: '80vh' }}>
                            {selectedFileDataForModal?.extractedData && selectedFileDataForModal.extractedData.length > 0 ? (
                                <>
                                    {Object.entries(groupDataByCategory(selectedFileDataForModal.extractedData))
                                        .filter(([, dataPoints]) => dataPoints.length > 0)
                                        .sort(([a], [b]) => {
                                            const order: CategoryType[] = ['General','Environmental','Social','Governance'];
                                            return order.indexOf(a as CategoryType) - order.indexOf(b as CategoryType);
                                        })
                                        .map(([category, dataPoints]) => {
                                            const style = esgCategoryStyles[category as CategoryType] || esgCategoryStyles.General;
                                            return (
                                        <Card key={category} className="mb-4 shadow-sm border-start border-5 modern-card" style={{ borderColor: `${style.color} !important` }}>
                                            <Card.Header className="d-flex align-items-center justify-content-between py-2 px-3" style={{ backgroundColor: 'rgba(0,0,0,0.02)' }}>
                                                <div className="d-flex align-items-center">
                                                    <span className="me-2" style={{ color: style.color }}>{React.cloneElement(style.icon as React.ReactElement, { size: 20 })}</span>
                                                    <h6 className="mb-0 fw-semibold">{style.name} Data Points</h6>
                                                </div>
                                                <Badge pill bg="secondary">{dataPoints.length}</Badge>
                                            </Card.Header>
                                            <ListGroup variant="flush" className="data-point-list">
                                                <ListGroup.Item className="py-2 px-3 bg-light d-none d-lg-block small text-muted fw-semibold"><Row><Col lg={4}>Metric / Value</Col><Col lg={3}>Disclosure Requirement</Col><Col lg={3}>Source IDs</Col><Col lg={2}>Context</Col></Row></ListGroup.Item>
                                                {dataPoints.map((dp) => (
                                                    <ListGroup.Item key={dp.id} className="py-3 px-3">
                                                        <Row className="align-items-start gy-2">
                                                            <Col xs={12} lg={4}>
                                                                <div className="d-flex align-items-start mb-1">{renderDataPointIcon(dp.unit, dp.metric)}<span className="fw-medium">{dp.metric || 'N/A'}</span></div>
                                                                <div className="ps-4 ms-2"><strong className="data-value fs-5 me-1">{dp.value}</strong>{dp.unit && <small className="text-muted">{dp.unit}</small>}</div>
                                                            </Col>
                                                            <Col xs={12} lg={3} className="small">
                                                                <div className="d-flex align-items-center text-muted mb-1"><FileText size={14} className="me-2 flex-shrink-0"/> Disclosure Ref.</div>
                                                                <OverlayTrigger placement="top" overlay={<Tooltip>{dp.disclosure_requirement || 'Not specified'}</Tooltip>}><span className="d-block ps-4 ms-1 text-dark text-truncate">{dp.disclosure_requirement || '-'}</span></OverlayTrigger>
                                                            </Col>
                                                            <Col xs={12} lg={3} className="small">
                                                                <div className="d-flex align-items-center text-muted mb-1"><Database size={14} className="me-2 flex-shrink-0"/> Source ID</div>
                                                                <Badge bg="light" text="dark" className="font-monospace fw-normal d-block ps-4 ms-1 mb-2">{dp.source_id ? `ID: ${dp.source_id}` : '-'}</Badge>
                                                                <div className="d-flex align-items-center text-muted mb-1 mt-1"><ListChecks size={14} className="me-2 flex-shrink-0"/> Related Data Points</div>
                                                                {dp.data_source_ids && dp.data_source_ids.length > 0 ? (<OverlayTrigger placement="top" overlay={<Tooltip>IDs: {dp.data_source_ids.join(', ')}</Tooltip>}><span className="d-block ps-4 ms-1 text-dark">{dp.data_source_ids.length} related ID(s)</span></OverlayTrigger>)
                                                                    : (<span className="d-block ps-4 ms-1 text-muted">-</span>)}
                                                            </Col>
                                                            <Col xs={12} lg={2} className="small">
                                                                <div className="d-flex align-items-center text-muted mb-1"><HelpCircle size={14} className="me-2 flex-shrink-0"/> Context</div>
                                                                <div className="ps-4 ms-1">
                                                                    {dp.confidence != null && <OverlayTrigger placement="top" overlay={<Tooltip>Extraction Confidence</Tooltip>}><Badge pill bg={dp.confidence > 0.7 ? "success-subtle" : "warning-subtle"} text={dp.confidence > 0.7 ? "success-emphasis" : "warning-emphasis"} className="me-1 mb-1">Conf: {(dp.confidence * 100).toFixed(0)}%</Badge></OverlayTrigger>}
                                                                    {dp.source_chunk_index != null && <OverlayTrigger placement="top" overlay={<Tooltip>Source Chunk Index</Tooltip>}><Badge pill bg="info-subtle" text="info-emphasis" className="me-1 mb-1">Chunk: {dp.source_chunk_index}</Badge></OverlayTrigger>}
                                                                    {dp.notes && <OverlayTrigger placement="top" overlay={<Tooltip>Notes: {dp.notes}</Tooltip>}><Info size={14} className="text-muted cursor-help ms-1" style={{cursor: 'help'}}/></OverlayTrigger>}
                                                                    {dp.confidence == null && dp.source_chunk_index == null && !dp.notes && <span className="text-muted">-</span>}
                                                                </div>
                                                            </Col>
                                                        </Row>
                                                    </ListGroup.Item>
                                                ))}
                                            </ListGroup>
                                        </Card>
                                    );
                                })}
                        </>
                    ) : (
                        <Alert variant="info" className="text-center d-flex align-items-center justify-content-center py-4">
                            <Info size={24} className="me-3" />
                            <div>{selectedFileDataForModal?.status !== 'completed' ? 'Details are available only after successful processing.' : 'No specific data points were extracted from this document.'}</div>
                        </Alert>
                    )}
                        </Col>
                    </Row>
                </Modal.Body>
                <Modal.Footer className="bg-light border-top-0">
                    <Button variant="outline-secondary" onClick={handleCloseDetails}>Close</Button>
                </Modal.Footer>
            </Modal>
        </>
    );
};

export default AiUploadPage;