// src/components/SubtopicItem.tsx
import React, { useState } from 'react';
import { CsrdSubtopic, EsrsDatapoint } from '../modules/types.ts';
import DataPointRenderer from './DataPointRenderer.tsx';

interface SubtopicItemProps {
    subtopic: CsrdSubtopic;
    onDataPointChange?: (datapointId: number, value: string) => void;
}

const SubtopicItem: React.FC<SubtopicItemProps> = ({ subtopic, onDataPointChange }) => {
    const [isOpen, setIsOpen] = useState(false);

    return (
        <div className="mb-3 border-l-4 border-blue-500 pl-3">
            <button
                onClick={() => setIsOpen(!isOpen)}
                className="w-full text-left py-2 px-3 bg-blue-100 hover:bg-blue-200 rounded text-blue-700 font-semibold flex justify-between items-center"
            >
                <span>{subtopic.csrdSubtopicId}: {subtopic.csrdSubtopicLabel} ({subtopic.datapoints.length} datapoints)</span>
                <span>{isOpen ? '▲' : '▼'}</span>
            </button>
            {isOpen && (
                <div className="mt-2 pl-4">
                    {subtopic.datapoints.length > 0 ? (
                        subtopic.datapoints.map((dp) => (
                            <DataPointRenderer key={dp.id} datapoint={dp} onChange={onDataPointChange} />
                        ))
                    ) : (
                        <p className="text-sm text-gray-500 italic">No datapoints for this subtopic.</p>
                    )}
                </div>
            )}
        </div>
    );
};

export default SubtopicItem;