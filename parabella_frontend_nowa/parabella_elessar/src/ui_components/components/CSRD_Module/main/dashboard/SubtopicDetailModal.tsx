// src/modules/csrd/components/SubtopicDetailModal.tsx
import React from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, Badge as BsBadge, OverlayTrigger, Tooltip } from 'react-bootstrap';
import {
    FileText as FileTextIconLucideReal, // Renamed
    Sparkles, ClipboardCopy,
    Lightbulb, FileCode2, BarChart3, AlertOctagon, CheckCircle, Info, Wand2,
    LayoutGrid
} from 'lucide-react';

// Use the specific Lucide icon for FileText, not the alias
const FileTextIconLucide = FileTextIconLucideReal;


import { ProcessedDocumentChunkDTO } from '../../api/csrdApiTypes.tsx';
import DataPointRenderer from './DataPointRenderer.tsx';


interface SubtopicDetailModalProps {
    show: boolean;
    onHide: () => void;
    selectedSubtopic: EnhancedCsrdSubtopic | null; // Use Enhanced type
    loadingModalContent: boolean;
    errorModalContent: string | null;
    focusedDatapointId: number | null;
    onDatapointFocus: (datapointId: number) => void;
    onDatapointUpdate: (datapointId: number, fieldName: string, newValue: any) => void;
    onAutofillDatapoint: (datapoint: EnhancedEsrsDatapoint, relevantChunks: ProcessedDocumentChunkDTO[]) => Promise<void>; // Use Enhanced type
    isAutofillingDpId: number | null;
    relevantDocumentChunks: ProcessedDocumentChunkDTO[];
    loadingChunks: boolean;
    errorChunks: string | null;
    aiInsightsForFocusedDP: AiInsight[];
    onApplyAiSuggestion: (suggestionContent: string) => void;
    companyIndustry?: string;
    renderCoverageIcon: (status: EnhancedEsrsDatapoint['coverageStatus'], size?: number) => React.ReactNode; // Added from dashboard for consistency if needed
}

const SubtopicDetailModal: React.FC<SubtopicDetailModalProps> = ({
                                                                     show,
                                                                     onHide,
                                                                     selectedSubtopic,
                                                                     loadingModalContent,
                                                                     errorModalContent,
                                                                     focusedDatapointId,
                                                                     onDatapointFocus,
                                                                     onDatapointUpdate,
                                                                     onAutofillDatapoint,
                                                                     isAutofillingDpId,
                                                                     relevantDocumentChunks,
                                                                     loadingChunks,
                                                                     errorChunks,
                                                                     aiInsightsForFocusedDP,
                                                                     onApplyAiSuggestion,
                                                                     // companyIndustry, // available if needed for context
                                                                     // renderCoverageIcon // available if needed
                                                                 }) => {

    const focusedDatapoint = selectedSubtopic?.datapoints?.find(dp => dp.id === focusedDatapointId);

    const renderedDataPoints = React.useMemo(() => {
        return selectedSubtopic?.datapoints?.map(dp => (
            <DataPointRenderer
                key={dp.id}
                datapoint={dp} // This dp should be EnhancedEsrsDatapoint with dataResponseSource
                isFocused={focusedDatapointId === dp.id}
                onFocus={onDatapointFocus}
                // autoFillValue is deprecated in favor of datapoint.dataResponse and datapoint.dataResponseSource
                onChange={onDatapointUpdate}
                onAutofill={async () => {
                    // Ensure relevantDocumentChunks is correctly passed (it's already a prop)
                    // The onAutofillDatapoint in dashboard will handle fetching chunks if this one doesn't
                    await onAutofillDatapoint(dp, relevantDocumentChunks || []);
                }}
                isAutofilling={isAutofillingDpId === dp.id}
                isReadOnly={false}
            />
        ))
    }, [selectedSubtopic, focusedDatapointId, onDatapointFocus, onDatapointUpdate, onAutofillDatapoint, isAutofillingDpId, relevantDocumentChunks]);


    return (
        <Modal show={show} onHide={onHide} size="xl" backdrop="static" centered scrollable={false} dialogClassName="modal-fullscreen-xl-down modal-extra-wide">
            <Modal.Header closeButton className="py-2 px-3 modal-header-custom">
                <Modal.Title className="modal-title-custom d-flex align-items-center">
                    <LayoutGrid size={20} className="me-2 text-primary flex-shrink-0" />
                    <span className="text-truncate">
                        {selectedSubtopic ? `${selectedSubtopic.csrd_subtopic_id} - ${selectedSubtopic.csrd_subtopic_label}` : 'Subtopic Datapoints'}
                    </span>
                </Modal.Title>
            </Modal.Header>

            <Modal.Body className="p-0 d-flex modal-body-splitview">
                <div className="datapoints-pane flex-grow-1 p-3 overflow-auto">
                    {loadingModalContent && <div className="text-center p-5"><Spinner animation="border" variant="primary" /></div>}
                    {errorModalContent && <Alert variant="danger" className="m-3">{errorModalContent}</Alert>}
                    {!loadingModalContent && !errorModalContent && selectedSubtopic && (
                        selectedSubtopic.datapoints && selectedSubtopic.datapoints.length > 0 ? (
                            renderedDataPoints
                        ) : (
                            <Alert variant="light" className="text-center m-3 py-4">
                                <Info size={30} className="mb-2 text-muted" />
                                <p className="mb-0">No datapoints defined for this subtopic.</p>
                            </Alert>
                        )
                    )}
                </div>

                <div className="ai-insights-pane p-3 border-start d-flex flex-column">
                    <div className="document-context-section mb-3 pb-3 border-bottom">
                        <div className="d-flex align-items-center mb-2">
                            <FileTextIconLucide size={20} className="me-2 text-info flex-shrink-0" />
                            <h6 className="mb-0 document-context-title fw-semibold">Document Context</h6>
                        </div>
                        {loadingChunks && (<div className="text-center p-2"><Spinner size="sm" animation="border" variant="secondary"/> <span className="ms-2 small text-muted">Loading context...</span></div>)}
                        {errorChunks && (<Alert variant="warning" className="small py-1 px-2 mb-1">{errorChunks}</Alert>)}
                        {!loadingChunks && !errorChunks && (
                            focusedDatapointId && relevantDocumentChunks && relevantDocumentChunks.length > 0 ? (
                                <div className="document-chunks-list small overflow-auto ps-1 pe-1" style={{ maxHeight: '200px' }}>
                                    {relevantDocumentChunks.map((chunk) => (
                                        <Card key={chunk.id} className="mb-2 bg-light-subtle border-0 shadow-sm">
                                            <Card.Body className="p-2">
                                                {chunk.documentName && (<p className="extra-small text-muted mb-1 fst-italic">
                                                    Source: {chunk.documentName}
                                                    {chunk.chunkIndex !== undefined ? ` (Chunk ${chunk.chunkIndex + 1})` : ''}
                                                    {chunk.matchScore !== undefined && <span className="ms-2">(Score: {(chunk.matchScore * 100).toFixed(0)}%)</span>}
                                                </p>)}
                                                <p className="mb-1 document-chunk-text" title={chunk.chunkText || undefined}>
                                                    {chunk.chunkSummary || (chunk.chunkText ? (chunk.chunkText.substring(0, 220) + (chunk.chunkText.length > 220 ? '...' : '')) : 'No text available.')}
                                                </p>
                                                <div className="text-end mt-1">
                                                    <Button variant="outline-secondary" size="sm" className="ai-action-button py-0 px-1" onClick={() => navigator.clipboard.writeText(chunk.chunkText || '')}>
                                                        <ClipboardCopy size={12} className="me-1"/> Copy Text
                                                    </Button>
                                                </div>
                                            </Card.Body>
                                        </Card>
                                    ))}
                                </div>
                            ) : focusedDatapointId ? (
                                <div className="text-center text-muted p-2 small fst-italic">
                                    <Info size={18} className="mb-1 opacity-50" />
                                    <p className="mb-0">No relevant context found in uploaded documents.</p>
                                    {(!relevantDocumentChunks || relevantDocumentChunks.length === 0) && <p className="extra-small">Ensure documents are uploaded and processed, and context exists for this datapoint.</p>}
                                </div>
                            ) : ( <div className="text-center text-muted p-2 small fst-italic">Select a datapoint to see context.</div> )
                        )}
                    </div>
                    <div className="ai-copilot-section flex-grow-1 d-flex flex-column">
                        <div className="d-flex align-items-center mb-1">
                            <Sparkles size={22} className="me-2 text-primary flex-shrink-0" />
                            <h5 className="mb-0 ai-insights-title">AI Co-Pilot</h5>
                        </div>
                        <p className="small text-muted mb-3">Intelligent assistance for your datapoints.</p>
                        {focusedDatapoint ? (
                            <>
                                <p className="small text-muted mb-2">
                                    Insights for: <strong className="text-dark">{focusedDatapoint.dataPointName}</strong>
                                </p>
                                <div className="flex-grow-1 overflow-auto ai-insights-content ps-1 pe-1 pb-2">
                                    {aiInsightsForFocusedDP.length > 0 ? aiInsightsForFocusedDP.map(insight => (
                                        <Card key={insight.id} className={`mb-2 ai-insight-card shadow-sm type-${insight.type}-border`}>
                                            <Card.Body className="p-2">
                                                <div className="d-flex justify-content-between align-items-start mb-1">
                                                    <div className="d-flex align-items-center flex-grow-1 overflow-hidden me-2">
                                                        {insight.type === 'suggestion' && <Lightbulb size={16} className="me-2 mt-0 text-info flex-shrink-0" />}
                                                        {insight.type === 'reference' && <FileCode2 size={16} className="me-2 mt-0 text-secondary flex-shrink-0" />}
                                                        {insight.type === 'benchmark' && <BarChart3 size={16} className="me-2 mt-0 text-success flex-shrink-0" />}
                                                        {insight.type === 'risk' && <AlertOctagon size={16} className="me-2 mt-0 text-danger flex-shrink-0" />}
                                                        <h6 className="small fw-semibold mb-0 flex-grow-1 text-truncate" title={insight.title}>{insight.title}</h6>
                                                    </div>
                                                    {insight.relevanceScore && (
                                                        <OverlayTrigger
                                                            placement="top"
                                                            overlay={<Tooltip id={`tooltip-relevance-${insight.id}`}>AI Confidence</Tooltip>}
                                                        >
                                                            <BsBadge pill bg="light" text="dark" className="fw-normal ms-auto relevance-badge flex-shrink-0">
                                                                {(insight.relevanceScore * 100).toFixed(0)}%
                                                            </BsBadge>
                                                        </OverlayTrigger>
                                                    )}  </div>
                                                <Card.Text className="small text-muted mb-2 insight-content-text" title={insight.content}> {insight.content} </Card.Text>
                                                {insight.source && <p className="extra-small text-muted mb-1 fst-italic">Source: {insight.source}</p>}
                                                <div className="text-end mt-2">
                                                    {insight.type === 'suggestion' && ( <Button variant="primary" size="sm" className="ai-action-button" onClick={() => onApplyAiSuggestion(insight.content)}> <CheckCircle size={14} className="me-1"/> Apply </Button> )}
                                                    <Button variant="outline-secondary" size="sm" className="ai-action-button ms-1" onClick={() => navigator.clipboard.writeText(insight.content)}> <ClipboardCopy size={14} className="me-1"/> Copy </Button>
                                                </div>
                                            </Card.Body>
                                        </Card>
                                    )) : ( <div className="text-center text-muted p-3 ai-empty-sub-state mt-2"> <Info size={28} className="mb-2 text-primary opacity-50" /> <p className="small mb-1 fw-medium">No specific AI insights.</p> <p className="small">AI has no specific insights for this datapoint yet.</p> </div> )}
                                </div>
                            </>
                        ) : ( <div className="text-center text-muted mt-4 flex-grow-1 d-flex flex-column justify-content-center align-items-center ai-empty-state p-3"> <Lightbulb size={40} className="mb-3 text-primary opacity-75" /> <h6 className="mb-1">AI Assistance Ready</h6> <p className="small">Click on a datapoint to activate AI Co-Pilot.</p> </div> )}
                    </div>
                </div>
            </Modal.Body>

            <Modal.Footer className="py-2 px-3 modal-footer-custom">
                <Button variant="outline-secondary" onClick={onHide}>Close</Button>
            </Modal.Footer>
        </Modal>
    );
};

export default SubtopicDetailModal;