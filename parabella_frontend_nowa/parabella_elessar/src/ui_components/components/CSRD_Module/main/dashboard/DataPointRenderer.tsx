// src/modules/csrd/components/DataPointRenderer.tsx
import React, { useState, ChangeEvent, useEffect, useMemo, useRef } from 'react';
import { Form, InputGroup, Card, Badge, Button, <PERSON>ert, <PERSON>dal, Spinner, OverlayTrigger, Tooltip } from 'react-bootstrap';
import { EsrsDatapoint } from '../modules/types.ts';
import {
    Type as TypeIconLucide,
    Tag,
    GitBranch,
    FileText as FileTextIconLucideReal,
    Hash,
    Percent as PercentIcon,
    CalendarDays,
    Table2,
    ToggleLeft,
    HelpCircle,
    Info,
    // XCircle, // Removed as per request
    DollarSign,
    Thermometer,
    Ruler,
    Database,
    Sigma,
    Palette,
    Wand2,
    Sparkles,
    Edit3 // Using Edit3 as a generic edit icon instead of Wand2 for textarea, Wand2 for Autofill button
} from 'lucide-react';
import {TextareaAutosize} from "@mui/material";

const FileTextIconLucide = FileTextIconLucideReal;


const NAN_SENTINEL_VALUES = ["Not Applicable / Value is NaN", "NaN", "N/A", "Not Applicable"];
const isEffectivelyNaNValue = (value: string | null | undefined): boolean => {
    if (value === null || value === undefined) return false;
    const trimmedValue = value.trim();
    return NAN_SENTINEL_VALUES.some(sentinel => sentinel.toLowerCase() === trimmedValue.toLowerCase());
};

const normalizeDataType = (rawDataType: string | null | undefined): string => {
    const lowerType = rawDataType?.toLowerCase().trim();
    if (!lowerType || lowerType === 'nan' || lowerType === '') {
        return 'narrative';
    }
    return lowerType;
};

const getDataTypeIcon = (dataType: string, dataUnit?: string | null): React.ReactNode => {
    switch (dataType) {
        case 'narrative': case 'semi-narrative': case 'text': case 'mdr-a': case 'mdr-t': return <TypeIconLucide size={16} />;
        case 'integer': case 'decimal': return <Sigma size={16} />;
        case 'monetary': return <DollarSign size={16} />;
        case 'mass': case 'volume': return <Ruler size={16} />;
        case 'energy': return <Thermometer size={16} />;
        case 'intensity': return <Thermometer size={16} />;
        case 'ghgemissions': return <Ruler size={16} />;
        case 'mdr-p': return <PercentIcon size={16} />;
        case 'percent': return <PercentIcon size={16} />;
        case 'date': case 'gyear': return <CalendarDays size={16} />;
        case 'boolean': return <ToggleLeft size={16} />;
        case 'alternative': return <Palette size={16} />;
        case 'table': return <Table2 size={16} />;
        default: return <Database size={16} />;
    }
};

interface DataPointRendererProps {
    datapoint: EsrsDatapoint;
    onChange?: (id: number, field: keyof EsrsDatapoint | string, value: any) => void;
    onFocus?: (datapointId: number) => void;
    onAutofill?: () => Promise<void>;
    isAutofilling?: boolean;
    isFocused?: boolean;
    isReadOnly?: boolean;
}

interface MockTableEditorModalProps {
    show: boolean;
    onHide: () => void;
    datapointName: string;
    initialData: string;
    onSave: (newData: string) => void;
    isReadOnly?: boolean;
}
const MockTableEditorModal: React.FC<MockTableEditorModalProps> = ({ show, onHide, datapointName, initialData, onSave, isReadOnly }) => {
    const [tableJson, setTableJson] = useState(initialData);
    useEffect(() => setTableJson(initialData), [initialData]);

    const handleSave = () => {
        try {
            JSON.parse(tableJson);
            onSave(tableJson);
            onHide();
        } catch (e) {
            alert("Invalid JSON format for table data. Please provide a valid JSON array of objects.");
        }
    };
    return (
        <Modal show={show} onHide={onHide} size="lg" centered backdrop="static">
            <Modal.Header closeButton>
                <Modal.Title><Table2 size={20} className="me-2" />Edit Table: {datapointName}</Modal.Title>
            </Modal.Header>
            <Modal.Body>
                <Alert variant="info" className="small d-flex align-items-center">
                    <Info size={18} className="me-2 flex-shrink-0"/>
                    <span>
                        Enter table data as a JSON array of objects. Each object represents a row.
                        Example: <code>{`[{"Column A": "Value1", "Column B": 10}, {"Column A": "Value2", "Column B": 20}]`}</code>
                    </span>
                </Alert>
                <Form.Control
                    as="textarea"
                    rows={12}
                    value={tableJson}
                    onChange={(e) => setTableJson(e.target.value)}
                    placeholder='e.g., [{"Header 1": "Row 1 Cell 1", "Header 2": "Row 1 Cell 2"}]'
                    className="font-monospace fs-sm"
                    readOnly={isReadOnly}
                />
            </Modal.Body>
            <Modal.Footer>
                <Button variant="outline-secondary" onClick={onHide}>Cancel</Button>
                {!isReadOnly && <Button variant="primary" onClick={handleSave}>Save Table Data</Button>}
            </Modal.Footer>
        </Modal>
    );
};

const DataPointRenderer: React.FC<DataPointRendererProps> = ({
                                                                 datapoint,
                                                                 onChange,
                                                                 onFocus,
                                                                 onAutofill,
                                                                 isAutofilling = false,
                                                                 isFocused = false,
                                                                 isReadOnly = false,
                                                             }) => {
    const [currentValue, setCurrentValue] = useState<string>('');
    const [showTableEditor, setShowTableEditor] = useState(false);
    const cardRef = useRef<HTMLDivElement>(null);

    useEffect(() => {
        const rawResponse = datapoint.dataResponse;
        let initialDisplayValue = '';

        if (rawResponse !== undefined && rawResponse !== null) {
            initialDisplayValue = String(rawResponse);
        } else if (isEffectivelyNaNValue(rawResponse)) {
            initialDisplayValue = '';
        }

        if (initialDisplayValue !== currentValue) {
            setCurrentValue(initialDisplayValue);
        }
    }, [datapoint.dataResponse]);


    useEffect(() => {
        if (isFocused && cardRef.current) {
            cardRef.current.scrollIntoView({ behavior: 'smooth', block: 'nearest', inline: 'start' });
        }
    }, [isFocused]);

    const handleValueChange = (newValue: any, field: keyof EsrsDatapoint | string = 'dataResponse') => {
        const finalValue = typeof newValue === 'boolean' ? newValue.toString() : newValue;
        setCurrentValue(finalValue);
        if (onChange) {
            onChange(datapoint.id, field, finalValue);
            if (field === 'dataResponse' && datapoint.dataResponseSource !== 'manual') {
                onChange(datapoint.id, 'dataResponseSource', 'manual');
            }
        }
    };

    // const handleClearInput = () => { // Removed as per request
    //     handleValueChange('');
    // };

    const handleCardClick = (e: React.MouseEvent<HTMLDivElement>) => {
        if (e.target instanceof Element && (e.target.closest('button, input, textarea, select'))) {
            return;
        }
        if (onFocus) {
            onFocus(datapoint.id);
        }
    };

    const {
        id,
        dataPointName,
        dataType: rawDataType,
        dataUnit,
        sourceId,
        conditionalDp,
        paragraph,
        relatedAr,
        dataResponseSource,
    } = datapoint;

    const dpAny = datapoint as any;
    const options = dpAny.options as string[] | undefined;
    const isRequired = dpAny.isRequired as boolean | undefined;
    const helperText = dpAny.helperText as string | undefined;

    const dataType = useMemo(() => normalizeDataType(rawDataType), [rawDataType]);
    const isMDRSource = useMemo(() => sourceId?.toUpperCase().startsWith('MDR') ?? false, [sourceId]);
    const effectiveConditionalDp = useMemo(() => (conditionalDp === true) || isMDRSource, [conditionalDp, isMDRSource]);

    const isCurrentValueEffectivelyNaN = isEffectivelyNaNValue(currentValue);
    const typeIcon = useMemo(() => getDataTypeIcon(dataType, dataUnit), [dataType, dataUnit]);
    const canAutofill = useMemo(() => !['boolean', 'alternative', 'table'].includes(dataType), [dataType]);
    const textAreaRef = useRef<HTMLTextAreaElement>(null); // Ref for autosize textarea

    const AiSourceBadge = () => {
        if (!dataResponseSource || dataResponseSource === 'manual' || !currentValue) return null;

        if (dataResponseSource === 'user-doc') {
            return (
                <OverlayTrigger placement="top" overlay={<Tooltip>Content generated by AI using your uploaded documents.</Tooltip>}>
                    <Badge pill bg="ai-doc" className="adv-ai-source-badge">
                        <FileTextIconLucide size={11} className="me-1" /> Doc AI
                    </Badge>
                </OverlayTrigger>
            );
        }
        if (dataResponseSource === 'ai-open-data') {
            return (
                <OverlayTrigger placement="top" overlay={<Tooltip>Draft content generated by AI based on general knowledge.</Tooltip>}>
                    <Badge pill bg="ai-draft" className="adv-ai-source-badge">
                        <Sparkles size={11} className="me-1" /> AI Draft
                    </Badge>
                </OverlayTrigger>
            );
        }
        return null;
    };

    const AutofillButton = () => { // This is the small Wand2 button
        if (!canAutofill || !onAutofill || isReadOnly) return null;

        return (
            <OverlayTrigger placement="top" overlay={<Tooltip>Autofill with AI</Tooltip>}>
                <Button
                    variant="light"
                    size="sm"
                    onClick={(e) => { e.stopPropagation(); onAutofill(); }}
                    disabled={isAutofilling}
                    className="adv-ai-button p-1" // Made padding smaller
                    aria-label="Autofill with AI"
                >
                    {isAutofilling ? (
                        <Spinner as="span" animation="border" size="sm" />
                    ) : (
                        <Wand2 size={14} /> // Slightly smaller icon
                    )}
                </Button>
            </OverlayTrigger>
        );
    };

    // Edit button for textareas - looks like the pencil icon in the image
    const EditButtonForTextarea = () => {
        if (isReadOnly) return null;
        return (
            <OverlayTrigger placement="top" overlay={<Tooltip>Edit Text</Tooltip>}>
                <Button
                    variant="light"
                    size="sm"
                    onClick={(e) => {
                        e.stopPropagation();
                        // Potentially focus the textarea, though it should focus on click anyway
                        const textarea = e.currentTarget.closest('.adv-textarea-wrapper')?.querySelector('textarea');
                        textarea?.focus();
                    }}
                    className="adv-edit-button p-1" // Similar style to AutofillButton
                    aria-label="Edit Text"
                >
                    <Edit3 size={14} />
                </Button>
            </OverlayTrigger>
        );
    }


    const renderInputControls = () => {
        const commonProps: any = {
            value: currentValue,
            onChange: (e: ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => handleValueChange(e.target.value),
            placeholder: helperText || `Enter value`,
            className: `adv-form-control ${isCurrentValueEffectivelyNaN ? 'input-effectively-nan' : ''}`,
            readOnly: isReadOnly || isAutofilling,
            onClick: (e: React.MouseEvent) => e.stopPropagation(),
            onFocus: () => onFocus && onFocus(id),
            "aria-readonly": isReadOnly ? true : undefined,
            "aria-describedby": helperText ? `dp-helper-${id}` : undefined,
        };

        switch (dataType) {
            case 'narrative': case 'semi-narrative': case 'text': case 'mdr-a': case 'mdr-t':
                return (
                    <div className="adv-textarea-wrapper position-relative">
                        {/* Using TextareaAutosize library */}
                        <TextareaAutosize
                            {...commonProps}
                            minRows={2} // Set a minimum number of rows
                            maxRows={15} // Optionally set a maximum
                            ref={textAreaRef} // Pass ref to TextareaAutosize
                            onHeightChange={(height) => console.log('Textarea height changed to:', height)} // Optional: for debugging
                        />
                        {/* If NOT using TextareaAutosize, and trying with Form.Control:
                        <Form.Control
                            as="textarea"
                            rows={4} // Initial rows, will be overridden by custom hook if it works
                            {...commonProps}
                            ref={textAreaRef} // This might not work as expected with Form.Control
                        />
                        */}
                        <div className="adv-textarea-actions">
                            <AiSourceBadge />
                            <AutofillButton />
                            <EditButtonForTextarea />
                        </div>
                    </div>
                );

            case 'integer': case 'decimal': case 'monetary': case 'mass': case 'volume': case 'energy': case 'intensity': case 'ghgemissions':
                return (
                    <InputGroup className="adv-input-group">
                        <InputGroup.Text>{typeIcon}</InputGroup.Text>
                        <Form.Control type="number" step={dataType === 'integer' ? '1' : 'any'} {...commonProps} />
                        {dataUnit && !isEffectivelyNaNValue(dataUnit) && <InputGroup.Text><Hash size={14} className="me-1 opacity-75"/>{dataUnit}</InputGroup.Text>}
                        <AiSourceBadge />
                        <AutofillButton />
                        {/* {!isReadOnly && currentValue && <Button variant="outline-secondary" size="sm" onClick={(e)=>{e.stopPropagation(); handleClearInput();}} className="adv-clear-btn"><XCircle size={16}/></Button>} // Removed clear button */}
                    </InputGroup>
                );

            case 'mdr-p': case 'percent':
                return (
                    <InputGroup className="adv-input-group">
                        <InputGroup.Text>{typeIcon}</InputGroup.Text>
                        <Form.Control type="number" step="any" min="0" {...commonProps} />
                        <InputGroup.Text>%</InputGroup.Text>
                        <AiSourceBadge />
                        <AutofillButton />
                        {/* {!isReadOnly && currentValue && <Button variant="outline-secondary" size="sm" onClick={(e)=>{e.stopPropagation(); handleClearInput();}} className="adv-clear-btn"><XCircle size={16}/></Button>} // Removed clear button */}
                    </InputGroup>
                );

            case 'date':
                return (
                    <InputGroup className="adv-input-group">
                        <InputGroup.Text>{typeIcon}</InputGroup.Text>
                        <Form.Control type="date" {...commonProps} />
                        <AiSourceBadge />
                        <AutofillButton />
                        {/* {!isReadOnly && currentValue && <Button variant="outline-secondary" size="sm" onClick={(e)=>{e.stopPropagation(); handleClearInput();}} className="adv-clear-btn"><XCircle size={16}/></Button>} // Removed clear button */}
                    </InputGroup>
                );
            case 'gyear':
                return (
                    <InputGroup className="adv-input-group">
                        <InputGroup.Text>{typeIcon}</InputGroup.Text>
                        <Form.Control type="number" min="1900" max="2100" step="1" {...commonProps} />
                        <AiSourceBadge />
                        <AutofillButton />
                        {/* {!isReadOnly && currentValue && <Button variant="outline-secondary" size="sm" onClick={(e)=>{e.stopPropagation(); handleClearInput();}} className="adv-clear-btn"><XCircle size={16}/></Button>} // Removed clear button */}
                    </InputGroup>
                );

            case 'boolean':
                return (
                    <Form.Check
                        type="switch"
                        id={`dp-bool-${id}`}
                        checked={currentValue === 'true'}
                        onChange={(e: ChangeEvent<HTMLInputElement>) => {e.stopPropagation(); handleValueChange(e.target.checked);}}
                        onClick={(e: React.MouseEvent) => e.stopPropagation()}
                        onFocus={() => onFocus && onFocus(id)}
                        label={currentValue === 'true' ? 'Yes / True' : 'No / False'}
                        disabled={isReadOnly}
                        className="adv-form-switch mt-2 mb-1"
                        aria-describedby={helperText ? `dp-helper-${id}` : undefined}
                    />
                );

            case 'alternative':
                if (options && options.length > 0) {
                    return (
                        <InputGroup className="adv-input-group">
                            <InputGroup.Text>{typeIcon}</InputGroup.Text>
                            <Form.Select {...commonProps} aria-label={`Select option for ${dataPointName}`}>
                                <option value="">-- Select an option --</option>
                                {options.map(opt => <option key={opt} value={opt}>{opt}</option>)}
                                {NAN_SENTINEL_VALUES.map(nanOpt => <option key={nanOpt} value={nanOpt}>{nanOpt}</option>)}
                            </Form.Select>
                            <AiSourceBadge />
                        </InputGroup>
                    );
                }
                return (
                    <InputGroup className="adv-input-group">
                        <InputGroup.Text>{typeIcon}</InputGroup.Text>
                        <Form.Control type="text" {...commonProps} placeholder={`Enter alternative value for ${dataPointName}`} />
                        <AiSourceBadge />
                        <AutofillButton />
                        {/* {!isReadOnly && currentValue && <Button variant="outline-secondary" size="sm" onClick={(e)=>{e.stopPropagation(); handleClearInput();}} className="adv-clear-btn"><XCircle size={16}/></Button>} // Removed clear button */}
                    </InputGroup>
                );

            case 'table':
                return (
                    <div className="adv-table-control">
                        <Button
                            variant="outline-primary"
                            onClick={(e) => {e.stopPropagation(); setShowTableEditor(true);}}
                            disabled={isReadOnly}
                            className="w-100 adv-table-edit-btn d-flex align-items-center justify-content-center"
                            onFocus={() => onFocus && onFocus(id)}
                        >
                            {React.cloneElement(typeIcon as React.ReactElement, { className: "me-2"})}
                            <span>
                                {currentValue && currentValue !== '[]' && currentValue.length > 2 ? 'Edit Table Data' : 'Add/Edit Table Data'}
                            </span>
                        </Button>
                        {currentValue && currentValue !== '[]' && currentValue.length > 2 && (
                            (() => {
                                try {
                                    const parsedTable = JSON.parse(currentValue);
                                    const rowCount = Array.isArray(parsedTable) ? parsedTable.length : 0;
                                    if (rowCount > 0) {
                                        return (
                                            <div className="adv-table-preview small text-muted mt-2 p-2 border rounded bg-light-subtle">
                                                <Info size={14} className="me-1"/>
                                                Table contains {rowCount} row(s). Click button to edit.
                                                <AiSourceBadge />
                                            </div>
                                        );
                                    }
                                } catch (e) { /* Do nothing */ }
                                return null;
                            })()
                        )}
                        <MockTableEditorModal
                            show={showTableEditor}
                            onHide={() => setShowTableEditor(false)}
                            datapointName={dataPointName}
                            initialData={currentValue || '[]'}
                            onSave={(newData) => handleValueChange(newData)}
                            isReadOnly={isReadOnly}
                        />
                    </div>
                );

            default:
                console.warn(`DataPointRenderer: Rendering default text input for unknown data type: ${dataType} for ${dataPointName}`);
                return (
                    <InputGroup className="adv-input-group">
                        <InputGroup.Text><HelpCircle size={16} /></InputGroup.Text>
                        <Form.Control type="text" {...commonProps} placeholder={`${dataPointName} (Unknown Type: ${dataType})`} />
                        <AiSourceBadge />
                        <AutofillButton />
                        {/* {!isReadOnly && currentValue && <Button variant="outline-secondary" size="sm" onClick={(e)=>{e.stopPropagation(); handleClearInput();}} className="adv-clear-btn"><XCircle size={16}/></Button>} // Removed clear button */}
                    </InputGroup>
                );
        }
    };

    const getBorderClass = () => {
        if (!dataResponseSource || dataResponseSource === 'manual' || !currentValue) return '';
        if (dataResponseSource === 'user-doc') return 'adv-datapoint-border-ai-doc';
        if (dataResponseSource === 'ai-open-data') return 'adv-datapoint-border-ai-draft';
        return '';
    };

    return (
        <Card
            ref={cardRef}
            className={`adv-datapoint-card ${isFocused ? 'adv-datapoint-focused' : ''} ${effectiveConditionalDp ? 'adv-datapoint-conditional' : ''} ${isReadOnly ? 'adv-datapoint-readonly' : ''} ${getBorderClass()}`}
            onClick={handleCardClick}
            role={onFocus ? 'button' : undefined}
            tabIndex={onFocus && !isFocused ? 0 : undefined}
            onKeyDown={(e) => { if ((e.key === 'Enter' || e.key === ' ') && onFocus && !isFocused) handleCardClick(e as any);}}
            aria-label={`${dataPointName}. Type: ${dataType}. ${isReadOnly ? "Read only." : "Editable."}`}
            aria-busy={isAutofilling}
        >
            <Card.Header className="adv-datapoint-header">
                <div className="adv-datapoint-title-wrapper">
                    {effectiveConditionalDp && (
                        <OverlayTrigger placement="top" overlay={<Tooltip>Conditional Datapoint</Tooltip>}>
                            <GitBranch size={18} className="adv-conditional-icon me-2 text-muted" />
                        </OverlayTrigger>
                    )}
                    <span className="adv-datapoint-name">{dataPointName}</span>
                    {isRequired && <span className="adv-required-indicator ms-1">*</span>}
                </div>
                <div className="adv-datapoint-badges">
                    {isMDRSource && <Badge pill bg="primary-soft" className="adv-data-badge me-1"><Tag size={11} className="me-1"/> MDR</Badge>}
                    <Badge pill bg="light" text="dark" className="adv-data-badge me-1">
                        {React.cloneElement(typeIcon as React.ReactElement, { size: 11, className: "me-1"})} {dataType.charAt(0).toUpperCase() + dataType.slice(1)}
                    </Badge>
                    {dataUnit && !isEffectivelyNaNValue(dataUnit) && !['percent', 'boolean', 'table', 'alternative'].includes(dataType) && (
                        <Badge pill bg="secondary-soft" className="adv-data-badge">
                            <Hash size={11} className="me-1"/> {dataUnit}
                        </Badge>
                    )}
                </div>
            </Card.Header>

            <Card.Body className="adv-datapoint-body">
                {renderInputControls()}
                {helperText && <Form.Text muted id={`dp-helper-${id}`} className="adv-helper-text d-block mt-2 small fst-italic">{helperText}</Form.Text>}
            </Card.Body>

            {(paragraph || relatedAr) && (
                <Card.Footer className="adv-datapoint-footer">
                    <div className="adv-metadata-refs">
                        {paragraph && <small title={`Paragraph Reference: ${paragraph}`}><FileTextIconLucide size={12} className="me-1" />Ref: {paragraph.substring(0, 20)}{paragraph.length > 20 ? '...' : ''}</small>}
                        {relatedAr && <small title={`AR Reference: ${relatedAr}`} className="ms-2"><GitBranch size={12} className="me-1" />AR: {relatedAr.substring(0, 10)}{relatedAr.length > 10 ? '...' : ''}</small>}
                    </div>
                </Card.Footer>
            )}
        </Card>
    );
};

export default DataPointRenderer;