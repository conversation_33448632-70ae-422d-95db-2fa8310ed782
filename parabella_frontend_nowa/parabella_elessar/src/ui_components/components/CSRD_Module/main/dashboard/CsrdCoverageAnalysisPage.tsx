// src/pages/CsrdCoverageAnalysisPage/CsrdCoverageAnalysisPage.tsx (create this file and folder)
import React, { useState, useEffect, useMemo } from 'react';
import {
    Container,
    Row,
    Col,
    Card,
    Spinner,
    Alert,
    ProgressBar,
    Table,
    Badge,
    Accordion,
    Tooltip,
    OverlayTrigger,
    Form,
    InputGroup,
    Button, // Import Button
} from 'react-bootstrap';
import {
    CheckCircle,
    XCircle,
    ClipboardList,
    Target,
    Activity,
    TrendingUp,
    Filter,
    Search, // Import Search icon
    Info,   // Import Info icon
    FileText, // Import FileText icon
} from 'lucide-react';
import { useCsrdProjectContext } from '../../context/CsrdProjectProvider'; // Adjust path
import { fetchCoverageAnalysis } from '../../api/csrdApi'; // Adjust path
import { CoverageAnalysisResultDto, CoverageDatapointDto } from '../../api/csrdApiTypes'; // Adjust path

import '../css/CsrdCoverageAnalysisPage.css'; // Create and add styles below

// Define colors for standards (optional, for visual flair)
const standardColors: Record<string, string> = {
    'ESRS E1': '#198754', // Green
    'ESRS E2': '#20c997',
    'ESRS E3': '#0dcaf0',
    'ESRS E4': '#6f42c1',
    'ESRS E5': '#fd7e14', // Orange
    'ESRS S1': '#fd7e14', // Orange
    'ESRS S2': '#ffc107',
    'ESRS S3': '#d63384',
    'ESRS S4': '#6610f2',
    'ESRS G1': '#c32020', // Red/Governance
    'General': '#6c757d', // Grey
};

const getStandardColor = (standard: string | null): string => {
    if (!standard) return standardColors['General'];
    // Find partial match if full name isn't present (e.g., "ESRS E1 - Climate Change")
    const matchedKey = Object.keys(standardColors).find(key => standard.startsWith(key));
    return matchedKey ? standardColors[matchedKey] : standardColors['General'];
};


const CsrdCoverageAnalysisPage: React.FC = () => {
    const { currentProject } = useCsrdProjectContext();
    const [analysisResult, setAnalysisResult] = useState<CoverageAnalysisResultDto | null>(null);
    const [isLoading, setIsLoading] = useState<boolean>(false);
    const [error, setError] = useState<string | null>(null);
    const [filterStatus, setFilterStatus] = useState<'all' | 'covered' | 'uncovered'>('all');
    const [filterStandard, setFilterStandard] = useState<string>('all');
    const [searchTerm, setSearchTerm] = useState<string>('');

    useEffect(() => {
        if (currentProject && currentProject.id) {
            setIsLoading(true);
            setError(null);
            setAnalysisResult(null); // Clear previous results
            fetchCoverageAnalysis(currentProject.id)
                .then(data => {
                    setAnalysisResult(data);
                    // Pre-populate standard filter options based on results
                    if (data && data.coverageByStandard && Object.keys(data.coverageByStandard).length > 0) {
                        // No action needed here now, filter options are derived dynamically
                    }
                })
                .catch(err => {
                    console.error("Error fetching coverage:", err);
                    setError(err.message || 'Failed to load coverage analysis.');
                })
                .finally(() => {
                    setIsLoading(false);
                });
        } else {
            setError("Please select a project to view coverage analysis.");
            setAnalysisResult(null);
        }
    }, [currentProject]); // Re-fetch when project changes

    // Memoized filtering logic
    const filteredDatapoints = useMemo(() => {
        if (!analysisResult?.datapoints) return [];

        return analysisResult.datapoints.filter(dp => {
            const matchesStatus = filterStatus === 'all'
                || (filterStatus === 'covered' && dp.covered)
                || (filterStatus === 'uncovered' && !dp.covered);

            const matchesStandard = filterStandard === 'all' || dp.esrsStandard === filterStandard;

            const matchesSearch = searchTerm === ''
                || dp.dataPointName?.toLowerCase().includes(searchTerm.toLowerCase())
                || dp.disclosureRequirement?.toLowerCase().includes(searchTerm.toLowerCase())
                || dp.esrsStandard?.toLowerCase().includes(searchTerm.toLowerCase())
                || dp.sourceId?.toLowerCase().includes(searchTerm.toLowerCase());


            return matchesStatus && matchesStandard && matchesSearch;
        });
    }, [analysisResult?.datapoints, filterStatus, filterStandard, searchTerm]);

    const uniqueStandards = useMemo(() => {
        if (!analysisResult?.coverageByStandard) return [];
        return Object.keys(analysisResult.coverageByStandard).sort();
    }, [analysisResult?.coverageByStandard]);


    // --- Render Helper Functions ---
    const renderProgressBar = (value: number) => {
        const variant = value >= 75 ? 'success' : value >= 40 ? 'warning' : 'danger';
        return (
            <ProgressBar
                now={value}
                variant={variant}
                label={`${value.toFixed(1)}%`}
                style={{ height: '20px', fontSize: '0.8rem' }}
                className="progress-bar-striped"
            />
        );
    };

    const renderCoveredDocsTooltip = (datapoint: CoverageDatapointDto) => (
        <Tooltip id={`tooltip-docs-${datapoint.esrsDatapointId}`}>
            {datapoint.coveringDocumentNames.length > 0
                ? (
                    <>
                        <strong>Covered by:</strong>
                        <ul className="list-unstyled mb-0 mt-1 small">
                            {datapoint.coveringDocumentNames.map((name, idx) => (
                                <li key={idx}><FileText size={12} className='me-1'/>{name}</li>
                            ))}
                            {/* Optionally add chunk IDs if useful: */}
                            {/* <li><small className='text-muted'>(Chunks: {datapoint.coveringChunkIds.join(', ')})</small></li> */}
                        </ul>
                    </>
                )
                : "Details not available"
            }
        </Tooltip>
    );

    // --- Main Render ---
    if (!currentProject) {
        return (
            <Container fluid className="py-4 px-lg-5 coverage-analysis-page">
                <Alert variant="warning">Please select a project first.</Alert>
            </Container>
        );
    }

    if (isLoading) {
        return (
            <Container fluid className="py-4 px-lg-5 coverage-analysis-page text-center">
                <Spinner animation="border" role="status" variant="primary">
                    <span className="visually-hidden">Loading Analysis...</span>
                </Spinner>
                <p className="mt-2 text-muted">Loading Coverage Analysis...</p>
            </Container>
        );
    }

    if (error) {
        return (
            <Container fluid className="py-4 px-lg-5 coverage-analysis-page">
                <Alert variant="danger">Error: {error}</Alert>
            </Container>
        );
    }

    if (!analysisResult || analysisResult.totalEsrsDatapoints === 0) {
        return (
            <Container fluid className="py-4 px-lg-5 coverage-analysis-page">
                <Alert variant="info" className="text-center">
                    <Info size={24} className="me-2" />
                    {analysisResult?.totalEsrsDatapoints === 0
                        ? "No ESRS datapoints configured in the system to analyze coverage against."
                        : "No analysis data available for this project yet. Ensure documents have been processed."
                    }
                </Alert>
            </Container>
        );
    }


    return (
        <Container fluid className="py-4 px-lg-5 coverage-analysis-page">
            {/* Header & Overall Summary */}
            <Row className="mb-4 align-items-center">
                <Col md={8}>
                    <h2>CSRD Coverage Analysis</h2>
                    <p className="text-muted">
                        Overview of required ESRS data points covered by processed documents for project: <strong>{currentProject.projectName}</strong>
                    </p>
                </Col>
                <Col md={4} className="text-md-end">
                    <Card className="bg-light border-0 shadow-sm p-2">
                        <small className="text-muted mb-1 text-center">Overall Coverage</small>
                        {renderProgressBar(analysisResult.overallCoveragePercentage)}
                    </Card>
                </Col>
            </Row>

            {/* Key Stats Cards */}
            <Row className="mb-4 g-3">
                <Col md={6} lg={3}>
                    <Card className="text-center shadow-sm border-0 h-100 stat-card">
                        <Card.Body>
                            <Target size={30} className="text-primary mb-2" />
                            <h4 className="mb-1">{analysisResult.totalEsrsDatapoints}</h4>
                            <Card.Subtitle className="text-muted small">Total Required Datapoints</Card.Subtitle>
                        </Card.Body>
                    </Card>
                </Col>
                <Col md={6} lg={3}>
                    <Card className="text-center shadow-sm border-0 h-100 stat-card">
                        <Card.Body>
                            <CheckCircle size={30} className="text-success mb-2"/>
                            <h4 className="mb-1">{analysisResult.totalCoveredDatapoints}</h4>
                            <Card.Subtitle className="text-muted small">Covered Datapoints</Card.Subtitle>
                        </Card.Body>
                    </Card>
                </Col>
                <Col md={6} lg={3}>
                    <Card className="text-center shadow-sm border-0 h-100 stat-card">
                        <Card.Body>
                            <XCircle size={30} className="text-danger mb-2" />
                            <h4 className="mb-1">{analysisResult.totalUncoveredDatapoints}</h4>
                            <Card.Subtitle className="text-muted small">Uncovered Datapoints</Card.Subtitle>
                        </Card.Body>
                    </Card>
                </Col>
                <Col md={6} lg={3}>
                    <Card className="text-center shadow-sm border-0 h-100 stat-card">
                        <Card.Body>
                            <TrendingUp size={30} className="text-info mb-2" />
                            <h4 className="mb-1">{analysisResult.overallCoveragePercentage.toFixed(1)}%</h4>
                            <Card.Subtitle className="text-muted small">Overall Coverage</Card.Subtitle>
                        </Card.Body>
                    </Card>
                </Col>
            </Row>

            {/* Coverage by Standard Accordion */}
            <Row className="mb-4">
                <Col>
                    <Accordion defaultActiveKey="0">
                        <Accordion.Item eventKey="0">
                            <Accordion.Header>
                                <Activity size={18} className="me-2"/> Coverage Breakdown by ESRS Standard
                            </Accordion.Header>
                            <Accordion.Body className="p-3">
                                {Object.keys(analysisResult.coverageByStandard).length > 0 ? (
                                    <Row className="g-3">
                                        {Object.entries(analysisResult.coverageByStandard)
                                            .sort(([stdA], [stdB]) => stdA.localeCompare(stdB)) // Sort alphabetically
                                            .map(([standard, stats]) => (
                                                <Col key={standard} md={6} lg={4}>
                                                    <Card className="h-100 shadow-sm category-card" style={{ borderLeft: `4px solid ${getStandardColor(standard)}` }}>
                                                        <Card.Body className="d-flex flex-column">
                                                            <Card.Title className="h6 mb-1 d-flex justify-content-between align-items-center">
                                                                <span style={{ color: getStandardColor(standard) }}>{standard}</span>
                                                                <Badge pill bg="light" text="dark" className="fw-normal">{stats.totalDatapoints} points</Badge>
                                                            </Card.Title>
                                                            <Card.Text className="small text-muted mb-2">
                                                                {stats.coveredDatapoints} covered / {stats.totalDatapoints - stats.coveredDatapoints} uncovered
                                                            </Card.Text>
                                                            <div className="mt-auto"> {/* Pushes progress bar to bottom */}
                                                                {renderProgressBar(stats.coveragePercentage)}
                                                            </div>
                                                        </Card.Body>
                                                    </Card>
                                                </Col>
                                            ))}
                                    </Row>
                                ) : (
                                    <Alert variant='light'>No data points found grouped by standard.</Alert>
                                )}
                            </Accordion.Body>
                        </Accordion.Item>
                    </Accordion>
                </Col>
            </Row>


            {/* Detailed Datapoints Table */}
            <Row>
                <Col>
                    <Card className="shadow-sm border-0">
                        <Card.Header className="d-flex justify-content-between align-items-center flex-wrap p-3">
                            <h5 className="mb-0 me-3"><ClipboardList size={20} className="me-2"/>Detailed Datapoint Status</h5>
                            {/* Filters */}
                            <div className="d-flex flex-wrap gap-2 align-items-center mt-2 mt-md-0">
                                <InputGroup size="sm" style={{ maxWidth: '250px' }}>
                                    <InputGroup.Text><Search size={14} /></InputGroup.Text>
                                    <Form.Control
                                        type="search"
                                        placeholder="Search name, req, standard..."
                                        value={searchTerm}
                                        onChange={(e) => setSearchTerm(e.target.value)}
                                    />
                                </InputGroup>
                                <Form.Select size="sm" value={filterStandard} onChange={(e) => setFilterStandard(e.target.value)} style={{ maxWidth: '180px' }}>
                                    <option value="all">All Standards</option>
                                    {uniqueStandards.map(std => (
                                        <option key={std} value={std}>{std}</option>
                                    ))}
                                </Form.Select>
                                <Form.Select size="sm" value={filterStatus} onChange={(e) => setFilterStatus(e.target.value as 'all' | 'covered' | 'uncovered')} style={{ maxWidth: '150px' }}>
                                    <option value="all">All Statuses</option>
                                    <option value="covered">Covered</option>
                                    <option value="uncovered">Uncovered</option>
                                </Form.Select>
                                {/* Optional: Add a clear filters button */}
                                {(filterStatus !== 'all' || filterStandard !== 'all' || searchTerm !== '') && (
                                    <Button
                                        variant="outline-secondary"
                                        size="sm"
                                        onClick={() => { setFilterStatus('all'); setFilterStandard('all'); setSearchTerm(''); }}
                                    >
                                        Clear Filters
                                    </Button>
                                )}
                            </div>
                        </Card.Header>
                        <Card.Body className="p-0">
                            <Table responsive hover className="mb-0 coverage-table align-middle">
                                <thead className="table-light">
                                <tr>
                                    <th style={{ width: '5%' }} className='text-center ps-3'>Status</th>
                                    <th style={{ width: '12%' }}>Standard</th>
                                    <th style={{ width: '25%' }}>Disclosure Requirement</th>
                                    <th>Data Point Name</th>
                                    <th style={{ width: '15%' }}>Source ID</th>
                                    <th style={{ width: '10%' }} className="text-center pe-3">Coverage</th>
                                </tr>
                                </thead>
                                <tbody>
                                {filteredDatapoints.length > 0 ? (
                                    filteredDatapoints.map((dp) => (
                                        <tr key={dp.esrsDatapointId}>
                                            <td className="text-center ps-3">
                                                {dp.covered ? (
                                                    <OverlayTrigger placement="top" overlay={<Tooltip id={`tooltip-cov-${dp.esrsDatapointId}`}>Covered</Tooltip>}>
                                                        <CheckCircle size={18} className="text-success" />
                                                    </OverlayTrigger>
                                                ) : (
                                                    <OverlayTrigger placement="top" overlay={<Tooltip id={`tooltip-uncov-${dp.esrsDatapointId}`}>Uncovered</Tooltip>}>
                                                        <XCircle size={18} className="text-danger" />
                                                    </OverlayTrigger>
                                                )}
                                            </td>
                                            <td>
                                                <Badge pill bg="" className="standard-badge" style={{ backgroundColor: `${getStandardColor(dp.esrsStandard)}20`, color: getStandardColor(dp.esrsStandard) }}>
                                                    {dp.esrsStandard || 'N/A'}
                                                </Badge>
                                            </td>
                                            <td className="small text-muted">{dp.disclosureRequirement || '-'}</td>
                                            <td className="fw-medium">{dp.dataPointName || '-'}</td>
                                            <td className="font-monospace small">{dp.sourceId || <span className='text-muted fst-italic'>None</span>}</td>
                                            <td className="text-center pe-3">
                                                {dp.covered && dp.coveringDocumentNames?.length > 0 ? (
                                                    <OverlayTrigger placement="left" overlay={renderCoveredDocsTooltip(dp)}>
                                                        <Button variant="link" size="sm" className="p-0 text-decoration-none info-icon-button">
                                                            <Info size={16} />
                                                        </Button>
                                                    </OverlayTrigger>
                                                ) : dp.covered ? (
                                                    <span className="text-muted small">-</span> // Covered, but no doc info? (Shouldn't happen with current logic)
                                                ) : (
                                                    <span className="text-muted small">-</span> // Uncovered
                                                )}
                                            </td>
                                        </tr>
                                    ))
                                ) : (
                                    <tr>
                                        <td colSpan={6} className="text-center text-muted py-4">
                                            No data points match the current filters.
                                        </td>
                                    </tr>
                                )}
                                </tbody>
                            </Table>
                        </Card.Body>
                        {filteredDatapoints.length > 0 && (
                            <Card.Footer className="text-muted small text-end py-2 px-3">
                                Showing {filteredDatapoints.length} of {analysisResult.datapoints.length} data points
                            </Card.Footer>
                        )}
                    </Card>
                </Col>
            </Row>

        </Container>
    );
};

export default CsrdCoverageAnalysisPage;