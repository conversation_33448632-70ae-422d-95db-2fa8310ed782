import React, { use<PERSON><PERSON><PERSON> } from 'react';
import {
    <PERSON><PERSON><PERSON>,
    <PERSON>,
    <PERSON>,
    <PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON>,
} from 'recharts';
import { CsrdTopic } from '../../api/csrdApiTypes.tsx';

/* ------------------------------------------------------------------
   🟡 1 |  Shared colours + helper
   ------------------------------------------------------------------ */
export const STATUS_COLORS = {
    Completed   : '#20c997',  // green
    'In Progress': '#ffc107', // yellow
    'Not Started': '#c32137', // red
} as const;

const DONUT_COLORS = Object.values(STATUS_COLORS);

/* ------------------------------------------------------------------
   🟡 2 |  Temporary random data generator
   ------------------------------------------------------------------ */
const generatePieData = () => ([
    { name: 'Completed',   value: Math.floor(Math.random() * 40) + 10 },
    { name: 'In Progress', value: Math.floor(Math.random() * 40) + 10 },
    { name: 'Not Started', value: Math.floor(Math.random() * 40) + 10 },
]);

/* ------------------------------------------------------------------
   🟡 3 |  Component
   ------------------------------------------------------------------ */
interface CriterionPieChartProps {
    topic: CsrdTopic;
}

const CriterionPieChart: React.FC<CriterionPieChartProps> = ({ topic }) => {
    /*  Don’t recalc the random data on every re-render               */
    const data = useMemo(generatePieData, []);

    return (
        <div style={{ width: '100%', height: 220 }}>
            <ResponsiveContainer>
                <PieChart>
                    <Pie
                        data={data}
                        cx="50%"
                        cy="50%"
                        innerRadius={50}   /* --→ gives us the donut “hole”   */
                        outerRadius={70}
                        paddingAngle={3}
                        dataKey="value"
                        nameKey="name"
                        labelLine={false}
                        /* Optional label – small charts can feel busy, so comment out
                           if you prefer */
                        label={({ name, value }) => `${name}: ${value}`}
                    >
                        {data.map((entry) => (
                            <Cell
                                key={entry.name}
                                fill={STATUS_COLORS[entry.name as keyof typeof STATUS_COLORS]}
                            />
                        ))}
                    </Pie>
                    <Tooltip formatter={(value: number) => `${value}%`} />
                    {/* Tiny legends can be skipped; include if you have space   */}

                </PieChart>
            </ResponsiveContainer>
        </div>
    );
};

export default CriterionPieChart;
