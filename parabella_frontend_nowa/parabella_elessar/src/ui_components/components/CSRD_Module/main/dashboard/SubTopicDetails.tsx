import React, { useEffect, useState, useCallback } from 'react';
import {
    <PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON>,
    <PERSON><PERSON>,
    Col,
    Container,
    <PERSON>,
    Spinner,
    Form // Import Form if needed for layout, but fields handle their own groups
} from 'react-bootstrap';
// Removed useNavigate as the submit button logic was commented out
// import { useNavigate } from 'react-router-dom';
// Removed BsArrow icons as navigation buttons were commented out
import { Bot, Send, AlertCircle } from 'lucide-react'; // Use Bot or Zap consistently

// Import field components (assuming paths are correct)
import TextFieldComponent from "../../InputTypeComponents/TextFieldComponent.tsx";
import NumberFieldComponent from "../../InputTypeComponents/NumberFieldComponent.tsx";
import RadioButtonGroupComponent from "../../InputTypeComponents/RadioButtonGroupComponent.tsx";
import CheckboxGroupComponent from "../../InputTypeComponents/CheckBoxGroupComponent.tsx";
import CheckboxComponent from "../../InputTypeComponents/CheckboxComponent.tsx";
import DropdownComponent from "../../InputTypeComponents/DropdownComponent.tsx";
import ListComponent from "../../InputTypeComponents/ListComponent.tsx";
import RadioButtonComponent from "../../InputTypeComponents/RadioButtonComponent.tsx"; // Make sure this exists or use Group

// Import CustomPromptOverlay (assuming path is correct)
import CustomPromptOverlay from '../CustomPromptOverlay.tsx';

// Import types (assuming paths are correct)
import { CsrdField, CsrdSubtopicSection, CsrdFieldOption } from '../../api/csrdApiTypes.tsx';
import { CsrdSubtopic } from '../modules/types.ts'; // Use augmented type
import api from "../../../DmaModule/context_module/api.ts";

interface PassedCompanyInfo {
    companyName: string;
    industry: string;
    // Add other fields if SubtopicDetails or its AI needs them directly
}

interface Props {
    subtopic: CsrdSubtopic; // Use augmented type which includes optional autoFillData
    companyInfo: PassedCompanyInfo; // Receive simplified company info
    userRoles: string[];
}

const SubtopicDetails: React.FC<Props> = ({ subtopic, companyInfo, userRoles }) => {
    // const navigate = useNavigate(); // Keep if navigation is needed later

    const [formValues, setFormValues] = useState<{ [key: string]: any }>({});
    const [aiLoading, setAiLoading] = useState(false);
    const [aiError, setAiError] = useState<string | null>(null);
    const [showCustomPrompt, setShowCustomPrompt] = useState(false);

    // Initialize form values based on subtopic fields AND merge autoFillData
    useEffect(() => {
        const initialValues: { [key: string]: any } = {};
        subtopic.sections?.forEach((section: CsrdSubtopicSection) => {
            section.fields?.forEach((field: CsrdField) => {
                // Set default based on field type
                switch (field.fieldType) {
                    case 'checkboxGroup':
                    case 'list':
                        initialValues[field.label] = []; break;
                    case 'checkbox':
                        initialValues[field.label] = false; break;
                    case 'numberField':
                        initialValues[field.label] = ''; break; // Or 0 if appropriate
                    case 'textField':
                    case 'dropdown':
                    case 'radioButtonGroup':
                    case 'radioButton':
                    default:
                        initialValues[field.label] = '';
                }
            });
        });

        // Merge any existing autoFillData from the parent AFTER setting defaults
        if (subtopic.autoFillData) {
            Object.keys(subtopic.autoFillData).forEach(key => {
                if (key in initialValues) { // Only merge if the field exists
                    initialValues[key] = subtopic.autoFillData![key];
                }
            });
        }
        setFormValues(initialValues);

    }, [subtopic]); // Rerun if the subtopic prop changes

    // Use useCallback for handlers passed to children if performance becomes an issue
    const handleChange = useCallback((fieldName: string, value: any) => {
        setFormValues((prev) => ({
            ...prev,
            [fieldName]: value,
        }));
    }, []);

    const handleListAdd = useCallback((fieldName: string, value: string) => {
        setFormValues((prev) => ({
            ...prev,
            [fieldName]: [...(prev[fieldName] || []), { id: Date.now(), value }], // Ensure array exists
        }));
    }, []);

    const handleListRemove = useCallback((fieldName: string, id: number) => {
        setFormValues((prev) => ({
            ...prev,
            [fieldName]: (prev[fieldName] || []).filter((item: any) => item.id !== id),
        }));
    }, []);

    // AI Auto-Fill for ALL fields in this subtopic
    const handleAutoFillAll = async () => {
        setAiLoading(true);
        setAiError(null);

        // Prepare payload using passed companyInfo
        const payload = {
            companyName: companyInfo.companyName,
            industry: companyInfo.industry,
            subtopicId: subtopic.csrdSubtopicId,
            subtopicLabel: subtopic.csrdSubtopicLabel,
            // Ensure sections and fields exist before mapping
            sections: subtopic.sections?.map((section) => ({
                id: section.id,
                sectionId: section.sectionId,
                sectionTitle: section.sectionTitle,
                fields: section.fields?.map((field) => ({
                    id: field.id,
                    label: field.label,
                    fieldType: field.fieldType,
                    options: field.options?.map((opt) => ({
                        id: opt.id,
                        optionValue: opt.optionValue,
                        child_field_id: opt.child_field_id,
                        parent_field_id: opt.parent_field_id,
                    })) || [], // Use empty array if options is null/undefined
                })) || [], // Use empty array if fields is null/undefined
            })) || [], // Use empty array if sections is null/undefined
        };

        try {
            const response = await api.post('/chat/generate-auto-fill-csrd', payload);
            const aiData = response.data.autoFilledFields;

            if (aiData && typeof aiData === 'object') {
                console.log('AI auto-fill successful:', aiData);
                // Update formValues state directly with all received fields
                setFormValues(prevValues => ({
                    ...prevValues, // Keep existing values
                    ...aiData     // Overwrite with AI data
                }));
                // Optional: Provide user feedback (e.g., toast message)
            } else {
                throw new Error('Invalid AI response format.');
            }
        } catch (error: any) {
            console.error('AI auto-fill failed:', error);
            setAiError(`Failed to auto-fill fields: ${error.message || 'Please try again.'}`);
        } finally {
            setAiLoading(false);
        }
    };


    // Example Submit Handler (if needed within the modal)
    // const handleSubmit = (e: React.FormEvent) => {
    //     e.preventDefault();
    //     console.log('Form Submitted in Modal:', formValues);
    //     // Send data to backend, then maybe close modal or show success
    // };

    // Render function for a single field
    const renderField = (field: CsrdField) => {
        const fieldName = field.label; // Assuming label is the unique key for state
        const value = formValues[fieldName];

        switch (field.fieldType) {
            case 'textField':
                return (
                    <TextFieldComponent
                        label={fieldName}
                        value={value || ''} // Ensure value is string
                        onChange={(e) => handleChange(fieldName, e.target.value)}
                        // Add props for per-field AI if implemented later
                        // onAutoFill={() => handleAutoFillField(fieldName)}
                        // aiLoading={fieldAiLoading[fieldName]}
                        // aiError={fieldAiError[fieldName]}
                    />
                );
            case 'numberField':
                return (
                    <NumberFieldComponent
                        label={fieldName}
                        value={value ?? ''} // Handle null/undefined, default to empty string for input
                        onChange={(e) => handleChange(fieldName, e.target.value)}
                    />
                );
            case 'radioButtonGroup': // Use the group component
                return (
                    <RadioButtonGroupComponent
                        label={fieldName}
                        options={field.options || []}
                        selectedValue={value || ''}
                        onChange={(val: string) => handleChange(fieldName, val)}
                    />
                );
            case 'radioButton': // Keep if distinct, otherwise use Group
                return (
                    <RadioButtonComponent // Ensure this component exists and works correctly
                        label={fieldName}
                        options={field.options || []}
                        selectedValue={value || ''}
                        onChange={(val: string) => handleChange(fieldName, val)}
                    />
                );
            case 'checkboxGroup':
                return (
                    <CheckboxGroupComponent
                        label={fieldName}
                        options={field.options || []}
                        checkedValues={Array.isArray(value) ? value : []} // Ensure value is array
                        onChange={(val: string) => {
                            const currentValues = Array.isArray(formValues[fieldName]) ? formValues[fieldName] : [];
                            const updatedValues = currentValues.includes(val)
                                ? currentValues.filter((v: string) => v !== val)
                                : [...currentValues, val];
                            handleChange(fieldName, updatedValues);
                        }}
                    />
                );
            case 'checkbox':
                return (
                    <CheckboxComponent
                        label={fieldName}
                        checked={!!value} // Ensure value is boolean
                        onChange={(checked: boolean) => handleChange(fieldName, checked)}
                    />
                );
            case 'dropdown':
                return (
                    <DropdownComponent
                        label={fieldName}
                        options={field.options || []}
                        selectedValue={value || ''}
                        onChange={(val: string) => handleChange(fieldName, val)}
                    />
                );
            case 'list':
                return (
                    <ListComponent
                        label={fieldName}
                        items={Array.isArray(value) ? value : []} // Ensure value is array
                        onAdd={(val: string) => handleListAdd(fieldName, val)}
                        onRemove={(id: number) => handleListRemove(fieldName, id)}
                    />
                );
            default:
                return (
                    <Alert variant="warning" key={field.id}>
                        Unsupported field type: {field.fieldType} - Label: {fieldName}
                    </Alert>
                );
        }
    };


    return (
        // No container needed as it's within Modal.Body
        // <form onSubmit={handleSubmit}> {/* Remove form tag if submit is handled outside/per field */}
        <>
            <Row className="mb-3 align-items-center">
                <Col>
                    {/* Removed redundant title/description - handled by Modal header/context */}
                    {aiError && <Alert variant="danger" onClose={() => setAiError(null)} dismissible><AlertCircle size={18} className="me-2"/>{aiError}</Alert>}
                </Col>
                <Col xs="auto" className="d-flex gap-2">
                    {/* Keep Custom Prompt button if needed */}
                    {/* <Button
                            variant="outline-secondary"
                            size="sm"
                            onClick={() => setShowCustomPrompt(true)}
                            className="d-flex align-items-center"
                        >
                            <Send size={16} className="me-1" /> Custom Prompt
                        </Button> */}

                    <Button
                        variant="primary" // Make AI Fill primary action here
                        size="sm"
                        onClick={handleAutoFillAll}
                        disabled={aiLoading}
                        className="d-flex align-items-center"
                    >
                        {aiLoading ? (
                            <>
                                <Spinner as="span" animation="border" size="sm" role="status" aria-hidden="true" className="me-2" />
                                Generating...
                            </>
                        ) : (
                            <>
                                <Bot size={16} className="me-2" /> AI Assist All
                            </>
                        )}
                    </Button>
                </Col>
            </Row>

            {/* Render Sections and Fields using Accordion */}
            {subtopic.sections && subtopic.sections.length > 0 ? (
                <Accordion defaultActiveKey={['0']} alwaysOpen className="mb-3"> {/* Start with first section open, alwaysOpen allows multiple */}
                    {subtopic.sections.map((section: CsrdSubtopicSection, idx: number) => (
                        <Accordion.Item eventKey={idx.toString()} key={section.id || idx}>
                            <Accordion.Header>
                                {section.sectionId}: {section.sectionTitle}
                            </Accordion.Header>
                            <Accordion.Body>
                                {section.fields && section.fields.length > 0 ? (
                                    section.fields.map((field: CsrdField) => (
                                        <div key={field.id || field.label} className="mb-3 border-bottom pb-3"> {/* Add separator */}
                                            {renderField(field)}
                                        </div>
                                    ))
                                ) : (
                                    <Alert variant="light" className="text-center">No fields defined in this section.</Alert>
                                )}
                            </Accordion.Body>
                        </Accordion.Item>
                    ))}
                </Accordion>
            ) : (
                <Alert variant="warning">No sections defined for this subtopic.</Alert>
            )}

            {/* Removed Navigation Buttons as they were commented out */}

            {/* Custom Prompt Overlay */}
            <CustomPromptOverlay show={showCustomPrompt} handleClose={() => setShowCustomPrompt(false)} />
        </>
        // </form>
    );
};

export default SubtopicDetails;