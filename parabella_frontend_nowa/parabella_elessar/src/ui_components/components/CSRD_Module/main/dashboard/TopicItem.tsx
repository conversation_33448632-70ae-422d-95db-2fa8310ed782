// src/components/TopicItem.tsx
import React, { useState } from 'react';
import { CsrdTopic } from '../modules/types.ts';
import SubtopicItem from './SubtopicItem.tsx';

interface TopicItemProps {
    topic: CsrdTopic;
    onDataPointChange?: (topicId: number, subtopicId: number, datapointId: number, value: string) => void;
}

const TopicItem: React.FC<TopicItemProps> = ({ topic, onDataPointChange }) => {
    const [isOpen, setIsOpen] = useState(false); // Default to closed, or true for testing

    const handleSubtopicDataPointChange = (subtopicId: number) => (datapointId: number, value: string) => {
        if (onDataPointChange) {
            onDataPointChange(topic.id, subtopicId, datapointId, value);
        }
    };

    return (
        <div className="mb-6 p-4 border rounded-lg shadow-md bg-gray-50">
            <button
                onClick={() => setIsOpen(!isOpen)}
                className="w-full text-left py-3 px-4 bg-gray-200 hover:bg-gray-300 rounded text-gray-800 text-lg font-bold flex justify-between items-center"
            >
                <span>{topic.code}: {topic.name}</span>
                <span>{isOpen ? '▲' : '▼'}</span>
            </button>
            {isOpen && (
                <div className="mt-4">
                    {topic.subtopics.map((sub) => (
                        <SubtopicItem
                            key={sub.id}
                            subtopic={sub}
                            onDataPointChange={onDataPointChange ? handleSubtopicDataPointChange(sub.id) : undefined}
                        />
                    ))}
                </div>
            )}
        </div>
    );
};

export default TopicItem;