// src/modules/csrd/components/CSRDDashboard.tsx

import React, { useState, useEffect, useCallback } from 'react';
import {
    Container,
    Row,
    Col,
    Card,
    Button,
    Table,
    Modal,
    Spinner,
    Alert,
    Accordion // Ensure Accordion is imported if used
} from 'react-bootstrap';
import {
    ChevronDown,
    ChevronUp,
    Compass,
    Eye,
    EyeOff,
    Zap,
    Users as UsersIcon, // Renamed to avoid conflict with React component
    Scale,
    Leaf,
    AlertCircle, // For error display
    Edit3, // For Edit button icon
    Info, // For project name
    Building, // For Company Name/Industry
    Briefcase, // For Size
    DollarSign, // For Revenue
    Users as EmployeesIcon // Use again for Employees count
} from 'lucide-react'; // Added more icons
import { useNavigate } from "react-router-dom";

// ... other imports remain the same (Recharts, Context, API, Types, Components) ...
import { useCsrdProjectContext } from "../context/CsrdProjectProvider";
import { getErrorMessage } from '../context/CsrdProjectProvider';
import { fetchCsrdTopics, fetchSubtopicDetails } from '../api/csrdApi';
import api from '../../DmaModule/context_module/api';
import { CompanyInfo, CsrdProject, CsrdSubtopic } from './modules/types.ts';
import { CsrdTopic } from '../api/csrdApiTypes';
import CriterionPieChart from './CriterionPieChart';
import SubtopicDetails from './SubTopicDetails';

// CSS import - make sure path is correct if you have specific dashboard styles
import './css/Dashboard.css';
import {Cell, Legend, Pie, PieChart, ResponsiveContainer, Tooltip} from "recharts";


// --- Type Definitions ---
type CategoryType = 'General' | 'Environmental' | 'Social' | 'Governance';

interface ESGCategory {
    category: CategoryType;
    color: string;
    icon: React.ReactNode;
}

// --- Constants ---
const esgCategories: ESGCategory[] = [
    { category: 'General', color: '#6c757d', icon: <Compass size={24} /> },
    { category: 'Environmental', color: '#198754', icon: <Leaf size={24} /> },
    { category: 'Social', color: '#fd7e14', icon: <UsersIcon size={24} /> }, // Use renamed icon
    { category: 'Governance', color: '#c32020', icon: <Scale size={24} /> },
];

// ... (dummyProgressData, DONUT_COLORS remain the same) ...
const dummyProgressData: Record<CategoryType, Array<{ name: string; value: number }>> = {
    General: [ { name: 'Completed', value: 35 }, { name: 'In Progress', value: 45 }, { name: 'Not Started', value: 20 }],
    Environmental: [ { name: 'Completed', value: 28 }, { name: 'In Progress', value: 40 }, { name: 'Not Started', value: 32 }],
    Social: [ { name: 'Completed', value: 22 }, { name: 'In Progress', value: 33 }, { name: 'Not Started', value: 45 }],
    Governance: [ { name: 'Completed', value: 30 }, { name: 'In Progress', value: 50 }, { name: 'Not Started', value: 20 }],
};
const DONUT_COLORS = ['#20c997', '#ffc107', '#c32137']; // Teal, Yellow, Red


// --- Helper Functions (determineCategoryFromCode, groupByCategory) remain the same ---
function determineCategoryFromCode(code: string): CategoryType {
    const upperCode = code.toUpperCase();
    if (upperCode.startsWith('ESRS_E')) return 'Environmental';
    if (upperCode.startsWith('ESRS_S')) return 'Social';
    if (upperCode.startsWith('ESRS_G')) return 'Governance';
    return 'General';
}

function groupByCategory(topics: CsrdTopic[]): Record<CategoryType, CsrdTopic[]> {
    const grouped: Record<CategoryType, CsrdTopic[]> = { General: [], Environmental: [], Social: [], Governance: [] };
    topics.forEach((t) => {
        if (t && t.code) {
            const cat = determineCategoryFromCode(t.code);
            if (!grouped[cat]) { grouped[cat] = []; }
            grouped[cat].push(t);
        } else {
            console.warn("Skipping topic due to missing data:", t);
        }
    });
    return grouped;
}


// --- Component ---
const CSRDDashboard: React.FC = () => {
    // --- Hooks ---
    const {
        companyInfo,
        currentProject,
        isLoading: contextIsLoading,
        error: contextError,
        clearError
    } = useCsrdProjectContext();
    const navigate = useNavigate();

    // --- State ---
    const [topics, setTopics] = useState<CsrdTopic[]>([]);
    const [groupedTopics, setGroupedTopics] = useState<Record<CategoryType, CsrdTopic[]>>({ General: [], Environmental: [], Social: [], Governance: [] });
    // --- DEFAULT TO ENVIRONMENTAL ---
    const [expandedCategory, setExpandedCategory] = useState<CategoryType | null>('Environmental');
    // ---------------------------------
    const [showSubtopicModal, setShowSubtopicModal] = useState(false);
    const [selectedSubtopic, setSelectedSubtopic] = useState<CsrdSubtopic | null>(null);
    const [expandedTopicIds, setExpandedTopicIds] = useState<Set<number>>(new Set());
    const [autoFillingTopicIds, setAutoFillingTopicIds] = useState<Set<number>>(new Set());
    const [loadingTopics, setLoadingTopics] = useState<boolean>(true);
    const [errorTopics, setErrorTopics] = useState<string | null>(null);
    const [loadingSubtopic, setLoadingSubtopic] = useState<boolean>(false);
    const [errorSubtopic, setErrorSubtopic] = useState<string | null>(null);

    // --- Effects (remain the same) ---
    useEffect(() => {
        setErrorTopics(null);
        setErrorSubtopic(null);
        if (!contextIsLoading) {
            if (!currentProject) {
                console.warn("No CSRD project loaded. Redirecting to project creation.");
                navigate('/csrd/create'); // Adjust if needed
            } else if (!companyInfo || !companyInfo.companyName) {
                console.warn("Company info or company name not found. Redirecting to company info form.");
                navigate('/csrd/company-info'); // Adjust if needed
            }
        }
    }, [currentProject, companyInfo, contextIsLoading, navigate]);

    useEffect(() => {
        if (currentProject && companyInfo && companyInfo.companyName) {
            const loadTopics = async () => {
                setLoadingTopics(true);
                setErrorTopics(null);
                try {
                    // Assuming fetchCsrdTopics doesn't need project ID based on previous examples
                    const data = await fetchCsrdTopics(/* currentProject.id */);
                    setTopics(data);
                    const grouped = groupByCategory(data);
                    setGroupedTopics(grouped);
                } catch (err: any) {
                    console.error('Failed to load topics', err);
                    setErrorTopics(`Failed to load CSRD topics: ${getErrorMessage(err)}`);
                } finally {
                    setLoadingTopics(false);
                }
            };
            loadTopics();
        } else {
            setLoadingTopics(false);
            setTopics([]);
            setGroupedTopics({ General: [], Environmental: [], Social: [], Governance: [] });
        }
    }, [currentProject, companyInfo]); // Dependencies


    // --- Handlers (remain the same: handleClearLocalErrors, toggleCategory, toggleTopic, handleOpenSubtopic, handleCloseSubtopic, handleAutoFillTopic) ---
    const handleClearLocalErrors = useCallback(() => {
        setErrorTopics(null);
        setErrorSubtopic(null);
        clearError?.();
    }, [clearError]);

    const toggleCategory = (cat: CategoryType) => {
        setExpandedCategory((prev) => (prev === cat ? null : cat));
    };

    const toggleTopic = (topicId: number) => {
        setExpandedTopicIds((prev) => {
            const newSet = new Set(prev);
            if (newSet.has(topicId)) newSet.delete(topicId);
            else newSet.add(topicId);
            return newSet;
        });
    };

    const handleOpenSubtopic = async (sub: CsrdSubtopic) => {
        setLoadingSubtopic(true);
        setErrorSubtopic(null);
        setShowSubtopicModal(true);
        try {
            // Fetch fresh details when opening
            const detailed = await fetchSubtopicDetails(sub.id);
            // Combine fetched details with potentially existing autoFillData from the parent topic state
            const subtopicWithPotentialData: CsrdSubtopic = {
                ...detailed,
                autoFillData: sub.autoFillData // Keep existing autoFill data if present
            };
            setSelectedSubtopic(subtopicWithPotentialData);
        } catch (err: any) {
            console.error('Failed to load subtopic details', err);
            setErrorSubtopic(`Failed to load details: ${getErrorMessage(err)}`);
            setSelectedSubtopic(null); // Clear selection on error
        } finally {
            setLoadingSubtopic(false);
        }
    };

    const handleCloseSubtopic = () => {
        setSelectedSubtopic(null);
        setShowSubtopicModal(false);
        setErrorSubtopic(null); // Clear error when closing
    };

    const handleAutoFillTopic = async (topic: CsrdTopic) => {
        handleClearLocalErrors();

        console.log("Attempting AI Assist for topic:", topic.name);
        console.log("Current Company Info from context:", companyInfo);

        if (!companyInfo || !companyInfo.companyName || companyInfo.companyName.trim() === '') {
            const errorMsg = "Cannot perform AI Assist: Company Information or Company Name is missing or empty.";
            console.error(errorMsg);
            setErrorTopics(errorMsg);
            return;
        }

        setAutoFillingTopicIds((prev) => new Set(prev).add(topic.id));

        try {
            if (!topic.subtopics || topic.subtopics.length === 0) {
                console.warn(`Topic ${topic.name} has no subtopics to auto-fill.`);
                setErrorTopics(`Topic ${topic.name} has no subtopics.`);
                setAutoFillingTopicIds((prev) => { const newSet = new Set(prev); newSet.delete(topic.id); return newSet; });
                return;
            }

            const subtopicPromises = topic.subtopics.map(async (sub) => {
                try {
                    // Fetch details again to ensure we have section/field structure for the payload
                    const detailed = await fetchSubtopicDetails(sub.id);
                    // Construct payload using detailed structure
                    const payload = {
                        companyName: companyInfo.companyName,
                        industry: companyInfo.industry || '',
                        size: companyInfo.size || '',
                        revenue: companyInfo.revenue || '',
                        numberOfEmployees: companyInfo.numberOfEmployees || '',
                        // Use IDs from the detailed subtopic object
                        subtopicId: detailed.csrdSubtopicId,
                        subtopicLabel: detailed.csrdSubtopicLabel,
                        sections: detailed.sections?.map((section) => ({
                            id: section.id, sectionId: section.sectionId, sectionTitle: section.sectionTitle,
                            fields: section.fields?.map((field) => ({
                                id: field.id, label: field.label, fieldType: field.fieldType,
                                // Ensure options structure is correct
                                options: field.options?.map((opt) => ({
                                    id: opt.id, optionValue: opt.optionValue,
                                    child_field_id: opt.child_field_id, parent_field_id: opt.parent_field_id,
                                })) || [],
                            })) || [],
                        })) || [],
                    };

                    console.log(`Sending AI payload for Subtopic ${sub.csrdSubtopicId}`);
                    const response = await api.post('/chat/generate-auto-fill-csrd', payload);
                    const aiData = response.data.autoFilledFields;

                    if (aiData && typeof aiData === 'object') {
                        return { subId: sub.id, autoFillData: aiData, error: null };
                    } else {
                        console.warn(`Invalid AI response format for subtopic ${sub.csrdSubtopicId}`);
                        return { subId: sub.id, autoFillData: undefined, error: "Invalid AI response format" };
                    }
                } catch (error: any) {
                    console.error(`Auto fill for subtopic ${sub.csrdSubtopicId} (${sub.id}) failed:`, error);
                    return { subId: sub.id, autoFillData: undefined, error: getErrorMessage(error) };
                }
            });

            const results = await Promise.all(subtopicPromises);

            const failedSubtopics = results.filter(r => r.error);
            if (failedSubtopics.length > 0) {
                const errorMessages = failedSubtopics.map(f => `Subtopic ${f.subId || 'N/A'}: ${f.error}`).join('; ');
                console.warn(`Some subtopics failed AI Assist: ${errorMessages}`);
                // Show a more concise error message to the user
                setErrorTopics(`AI Assist completed with some errors. Check console for details.`);
            } else {
                console.log("All subtopics processed successfully by AI.");
                // Optionally show a success message if needed
            }

            // Update the main topics state with the new autoFillData
            setTopics(prevTopics => {
                const updatedTopics = prevTopics.map(t => {
                    if (t.id === topic.id) {
                        const updatedSubtopics = t.subtopics.map(sub => {
                            const result = results.find(r => r.subId === sub.id);
                            // Only update if successful and data exists
                            if (result && result.autoFillData && !result.error) {
                                return { ...sub, autoFillData: result.autoFillData };
                            }
                            return sub; // Return original subtopic if no data or error
                        });
                        return { ...t, subtopics: updatedSubtopics };
                    }
                    return t;
                });

                // Update grouped topics after state update
                setGroupedTopics(groupByCategory(updatedTopics));
                return updatedTopics; // Return the updated topics array
            });

            // // This approach might cause issues if setTopics is async
            // // It's better to update groupedTopics *after* setTopics has completed
            // // or derive it directly from the new state within setTopics callback
            // setTopics(currentTopicsState => {
            //     setGroupedTopics(groupByCategory(currentTopicsState));
            //     return currentTopicsState; // This might not reflect the *very latest* update
            // });

        } catch (error: any) {
            console.error('Error during AI Assist batch process:', error);
            setErrorTopics(`AI Assist failed: ${getErrorMessage(error)}`);
        } finally {
            setAutoFillingTopicIds((prev) => {
                const newSet = new Set(prev);
                newSet.delete(topic.id);
                return newSet;
            });
        }
    };

    // --- Render Functions ---
    const renderCategoryDonut = (category: CategoryType) => {
        // (Donut rendering logic remains the same)
        const data = dummyProgressData[category] || [];
        if (data.length === 0) return <p className="text-center text-muted small">No progress data for {category}.</p>;
        return (
            <div className="mb-4">
                <h5 className="text-center mb-3">Progress: <strong>{category}</strong></h5>
                <div style={{ width: '100%', height: 300 }}>
                    <ResponsiveContainer>
                        <PieChart>
                            <Pie data={data} cx="50%" cy="50%" dataKey="value" nameKey="name" innerRadius={60} outerRadius={100} paddingAngle={3} labelLine={false}>
                                {data.map((entry, index) => ( <Cell key={`cell-${index}`} fill={DONUT_COLORS[index % DONUT_COLORS.length]} /> ))}
                            </Pie>
                            <Tooltip formatter={(value: number) => `${value}%`} />
                            <Legend verticalAlign="bottom" height={36} iconSize={10} />
                        </PieChart>
                    </ResponsiveContainer>
                </div>
            </div>
        );
    };

    // --- Main Render Logic ---
    if (contextIsLoading) {
        return ( <Container fluid className="d-flex justify-content-center align-items-center" style={{ minHeight: 'calc(100vh - 200px)' }}><Spinner animation="border" role="status"><span className="visually-hidden">Loading...</span></Spinner></Container> );
    }
    if (!currentProject || !companyInfo || !companyInfo.companyName) {
        return ( <Container fluid className="text-center py-5"><Alert variant="warning"><AlertCircle size={20} className="me-2"/>Missing project or company info. Cannot display dashboard.</Alert><Button variant="primary" onClick={() => navigate(currentProject ? '/csrd/company-info' : '/csrd/create')}>{currentProject ? 'Go to Company Info' : 'Go to Project Selection'}</Button></Container> );
    }

    return (
        <>
            {/* Navbar is now handled by CsrdLayout */}

            <Container fluid className="py-4 px-md-4">

                {/* --- MODIFIED Company Info Header --- */}
                {/* --- NEW Company Info Header (Single Row Flex Layout) --- */}
                <Card className="mb-4 shadow-sm border-0">
                    <Card.Body className="d-flex flex-wrap align-items-center justify-content-between gap-3 py-3"> {/* Main Flex Container */}

                        {/* 1. Project Name (Emphasized) */}
                        <div className="d-flex align-items-center me-lg-3"> {/* Group Icon + Text */}
                            <Info size={22} className="me-2 flex-shrink-0 text-primary"/> {/* Slightly larger icon */}
                            <div>
                                <span className="text-muted d-block small" style={{ lineHeight: '1' }}>Project</span>
                                <h5 className="mb-0 fw-bold">{currentProject.projectName}</h5> {/* Larger Font + Bold */}
                            </div>
                        </div>

                        {/* Vertical Separator (Optional, for visual separation) */}
                        <div className="vr d-none d-lg-block mx-2"></div>

                        {/* 2. Company Name (Most Emphasized) */}
                        <div className="d-flex align-items-center me-lg-auto"> {/* Group Icon + Text, me-lg-auto pushes details right */}
                            <Building size={24} className="me-2 flex-shrink-0 text-success"/> {/* Largest icon */}
                            <div>
                                <span className="text-muted d-block small" style={{ lineHeight: '1' }}>Company</span>
                                <h4 className="mb-0 fw-bolder">{companyInfo.companyName}</h4> {/* Largest Font + Bolder */}
                            </div>
                        </div>

                        {/* 3. Details Group (Smaller Info Items) */}
                        <div className="d-flex flex-wrap align-items-center gap-3 ms-lg-3"> {/* Group for details */}

                            {/* Industry */}
                            <div className="d-flex align-items-center">
                                <Briefcase size={18} className="me-2 flex-shrink-0 text-info"/>
                                <div>
                                    <span className="text-muted d-block small" style={{ lineHeight: '1' }}>Industry</span>
                                    <strong className="text-dark d-block" style={{ lineHeight: '1.2' }}>{companyInfo.industry || 'N/A'}</strong>
                                </div>
                            </div>

                            {/* Size / Employees */}
                            <div className="d-flex align-items-center">
                                <EmployeesIcon size={18} className="me-2 flex-shrink-0 text-warning"/>
                                <div>
                                    <span className="text-muted d-block small" style={{ lineHeight: '1' }}>Size / Empl.</span>
                                    <strong className="text-dark d-block" style={{ lineHeight: '1.2' }}>{companyInfo.size || 'N/A'} / {companyInfo.numberOfEmployees || 'N/A'}</strong>
                                </div>
                            </div>

                            {/* Revenue */}
                            <div className="d-flex align-items-center">
                                <DollarSign size={18} className="me-2 flex-shrink-0 text-danger"/>
                                <div>
                                    <span className="text-muted d-block small" style={{ lineHeight: '1' }}>Revenue</span>
                                    <strong className="text-dark d-block" style={{ lineHeight: '1.2' }}>{companyInfo.revenue || 'N/A'}</strong>
                                </div>
                            </div>
                        </div>

                    </Card.Body>
                </Card>
                {/* --- END NEW Company Info Header --- */}
                {/* --- END MODIFIED Company Info Header --- */}


                {/* Error Display */}
                {(contextError || errorTopics) && (
                    <Alert variant="danger" onClose={handleClearLocalErrors} dismissible className="mx-1 mb-3">
                        <AlertCircle size={18} className="me-2"/>
                        {contextError || errorTopics}
                    </Alert>
                )}

                {/* Category Selection */}
                <Row className="mb-4 g-3 mx-1">
                    {esgCategories.map((cat) => {
                        const isActive = expandedCategory === cat.category;
                        return (
                            <Col key={cat.category} md={6} lg={3}>
                                {/* Added pointer cursor for clarity */}
                                <Card
                                    className={`category-card shadow-sm ${isActive ? 'card-active' : ''}`}
                                    onClick={() => toggleCategory(cat.category)}
                                    style={{ borderLeft: `5px solid ${cat.color}`, cursor: 'pointer' }}
                                >
                                    <Card.Body className="py-3">
                                        <div className="d-flex justify-content-between align-items-center">
                                            <div className="d-flex align-items-center">
                                                <span style={{ color: cat.color }}>{cat.icon}</span>
                                                <h5 className="ms-3 mb-0 fw-semibold">{cat.category}</h5> {/* Slightly bolder */}
                                            </div>
                                            {isActive ? <ChevronUp size={22} color="#495057"/> : <ChevronDown size={22} color="#6c757d"/>}
                                        </div>
                                    </Card.Body>
                                </Card>
                            </Col>
                        );
                    })}
                </Row>

                {/* Topic Loading Spinner */}
                {loadingTopics && ( <div className="text-center py-5"><Spinner animation="border" /> Loading Topics...</div> )}

                {/* Expanded Category Details - Conditionally render based on expandedCategory */}
                {/* No explicit Accordion needed, just conditional rendering */}
                {expandedCategory && !loadingTopics && (
                    <div className="mt-4 px-1">
                        {/* Donut chart specific to the expanded category */}
                        <Row className="mb-4">
                            <Col md={8} lg={6} className="mx-auto"> {/* Center the donut chart */}
                                {renderCategoryDonut(expandedCategory)}
                            </Col>
                        </Row>

                        {/* Topics for the expanded category */}
                        <h4 className="mb-3 border-bottom pb-2">{expandedCategory} Topics</h4>
                        <Row xs={1} lg={2} xl={3} className="g-4"> {/* Adjust columns for different screen sizes */}
                            {groupedTopics[expandedCategory]?.length > 0 ? (
                                groupedTopics[expandedCategory].map((topic) => {
                                    const isTopicExpanded = expandedTopicIds.has(topic.id);
                                    const isAutoFilling = autoFillingTopicIds.has(topic.id);
                                    return (
                                        <Col key={topic.id}>
                                            <Card className="topic-card h-100 shadow-sm">
                                                <Card.Header className="py-2 bg-light border-bottom d-flex justify-content-between align-items-center">
                                                    <h6 className="mb-0 text-truncate" title={`${topic.code} – ${topic.name}`}>
                                                        {topic.code} – {topic.name}
                                                    </h6>
                                                    {/* Optional: Add a small status indicator here */}
                                                </Card.Header>
                                                <Card.Body className="d-flex flex-column pt-2 pb-3"> {/* Adjusted padding */}
                                                    {topic ? (
                                                        <div className="criterion-chart-container flex-grow-1 mb-3" style={{ minHeight: '150px' }}>
                                                            <CriterionPieChart topic={topic} />
                                                        </div>
                                                    ) : ( <div className="text-muted text-center small my-3 flex-grow-1 d-flex align-items-center justify-content-center" style={{ minHeight: '150px' }}>No criteria data available.</div> )}

                                                    {/* Action Buttons - Placed below chart/message */}
                                                    <div className="d-flex justify-content-between mt-auto border-top pt-3">
                                                        <Button variant="outline-secondary" size="sm" onClick={() => toggleTopic(topic.id)} aria-expanded={isTopicExpanded} className="d-flex align-items-center">
                                                            {isTopicExpanded ? <EyeOff size={16} className="me-1"/> : <Eye size={16} className="me-1"/>} {isTopicExpanded ? 'Hide Details' : 'Show Details'}
                                                        </Button>
                                                        <Button variant="outline-primary" size="sm" onClick={() => handleAutoFillTopic(topic)} disabled={isAutoFilling || !topic.subtopics?.length} className="d-flex align-items-center">
                                                            {isAutoFilling ? (<><Spinner animation="border" size="sm" className="me-1" /> Filling...</>) : (<><Zap size={16} className="me-1"/> AI Assist</>)}
                                                        </Button>
                                                    </div>

                                                    {/* Collapsible Subtopic Details */}
                                                    {isTopicExpanded && (
                                                        <div className="mt-3 border rounded p-2 bg-light" id={`subtopics-${topic.id}`}>
                                                            <h6 className="text-muted small mb-2">Subtopics:</h6>
                                                            {topic.subtopics && topic.subtopics.length > 0 ? (
                                                                <Table striped hover responsive size="sm" className="subtopic-table align-middle mb-0">
                                                                    {/* Removed thead for cleaner look in collapsed view */}
                                                                    <tbody>
                                                                    {topic.subtopics.map((sub) => (
                                                                        <tr key={sub.id}>
                                                                            <td className="fw-semibold" style={{ width: '15%' }}>{sub.csrdSubtopicId}</td>
                                                                            <td>{sub.csrdSubtopicLabel}</td>
                                                                            <td style={{ width: '10%' }} className="text-center">
                                                                                <Button variant="light" size="sm" className="border p-1" onClick={() => handleOpenSubtopic(sub)} title={`View ${sub.csrdSubtopicId}`}>
                                                                                    <Eye size={16} />
                                                                                </Button>
                                                                            </td>
                                                                        </tr>
                                                                    ))}
                                                                    </tbody>
                                                                </Table>
                                                            ) : ( <Alert variant="light" className="text-center m-0 py-2 small">No subtopics found for this topic.</Alert> )}
                                                        </div>
                                                    )}
                                                </Card.Body>
                                            </Card>
                                        </Col>
                                    );
                                })
                            ) : ( <Col xs={12}><Alert variant="info" className="text-center">No topics found for the '{expandedCategory}' category.</Alert></Col> )}
                        </Row>
                    </div>
                )}

                {/* Message when no category is selected */}
                {!expandedCategory && !loadingTopics && (
                    <Alert variant="secondary" className="text-center mt-4">
                        Select a category (General, Environmental, Social, Governance) above to view details and topics.
                    </Alert>
                )}

            </Container>

            {/* Subtopic Modal (remains the same) */}
            <Modal show={showSubtopicModal} onHide={handleCloseSubtopic} size="xl" backdrop="static" centered scrollable> {/* Added scrollable */}
                <Modal.Header closeButton>
                    <Modal.Title>{selectedSubtopic ? `${selectedSubtopic.csrdSubtopicId} - ${selectedSubtopic.csrdSubtopicLabel}` : 'Subtopic Details'}</Modal.Title>
                </Modal.Header>
                <Modal.Body /* style={{ maxHeight: '75vh', overflowY: 'auto' }} - Removed redundant style because of scrollable prop */ >
                    {loadingSubtopic && <div className="text-center p-5"><Spinner animation="border" /> Loading Details...</div>}
                    {errorSubtopic && <Alert variant="danger"><AlertCircle size={18} className="me-2"/>{errorSubtopic}</Alert>}
                    {!loadingSubtopic && !errorSubtopic && selectedSubtopic && (
                        // Pass necessary props to SubtopicDetails
                        <SubtopicDetails
                            subtopic={selectedSubtopic}
                            // Ensure companyInfo is passed correctly, handling potential null/undefined
                            companyInfo={{
                                companyName: companyInfo?.companyName || 'N/A',
                                industry: companyInfo?.industry || 'N/A'
                            }}
                            // Pass userRoles if available/needed by SubtopicDetails
                            userRoles={[]} // Replace with actual roles if needed
                        />
                    )}
                    {/* Add message if subtopic couldn't be loaded but modal is open */}
                    {!loadingSubtopic && !errorSubtopic && !selectedSubtopic && (
                        <Alert variant="warning">Could not load subtopic details.</Alert>
                    )}
                </Modal.Body>
                <Modal.Footer>
                    <Button variant="secondary" onClick={handleCloseSubtopic}> Close </Button>
                    {/* Optional: Add a Save button if SubtopicDetails allows editing */}
                    {/* <Button variant="primary" onClick={handleSaveSubtopic}> Save Changes </Button> */}
                </Modal.Footer>
            </Modal>
        </>
    );
};

export default CSRDDashboard;