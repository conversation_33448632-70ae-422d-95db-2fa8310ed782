// src/modules/csrd/api/csrdApiTypes.tsx

export interface CsrdFieldOption {
    id: number;
    optionValue: string;
    child_field_id:number;
    parent_field_id:number;
}

export interface CsrdConditionalField {
    id: number;
    parentOptionValue: string;
    parentField: CsrdField;
    childField: CsrdField;
}

export interface CsrdField {
    id: number;
    fieldType: string;
    label: string | null;
    options: CsrdFieldOption[];
    sections: CsrdSubtopicSection;
}

export interface CsrdSubtopicSection {
    id: number;
    sectionId: string;
    sectionTitle: string;
    fields: CsrdField[];
}

export interface ProcessedDocumentChunkDTO {
    id: number;
    projectId: number;
    uploadedByUserId?: string | null;
    documentName?: string | null;
    chunkIndex?: number | null;
    chunkText?: string | null;
    chunkSummary?: string | null;
    disclosureRequirement?: string | null;
    sourceId?: string | null;
    dataSourceIds?: any[] | null;
    matchScore?: number | null;
    metadata?: any | null;
    createdAt?: string | null;
}

interface AutofillDatapointRequestChunkDTO {
    documentName?: string | null;
    chunkIndex?: number | null;
    chunkText?: string | null;
    chunkSummary?: string | null;
}

export interface AutofillDatapointRequestDTO {
    projectId: number;
    companyName: string;
    companyIndustry: string;
    datapointId: number;
    datapointLabel: string;
    disclosureRequirement: string;
    sourceId: string;
    datapointDataType: string; // << NEW FIELD to send to backend
    documentChunks?: AutofillDatapointRequestChunkDTO[]; // Made optional
}

export interface AutofillDatapointResponseDTO {
    datapointId: number;
    generatedText: string;
    // Optional: Backend could also return the source if it makes the decision
    // generatedSource?: 'user-doc' | 'ai-open-data';
}

export interface CoverageDatapointDto {
    esrsDatapointId: number;
    sourceId: string | null;
    esrsStandard: string;
    disclosureRequirement: string;
    dataPointName: string;
    covered: boolean;
    coveringChunkIds: number[];
    coveringDocumentNames: string[];
}

export interface CategoryCoverageStatsDto {
    totalDatapoints: number;
    coveredDatapoints: number;
    coveragePercentage: number;
}

export interface CoverageAnalysisResultDto {
    projectId: number;
    totalEsrsDatapoints: number;
    totalCoveredDatapoints: number;
    totalUncoveredDatapoints: number;
    overallCoveragePercentage: number;
    coverageByStandard: Record<string, CategoryCoverageStatsDto>;
    datapoints: CoverageDatapointDto[];
}