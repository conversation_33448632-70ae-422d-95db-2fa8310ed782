import React from "react";
import { Table, Button, Form } from "react-bootstrap";
import { Plus, Trash2 } from "lucide-react";

interface ListItem {
    id: number;
    value: string;
}

interface Props {
    label: string;
    items?: ListItem[]; // Mark items as optional
    onAdd: (value: string) => void;
    onRemove: (id: number) => void;
}

const ListComponent: React.FC<Props> = ({
                                            label,
                                            items = [], // Default to empty array if undefined
                                            onAdd,
                                            onRemove,
                                        }) => {
    const [inputValue, setInputValue] = React.useState("");

    const handleAdd = () => {
        if (inputValue.trim() !== "") {
            onAdd(inputValue.trim());
            setInputValue("");
        }
    };

    return (
        <div className="mb-3">
            <h6>{label}</h6>
            <div className="d-flex mb-2">
                <Form.Control
                    type="text"
                    value={inputValue}
                    placeholder="Enter item..."
                    onChange={(e) => setInputValue(e.target.value)}
                    className="me-2"
                />
                <Button variant="success" onClick={handleAdd}>
                    <Plus size={16} /> Add
                </Button>
            </div>

            {items.length > 0 ? (
                <Table striped bordered hover size="sm">
                    <thead className="bg-light">
                    <tr>
                        <th>Item Value</th>
                        <th style={{ width: "70px" }}>Remove</th>
                    </tr>
                    </thead>
                    <tbody>
                    {items.map((row, index) => (
                        <tr key={`${row.id}-${index}`}>
                            <td>{row.value}</td>
                            <td className="text-center">
                                <Button
                                    variant="outline-danger"
                                    size="sm"
                                    onClick={() => onRemove(row.id)}
                                >
                                    <Trash2 size={16} />
                                </Button>
                            </td>
                        </tr>
                    ))}
                    </tbody>
                </Table>
            ) : (
                <Form.Text className="text-muted">
                    No items added yet.
                </Form.Text>
            )}
        </div>
    );
};

export default ListComponent;
