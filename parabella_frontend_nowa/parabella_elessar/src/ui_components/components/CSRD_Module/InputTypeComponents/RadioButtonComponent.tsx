// form-fields/RadioButtonComponent.tsx

import { Form } from 'react-bootstrap';
import { Radio } from 'lucide-react';
import styled from 'styled-components';
import { CsrdFieldOption } from '../api/csrdApiTypes.tsx'; // Adjust the import path as needed

interface RadioButtonComponentProps {
    label: string;
    options: CsrdFieldOption[];
    selectedValue: string;
    onChange: (value: string) => void;
}

const StyledFormGroup = styled(Form.Group)`
    margin-bottom: 0.75rem;
`;

const RadioButtonGroupComponent: React.FC<RadioButtonComponentProps> = ({
                                                                            label,
                                                                            options,
                                                                            selectedValue,
                                                                            onChange,
                                                                        }) => {
    return (
        <StyledFormGroup controlId={`radio-group-${label}`}>
            <Form.Label>
                <Radio className="me-2" /> {label}
            </Form.Label>
            {options.length > 0 ? (
                options.map((opt) => (
                    <Form.Check
                        key={opt.id}
                        type="radio"
                        label={opt.optionValue}
                        name={`radio-group-${label}`}
                        id={`radio-${label}-${opt.id}`}
                        value={opt.optionValue}
                        checked={selectedValue === opt.optionValue}
                        onChange={() => onChange(opt.optionValue)}
                        className="mb-2"
                    />
                ))
            ) : (
                <Form.Text className="text-muted">No options provided.</Form.Text>
            )}
        </StyledFormGroup>
    );
};

export default RadioButtonGroupComponent;
