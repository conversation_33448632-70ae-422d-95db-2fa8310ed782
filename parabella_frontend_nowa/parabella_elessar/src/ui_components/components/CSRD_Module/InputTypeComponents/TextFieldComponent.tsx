import React, { useRef, useEffect, ChangeEvent, useCallback } from 'react';
import { Form, InputGroup, But<PERSON>, Spinner } from 'react-bootstrap';
import { Edit3, Send, AlertCircle } from 'lucide-react';

interface Props {
    label: string;
    value: string;
    onChange: (e: ChangeEvent<HTMLTextAreaElement>) => void;
    placeholder?: string;
    onAutoFill?: () => void;
    aiLoading?: boolean;
    aiError?: string | null;
    // Optional: Add max height if you don't want it to grow indefinitely
    maxHeight?: string | number; // e.g., '200px' or 200
}

const TextFieldComponent: React.FC<Props> = ({
                                                 label,
                                                 value,
                                                 onChange,
                                                 placeholder = "Enter text...",
                                                 onAutoFill,
                                                 aiLoading = false,
                                                 aiError = null,
                                                 maxHeight, // Destructure maxHeight prop
                                             }) => {
    const textareaRef = useRef<HTMLTextAreaElement>(null);

    // Function to adjust textarea height
    const adjustTextareaHeight = useCallback(() => {
        const textarea = textareaRef.current;
        if (textarea) {
            // Temporarily reset height to 'auto' to get the correct scrollHeight
            textarea.style.height = 'auto';
            // Set the height to the scroll height, but not less than a minimum (e.g., 40px)
            const newHeight = Math.max(textarea.scrollHeight, 40); // Ensure a minimum height
            textarea.style.height = `${newHeight}px`;

            // Apply max height if provided and needed
            if (maxHeight) {
                // Check if scrollHeight exceeds max height
                const maxHeightValue = typeof maxHeight === 'number' ? maxHeight : parseInt(maxHeight, 10);
                if (!isNaN(maxHeightValue) && newHeight > maxHeightValue) {
                    textarea.style.height = typeof maxHeight === 'number' ? `${maxHeight}px` : maxHeight;
                    textarea.style.overflowY = 'auto'; // Show scrollbar if max height is reached
                } else {
                    textarea.style.overflowY = 'hidden'; // Hide scrollbar if within max height
                }
            } else {
                textarea.style.overflowY = 'hidden'; // Default: hide scrollbar if no max height
            }
        }
    }, [maxHeight]); // Re-run if maxHeight changes

    // Adjust height on initial render and when the external value changes
    useEffect(() => {
        adjustTextareaHeight();
    }, [value, adjustTextareaHeight]); // adjustTextareaHeight is now a dependency

    // Adjust height on window resize
    useEffect(() => {
        window.addEventListener('resize', adjustTextareaHeight);
        // Cleanup function to remove the event listener
        return () => {
            window.removeEventListener('resize', adjustTextareaHeight);
        };
    }, [adjustTextareaHeight]); // Re-add listener if the function identity changes

    // Handle combined change and input events
    const handleInputChange = (e: ChangeEvent<HTMLTextAreaElement>) => {
        onChange(e); // Call the original onChange passed from parent to update state
        adjustTextareaHeight(); // Adjust height immediately on input
    };

    return (
        <Form.Group controlId={`textfield-${label.replace(/\s+/g, '-')}`} className="textarea-autosize-group">
            <Form.Label className="d-flex align-items-center fw-medium"> {/* Slightly bolder label */}
                <Edit3 size={15} className="me-2 text-muted" /> {/* Adjusted icon size/color */}
                {label}
            </Form.Label>
            <InputGroup hasValidation>
                <Form.Control
                    as="textarea"
                    ref={textareaRef}
                    value={value}
                    onChange={handleInputChange} // Use combined handler
                    // onInput={adjustTextareaHeight} // No longer needed here, handled by combined handler
                    placeholder={placeholder}
                    style={{
                        resize: 'none', // Disable manual resize handle
                        minHeight: '40px', // Ensure a minimum height visually
                        // Max height is applied dynamically by adjustTextareaHeight if prop is set
                        // Overflow is also handled dynamically
                    }}
                    rows={1} // Start with minimum rows, height will adjust
                    isInvalid={!!aiError}
                />
                {/* AI Assist Button */}
                {onAutoFill && (
                    <Button
                        variant="outline-secondary"
                        onClick={onAutoFill}
                        disabled={aiLoading}
                        size="sm"
                        className="d-flex align-items-center ai-assist-button" // Added class
                        style={{ borderTopRightRadius: '0.375rem', borderBottomRightRadius: '0.375rem' }}
                        title={`AI Assist for ${label}`}
                    >
                        {aiLoading ? (
                            <Spinner animation="border" size="sm" aria-hidden="true" />
                        ) : (
                            <Send size={16} />
                        )}
                    </Button>
                )}
                {/* Validation Feedback */}
                {aiError && (
                    <Form.Control.Feedback type="invalid" className="d-flex align-items-center mt-1">
                        <AlertCircle size={14} className="me-1 flex-shrink-0"/> {aiError}
                    </Form.Control.Feedback>
                )}
            </InputGroup>
        </Form.Group>
    );
};

export default TextFieldComponent;