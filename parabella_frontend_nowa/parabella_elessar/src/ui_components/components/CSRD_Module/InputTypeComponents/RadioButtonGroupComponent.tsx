// form-fields/RadioButtonGroupComponent.tsx

import React from "react";
import { Form } from "react-bootstrap";
import { Radio } from "lucide-react";

interface CsrdFieldOption {
    id: number;
    optionValue: string;
    child_field_id: number;
    parent_field_id: number;
}

interface Props {
    label: string;
    options: CsrdFieldOption[];
    selectedValue: string;
    onChange: (value: string) => void;
}

const RadioButtonGroupComponent: React.FC<Props> = ({ label, options, selectedValue, onChange }) => {
    return (
        <Form.Group className="mb-3" controlId={`radiogroup-${label}`}>
            <Form.Label>
                <Radio className="me-2" />
                {label}
            </Form.Label>
            {options.length > 0 ? (
                options.map((opt) => (
                    <Form.Check
                        key={opt.id}
                        type="radio"
                        label={opt.optionValue}
                        name={`radiogroup-${label}`}
                        id={`radio-${label}-${opt.id}`}
                        value={opt.optionValue}
                        checked={selectedValue === opt.optionValue}
                        onChange={() => onChange(opt.optionValue)}
                        className="mb-2"
                    />
                ))
            ) : (
                <Form.Text className="text-muted">No options provided.</Form.Text>
            )}
        </Form.Group>
    );
};

export default RadioButtonGroupComponent;
