// form-fields/CheckboxGroupComponent.tsx

import React from "react";
import { Form } from "react-bootstrap";

interface CsrdFieldOption {
    id: number;
    optionValue: string;
    child_field_id: number;
    parent_field_id: number;
}

interface Props {
    label: string;
    options: CsrdFieldOption[];
    checkedValues: string[] | undefined; // or any[] if needed
    onChange: (value: string) => void;
}

const CheckboxGroupComponent: React.FC<Props> = ({
                                                     label,
                                                     options,
                                                     checkedValues = [],   // <-- fallback to an empty array
                                                     onChange
                                                 }) => {
    return (
        <Form.Group className="mb-3" controlId={`checkboxgroup-${label}`}>
            <Form.Label>{label}</Form.Label>
            {options.length > 0 ? (
                options.map((opt) => (
                    <Form.Check
                        key={opt.id}
                        type="checkbox"
                        label={opt.optionValue}
                        id={`checkbox-${label}-${opt.id}`}
                        value={opt.optionValue}
                        checked={checkedValues.includes(opt.optionValue)} // safe because fallback is []
                        onChange={() => onChange(opt.optionValue)}
                        className="mb-2"
                    />
                ))
            ) : (
                <Form.Text className="text-muted">No options provided.</Form.Text>
            )}
        </Form.Group>
    );
};

export default CheckboxGroupComponent;

