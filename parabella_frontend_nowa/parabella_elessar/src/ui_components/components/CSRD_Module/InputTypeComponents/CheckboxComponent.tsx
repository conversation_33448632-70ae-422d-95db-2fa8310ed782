// form-fields/CheckboxComponent.tsx

import React from 'react';
import { Form } from 'react-bootstrap';
import { CheckSquare } from 'lucide-react';
import styled from 'styled-components';

interface Props {
    label: string;
    checked: boolean;
    onChange: (checked: boolean) => void;
}

const StyledLabel = styled(Form.Label)`
    display: flex;
    align-items: center;
    cursor: pointer;
`;

const CheckboxComponent: React.FC<Props> = ({ label, checked, onChange }) => {
    return (
        <Form.Group className="mb-3" controlId={`checkbox-${label}`}>
            <StyledLabel>
                <CheckSquare className="me-2" size={16} />
                {label}
                <Form.Check
                    type="switch"
                    checked={checked}
                    onChange={(e) => onChange(e.target.checked)}
                    className="ms-auto"
                />
            </StyledLabel>
        </Form.Group>
    );
};

export default CheckboxComponent;
