// form-fields/NumberFieldComponent.tsx

import React from "react";
import { Form, InputGroup } from "react-bootstrap";
import { DollarSign } from "lucide-react";
import styled from "styled-components";

interface Props {
    label: string;
    value: number | '';
    onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
}

const StyledFormControl = styled(Form.Control)`
    border-radius: 8px;
    padding: 10px 12px;
    font-size: 1rem;
    box-shadow: none;
    &:focus {
        border-color: #6c63ff;
        box-shadow: 0 0 0 0.2rem rgba(108, 99, 255, 0.25);
    }
`;

const NumberFieldComponent: React.FC<Props> = ({ label, value, onChange }) => {
    return (
        <Form.Group className="mb-3" controlId={`numberfield-${label}`}>
            <Form.Label>
                <DollarSign className="me-2" />
                {label}
            </Form.Label>
            <InputGroup>
                <InputGroup.Text>$</InputGroup.Text>
                <StyledFormControl
                    type="number"
                    value={value}
                    onChange={onChange}
                    placeholder="Enter amount"
                />
            </InputGroup>
        </Form.Group>
    );
};

export default NumberFieldComponent;
