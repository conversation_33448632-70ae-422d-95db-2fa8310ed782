// form-fields/DropdownComponent.tsx

import React from 'react';
import { Form } from 'react-bootstrap';
import { ChevronDown } from 'lucide-react';

interface CsrdFieldOption {
    id: number;
    optionValue: string;
    child_field_id: number;
    parent_field_id: number;
}

interface Props {
    label: string;
    options: CsrdFieldOption[];
    selectedValue: string;
    onChange: (value: string) => void;
}

const DropdownComponent: React.FC<Props> = ({ label, options, selectedValue, onChange }) => {
    return (
        <Form.Group className="mb-3" controlId={`dropdown-${label}`}>
            <Form.Label>
                <ChevronDown className="me-2" />
                {label}
            </Form.Label>
            <Form.Select
                value={selectedValue}
                onChange={(e) => onChange(e.target.value)}
            >
                <option value="">Select an option</option>
                {options.map((opt) => (
                    <option key={opt.id} value={opt.optionValue}>
                        {opt.optionValue}
                    </option>
                ))}
            </Form.Select>
        </Form.Group>
    );
};

export default DropdownComponent;
