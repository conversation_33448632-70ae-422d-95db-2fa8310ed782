// src/modules/csrd/context/CsrdProjectProvider.tsx

import React, { createContext, useState, useContext, ReactNode, useCallback } from 'react';
import { CsrdProject, CompanyInfo, CsrdProjectContextState, CsrdProjectContextValue } from '../types/csrdTypes'; // Adjust path
// Import API functions
import {
    fetchUserCsrdProjects,
    createCsrdProjectApi,
    saveCompanyInfoApi,
    fetchCompanyInfoForProject,
    fetchCsrdProjectDetails // Optional, if needed
} from '../api/csrdApi'; // Adjust path


// Helper to extract error messages from Axios errors
export const getErrorMessage = (error: any): string => {
    if (error.response) {
        // Server responded with a status code outside 2xx range
        if (error.response.data) {
            // Use server-provided error message if available
            if (typeof error.response.data.message === 'string') {
                return error.response.data.message;
            }
            // Handle validation errors structure from GlobalExceptionHandler
            if (typeof error.response.data.errors === 'object') {
                return Object.values(error.response.data.errors).join(', ');
            }
            // Fallback for other object responses
            if(typeof error.response.data === 'object'){
                return JSON.stringify(error.response.data);
            }
            return String(error.response.data);

        }
        return `Error ${error.response.status}: ${error.response.statusText}`;
    } else if (error.request) {
        // Request was made but no response received
        return 'Network Error: Could not connect to server.';
    } else {
        // Something else happened in setting up the request
        return error.message || 'An unknown error occurred.';
    }
};


const defaultState: CsrdProjectContextState = {
    currentProject: null,
    companyInfo: null, // Still useful for the CompanyInfoForm pre-fill
    userCsrdProjects: [],
    isLoading: false,
    error: null,
};

const CsrdProjectContext = createContext<CsrdProjectContextValue | undefined>(undefined);

interface CsrdProjectProviderProps {
    children: ReactNode;
}

export const CsrdProjectProvider: React.FC<CsrdProjectProviderProps> = ({ children }) => {
    const [state, setState] = useState<CsrdProjectContextState>(defaultState);

    const clearError = useCallback(() => {
        setState(prev => ({ ...prev, error: null }));
    }, []);

    const selectCsrdProject = useCallback(async (project: CsrdProject | null) => {
        setState(prev => ({
            ...prev,
            isLoading: true, // Indicate loading while fetching associated info
            currentProject: project,
            companyInfo: null, // Reset company info initially
            error: null,
        }));

        if (project) {
            try {
                // Fetch associated company info when a project is selected
                const fetchedCompanyInfo = await fetchCompanyInfoForProject(project.id);
                // We could also fetch full project details if the list view is minimal
                // const fullProjectDetails = await fetchCsrdProjectDetails(project.id);
                setState(prev => ({
                    ...prev,
                    // currentProject: fullProjectDetails, // Update if full details fetched
                    companyInfo: fetchedCompanyInfo, // Set fetched info
                    isLoading: false,
                }));
            } catch (err: any) {
                console.error(`Error fetching data for project ${project.id}:`, err);
                setState(prev => ({
                    ...prev,
                    error: `Failed to load details for project ${project.projectName}: ${getErrorMessage(err)}`,
                    isLoading: false,
                    // Optionally clear currentProject if details fail? Or keep it selected?
                    // currentProject: null,
                }));
            }
        } else {
            // No project selected, just finish loading state
            setState(prev => ({ ...prev, isLoading: false }));
        }
    }, []); // No dependencies needed if fetch functions don't depend on state here

    const setCurrentProjectDirectly = useCallback((project: CsrdProject) => {
        // Used after creation, assumes company info might be included or needs fetching
        setState(prev => ({
            ...prev,
            currentProject: project,
            companyInfo: project.companyInfo || null, // Use included info if available
            error: null,
        }));
        // If company info wasn't included in creation response, fetch it
        if (!project.companyInfo && project.id) {
            fetchCompanyInfoForProject(project.id)
                .then(info => setState(prev => ({ ...prev, companyInfo: info })))
                .catch(err => console.error("Error fetching company info after creation:", err));
        }
    }, []);

    const loadUserCsrdProjects = useCallback(async (userId: number) => {
        setState(prev => ({ ...prev, isLoading: true, error: null }));
        try {
            const projects = await fetchUserCsrdProjects(userId);
            setState(prev => ({
                ...prev,
                userCsrdProjects: projects || [],
                isLoading: false,
            }));
        } catch (err: any) {
            console.error("Error loading CSRD projects:", err);
            setState(prev => ({
                ...prev,
                isLoading: false,
                error: getErrorMessage(err),
                userCsrdProjects: [],
            }));
        }
    }, []); // fetchUserCsrdProjects is stable

    const createCsrdProject = useCallback(async (projectData: {
        userId: number;
        projectName: string;
        projectDescription: string;
        projectType: 'company' | 'companyGroup';
    }): Promise<CsrdProject | null> => {
        setState(prev => ({ ...prev, isLoading: true, error: null }));
        try {
            const newProject = await createCsrdProjectApi(projectData);
            setState(prev => ({
                ...prev,
                // Add to list only if creation was successful
                userCsrdProjects: newProject ? [...prev.userCsrdProjects, newProject] : prev.userCsrdProjects,
                isLoading: false,
            }));
            return newProject;
        } catch (err: any) {
            console.error("Error creating CSRD project:", err);
            setState(prev => ({
                ...prev,
                isLoading: false,
                error: getErrorMessage(err),
            }));
            return null;
        }
    }, []); // createCsrdProjectApi is stable

    const saveCompanyInfo = useCallback(async (projectId: number, info: CompanyInfo) => {
        // Check if projectId is valid before proceeding
        if (!projectId) {
            const errMsg = "Cannot save company info: No project selected or project ID is invalid.";
            console.error(errMsg);
            setState(prev => ({ ...prev, error: errMsg, isLoading: false }));
            // Throw an error to be caught by the form's submit handler
            throw new Error(errMsg);
        }

        setState(prev => ({ ...prev, isLoading: true, error: null }));
        try {
            const savedInfo = await saveCompanyInfoApi(projectId, info);
            setState(prev => ({
                ...prev,
                companyInfo: savedInfo, // Update context state with saved info
                // Optionally update the companyInfo within the currentProject as well
                currentProject: prev.currentProject
                    ? { ...prev.currentProject, companyInfo: savedInfo }
                    : null,
                isLoading: false,
            }));
            console.log("Company Info saved via API and context updated.");
            // No need to return anything, success indicated by lack of error
        } catch (err: any) {
            console.error("Error saving company info:", err);
            const errorMessage = getErrorMessage(err);
            setState(prev => ({
                ...prev,
                isLoading: false,
                error: errorMessage,
            }));
            // Re-throw the error so the calling component (CompanyInfoForm) knows it failed
            throw new Error(errorMessage);
        }
    }, []); // saveCompanyInfoApi is stable


    const contextValue: CsrdProjectContextValue = {
        ...state,
        selectCsrdProject,
        loadUserCsrdProjects,
        createCsrdProject,
        saveCompanyInfo,
        setCurrentProjectDirectly,
        clearError
    };

    return (
        <CsrdProjectContext.Provider value={contextValue}>
            {children}
        </CsrdProjectContext.Provider>
    );
};

export const useCsrdProjectContext = (): CsrdProjectContextValue => {
    const context = useContext(CsrdProjectContext);
    if (context === undefined) {
        throw new Error('useCsrdProjectContext must be used within a CsrdProjectProvider');
    }
    return context;
};