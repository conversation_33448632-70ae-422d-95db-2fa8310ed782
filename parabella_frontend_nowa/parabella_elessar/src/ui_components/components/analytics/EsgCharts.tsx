import React, { <PERSON> } from "react";
import {
    Responsive<PERSON><PERSON>r,
    AreaChart,
    Area,
    XAxis,
    <PERSON>Axis,
    CartesianGrid,
    Tooltip,
    Pie<PERSON>hart,
    Pie,
    Cell,
    RadarChart,
    PolarGrid,
    PolarAngle<PERSON>xis,
    Radar,
} from "recharts";

/** ------------------------------------------------------------------------------------------------
 * ESG PERFORMANCE AREA CHART
 * ------------------------------------------------------------------------------------------------*/
interface PerformancePoint {
    year: number;
    emissions: number;
}
interface PerformanceChartProps {
    data?: PerformancePoint[];
}
export const EsgPerformanceAreaChart: FC<PerformanceChartProps> = ({ data }) => {
    if (!data || !data.length) {
        return (
            <div className="futuristic-chart-placeholder">
                <p>No performance trend data</p>
            </div>
        );
    }

    return (
        <div style={{ width: "100%", height: 280 }}>
            <ResponsiveContainer>
                <AreaChart data={data} margin={{ top: 20, right: 30, left: 0, bottom: 0 }}>
                    <defs>
                        <linearGradient id="colorEm" x1="0" y1="0" x2="0" y2="1">
                            <stop offset="5%" stopColor="#00ffaa" stopOpacity={0.8} />
                            <stop offset="95%" stopColor="#00ffaa" stopOpacity={0} />
                        </linearGradient>
                    </defs>
                    <CartesianGrid strokeDasharray="3 3" stroke="rgba(255,255,255,0.05)" />
                    <XAxis dataKey="year" tick={{ fill: "var(--futuristic-text-muted)", fontSize: 11 }} />
                    <YAxis tick={{ fill: "var(--futuristic-text-muted)", fontSize: 11 }} />
                    <Tooltip contentStyle={{ background: "#141c30", border: "none", borderRadius: 8 }} />
                    <Area type="monotone" dataKey="emissions" stroke="#00ffaa" fillOpacity={1} fill="url(#colorEm)" />
                </AreaChart>
            </ResponsiveContainer>
        </div>
    );
};

/** ------------------------------------------------------------------------------------------------
 * STAKEHOLDER ENGAGEMENT RADAR CHART
 * ------------------------------------------------------------------------------------------------*/
interface SentimentData {
    positive: number;
    negative: number;
    neutral?: number;
}
interface StakeholderRadarProps {
    data?: SentimentData;
}
export const StakeholderEngagementRadar: FC<StakeholderRadarProps> = ({ data }) => {
    if (!data) {
        return (
            <div className="futuristic-chart-placeholder">
                <p>No sentiment data</p>
            </div>
        );
    }

    const chartData = [
        { metric: "Positive", value: data.positive },
        { metric: "Negative", value: data.negative },
        { metric: "Neutral", value: data.neutral ?? 0 },
    ];

    const COLORS = ["#00ffaa", "#ff4466", "#8a2be2"];

    return (
        <div style={{ width: "100%", height: 260 }}>
            <ResponsiveContainer>
                <RadarChart data={chartData} outerRadius={90}>
                    <PolarGrid stroke="rgba(255,255,255,0.05)" />
                    <PolarAngleAxis
                        dataKey="metric"
                        tick={{ fill: "var(--futuristic-text-muted)", fontSize: 11 }}
                    />
                    <Radar
                        name="Sentiment"
                        dataKey="value"
                        stroke="#00f2ff"
                        fill="#00f2ff"
                        fillOpacity={0.6}
                    />
                    <Tooltip
                        formatter={(val: number) => `${val}%`}
                        contentStyle={{ background: "#141c30", border: "none", borderRadius: 8 }}
                    />
                </RadarChart>
            </ResponsiveContainer>
        </div>
    );
};

/** ------------------------------------------------------------------------------------------------
 * DONUT HELPER (re‑export)
 * ------------------------------------------------------------------------------------------------*/
export { DonutScoreCard, OverallScoreCard, ClimateScoreCard, SocialScoreCard, GovernanceScoreCard } from "./DonutScoreCard.tsx";
