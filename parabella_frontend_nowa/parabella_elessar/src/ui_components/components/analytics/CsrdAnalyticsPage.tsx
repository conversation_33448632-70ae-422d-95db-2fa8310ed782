// src/modules/csrd/components/CsrdAnalyticsPage.tsx
import React, { useState, useEffect, useCallback, Fragment } from 'react';
import {
    Container, Row, Col, Card, Spinner, Alert, Form, Button, InputGroup,
    ListGroup, ListGroupItem, ProgressBar, Dropdown, Table
} from 'react-bootstrap';
import { Search, TrendingUp, TrendingDown, Target, CheckCircle, AlertCircle, Clock, BarChart, Users, Building, Leaf, HeartHandshake, Scale, BrainCircuit, Eye, ThumbsUp, ThumbsDown, Edit, Trash2 } from 'lucide-react'; // Added more icons

// --- Chart Component Placeholders ---
// In a real app, replace these with actual charting components (e.g., Recharts, Chart.js)
// and pass the relevant ESG data to them.
const EsgPerformanceChartPlaceholder: React.FC<{ data?: any }> = ({ data }) => (
    <div className="text-center p-5 border rounded bg-light">
        <BarChart size={48} className="text-muted mb-2" />
        <p><i>[ESG Performance Trends Chart Placeholder]</i></p>
        {/* Pass 'data' to your actual chart component here */}
        {data && <pre style={{ fontSize: '10px', textAlign: 'left' }}>{JSON.stringify(data, null, 2)}</pre>}
    </div>
);

const StakeholderEngagementChartPlaceholder: React.FC<{ data?: any }> = ({ data }) => (
    <div className="text-center p-5 border rounded bg-light">
        <Users size={48} className="text-muted mb-2" />
        <p><i>[Stakeholder Engagement/Sentiment Chart Placeholder]</i></p>
        {/* Pass 'data' to your actual chart component here */}
        {data && <pre style={{ fontSize: '10px', textAlign: 'left' }}>{JSON.stringify(data, null, 2)}</pre>}
    </div>
);

const EsgBudgetChartPlaceholder: React.FC<{ data?: any }> = ({ data }) => (
    <div className="text-center p-5 border rounded bg-light">
        <Target size={48} className="text-muted mb-2" />
        <p><i>[ESG Initiative Budget Allocation Chart Placeholder]</i></p>
        {/* Pass 'data' to your actual chart component here */}
        {data && <pre style={{ fontSize: '10px', textAlign: 'left' }}>{JSON.stringify(data, null, 2)}</pre>}
    </div>
);
// --- End Chart Placeholders ---


// Import CSS for this page (ensure styles don't clash heavily with dashboard layout)
import '../CSRD_Module/main/css/CsrdAnalyticsPageFuturistic.css';
import {ClimateScoreCard, GovernanceScoreCard, OverallScoreCard, SocialScoreCard} from "./DonutScoreCard.tsx";
import {EsgPerformanceAreaChart, StakeholderEngagementRadar} from "./EsgCharts.tsx";
//import '../../common/css/dashboard-styles.css'; // Add a reference to dashboard styles if needed

// Define the expected structure of the analytics data returned by the API
// This should match the structure within the 'results_data' field in the DB
interface AnalyticsReportData {
    report_metadata: {
        document_name: string;
        analysis_timestamp: string;
        job_id: string;
    };
    executive_summary: string;
    analysis_sections?: {
        // Assuming some structure within these sections
        overall_esg_score?: number; // Example: 0-100
        climate?: {
            summary?: string;
            key_metrics?: { [key: string]: string | number }; // e.g., { "Scope 1 Emissions": "1000 tCO2e", "Reduction Target Met": "Yes" }
            score?: number; // Example: 0-100
            trends?: any[]; // Data for charts
            risks?: string[];
            opportunities?: string[];
            goals?: { text: string, completed: boolean }[];
        };
        social?: {
            summary?: string;
            key_metrics?: { [key: string]: string | number }; // e.g., { "Employee Turnover": "10%", "Diversity Ratio": "45%" }
            score?: number;
            stakeholder_sentiment?: any; // Data for charts
            incidents?: { description: string, date: string, severity: 'Low' | 'Medium' | 'High' }[];
            goals?: { text: string, completed: boolean }[];
        };
        governance?: {
            summary?: string;
            key_metrics?: { [key: string]: string | number }; // e.g., { "Board Independence": "80%", "Compliance Breaches": 0 }
            score?: number;
            compliance_status?: any[]; // Data for table
            policy_updates?: { name: string, status: string, date: string }[];
            goals?: { text: string, completed: boolean }[];
        };
        ai_ethics?: { // Optional section
            summary?: string;
            key_metrics?: { [key: string]: string | number };
            score?: number;
            risks?: string[];
            audits?: any[];
        };
        detailed_metrics?: { // For the table
            id: string;
            category: 'E' | 'S' | 'G' | 'AI';
            metric: string;
            value: string | number;
            unit?: string;
            period?: string;
            trend?: 'Up' | 'Down' | 'Stable';
            status?: 'On Track' | 'Needs Improvement' | 'Exceeded' | 'N/A';
            target?: string | number;
        }[];
        recent_activity?: { // For the timeline
            timestamp: string;
            type: 'Alert' | 'Update' | 'Goal' | 'Milestone';
            description: string;
            severity?: 'Low' | 'Medium' | 'High' | 'Info';
        }[]
    };
}

// Define a more specific error type for API errors
class ApiError extends Error {
    status: number;
    data: any;

    constructor(message: string, status: number, data: any = null) {
        super(message);
        this.name = 'ApiError';
        this.status = status;
        this.data = data;
    }
}

const CsrdAnalyticsPage: React.FC = () => {
    const [documentName, setDocumentName] = useState<string>('');
    const [searchQuery, setSearchQuery] = useState<string>('');
    const [analyticsData, setAnalyticsData] = useState<AnalyticsReportData | null>(null);
    const [isLoading, setIsLoading] = useState<boolean>(false);
    const [error, setError] = useState<string | null>(null);

    // --- API Fetch Function (Keep as is) ---
    const fetchAnalyticsData = useCallback(async (docName: string): Promise<AnalyticsReportData | null> => {
        // ... (keep the existing fetchAnalyticsData implementation)
        if (!docName) return null;

        console.log(`Fetching analytics data for: ${docName}...`);
        setIsLoading(true);
        setError(null);
        setAnalyticsData(null); // Clear previous data

        // --- MOCK DATA FOR TESTING ---
        if (docName.toLowerCase() === "test.pdf") {
            console.warn("Using Mock Data for test.pdf");
            await new Promise(resolve => setTimeout(resolve, 1500)); // Simulate network delay
            const mockData: AnalyticsReportData = {
                report_metadata: {
                    document_name: "test.pdf",
                    analysis_timestamp: new Date().toISOString(),
                    job_id: "mock-job-123"
                },
                executive_summary: "Overall ESG performance shows strong environmental initiatives but requires improvement in social metrics, particularly employee retention. Governance structures are robust, and AI ethics considerations are emerging.",
                analysis_sections: {
                    overall_esg_score: 75,
                    climate: {
                        summary: "Significant progress in reducing Scope 1 & 2 emissions. Investment in renewable energy sources noted.",
                        key_metrics: { "Scope 1+2 Emissions (tCO2e)": 950, "Renewable Energy Usage": "30%", "Water Consumption (m3)": 5000 },
                        score: 85,
                        trends: [{ year: 2021, emissions: 1200 }, { year: 2022, emissions: 1050 }, { year: 2023, emissions: 950 }],
                        risks: ["Potential impact of new carbon tax legislation.", "Water scarcity in Region X."],
                        opportunities: ["Green bonds for financing new projects.", "Improved brand reputation."],
                        goals: [{ text: "Reduce Scope 1 emissions by 5% in 2024", completed: false }, { text: "Source 40% renewable energy by 2025", completed: false }]
                    },
                    social: {
                        summary: "Employee satisfaction scores decreased slightly. Diversity initiatives are ongoing but gender pay gap persists.",
                        key_metrics: { "Employee Turnover Rate": "12%", "Gender Diversity (Women)": "42%", "Safety Incidents (LTI)": 3 },
                        score: 60,
                        stakeholder_sentiment: { positive: 65, negative: 25, neutral: 10 },
                        incidents: [{ description: "Minor safety incident (Plant A)", date: "2023-10-15", severity: "Low" }],
                        goals: [{ text: "Implement new employee wellness program", completed: true }, { text: "Reduce gender pay gap by 3%", completed: false }]
                    },
                    governance: {
                        summary: "Strong board independence and clear reporting lines. Whistleblower policy recently updated.",
                        key_metrics: { "Board Independence": "85%", "Compliance Training Completion": "98%", "Data Privacy Audits": 2 },
                        score: 80,
                        policy_updates: [{ name: "Code of Conduct", status: "Updated", date: "2023-09-01" }, { name: "Whistleblower Policy", status: "Updated", date: "2023-11-01" }],
                        goals: [{ text: "Conduct annual board effectiveness review", completed: true }, { text: "Review supply chain ESG compliance", completed: false }]
                    },
                    ai_ethics: {
                        summary: "Initial assessment of AI use cases completed. Focus on bias detection in algorithms.",
                        key_metrics: { "AI Systems Audited": 1, "Bias Mitigation Techniques Applied": "Yes"},
                        score: 70,
                        risks: ["Algorithmic bias in recruitment tools.", "Lack of transparency in AI decision-making."]
                    },
                    detailed_metrics: [
                        { id: "E1", category: 'E', metric: "Scope 1 Emissions", value: 450, unit: "tCO2e", period: "FY2023", trend: 'Down', status: 'On Track', target: "<500" },
                        { id: "E2", category: 'E', metric: "Water Withdrawal", value: 5000, unit: "m3", period: "FY2023", trend: 'Stable', status: 'Needs Improvement', target: "<4800" },
                        { id: "S1", category: 'S', metric: "Employee Turnover", value: 12, unit: "%", period: "FY2023", trend: 'Up', status: 'Needs Improvement', target: "<10%" },
                        { id: "S2", category: 'S', metric: "Gender Pay Gap", value: 18, unit: "%", period: "FY2023", trend: 'Stable', status: 'Needs Improvement', target: "<15%" },
                        { id: "G1", category: 'G', metric: "Board Independence", value: 85, unit: "%", period: "FY2023", trend: 'Stable', status: 'On Track' },
                        { id: "AI1", category: 'AI', metric: "AI Systems with Bias Audit", value: 1, unit: "systems", period: "FY2023", trend: 'Stable', status: 'On Track', target: "All High-Risk Systems" },
                    ],
                    recent_activity: [
                        { timestamp: "2023-11-15T10:30:00Z", type: 'Update', description: "Whistleblower policy updated.", severity: 'Info'},
                        { timestamp: "2023-11-10T14:00:00Z", type: 'Goal', description: "Employee wellness program launched.", severity: 'Milestone'},
                        { timestamp: "2023-10-28T09:15:00Z", type: 'Alert', description: "Potential risk identified: Carbon tax legislation.", severity: 'Medium'},
                        { timestamp: "2023-10-15T16:05:00Z", type: 'Update', description: "Minor safety incident reported.", severity: 'Low'},
                    ]
                }
            };
            setIsLoading(false);
            return mockData;
        }
        // --- END MOCK DATA ---


        const apiUrlBase = 'http://localhost:8000';
        const encodedDocName = encodeURIComponent(docName);
        const apiUrl = `${apiUrlBase}/api/v1/analytics/results/${encodedDocName}`;

        try {
            const response = await fetch(apiUrl, {
                method: 'GET',
                headers: { 'Accept': 'application/json' },
            });

            if (response.ok) {
                const data: AnalyticsReportData = await response.json();
                console.log("Successfully fetched analytics data:", data);
                // Add mock sections if real data lacks them, for consistent display
                if (!data.analysis_sections) data.analysis_sections = {};
                if (!data.analysis_sections.detailed_metrics) data.analysis_sections.detailed_metrics = []; // Ensure array exists
                if (!data.analysis_sections.recent_activity) data.analysis_sections.recent_activity = []; // Ensure array exists
                return data;
            } else {
                let errorData: any = null;
                let errorMessage = `API Error: ${response.status} ${response.statusText}`;
                try {
                    errorData = await response.json();
                    if (errorData && errorData.detail) {
                        errorMessage = typeof errorData.detail === 'string' ? errorData.detail : JSON.stringify(errorData.detail);
                    }
                } catch (e) { console.warn("Could not parse error response JSON."); }
                console.error(`API request failed with status ${response.status}:`, errorMessage, errorData);
                throw new ApiError(errorMessage, response.status, errorData);
            }
        } catch (err) {
            console.error("Error during fetchAnalyticsData:", err);
            if (err instanceof ApiError) throw err;
            if (err instanceof Error) throw new Error(`Network or unexpected error: ${err.message}`);
            throw new Error('An unknown error occurred during data fetching.');
        } finally {
            // isLoading is handled in useEffect
        }
    }, []); // Keep dependencies as needed

    const handleSearch = () => {
        const trimmedQuery = searchQuery.trim();
        if (trimmedQuery) {
            setDocumentName(trimmedQuery);
        } else {
            setDocumentName('');
            setAnalyticsData(null);
            setError(null);
        }
    };

    const handleKeyPress = (event: React.KeyboardEvent<HTMLInputElement>) => {
        if (event.key === 'Enter') {
            handleSearch();
        }
    };

    // Effect to fetch data when documentName changes
    useEffect(() => {
        if (documentName) {
            setIsLoading(true);
            setError(null);
            setAnalyticsData(null);

            fetchAnalyticsData(documentName)
                .then(data => {
                    setAnalyticsData(data);
                    if (!data) {
                        // Should be handled by 404 in ApiError now, but keep as fallback
                        setError(`No analysis data found for document: ${documentName}.`);
                    }
                })
                .catch(err => {
                    if (err instanceof ApiError) {
                        if (err.status === 404) {
                            setError(`Analytics results not found for: "${documentName}". Verify name and job completion.`);
                        } else {
                            setError(`Error ${err.status}: ${err.message}`);
                        }
                    } else if (err instanceof Error) {
                        setError(err.message);
                    } else {
                        setError("An unknown error occurred.");
                    }
                    setAnalyticsData(null);
                })
                .finally(() => {
                    setIsLoading(false);
                });
        } else {
            setAnalyticsData(null);
            setError(null);
            setIsLoading(false);
        }
    }, [documentName, fetchAnalyticsData]);

    // Helper to get score or N/A
    const getScore = (score: number | undefined) => score !== undefined ? score.toFixed(0) : 'N/A';
    const getMetric = (metrics: { [key: string]: any } | undefined, key: string) => metrics?.[key] ?? 'N/A';

    return (
        <Container fluid className="analytics-page-container p-3 p-md-4">

            {/* --- Page Header & Search --- */}
            <Row className="mb-4 align-items-center">
                <Col md={6}>
                    <h2 className='fw-bold'>ESG Analytics Dashboard</h2>
                    {documentName && !isLoading && analyticsData && <p className="text-muted mb-0">Displaying results for: {analyticsData.report_metadata.document_name}</p>}
                    {documentName && !isLoading && analyticsData && analyticsData.report_metadata?.analysis_timestamp && <p className="text-muted fs-sm mb-0">Generated: {new Date(analyticsData.report_metadata.analysis_timestamp).toLocaleString()}</p>}
                </Col>
                <Col md={6}>
                    <InputGroup className="mt-2 mt-md-0">
                        <Form.Control
                            type="text"
                            placeholder="Enter document name (e.g., report.pdf or test.pdf)"
                            value={searchQuery}
                            onChange={(e) => setSearchQuery(e.target.value)}
                            onKeyPress={handleKeyPress}
                            disabled={isLoading}
                        />
                        <Button variant="primary" onClick={handleSearch} disabled={isLoading || !searchQuery.trim()}>
                            <Search size={18} />
                            {isLoading ? ' Loading...' : ' Load Analytics'}
                        </Button>
                    </InputGroup>
                    <Form.Text className="text-muted">
                        Enter the document name used during analysis (try "test.pdf" for mock data).
                    </Form.Text>
                </Col>
            </Row>


            {/* --- Loading State --- */}
            {isLoading && (
                <div className="text-center my-5">
                    <Spinner animation="border" variant="primary" role="status">
                        <span className="visually-hidden">Loading analytics...</span>
                    </Spinner>
                    <p className="mt-3 text-primary">Loading analytics for "{documentName}"...</p>
                </div>
            )}

            {/* --- Error State --- */}
            {error && !isLoading && (
                <Alert variant="danger" onClose={() => setError(null)} dismissible className="shadow-sm">
                    <Alert.Heading><AlertCircle className="me-2" /> Error Loading Analytics</Alert.Heading>
                    <p>{error}</p>
                </Alert>
            )}

            {/* --- Initial State / No Search --- */}
            {!isLoading && !error && !analyticsData && !documentName && (
                <Alert variant="info" className="text-center shadow-sm">
                    <p className="mb-0">Please enter a document name above to load the ESG analytics dashboard.</p>
                </Alert>
            )}

            {/* --- Success State - Display Dashboard --- */}
            {!isLoading && !error && analyticsData && (
                <Fragment>
                    {/* --- Top Row: Key ESG Scores/Metrics --- */}
                    <Row>
                        <Col xl={3} lg={6} md={6} sm={12} className="mb-3">
                            <OverallScoreCard
                                title="Overall ESG Score"
                                score={analyticsData.analysis_sections?.overall_esg_score ?? 0}
                                subtitle=""
                            />
                        </Col>
                        <Col xl={3} lg={6} md={6} sm={12} className="mb-3">
                            <ClimateScoreCard
                                title="Climate Score (E)"
                                score={analyticsData.analysis_sections?.climate?.score ?? 0}
                                subtitle={`Emissions: ${getMetric(
                                    analyticsData.analysis_sections?.climate?.key_metrics,
                                    'Scope 1+2 Emissions (tCO2e)'
                                )}`}
                            />
                        </Col>
                        <Col xl={3} lg={6} md={6} sm={12} className="mb-3">
                            <SocialScoreCard
                                title="Social Score (S)"
                                score={analyticsData.analysis_sections?.social?.score ?? 0}
                                subtitle={`Turnover: ${getMetric(
                                    analyticsData.analysis_sections?.social?.key_metrics,
                                    'Employee Turnover Rate'
                                )}`}
                            />
                        </Col>
                        <Col xl={3} lg={6} md={6} sm={12} className="mb-3">
                            <GovernanceScoreCard
                                title="Governance Score (G)"
                                score={analyticsData.analysis_sections?.governance?.score ?? 0}
                                subtitle={`Board Indep: ${getMetric(
                                    analyticsData.analysis_sections?.governance?.key_metrics,
                                    'Board Independence'
                                )}`}
                            />
                        </Col>
                    </Row>

                    {/* --- Executive Summary --- */}
                    <Row>
                        <Col>
                            <Card className="mb-4 shadow-sm">
                                <Card.Header>
                                    <Building size={18} className="me-2"/> Executive Summary
                                </Card.Header>
                                <Card.Body>
                                    <p className="executive-summary">{analyticsData.executive_summary || "No executive summary available."}</p>
                                </Card.Body>
                            </Card>
                        </Col>
                    </Row>

                    <Row>
                        {/* --- ESG Performance Chart & Summaries --- */}
                        <Col xl={8} lg={12} className="mb-4">
                            <Card className="custom-card overflow-hidden shadow-sm h-100">
                                <Card.Header className="border-bottom-0 d-flex justify-content-between align-items-center">
                                    <h5 className="card-title mb-0">ESG Performance Trends</h5>
                                    {/* Optional: Timeframe Selector */}
                                    {/* <div className="btn-group btn-group-sm p-0">
                                        <Button variant="outline-secondary" size="sm">1Y</Button>
                                        <Button variant="light" size="sm">3Y</Button>
                                        <Button variant="outline-secondary" size="sm">5Y</Button>
                                    </div> */}
                                </Card.Header>
                                <Card.Body>
                                    {/* Key Metrics Display */}
                                    <Row className="mb-3 text-center text-md-start">
                                        <Col md={4} sm={12} className="mb-2 mb-md-0">
                                            <p className="text-muted mb-1 fs-sm">Total Emissions (Scope 1+2)</p>
                                            <h6 className="mb-0">{getMetric(analyticsData.analysis_sections?.climate?.key_metrics, 'Scope 1+2 Emissions (tCO2e)')}</h6>
                                            {/* <p className="fs-xs text-success mb-0"><TrendingDown size={12}/> -5% vs last year</p> */}
                                        </Col>
                                        <Col md={4} sm={12} className="mb-2 mb-md-0">
                                            <p className="text-muted mb-1 fs-sm">Employee Diversity %</p>
                                            <h6 className="mb-0">{getMetric(analyticsData.analysis_sections?.social?.key_metrics, 'Gender Diversity (Women)')}</h6>
                                            {/* <p className="fs-xs text-success mb-0"><TrendingUp size={12}/> +2% vs last year</p> */}
                                        </Col>
                                        <Col md={4} sm={12}>
                                            <p className="text-muted mb-1 fs-sm">Open Governance Actions</p>
                                            <h6 className="mb-0">{analyticsData.analysis_sections?.governance?.goals?.filter(g => !g.completed).length ?? 'N/A'}</h6>
                                            {/* <p className="fs-xs text-danger mb-0">Needs Attention</p> */}
                                        </Col>
                                    </Row>
                                    {/* Chart Placeholder */}
                                    <EsgPerformanceAreaChart
                                        data={analyticsData.analysis_sections?.climate?.trends as any}
                                    />   </Card.Body>
                            </Card>
                        </Col>

                        {/* --- Recent ESG Activity/Timeline --- */}
                        <Col xl={4} lg={12} className="mb-4">
                            <Card className="shadow-sm h-100">
                                <Card.Header className="bg-transparent pb-0 border-bottom-0">
                                    <h5 className="card-title mb-2"><Clock size={18} className="me-2"/>Recent ESG Activity</h5>
                                </Card.Header>
                                <Card.Body className="mt-0 pt-2 esg-timeline">
                                    {analyticsData.analysis_sections?.recent_activity && analyticsData.analysis_sections.recent_activity.length > 0 ? (
                                        <ListGroup variant="flush" className="latest-timelines">
                                            {analyticsData.analysis_sections.recent_activity.slice(0, 6).map((activity, index) => ( // Limit items shown
                                                <ListGroup.Item key={index} className="border-0 px-0 py-2 d-flex">
                                                    <div className={`timeline-icon me-3 ${activity.severity?.toLowerCase() || 'info'}`}>
                                                        {activity.type === 'Alert' && <AlertCircle size={16}/>}
                                                        {activity.type === 'Update' && <Edit size={16}/>}
                                                        {activity.type === 'Goal' && <Target size={16}/>}
                                                        {activity.type === 'Milestone' && <CheckCircle size={16}/>}
                                                    </div>
                                                    <div className="flex-grow-1">
                                                        <p className="mb-0 fw-semibold text-dark fs-sm">{activity.description}</p>
                                                        <span className="fs-xs text-muted">{new Date(activity.timestamp).toLocaleString()} ({activity.type})</span>
                                                    </div>
                                                </ListGroup.Item>
                                            ))}
                                        </ListGroup>
                                    ) : (
                                        <p className="text-muted text-center mt-3">No recent activity recorded.</p>
                                    )}
                                </Card.Body>
                                {/* Optionally add a "View All" link */}
                                {/* <Card.Footer className="text-center bg-light border-top-0">
                                    <a href="#more-activity" className="fs-sm">View All Activity</a>
                                </Card.Footer> */}
                            </Card>
                        </Col>
                    </Row>

                    <Row>
                        {/* --- ESG Pillar Performance (Progress Bars) --- */}
                        <Col xl={4} lg={12} md={12} className="mb-4">
                            <Card className="shadow-sm h-100">
                                <Card.Header className="pb-3">
                                    <h5 className="card-title mb-0"><BarChart size={18} className="me-2"/>ESG Pillar Performance</h5>
                                </Card.Header>
                                <Card.Body className="pt-3">
                                    {analyticsData.analysis_sections?.climate && (
                                        <div className="mb-4">
                                            <div className="d-flex justify-content-between">
                                                <span className="fs-sm fw-semibold"><Leaf size={14} className="me-1 text-success"/> Environment</span>
                                                <span className="number-font fw-bold">{getScore(analyticsData.analysis_sections.climate.score)}%</span>
                                            </div>
                                            <ProgressBar variant="success" now={analyticsData.analysis_sections.climate.score || 0} className="progress-style ht-8 mt-1" />
                                            {/* <p className="fs-xs text-muted mt-1">{analyticsData.analysis_sections.climate.summary?.substring(0, 80)}...</p> */}
                                        </div>
                                    )}
                                    {analyticsData.analysis_sections?.social && (
                                        <div className="mb-4">
                                            <div className="d-flex justify-content-between">
                                                <span className="fs-sm fw-semibold"><HeartHandshake size={14} className="me-1 text-warning"/> Social</span>
                                                <span className="number-font fw-bold">{getScore(analyticsData.analysis_sections.social.score)}%</span>
                                            </div>
                                            <ProgressBar variant="warning" now={analyticsData.analysis_sections.social.score || 0} className="progress-style ht-8 mt-1" />
                                            {/* <p className="fs-xs text-muted mt-1">{analyticsData.analysis_sections.social.summary?.substring(0, 80)}...</p> */}
                                        </div>
                                    )}
                                    {analyticsData.analysis_sections?.governance && (
                                        <div className="mb-4">
                                            <div className="d-flex justify-content-between">
                                                <span className="fs-sm fw-semibold"><Scale size={14} className="me-1 text-info"/> Governance</span>
                                                <span className="number-font fw-bold">{getScore(analyticsData.analysis_sections.governance.score)}%</span>
                                            </div>
                                            <ProgressBar variant="info" now={analyticsData.analysis_sections.governance.score || 0} className="progress-style ht-8 mt-1" />
                                            {/* <p className="fs-xs text-muted mt-1">{analyticsData.analysis_sections.governance.summary?.substring(0, 80)}...</p> */}
                                        </div>
                                    )}
                                    {analyticsData.analysis_sections?.ai_ethics && (
                                        <div className="mb-3">
                                            <div className="d-flex justify-content-between">
                                                <span className="fs-sm fw-semibold"><BrainCircuit size={14} className="me-1 text-secondary"/> AI Ethics</span>
                                                <span className="number-font fw-bold">{getScore(analyticsData.analysis_sections.ai_ethics.score)}%</span>
                                            </div>
                                            <ProgressBar variant="secondary" now={analyticsData.analysis_sections.ai_ethics.score || 0} className="progress-style ht-8 mt-1" />
                                        </div>
                                    )}
                                    {!analyticsData.analysis_sections?.climate && !analyticsData.analysis_sections?.social && !analyticsData.analysis_sections?.governance && (
                                        <p className="text-muted text-center mt-3">Pillar performance data not available.</p>
                                    )}
                                </Card.Body>
                            </Card>
                        </Col>

                        {/* --- Stakeholder Engagement / Sentiment --- */}
                        <Col xl={4} lg={12} md={12} className="mb-4">
                            <Card className="shadow-sm h-100">
                                <Card.Header className="pb-0 border-bottom-0">
                                    <h5 className="card-title mb-2"><Users size={18} className="me-2"/>Stakeholder Engagement</h5>
                                </Card.Header>
                                <Card.Body className="pb-0">
                                    <Row className="mb-3 text-center">
                                        <Col xs={6}>
                                            <div className="text-muted fs-sm mb-1">Positive Sentiment</div>
                                            <div className="d-flex justify-content-center align-items-center">
                                                <ThumbsUp size={16} className="text-success me-2"/>
                                                <span className="me-3 fs-lg fw-semibold">
                                                    {analyticsData.analysis_sections?.social?.stakeholder_sentiment?.positive ?? 'N/A'}%
                                                </span>
                                            </div>
                                        </Col>
                                        <Col xs={6}>
                                            <div className="text-muted fs-sm mb-1">Negative Sentiment</div>
                                            <div className="d-flex justify-content-center align-items-center">
                                                <ThumbsDown size={16} className="text-danger me-2"/>
                                                <span className="me-3 fs-lg fw-semibold">
                                                   {analyticsData.analysis_sections?.social?.stakeholder_sentiment?.negative ?? 'N/A'}%
                                                </span>
                                            </div>
                                        </Col>
                                    </Row>
                                    {/* Chart Placeholder */}
                                    <StakeholderEngagementRadar
                                        data={analyticsData.analysis_sections?.social?.stakeholder_sentiment as any}
                                    />   </Card.Body>
                            </Card>
                        </Col>

                        {/* --- ESG Goals & Action Items --- */}
                        <Col xl={4} lg={12} md={12} className="mb-4">

                        </Col>
                    </Row>


                    {/* --- Detailed ESG Metrics Table --- */}
                    <Row>
                        <Col sm={12} className="col-12">
                            <Card className="shadow-sm">
                                <Card.Header className="d-flex justify-content-between align-items-center">
                                    <h5 className="card-title mb-0"><Eye size={18} className="me-2"/>Detailed ESG Metrics & Risks</h5>
                                    {/* Optional: Add controls like search/filter here */}
                                    {/* <Dropdown size="sm">
                                         <Dropdown.Toggle variant="outline-secondary" id="metrics-sort">
                                             Sort By <i className="ri-arrow-down-s-line align-middle ms-1 d-inline-block"></i>
                                         </Dropdown.Toggle>
                                         <Dropdown.Menu>
                                             <Dropdown.Item href="#">Category</Dropdown.Item>
                                             <Dropdown.Item href="#">Status</Dropdown.Item>
                                             <Dropdown.Item href="#">Trend</Dropdown.Item>
                                         </Dropdown.Menu>
                                     </Dropdown> */}
                                </Card.Header>
                                <Card.Body className="pt-0">
                                    {analyticsData.analysis_sections?.detailed_metrics && analyticsData.analysis_sections.detailed_metrics.length > 0 ? (
                                        <div className="table-responsive mt-3">
                                            <Table hover className="table text-nowrap mb-0" id="esg-metrics-table">
                                                <thead className="table-light">
                                                <tr>
                                                    <th className="text-center">Category</th>
                                                    <th>Metric / Risk</th>
                                                    <th>Value</th>
                                                    <th>Unit</th>
                                                    <th>Period</th>
                                                    <th>Trend</th>
                                                    <th>Status</th>
                                                    <th>Target</th>
                                                    {/* <th>Action</th> */}
                                                </tr>
                                                </thead>
                                                <tbody>
                                                {analyticsData.analysis_sections.detailed_metrics.map((item) => (
                                                    <tr key={item.id}>
                                                        <td className="text-center">
                                                            <span className={`badge bg-${item.category === 'E' ? 'success' : item.category === 'S' ? 'warning' : item.category === 'G' ? 'info' : 'secondary'}-light fw-semibold`}>{item.category}</span>
                                                        </td>
                                                        <td>{item.metric}</td>
                                                        <td>{item.value}</td>
                                                        <td>{item.unit || '-'}</td>
                                                        <td>{item.period || '-'}</td>
                                                        <td>
                                                            {item.trend === 'Up' && <TrendingUp size={16} className="text-danger" />}
                                                            {item.trend === 'Down' && <TrendingDown size={16} className="text-success" />}
                                                            {item.trend === 'Stable' && <span className="text-muted">-</span>}
                                                            {!item.trend && '-'}
                                                        </td>
                                                        <td>
                                                                 <span className={`badge bg-${item.status === 'On Track' || item.status === 'Exceeded' ? 'success' : item.status === 'Needs Improvement' ? 'danger' : 'light'}-transparent`}>
                                                                     {item.status || 'N/A'}
                                                                 </span>
                                                        </td>
                                                        <td>{item.target || '-'}</td>
                                                        {/* <td className="">
                                                                 <Button variant="outline-primary" size="sm" className="me-1 btn-icon"><Eye size={14}/></Button>
                                                                 <Button variant="outline-secondary" size="sm" className="btn-icon"><Edit size={14}/></Button>
                                                            </td> */}
                                                    </tr>
                                                ))}
                                                </tbody>
                                            </Table>
                                        </div>
                                    ) : (
                                        <Alert variant="light" className="text-center mt-3">No detailed metrics available.</Alert>
                                    )}
                                </Card.Body>
                            </Card>
                        </Col>
                    </Row>

                    {/* --- Optional Sections (e.g., Risks/Opportunities Lists) --- */}
                    {/* You can add more cards here to display lists like: */}
                    {/* - Climate Risks (from analysis_sections.climate.risks) */}
                    {/* - Social Incidents (from analysis_sections.social.incidents) */}
                    {/* - Governance Policy Updates (from analysis_sections.governance.policy_updates) */}

                    <Row className="mt-4">
                        <Col md={6} className="mb-4">
                            <Card className='shadow-sm h-100'>
                                <Card.Header><AlertCircle size={16} className='me-2 text-danger'/> Key Risks Identified</Card.Header>
                                <ListGroup variant='flush'>
                                    {analyticsData.analysis_sections?.climate?.risks?.slice(0,3).map((risk, i) => <ListGroup.Item key={`cr-${i}`} className='fs-sm'>{risk}</ListGroup.Item>)}
                                    {analyticsData.analysis_sections?.social?.incidents?.slice(0,2).map((inc, i) => <ListGroup.Item key={`si-${i}`} className='fs-sm'>{inc.description} ({inc.severity})</ListGroup.Item>)}
                                    {analyticsData.analysis_sections?.ai_ethics?.risks?.slice(0,2).map((risk, i) => <ListGroup.Item key={`ai-${i}`} className='fs-sm'>{risk}</ListGroup.Item>)}
                                    {(!analyticsData.analysis_sections?.climate?.risks?.length && !analyticsData.analysis_sections?.social?.incidents?.length && !analyticsData.analysis_sections?.ai_ethics?.risks?.length) && <ListGroup.Item className='text-muted text-center'>No key risks highlighted.</ListGroup.Item>}
                                </ListGroup>
                            </Card>
                        </Col>
                        <Col md={6} className="mb-4">
                            <Card className='shadow-sm h-100'>
                                <Card.Header><CheckCircle size={16} className='me-2 text-success'/> Key Opportunities</Card.Header>
                                <ListGroup variant='flush'>
                                    {analyticsData.analysis_sections?.climate?.opportunities?.slice(0,3).map((opp, i) => <ListGroup.Item key={`co-${i}`} className='fs-sm'>{opp}</ListGroup.Item>)}
                                    {/* Add opportunities from other sections if available */}
                                    {(!analyticsData.analysis_sections?.climate?.opportunities?.length) && <ListGroup.Item className='text-muted text-center'>No key opportunities highlighted.</ListGroup.Item>}
                                </ListGroup>
                            </Card>
                        </Col>
                    </Row>


                </Fragment>
            )}
        </Container>
    );
};

export default CsrdAnalyticsPage;