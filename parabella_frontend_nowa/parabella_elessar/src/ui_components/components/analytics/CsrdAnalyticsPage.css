/* src/modules/csrd/components/css/CsrdAnalyticsPageFuturistic.css */

/* --- Root Variables --- */
:root {
    --primary-color: rgb(56, 202, 179);
    --primary-rgb: 56, 202, 179; /* For use in rgba */
    --secondary-color: #7E9F14;

    /* Dark Theme Palette (inspired by OrbitNest example) */
    --futuristic-bg-dark: #0F132A; /* Deep dark blue/purple */
    --futuristic-bg-medium: #1A1F37; /* Slightly lighter for card surfaces, panels */
    --futuristic-bg-light: #252A40; /* Lighter accents, hover states */

    --futuristic-card-bg: rgba(26, 31, 55, 0.75); /* Semi-transparent card bg */
    --futuristic-card-border: rgba(56, 77, 130, 0.3); /* Subtle border */
    --futuristic-card-shadow: 0 8px 24px rgba(0, 0, 0, 0.25);
    --futuristic-card-hover-shadow: 0 12px 32px rgba(var(--primary-rgb), 0.15);


    --futuristic-text-primary: #E0E6F1; /* Primary text color - slightly bluish white */
    --futuristic-text-secondary: #A8B2C5; /* Secondary text color */
    --futuristic-text-muted: #7B88A0; /* Muted text, placeholders */

    --futuristic-accent-primary: var(--primary-color);
    --futuristic-accent-secondary: var(--secondary-color);
    --futuristic-accent-info: #5A67D8; /* A nice blue for info states */
    --futuristic-accent-success: #38A169; /* Green for success */
    --futuristic-accent-warning: #DD6B20; /* Orange for warning */
    --futuristic-accent-danger: #E53E3E; /* Red for danger */

    --futuristic-font-family: 'Inter', -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
    --futuristic-border-radius: 0.875rem; /* 14px */
    --futuristic-border-radius-sm: 0.625rem; /* 10px */
}

/* --- Global Styles --- */
/* Apply to body if this page is the whole view, or a specific container */
.analytics-page-container, body.futuristic-theme {
    font-family: var(--futuristic-font-family);
    background-color: var(--futuristic-bg-dark);
    color: var(--futuristic-text-primary);
    padding-bottom: 2rem;
    min-height: 100vh;
}

/* --- Typography --- */
h1, h2, h3, h4, h5, h6 {
    color: var(--futuristic-text-primary);
    font-weight: 600;
}
.analytics-page-container h2.fw-bold { /* Page Title */
    font-size: 2rem;
    font-weight: 700 !important;
    letter-spacing: -0.5px;
    margin-bottom: 0.5rem;
    background: linear-gradient(90deg, var(--futuristic-accent-primary), var(--futuristic-accent-info));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    display: inline-block;
}


p {
    color: var(--futuristic-text-secondary);
    line-height: 1.7;
}

a {
    color: var(--futuristic-accent-primary);
    text-decoration: none;
    transition: color 0.2s ease;
}
a:hover {
    color: rgba(var(--primary-rgb), 0.8);
    text-decoration: none; /* No underline for modern look */
}

/* --- Bootstrap Component Overrides & Enhancements --- */

/* Cards */
.card {
    background-color: var(--futuristic-card-bg);
    border: 1px solid var(--futuristic-card-border);
    border-radius: var(--futuristic-border-radius);
    box-shadow: var(--futuristic-card-shadow);
    margin-bottom: 1.75rem; /* Increased margin */
    backdrop-filter: blur(12px);
    -webkit-backdrop-filter: blur(12px);
    transition: transform 0.2s ease-out, box-shadow 0.2s ease-out;
}
.card:hover {
    /* transform: translateY(-3px); */ /* Optional: subtle lift on hover */
    box-shadow: var(--futuristic-card-hover-shadow);
}

.card-header {
    background-color: rgba(37, 42, 64, 0.6); /* Slightly different from card body */
    border-bottom: 1px solid var(--futuristic-card-border);
    color: var(--futuristic-text-primary);
    font-weight: 600; /* Bolder header text */
    padding: 1rem 1.5rem; /* Increased padding */
    border-top-left-radius: var(--futuristic-border-radius);
    border-top-right-radius: var(--futuristic-border-radius);
    display: flex;
    align-items: center;
}
.card-header .card-title {
    color: var(--futuristic-text-primary);
    font-size: 1.15rem; /* Slightly larger title */
    margin-bottom: 0;
}
.card-header .lucide { /* Align icons nicely in header */
    margin-right: 0.6rem;
    color: var(--futuristic-accent-primary); /* Color icons in header */
}


.card-body {
    padding: 1.5rem; /* Increased padding */
    color: var(--futuristic-text-secondary);
}
.card-body p, .card-body .text-muted {
    color: var(--futuristic-text-muted) !important;
}
.card-body h6 {
    color: var(--futuristic-text-primary);
    font-weight: 600;
}

/* Form Controls (Search Input) */
.form-control {
    background-color: var(--futuristic-bg-light);
    border: 1px solid var(--futuristic-card-border);
    color: var(--futuristic-text-primary);
    border-radius: var(--futuristic-border-radius-sm);
    padding: 0.75rem 1.15rem; /* Increased padding */
    transition: border-color 0.2s ease, box-shadow 0.2s ease;
}
.form-control:focus {
    background-color: var(--futuristic-bg-medium); /* Darker on focus */
    color: var(--futuristic-text-primary);
    border-color: var(--futuristic-accent-primary);
    box-shadow: 0 0 0 3px rgba(var(--primary-rgb), 0.2); /* Softer shadow */
}
.form-control::placeholder {
    color: var(--futuristic-text-muted);
    opacity: 0.8;
}
.form-text.text-muted {
    color: var(--futuristic-text-muted) !important;
    font-size: 0.8rem;
    margin-top: 0.5rem;
}

.input-group {
    border-radius: var(--futuristic-border-radius-sm);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15); /* Add shadow to input group */
}
.input-group .form-control {
    border-right-color: transparent; /* Make it seamless with button */
}
.input-group .btn {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
}


/* Buttons */
.btn {
    border-radius: var(--futuristic-border-radius-sm);
    padding: 0.75rem 1.5rem; /* Increased padding */
    font-weight: 600; /* Bolder button text */
    letter-spacing: 0.5px;
    transition: all 0.2s ease-in-out;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}
.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}
.btn-primary {
    background-image: linear-gradient(45deg, var(--futuristic-accent-primary), color-mix(in srgb, var(--futuristic-accent-primary) 80%, var(--futuristic-accent-info) 20%));
    border: none;
    color: var(--futuristic-bg-dark); /* Dark text on light primary */
}
.btn-primary:hover, .btn-primary:focus {
    background-image: linear-gradient(45deg, color-mix(in srgb, var(--futuristic-accent-primary) 90%, black 10%), color-mix(in srgb, var(--futuristic-accent-primary) 70%, var(--futuristic-accent-info) 20%, black 10%));
    box-shadow: 0 0 0 3px rgba(var(--primary-rgb), 0.25);
    color: var(--futuristic-bg-dark);
}
.btn-primary:disabled {
    background-image: none;
    background-color: var(--futuristic-text-muted);
    border-color: var(--futuristic-text-muted);
    opacity: 0.6;
    cursor: not-allowed;
}
.btn-outline-secondary {
    color: var(--futuristic-text-secondary);
    border-color: var(--futuristic-card-border);
}
.btn-outline-secondary:hover {
    background-color: var(--futuristic-bg-light);
    color: var(--futuristic-text-primary);
    border-color: var(--futuristic-accent-primary);
}
.btn .lucide { /* Align icons in buttons */
    vertical-align: middle;
    margin-right: 0.3em; /* Space between icon and text */
    margin-bottom: 0.1em; /* Fine-tune vertical alignment */
}


/* Alerts */
.alert {
    border-radius: var(--futuristic-border-radius); /* Match card radius */
    border-width: 1px;
    border-style: solid;
    padding: 1.25rem 1.5rem;
    box-shadow: 0 4px 10px rgba(0,0,0,0.1);
}
.alert .alert-heading {
    font-weight: 600;
    display: flex;
    align-items: center;
}
.alert .alert-heading .lucide {
    margin-right: 0.5rem;
}
.alert-danger {
    background-color: rgba(229, 62, 62, 0.1);
    border-color: var(--futuristic-accent-danger);
    color: color-mix(in srgb, var(--futuristic-accent-danger) 80%, white 20%);
}
.alert-danger .alert-heading, .alert-danger p {
    color: color-mix(in srgb, var(--futuristic-accent-danger) 85%, white 15%);
}
.alert-info {
    background-color: rgba(90, 103, 216, 0.1);
    border-color: var(--futuristic-accent-info);
    color: color-mix(in srgb, var(--futuristic-accent-info) 80%, white 20%);
}
.alert-info p {
    color: color-mix(in srgb, var(--futuristic-accent-info) 85%, white 15%);
}

/* Spinner */
.spinner-border {
    width: 2.5rem; /* Larger spinner */
    height: 2.5rem;
    border-width: 0.25em;
    color: var(--futuristic-accent-primary);
}
.text-primary { /* For spinner text */
    color: var(--futuristic-accent-primary) !important;
    font-weight: 500;
    font-size: 1.1rem;
}

/* Progress Bars */
.progress {
    height: 12px; /* Slightly thicker */
    border-radius: var(--futuristic-border-radius-sm);
    background-color: var(--futuristic-bg-light);
    overflow: visible; /* To allow for labels outside if needed */
}
.progress-bar {
    border-radius: var(--futuristic-border-radius-sm);
    box-shadow: inset 0 -1px 1px rgba(0,0,0,0.15);
    transition: width .6s ease;
}
.progress-bar.bg-success { background-color: var(--futuristic-accent-success); }
.progress-bar.bg-warning { background-color: var(--futuristic-accent-warning); }
.progress-bar.bg-info { background-color: var(--futuristic-accent-info); }
.progress-bar.bg-secondary { background-color: var(--futuristic-text-muted); }

/* Tables */
.table-responsive {
    border-radius: var(--futuristic-border-radius-sm);
    border: 1px solid var(--futuristic-card-border);
    overflow: hidden; /* Ensure rounded corners are applied */
    margin-top: 1rem; /* Add some space above table */
}
.table {
    color: var(--futuristic-text-secondary);
    border-color: var(--futuristic-card-border);
    margin-bottom: 0; /* Remove default margin as it's in .table-responsive */
}
.table th, .table td {
    border-color: var(--futuristic-card-border);
    padding: 1rem 1.25rem; /* Increased padding */
    vertical-align: middle;
    white-space: nowrap; /* Prevent wrapping in cells by default */
}
.table th:nth-child(2), .table td:nth-child(2) { /* Metric/Risk column can wrap */
    white-space: normal;
    min-width: 200px; /* Give it some space */
}
.table thead th {
    background-color: rgba(37, 42, 64, 0.7); /* Darker header for table */
    color: var(--futuristic-text-primary);
    font-weight: 600;
    font-size: 0.9rem;
    letter-spacing: 0.3px;
    border-bottom-width: 2px;
    border-bottom-color: var(--futuristic-accent-primary); /* Accent line under header */
}
.table tbody tr {
    transition: background-color 0.15s ease;
}
.table tbody tr:hover {
    background-color: var(--futuristic-bg-light);
    color: var(--futuristic-text-primary);
}

/* Badges in Table */
.badge {
    font-size: 0.8rem; /* Slightly larger badges */
    padding: 0.45em 0.8em;
    font-weight: 600; /* Bolder badge text */
    border-radius: var(--futuristic-border-radius-sm);
    letter-spacing: 0.2px;
}
.bg-success-light { background-color: rgba(var(--primary-rgb), 0.15) !important; color: var(--futuristic-accent-primary) !important; } /* Using primary for 'E' category as per OrbitNest green */
.bg-warning-light { background-color: rgba(221, 107, 32, 0.15) !important; color: var(--futuristic-accent-warning) !important; }
.bg-info-light { background-color: rgba(90, 103, 216, 0.15) !important; color: var(--futuristic-accent-info) !important; }
.bg-secondary-light { background-color: rgba(123, 136, 160, 0.15) !important; color: var(--futuristic-text-muted) !important; }

/* Specific to status badges in table */
.badge.bg-success-transparent { background-color: rgba(56, 161, 105, 0.15) !important; color: var(--futuristic-accent-success) !important; border: 1px solid rgba(var(--futuristic-accent-success),0.5); }
.badge.bg-danger-transparent { background-color: rgba(229, 62, 62, 0.15) !important; color: var(--futuristic-accent-danger) !important; border: 1px solid rgba(var(--futuristic-accent-danger),0.5); }
.badge.bg-light-transparent { background-color: rgba(123, 136, 160, 0.1) !important; color: var(--futuristic-text-muted) !important; border: 1px solid rgba(var(--futuristic-text-muted),0.5); }


/* List Group (for Recent Activity & Key Risks/Opportunities) */
.list-group-item {
    background-color: transparent;
    border-color: var(--futuristic-card-border);
    color: var(--futuristic-text-secondary);
    padding: 0.85rem 0.25rem; /* Adjust if using .px-0 from bootstrap */
    font-size: 0.9rem;
}
.list-group-item:first-child { border-top: none; }
.list-group-item:last-child { border-bottom: none; }

.list-group.flush .list-group-item {
    border-right: 0;
    border-left: 0;
    border-radius: 0;
}


/* --- Page Specific Styles --- */

/* Page Header Text */
.analytics-page-container p.text-muted { /* For sub-header text */
    color: var(--futuristic-text-muted) !important;
    font-size: 0.9rem;
}
.analytics-page-container .fs-sm { font-size: 0.9rem; }
.analytics-page-container .fs-xs { font-size: 0.8rem; }

/* Executive Summary */
.executive-summary {
    font-size: 1.05rem; /* Slightly larger */
    line-height: 1.8;
    white-space: pre-wrap;
    color: var(--futuristic-text-secondary);
}

/* ESG Pillar Performance */
.progress-style.ht-8 { height: 8px !important; }
.fs-sm.fw-semibold { /* For pillar labels */
    color: var(--futuristic-text-primary);
    font-size: 0.95rem;
    display: flex;
    align-items: center;
}
.fs-sm.fw-semibold .lucide {
    margin-right: 0.4em;
    color: currentColor; /* Inherit color from parent span if not specifically colored */
}
.fs-sm.fw-semibold .text-success .lucide { color: var(--futuristic-accent-success); } /* Example */
.fs-sm.fw-semibold .text-warning .lucide { color: var(--futuristic-accent-warning); }
.fs-sm.fw-semibold .text-info .lucide { color: var(--futuristic-accent-info); }
.fs-sm.fw-semibold .text-secondary .lucide { color: var(--futuristic-text-muted); }

.number-font.fw-bold { /* For pillar scores */
    color: var(--futuristic-text-primary);
    font-size: 1.1rem;
}

/* Recent ESG Activity Timeline */
.esg-timeline .list-group-item {
    border-bottom: 1px dashed var(--futuristic-card-border) !important;
    padding-top: 1rem;
    padding-bottom: 1rem;
    position: relative;
}
.esg-timeline .list-group-item:last-child {
    border-bottom: none !important;
}
.esg-timeline .timeline-icon {
    width: 36px; /* Slightly larger */
    height: 36px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    box-shadow: 0 0 10px rgba(var(--primary-rgb), 0.2); /* Glow effect */
}
/* Specific severity colors for timeline icons */
.esg-timeline .timeline-icon.info { background-color: var(--futuristic-accent-info); color: white; }
.esg-timeline .timeline-icon.milestone { background-color: var(--futuristic-accent-success); color: white; }
.esg-timeline .timeline-icon.medium { background-color: var(--futuristic-accent-warning); color: white; }
.esg-timeline .timeline-icon.low { background-color: var(--futuristic-text-muted); color: var(--futuristic-bg-dark); }
.esg-timeline .timeline-icon.update { background-color: var(--futuristic-accent-secondary); color: white; } /* Added update style */
.esg-timeline .timeline-icon.alert { background-color: var(--futuristic-accent-danger); color: white; }


.esg-timeline .timeline-icon svg {
    width: 18px; /* Larger icons */
    height: 18px;
}
.esg-timeline .fw-semibold.text-dark {
    color: var(--futuristic-text-primary) !important;
    font-size: 0.95rem;
}
.esg-timeline .fs-xs.text-muted {
    font-size: 0.8rem;
}


/* Stakeholder Engagement Section */
.fs-lg { font-size: 1.35rem; } /* For sentiment percentages */
.text-success { color: var(--futuristic-accent-success) !important; }
.text-danger { color: var(--futuristic-accent-danger) !important; }
.stakeholder-engagement-card .lucide { /* Specific icon styling in this card */
    transform: scale(1.1); /* Slightly larger icons */
}

/* Key Risks/Opportunities Lists */
.key-risks-card .lucide, .key-opportunities-card .lucide {
    color: currentColor !important; /* Ensure header icon takes parent color */
}
.key-risks-card .card-header .lucide { color: var(--futuristic-accent-danger) !important; }
.key-opportunities-card .card-header .lucide { color: var(--futuristic-accent-success) !important; }


/* DonutScoreCard Compatibility:
   The DonutScoreCard uses Tailwind-like classes and some CSS vars.
   The CSS vars like --futuristic-text-primary, --futuristic-text-muted, --futuristic-accent-primary
   are defined here and should be picked up.
   The bg and border are set inline in DonutScoreCard.
   We style the outer <Card> (from react-bootstrap) that wraps DonutScoreCard.
*/
.col-xl-3 .card { /* Targeting the DonutScoreCards more specifically if needed */
    /* any specific overrides for donut score cards if the default card style is not enough */
}


/* Chart Placeholders / Actual Charts */
.futuristic-chart-placeholder {
    display: flex;
    flex-direction: column; /* Allow icon and text stacking */
    align-items: center;
    justify-content: center;
    height: 100%;
    min-height: 250px; /* Minimum height for placeholders */
    background-color: rgba(37, 42, 64, 0.3);
    border-radius: var(--futuristic-border-radius-sm);
    color: var(--futuristic-text-muted);
    text-align: center;
    padding: 20px;
    border: 1px dashed var(--futuristic-card-border);
}
.futuristic-chart-placeholder .lucide { /* Icon in placeholder */
    font-size: 3rem; /* Larger icon */
    margin-bottom: 0.75rem;
    opacity: 0.7;
}
.futuristic-chart-placeholder p {
    margin: 0;
    font-style: italic;
    font-size: 0.9rem;
}

/* Custom scrollbar for better aesthetics in dark mode */
::-webkit-scrollbar {
    width: 10px;
    height: 10px;
}
::-webkit-scrollbar-track {
    background: var(--futuristic-bg-medium);
    border-radius: 10px;
}
::-webkit-scrollbar-thumb {
    background: var(--futuristic-text-muted);
    border-radius: 10px;
    border: 2px solid var(--futuristic-bg-medium); /* Creates padding around thumb */
}
::-webkit-scrollbar-thumb:hover {
    background: var(--futuristic-accent-primary);
}

/* Lucide Icon general styling */
.lucide {
    stroke-width: 2px; /* Default stroke width for most icons */
}