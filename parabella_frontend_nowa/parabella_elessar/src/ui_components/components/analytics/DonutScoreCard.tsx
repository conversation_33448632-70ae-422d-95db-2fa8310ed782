import React from "react";

import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON> } from "recharts";
import { LucideIcon, Target, Leaf, HeartHandshake, Scale } from "lucide-react";
import {Card} from "react-bootstrap";
import {CardContent} from "@mui/material";

/**
 * Re‑usable donut‑chart score card.
 * -----------------------------------------------------------------------------
 * Props
 *  - title:            "Overall ESG Score" | "Climate Score (E)" | ...
 *  - score:            number 0‑100
 *  - subtitle:         small helper text that appears under the numeric score
 *  - color:            hex colour for the filled section of the donut
 *  - Icon:             optional Lucide icon displayed on the card header
 */
export interface DonutScoreCardProps {
    title: string;
    score: number;
    subtitle?: string;
    color?: string;
    Icon?: LucideIcon;
}

const DEFAULT_TRACK_COLOR = "rgba(255,255,255,0.05)";

export const DonutScoreCard: React.FC<DonutScoreCardProps> = ({
                                                                  title,
                                                                  score,
                                                                  subtitle,
                                                                  color = "#00f2ff",
                                                                  Icon = Target,
                                                              }) => {
    // Clamp score between 0 and 100 to protect against bad data
    const pct = Math.max(0, Math.min(score, 100));

    const pieData = [
        { name: "score", value: pct },
        { name: "rest", value: 100 - pct },
    ];

    return (
        <Card className="bg-[rgba(20,28,48,0.6)] border border-[rgba(56,77,130,0.4)] rounded-xl shadow-lg hover:shadow-xl/50 transition-all">
            <CardContent className="p-4 flex flex-col items-center justify-center">
                {/* optional icon */}
                <div className="mb-3 flex items-center gap-2 text-xs uppercase tracking-wide text-[var(--futuristic-text-muted)]">
                    <Icon size={14} className="text-[var(--futuristic-accent-primary)]" />
                    {title}
                </div>

                {/* donut */}
                <div className="relative flex items-center justify-center">
                    <PieChart width={140} height={140}>
                        <Pie
                            data={pieData}
                            dataKey="value"
                            startAngle={90}
                            endAngle={-270}
                            innerRadius={52}
                            outerRadius={68}
                            cornerRadius={8}
                            strokeWidth={0}
                        >
                            <Cell key="score" fill={color} />
                            <Cell key="rest" fill={DEFAULT_TRACK_COLOR} />
                        </Pie>
                    </PieChart>

                    {/* numeric score in the middle */}
                    <div className="absolute flex flex-col items-center justify-center">
            <span className="text-3xl font-semibold text-[var(--futuristic-text-primary)] leading-none">
              {pct}
            </span>
                        <span className="text-[10px] mt-0.5 text-[var(--futuristic-text-muted)]">
              / 100
            </span>
                        {subtitle && (
                            <span className="text-[10px] mt-1 text-[var(--futuristic-text-secondary)]">
                {subtitle}
              </span>
                        )}
                    </div>
                </div>
            </CardContent>
        </Card>
    );
};

/* -------------------------------------------------------------------------- */
/*                          Convenience coloured variants                     */
/* -------------------------------------------------------------------------- */
export const OverallScoreCard = (props: Omit<DonutScoreCardProps, "color" | "Icon">) => (
    <DonutScoreCard {...props} color="#00f2ff" Icon={Target} />
);
export const ClimateScoreCard = (props: Omit<DonutScoreCardProps, "color" | "Icon">) => (
    <DonutScoreCard {...props} color="#00ffaa" Icon={Leaf} />
);
export const SocialScoreCard = (props: Omit<DonutScoreCardProps, "color" | "Icon">) => (
    <DonutScoreCard {...props} color="#ffdd00" Icon={HeartHandshake} />
);
export const GovernanceScoreCard = (props: Omit<DonutScoreCardProps, "color" | "Icon">) => (
    <DonutScoreCard {...props} color="#6495ED" Icon={Scale} />
);
