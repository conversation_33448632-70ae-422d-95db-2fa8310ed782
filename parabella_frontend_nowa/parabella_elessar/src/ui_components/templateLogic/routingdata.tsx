
import Dashboard from '../components/dashboard/Dashboard';
import Dashboard2 from '../components/dashboard/Dashboard2';
import Dashboard3 from '../components/dashboard/Dashboard3';

import Profile from '../components/general_pages/Profile';
import Notificationslist from '../components/general_pages/Notificationslist';
import Aboutus from '../components/general_pages/Contactus.tsx';
import Settings from '../components/general_pages/Settings';

import Invoice from '../components/general_pages/Invoice';
import Pricing from '../components/general_pages/Pricing';
import Faqs from '../components/general_pages/Faqs';
import EmptyPage from '../components/general_pages/Emptypage';

import Mithril from "../components/DmaModule/mainuser_navigation_module/00_MainMithril/00_Mithril";
import GeneralCompanyInformation
    from "../components/DmaModule/mainuser_navigation_module/01_GeneralCompanyInformation/02_GeneralCompanyInformation";
import Login from "../components/authentication/Login.tsx";
import Register from "../components/authentication/Register.tsx";
import CreateNewProjectDMA from "../components/DmaModule/mainuser_navigation_module/CreateNewProjectDMA.tsx";
import ValueChainMapping
    from "../components/DmaModule/mainuser_navigation_module/02_ValueChainMapping/03_ValueChainMapping";

import AdminDashboard from "../components/DmaModule/mainuser_navigation_module/DMAdminDashboard.tsx";
import CSRDLandingPage from "../components/CSRD_Module/CSRD_landing.tsx";
import CarbonFootprint from "../components/PCFModule/CarbonFootprint.tsx";
import CarbonFootprintDashboard from "../components/PCFModule/PCFDashboard.tsx";
import AuditLogs from "../components/DmaModule/mainuser_navigation_module/AuditLogs.tsx";
import CSRDDashboard from "../components/CSRD_Module/main/dashboard/CsrdDashboard.tsx";
import CompanyInfoForm from "../components/CSRD_Module/main/onboarding/company-info-form.tsx";
import CreateNewProjectCsrd from "../components/CSRD_Module/main/onboarding/CreateNewProjectCsrd.tsx";
import AiUploadPage from "../components/CSRD_Module/main/dashboard/ai-upload-page.tsx";
import SettingsPage from "../components/CSRD_Module/main/SettingsPage.tsx";
import ReportsPage from "../components/CSRD_Module/main/report/ReportsPage.tsx";
import CsrdLayout from "../components/CSRD_Module/main/CsrdLayout.tsx";
import CsrdAnalyticsPage from "../components/analytics/CsrdAnalyticsPage.tsx";


export const Routingdata = [
    //PARABELLA ROUTING
    {path: `${import.meta.env.BASE_URL}authentication/Login`, element: <Login/>},
    {path: `${import.meta.env.BASE_URL}authentication/Register`, element: <Register/>},

    //MITHRIL - DMA
    {path: `${import.meta.env.BASE_URL}mithril/mithril/*`, element: <Mithril/>},
    {path: `${import.meta.env.BASE_URL}mithril/mithril/ValueChainMapping`, element: <ValueChainMapping/>},
    {path: `${import.meta.env.BASE_URL}mithril/GeneralCompanyInformation`, element: <GeneralCompanyInformation/>},

    {path: `${import.meta.env.BASE_URL}mithril/CreateNewProjectDMA`, element: <CreateNewProjectDMA/>},
    {path: `${import.meta.env.BASE_URL}mithril/DMAAdminDashboard`, element: <AdminDashboard/>},
    {path: `${import.meta.env.BASE_URL}mithril/auditlog`, element: <AuditLogs/>},


    //Vilya - PCF
    {path: `${import.meta.env.BASE_URL}vilya/CarbonFootprint`, element: <CarbonFootprintDashboard/>},
    {path: `${import.meta.env.BASE_URL}vilya/CarbonFootprintDashboard`, element: <CarbonFootprint/>},

    //CSRD
    {path: `${import.meta.env.BASE_URL}csrd/dashboard`, element: <CSRDDashboard />},
    {path: `${import.meta.env.BASE_URL}csrd/companyinform`, element: <CompanyInfoForm />},
    {path: `${import.meta.env.BASE_URL}csrd/aiupload`, element: <AiUploadPage />},
    {path: `${import.meta.env.BASE_URL}csrd/settings`, element: <SettingsPage
        />},
    {path: `${import.meta.env.BASE_URL}csrd/reports`, element: <ReportsPage />},
    {path: `${import.meta.env.BASE_URL}csrd/main`, element: <CsrdLayout />},
    {path: `${import.meta.env.BASE_URL}csrd/create`, element: <CreateNewProjectCsrd />},

    //Analytics
    {path: `${import.meta.env.BASE_URL}analytics`, element: <CsrdAnalyticsPage />},

    //Dashboard
    {path: `${import.meta.env.BASE_URL}dashboard/dashboard`, element: <Dashboard/>},
    {path: `${import.meta.env.BASE_URL}dashboard/dashboard2`, element: <Dashboard2/>},
    {path: `${import.meta.env.BASE_URL}dashboard/dashboard3`, element: <Dashboard3/>},


    {path: `${import.meta.env.BASE_URL}pages/profile`, element: <Profile/>},
    {path: `${import.meta.env.BASE_URL}pages/notificationslist`, element: <Notificationslist/>},
    {path: `${import.meta.env.BASE_URL}pages/aboutus`, element: <Aboutus/>},
    {path: `${import.meta.env.BASE_URL}pages/settings`, element: <Settings/>},


    {path: `${import.meta.env.BASE_URL}pages/invoice`, element: <Invoice/>},
    {path: `${import.meta.env.BASE_URL}pages/pricing`, element: <Pricing/>},

    {path: `${import.meta.env.BASE_URL}pages/faqs`, element: <Faqs/>},
    {path: `${import.meta.env.BASE_URL}pages/emptypage`, element: <EmptyPage/>},




];

export const Sidebarcomponents = [];
