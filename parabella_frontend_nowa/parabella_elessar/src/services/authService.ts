import axios from 'axios';
import {jwtDecode} from 'jwt-decode';
import {API_BASE_URL} from "../config/APIEndpoints.ts";
const API_URL = API_BASE_URL + '/auth';

export interface User {
    id: number;
    username: string;
    email: string;
    roles: string[];
}

export interface AuthResponse {
    accessToken: string;
    id: number;
    username: string;
    email: string;
    roles: string[];
}

export interface JwtPayload {
    id: number;
    sub: string;
    email: string;
    roles: string[];
}

export const login = async (username: string, password: string, totpCode: string): Promise<AuthResponse> => {
    const payload = { username, password, totpCode };
    const response = await axios.post<AuthResponse>(`${API_URL}/signin`, payload);

    if (response.data.accessToken) {
        localStorage.setItem('user', JSON.stringify(response.data));
    }
    return response.data;
};

export const register = async (username: string, email: string, password: string, roles: string[]): Promise<void> => {
    await axios.post(`${API_URL}/signup`, { username, email, password, role: roles });
};

export const logout = () => {
    localStorage.removeItem('user');
};

export const getCurrentUser = (): User | null => {
    const userStr = localStorage.getItem('user');
    if (userStr) {
        const user = JSON.parse(userStr);
        try {
            const decodedToken: JwtPayload = jwtDecode(user.accessToken);
            if (!decodedToken.id) {
                console.error("The token does not contain an 'id' field.");
                return null;
            }
            return {
                id: decodedToken.id,
                username: decodedToken.sub,
                email: decodedToken.email,
                roles: decodedToken.roles,
            };
        } catch (error) {
            console.error("Failed to decode token", error);
            return null;
        }
    }
    return null;
};

export const getAuthToken = (): string | null => {
    const userStr = localStorage.getItem('user');
    if (userStr) {
        const user = JSON.parse(userStr);
        return user.accessToken;
    }
    return null;
};

export const isAdmin = (): boolean => {
    const user = getCurrentUser();
    return user ? user.roles.includes('ROLE_ADMIN') : false;
};

export const isModerator = (): boolean => {
    const user = getCurrentUser();
    return user ? user.roles.includes('ROLE_MODERATOR') : false;
};
