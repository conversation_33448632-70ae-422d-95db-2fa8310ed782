export const API_BASE_URL = 'https://gcr-parabella-production-backend-1091242934000.europe-west10.run.app/api';
export const BASE_URL= 'https://gcr-parabella-production-backend-1091242934000.europe-west10.run.app';
//https://gcr-parabella-staging-0-1-1091242934000.europe-west10.run.app
//https://gcr-parabella-staging-backend-1091242934000.europe-west10.run.app (staging)
//https://gcr-parabella-production-backend-1091242934000.europe-west10.run.app (production)

export const API_ENDPOINTS = {


    PROGRESS: {
        SAVE: `${API_BASE_URL}/progress/save`,
        SAVE_STEP: `${API_BASE_URL}/progress/save-step`,
        LOAD: (userId: number, projectId: number, moduleType: string) =>
            `${API_BASE_URL}/progress/load/${userId}/${projectId}/${moduleType}`,
    },


    PROJECTS: {
            GET_USER_PROJECTS: (userId: number) => `/projects/user/${userId}`,
            GET: (projectId: number) => `/projects/${projectId}`,
            CREATE: `/projects/createProject`,
            UPDATE: (projectId: number) => `/projects/${projectId}`,
            DELETE: (projectId: number) => `/projects/${projectId}`,
            COPY: (projectId: number) => `/projects/${projectId}/copy`,
    },

    DASHBOARD: {
        GET_DATAPOINTS_STATUS: (companyId: number) => `${API_BASE_URL}/dashboard/datapoints-status?companyId=${companyId}`,
        GET_STAKEHOLDERS_PROGRESS: (companyId: number) => `${API_BASE_URL}/dashboard/stakeholders-progress?companyId=${companyId}`,
    },

    COMPANIES: {
        GET_ALL: `${API_BASE_URL}/companies`,
    },
    COMPANY_GROUPS: {
        GET_ALL: `${API_BASE_URL}/company-groups`,
    },
    EXCEL: {
        GET_DATA: (fileName: string) => `${API_BASE_URL}/excel/data?fileName=${fileName}`,
    },
    STAKEHOLDER: {
        SEND_EMAILS: `${API_BASE_URL}/stakeholders/send-stakeholder-emails`,
        GET_DASHBOARD: (projectId: number) => `${API_BASE_URL}/stakeholder/dashboard/${projectId}`,

        GET_DATA: (token: string | undefined) => `${API_BASE_URL}/stakeholder/stakeholder-data/${token}`,
        UPDATE_PROGRESS: (token: string | undefined) => `${API_BASE_URL}/stakeholder/update-progress/${token}`,
        GET_STATUS: (token: string | undefined) => `${API_BASE_URL}/stakeholder/status/${token}`,
        UPDATE_STATUS: (token: string | undefined) => `${API_BASE_URL}/stakeholder/update-status/${token}`,
        SAVE_STEPDATA: (token: string | undefined) => `${API_BASE_URL}/stakeholder/stakeholder-stepdata-saving/${token}`,

    },
};