// src/api/axiosConfig.ts
import axios from 'axios';
import {getAuthToken} from "../services/authService.ts";
import {BASE_URL} from "./APIEndpoints.ts";


const instance = axios.create({
    baseURL: BASE_URL,
});

// Add a request interceptor to include the JWT token
instance.interceptors.request.use(
    (config) => {
        const token = getAuthToken();
        if (token && config.headers) {
            config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
    },
    (error) => Promise.reject(error)
);

export default instance;
