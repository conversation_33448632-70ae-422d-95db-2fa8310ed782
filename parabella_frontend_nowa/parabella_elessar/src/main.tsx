import React, { Fragment } from 'react';
import ReactDOM from 'react-dom/client';
import App from './App';
import './index.scss';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>ider } from 'react-helmet-async';
import { BrowserRouter, Route, Routes, Navigate } from 'react-router-dom';
import Scrolltotop from './Scrolltotop';
import { Routingdata } from './ui_components/templateLogic/routingdata';

import Forgotpassword from './ui_components/components/authentication/Forgotpassword.tsx';
import Lockscreen from './ui_components/components/authentication/Lockscreen.tsx';
import Resetpassword from './ui_components/components/authentication/Resetpassword.tsx';
import Signup from './ui_components/components/authentication/Signup.tsx';
import Login from "./ui_components/components/authentication/Login.tsx";
import Register from "./ui_components/components/authentication/Register.tsx";
import Error500 from "./ui_components/components/authentication/500error.tsx";
import Error501 from "./ui_components/components/authentication/501error.tsx";
import Underconstruction from "./ui_components/components/authentication/Underconstruction.tsx";
import StakeholderRoute
    from "./ui_components/components/DmaModule/stakeholder_navigation_module/navigation_modules/01_StakeholderRoute.tsx";
import StakeholderImpactFinancialAnalysis
    from "./ui_components/components/DmaModule/stakeholder_navigation_module/navigation_modules/02_StakeholderImpactFinancialAnalysis.tsx";
import ProtectedRoute from "./ui_components/components/authentication/ProtectedRoute.tsx";
import {ProjectProvider} from "./ui_components/components/DmaModule/context_module/ProjectContext.tsx";
import {CompanyProvider} from "./ui_components/components/DmaModule/context_module/CompanyContext.tsx";
import {Dashboard} from "@mui/icons-material";
import {CsrdProjectProvider} from "./ui_components/components/CSRD_Module/context/CsrdProjectProvider.tsx";
import CsrdLayout from "./ui_components/components/CSRD_Module/main/CsrdLayout.tsx";
import AiUploadPage from "./ui_components/components/CSRD_Module/main/dashboard/ai-upload-page.tsx";
import CSRDDashboard from "./ui_components/components/CSRD_Module/main/dashboard/CsrdDashboard.tsx";
import ReportsPage from "./ui_components/components/CSRD_Module/main/report/ReportsPage.tsx";
import SettingsPage from "./ui_components/components/CSRD_Module/main/SettingsPage.tsx";




ReactDOM.createRoot(document.getElementById('root')!).render(
    <Fragment>
        <HelmetProvider>
            <BrowserRouter>

                <Scrolltotop />
                <Routes>
                    {/* Unprotected authentication routes */}
                    <Route path={`${import.meta.env.BASE_URL}`} element={<Navigate to={`${import.meta.env.BASE_URL}authentication/login`} replace />} />
                    <Route path={`${import.meta.env.BASE_URL}authentication/login`} element={<Login />} />
                    <Route path={`${import.meta.env.BASE_URL}authentication/register`} element={<Register />} />
                    <Route path={`${import.meta.env.BASE_URL}pages/authentication/500error`} element={<Error500 />} />
                    <Route path={`${import.meta.env.BASE_URL}pages/authentication/501error`} element={<Error501 />} />
                    <Route path={`${import.meta.env.BASE_URL}pages/authentication/404error`} element={<Login />} />
                    <Route path={`${import.meta.env.BASE_URL}pages/authentication/forgotpassword`} element={<Forgotpassword />} />
                    <Route path={`${import.meta.env.BASE_URL}pages/authentication/lockscreen`} element={<Lockscreen />} />
                    <Route path={`${import.meta.env.BASE_URL}pages/authentication/resetpassword/:token`} element={<Resetpassword />} />
                    <Route path={`${import.meta.env.BASE_URL}pages/authentication/signin`} element={<Login />} />
                    <Route path={`${import.meta.env.BASE_URL}pages/authentication/signup`} element={<Signup />} />
                    <Route path={`${import.meta.env.BASE_URL}pages/authentication/underconstruction/`} element={<Underconstruction />} />
                    <Route path={`${import.meta.env.BASE_URL}stakeholder/main/:token`} element={<StakeholderRoute />} />
                    <Route path={`${import.meta.env.BASE_URL}stakeholder/impact-financial-analysis/:token`} element={<StakeholderImpactFinancialAnalysis />} />

                    {/* Protected routes */}
                    <Route path={`${import.meta.env.BASE_URL}`} element={<ProtectedRoute roles={['ROLE_USER', 'ROLE_ADMIN']}><ProjectProvider><CompanyProvider> <CsrdProjectProvider><App /> </CsrdProjectProvider></CompanyProvider></ProjectProvider></ProtectedRoute>}>

                        <Route path={`${import.meta.env.BASE_URL}dashboard/dashboard`} element={<Dashboard />} />
                        {Routingdata.map((route, index) => (
                            <Route key={index} path={route.path} element={<ProtectedRoute roles={['ROLE_USER', 'ROLE_ADMIN']}>{route.element}</ProtectedRoute>} />
                        ))}


                    </Route>

                    {/* Redirect all unknown paths to 404 */}
                    <Route path="*" element={<Navigate to={`${import.meta.env.BASE_URL}pages/authentication/404error`} />} />
                </Routes>

            </BrowserRouter>
        </HelmetProvider>

    </Fragment>
);
