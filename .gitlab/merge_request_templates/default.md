**1. Purpose & Changes:**
<!-- What problem does this solve? Key changes? Link to issues (e.g., Closes #123) -->


**2. How to Test:**
<!-- Essential steps for reviewers to verify functionality. -->
1.
2.

**3. Author's Checklist:**
*   [ ] **It Works:** Code functions as intended, manually tested.
*   [ ] **Clean Code:** Follows style guides, linter/formatter passed, no debug code.
*   [ ] **Tests:** New/updated tests pass (locally & CI).
*   [ ] **Clear MR:** Conventional Commit title, branch up-to-date with target.
*   [ ] **Docs:** Relevant documentation updated.

---
---

## 🕵️ Reviewer Guide

*   **Understand:** Does the MR clearly state its purpose and achieve it?
*   **Functionality:** Does it work correctly? Are edge cases considered?
*   **Code Quality:** Is it readable, maintainable, and secure? Does it follow our standards?
*   **Tests:** Is testing adequate for the changes?
*   **Impact:** Any unintended consequences or performance issues?

**Provide feedback via comments. Approve or request changes.**