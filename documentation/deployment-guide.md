# Deployment Guide

This guide provides detailed instructions for deploying the Parabella CSRD backend application to various environments.

## Deployment Options

The Parabella CSRD backend can be deployed in several ways:

1. **Traditional Deployment**: Deploy the application as a JAR file on a server
2. **Docker Deployment**: Deploy the application as a Docker container
3. **Kubernetes Deployment**: Deploy the application on a Kubernetes cluster
4. **Cloud Deployment**: Deploy the application on a cloud platform (Google Cloud Run, AWS, Azure)

## Prerequisites

Before deploying the application, ensure you have:

1. A PostgreSQL database server (version 13 or higher)
2. Java 17 or higher installed on the deployment server
3. Sufficient memory and CPU resources (minimum 2GB RAM, 2 CPU cores)
4. Network access to the database server
5. Required environment variables set

## Building the Application

### Building with Maven

```bash
# Clean and package the application
mvn clean package -DskipTests

# The JAR file will be created in the target directory
ls -la target/*.jar
```

### Building with Docker

```bash
# Build the Docker image
docker build -t parabella-csrd-backend:latest .
```

## Traditional Deployment

### Server Requirements

- Java 17 or higher
- At least 2GB RAM
- At least 2 CPU cores
- Sufficient disk space (at least 1GB)

### Deployment Steps

1. Copy the JAR file to the server:

   ```bash
   scp target/parabella-csrd-db-1.0.0.jar user@server:/opt/parabella/
   ```

2. Create a service file for systemd:

   ```bash
   # /etc/systemd/system/parabella-csrd.service
   [Unit]
   Description=Parabella CSRD Backend
   After=network.target

   [Service]
   User=parabella
   WorkingDirectory=/opt/parabella
   ExecStart=/usr/bin/java -jar /opt/parabella/parabella-csrd-db-1.0.0.jar
   SuccessExitStatus=143
   TimeoutStopSec=10
   Restart=on-failure
   RestartSec=5
   Environment="SPRING_PROFILES_ACTIVE=prod"
   Environment="JWT_SECRET=your_jwt_secret"
   Environment="EMAIL_USERNAME=your_email"
   Environment="EMAIL_PASSWORD=your_password"
   Environment="OPENAI_API_KEY=your_openai_key"

   [Install]
   WantedBy=multi-user.target
   ```

3. Enable and start the service:

   ```bash
   sudo systemctl enable parabella-csrd.service
   sudo systemctl start parabella-csrd.service
   ```

4. Check the service status:

   ```bash
   sudo systemctl status parabella-csrd.service
   ```

5. Check the logs:

   ```bash
   sudo journalctl -u parabella-csrd.service -f
   ```

## Docker Deployment

### Docker Requirements

- Docker Engine 20.10 or higher
- Docker Compose 2.0 or higher (optional)

### Dockerfile

Create a `Dockerfile` in the root directory of the project:

```dockerfile
FROM eclipse-temurin:17-jdk-alpine as build
WORKDIR /workspace/app

COPY mvnw .
COPY .mvn .mvn
COPY pom.xml .
COPY src src

RUN ./mvnw install -DskipTests
RUN mkdir -p target/dependency && (cd target/dependency; jar -xf ../*.jar)

FROM eclipse-temurin:17-jre-alpine
VOLUME /tmp
ARG DEPENDENCY=/workspace/app/target/dependency
COPY --from=build ${DEPENDENCY}/BOOT-INF/lib /app/lib
COPY --from=build ${DEPENDENCY}/META-INF /app/META-INF
COPY --from=build ${DEPENDENCY}/BOOT-INF/classes /app
ENTRYPOINT ["java","-cp","app:app/lib/*","com.example.parabella_csrd_db.ParabellaCsrdDbApplication"]
```

### Docker Compose

Create a `docker-compose.yml` file:

```yaml
version: '3.8'

services:
  app:
    image: parabella-csrd-backend:latest
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "8080:8080"
    environment:
      - SPRING_PROFILES_ACTIVE=prod
      - SPRING_DATASOURCE_CSRD_URL=*******************************************
      - SPRING_DATASOURCE_CSRD_USERNAME=parabella_user
      - SPRING_DATASOURCE_CSRD_PASSWORD=your_password
      - SPRING_DATASOURCE_VECTOR_URL=*********************************************
      - SPRING_DATASOURCE_VECTOR_USERNAME=parabella_user
      - SPRING_DATASOURCE_VECTOR_PASSWORD=your_password
      - JWT_SECRET=your_jwt_secret
      - EMAIL_USERNAME=your_email
      - EMAIL_PASSWORD=your_password
      - OPENAI_API_KEY=your_openai_key
    depends_on:
      - db
    networks:
      - parabella-network

  db:
    image: postgres:13-alpine
    ports:
      - "5432:5432"
    environment:
      - POSTGRES_USER=parabella_user
      - POSTGRES_PASSWORD=your_password
      - POSTGRES_DB=parabella_csrd_db
    volumes:
      - postgres-data:/var/lib/postgresql/data
      - ./init-db.sql:/docker-entrypoint-initdb.d/init-db.sql
    networks:
      - parabella-network

networks:
  parabella-network:
    driver: bridge

volumes:
  postgres-data:
```

Create an `init-db.sql` file:

```sql
CREATE DATABASE parabella_vector_db;
GRANT ALL PRIVILEGES ON DATABASE parabella_vector_db TO parabella_user;
```

### Deployment Steps

1. Build and start the containers:

   ```bash
   docker-compose up -d
   ```

2. Check the container status:

   ```bash
   docker-compose ps
   ```

3. Check the logs:

   ```bash
   docker-compose logs -f app
   ```

4. Stop the containers:

   ```bash
   docker-compose down
   ```

## Kubernetes Deployment

### Kubernetes Requirements

- Kubernetes cluster 1.19 or higher
- kubectl command-line tool
- Helm 3.0 or higher (optional)

### Kubernetes Manifests

Create a `kubernetes` directory with the following files:

#### namespace.yaml

```yaml
apiVersion: v1
kind: Namespace
metadata:
  name: parabella
```

#### secrets.yaml

```yaml
apiVersion: v1
kind: Secret
metadata:
  name: parabella-secrets
  namespace: parabella
type: Opaque
data:
  db-password: eW91cl9wYXNzd29yZA==  # base64 encoded 'your_password'
  jwt-secret: eW91cl9qd3Rfc2VjcmV0  # base64 encoded 'your_jwt_secret'
  email-username: eW91cl9lbWFpbA==  # base64 encoded 'your_email'
  email-password: eW91cl9wYXNzd29yZA==  # base64 encoded 'your_password'
  openai-api-key: eW91cl9vcGVuYWlfa2V5  # base64 encoded 'your_openai_key'
```

#### configmap.yaml

```yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: parabella-config
  namespace: parabella
data:
  application.properties: |
    spring.profiles.active=prod
    spring.datasource.csrd.url=*****************************************************
    spring.datasource.csrd.username=parabella_user
    spring.datasource.vector.url=*******************************************************
    spring.datasource.vector.username=parabella_user
    spring.jpa.hibernate.ddl-auto=update
    spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.PostgreSQLDialect
    frontend.url=https://parabella.app/
```

#### db-deployment.yaml

```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: parabella-db
  namespace: parabella
spec:
  replicas: 1
  selector:
    matchLabels:
      app: parabella-db
  template:
    metadata:
      labels:
        app: parabella-db
    spec:
      containers:
      - name: postgres
        image: postgres:13-alpine
        ports:
        - containerPort: 5432
        env:
        - name: POSTGRES_USER
          value: parabella_user
        - name: POSTGRES_PASSWORD
          valueFrom:
            secretKeyRef:
              name: parabella-secrets
              key: db-password
        - name: POSTGRES_DB
          value: parabella_csrd_db
        volumeMounts:
        - name: postgres-data
          mountPath: /var/lib/postgresql/data
        - name: init-db
          mountPath: /docker-entrypoint-initdb.d
      volumes:
      - name: postgres-data
        persistentVolumeClaim:
          claimName: postgres-pvc
      - name: init-db
        configMap:
          name: init-db-script
---
apiVersion: v1
kind: Service
metadata:
  name: parabella-db
  namespace: parabella
spec:
  selector:
    app: parabella-db
  ports:
  - port: 5432
    targetPort: 5432
  type: ClusterIP
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: postgres-pvc
  namespace: parabella
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 10Gi
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: init-db-script
  namespace: parabella
data:
  init-db.sql: |
    CREATE DATABASE parabella_vector_db;
    GRANT ALL PRIVILEGES ON DATABASE parabella_vector_db TO parabella_user;
```

#### app-deployment.yaml

```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: parabella-backend
  namespace: parabella
spec:
  replicas: 2
  selector:
    matchLabels:
      app: parabella-backend
  template:
    metadata:
      labels:
        app: parabella-backend
    spec:
      containers:
      - name: app
        image: parabella-csrd-backend:latest
        imagePullPolicy: Always
        ports:
        - containerPort: 8080
        env:
        - name: SPRING_DATASOURCE_CSRD_PASSWORD
          valueFrom:
            secretKeyRef:
              name: parabella-secrets
              key: db-password
        - name: SPRING_DATASOURCE_VECTOR_PASSWORD
          valueFrom:
            secretKeyRef:
              name: parabella-secrets
              key: db-password
        - name: JWT_SECRET
          valueFrom:
            secretKeyRef:
              name: parabella-secrets
              key: jwt-secret
        - name: EMAIL_USERNAME
          valueFrom:
            secretKeyRef:
              name: parabella-secrets
              key: email-username
        - name: EMAIL_PASSWORD
          valueFrom:
            secretKeyRef:
              name: parabella-secrets
              key: email-password
        - name: OPENAI_API_KEY
          valueFrom:
            secretKeyRef:
              name: parabella-secrets
              key: openai-api-key
        volumeMounts:
        - name: config-volume
          mountPath: /app/config
        readinessProbe:
          httpGet:
            path: /actuator/health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
        livenessProbe:
          httpGet:
            path: /actuator/health
            port: 8080
          initialDelaySeconds: 60
          periodSeconds: 30
        resources:
          requests:
            memory: "1Gi"
            cpu: "500m"
          limits:
            memory: "2Gi"
            cpu: "1000m"
      volumes:
      - name: config-volume
        configMap:
          name: parabella-config
---
apiVersion: v1
kind: Service
metadata:
  name: parabella-backend
  namespace: parabella
spec:
  selector:
    app: parabella-backend
  ports:
  - port: 80
    targetPort: 8080
  type: ClusterIP
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: parabella-ingress
  namespace: parabella
  annotations:
    kubernetes.io/ingress.class: nginx
    cert-manager.io/cluster-issuer: letsencrypt-prod
spec:
  tls:
  - hosts:
    - api.parabella.app
    secretName: parabella-tls
  rules:
  - host: api.parabella.app
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: parabella-backend
            port:
              number: 80
```

### Deployment Steps

1. Apply the Kubernetes manifests:

   ```bash
   kubectl apply -f kubernetes/namespace.yaml
   kubectl apply -f kubernetes/secrets.yaml
   kubectl apply -f kubernetes/configmap.yaml
   kubectl apply -f kubernetes/db-deployment.yaml
   kubectl apply -f kubernetes/app-deployment.yaml
   ```

2. Check the deployment status:

   ```bash
   kubectl get all -n parabella
   ```

3. Check the logs:

   ```bash
   kubectl logs -f deployment/parabella-backend -n parabella
   ```

## Google Cloud Run Deployment

### Google Cloud Requirements

- Google Cloud account
- Google Cloud SDK installed
- Docker installed

### Deployment Steps

1. Build the Docker image:

   ```bash
   docker build -t gcr.io/your-project-id/parabella-csrd-backend:latest .
   ```

2. Push the Docker image to Google Container Registry:

   ```bash
   docker push gcr.io/your-project-id/parabella-csrd-backend:latest
   ```

3. Deploy to Cloud Run:

   ```bash
   gcloud run deploy parabella-csrd-backend \
     --image gcr.io/your-project-id/parabella-csrd-backend:latest \
     --platform managed \
     --region europe-west3 \
     --allow-unauthenticated \
     --memory 2Gi \
     --cpu 2 \
     --set-env-vars="SPRING_PROFILES_ACTIVE=prod,SPRING_DATASOURCE_CSRD_URL=*****************************************************,SPRING_DATASOURCE_CSRD_USERNAME=parabella_user,SPRING_DATASOURCE_CSRD_PASSWORD=your_password,SPRING_DATASOURCE_VECTOR_URL=*******************************************************,SPRING_DATASOURCE_VECTOR_USERNAME=parabella_user,SPRING_DATASOURCE_VECTOR_PASSWORD=your_password,JWT_SECRET=your_jwt_secret,EMAIL_USERNAME=your_email,EMAIL_PASSWORD=your_password,OPENAI_API_KEY=your_openai_key"
   ```

4. Check the deployment status:

   ```bash
   gcloud run services describe parabella-csrd-backend --platform managed --region europe-west3
   ```

## Production Considerations

### Security

1. **Use HTTPS**: Always use HTTPS in production
2. **Secure Secrets**: Use secret management solutions (Kubernetes Secrets, Google Secret Manager, AWS Secrets Manager)
3. **Restrict Access**: Use network policies to restrict access to the database
4. **Regular Updates**: Keep dependencies up to date to address security vulnerabilities

### Scalability

1. **Horizontal Scaling**: Deploy multiple instances of the application
2. **Database Scaling**: Use a managed database service with scaling capabilities
3. **Load Balancing**: Use a load balancer to distribute traffic
4. **Caching**: Implement caching to reduce database load

### Monitoring

1. **Health Checks**: Implement health checks to monitor application health
2. **Metrics**: Collect metrics using Prometheus and Grafana
3. **Logging**: Centralize logs using ELK stack or Google Cloud Logging
4. **Alerting**: Set up alerts for critical issues

### Backup and Recovery

1. **Database Backups**: Regularly backup the database
2. **Disaster Recovery Plan**: Have a plan for recovering from failures
3. **Data Retention Policy**: Define a policy for data retention

### CI/CD

1. **Automated Testing**: Run automated tests before deployment
2. **Continuous Integration**: Use CI/CD pipelines for automated deployment
3. **Blue-Green Deployment**: Use blue-green deployment for zero-downtime updates
4. **Rollback Plan**: Have a plan for rolling back failed deployments

## Troubleshooting

### Common Issues

1. **Database Connection Issues**:
   - Check database credentials
   - Verify network connectivity
   - Check database server status

2. **Memory Issues**:
   - Increase memory allocation
   - Check for memory leaks
   - Optimize JVM settings

3. **Performance Issues**:
   - Check database query performance
   - Optimize JPA/Hibernate settings
   - Implement caching

### Debugging

1. **Enable Debug Logging**:
   ```properties
   logging.level.com.example.parabella_csrd_db=DEBUG
   ```

2. **Check Application Logs**:
   ```bash
   kubectl logs -f deployment/parabella-backend -n parabella
   ```

3. **Check Database Logs**:
   ```bash
   kubectl logs -f deployment/parabella-db -n parabella
   ```

4. **Check Health Endpoints**:
   ```bash
   curl https://api.parabella.app/actuator/health
   ```

## Maintenance

### Regular Maintenance Tasks

1. **Database Maintenance**:
   - Vacuum and analyze PostgreSQL tables
   - Check for index fragmentation
   - Monitor database size

2. **Application Maintenance**:
   - Update dependencies
   - Monitor memory usage
   - Check for slow queries

3. **System Maintenance**:
   - Apply security patches
   - Monitor disk space
   - Check for resource constraints
