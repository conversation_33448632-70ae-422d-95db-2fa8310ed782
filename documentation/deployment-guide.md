
# Deployment Guide

This guide outlines the necessary steps to configure your Google Cloud Platform (GCP) project and GitLab repository for automated deployments of the frontend and backend applications.

## 1. Prerequisites

Before you begin, ensure you have the following:

* A **Google Cloud Platform (GCP) account** with billing enabled.
* A **GitLab account** and a project where your application code is hosted.
* **Owner** or **Editor** permissions for your GCP project.
* **Maintainer** or **Owner** role for your GitLab project.

---

## 2. Google Cloud Platform (GCP) Setup

Follow these steps to prepare your GCP environment.

### 2.1. Create a GCP Project

If you don't have one already, create a new GCP project in the [Google Cloud Console](https://console.cloud.google.com/).

### 2.2. Enable Required APIs

Enable the following APIs for your project. You can do this by navigating to the "APIs & Services" > "Library" section in the Cloud Console.

* **Artifact Registry API**: To store your Docker images.
* **Cloud Run API**: To deploy and run your containerized applications.
* **Cloud Build API**: While your CI is in GitLab, this API is often a helpful dependency.

### 2.3. Create Artifact Registry Repositories

You need two repositories to store the Docker images for your frontend and backend.

1.  Navigate to **Artifact Registry** in the Cloud Console.
2.  Click **Create Repository**:
    * **Name**: `your-backend-repo-name` (e.g., `parabella-backend`)
    * **Format**: Docker
    * **Region**: Select a region (e.g., `europe-west3`). **Note this down.**
3.  Click **Create Repository** again:
    * **Name**: `your-frontend-repo-name` (e.g., `parabella-frontend`)
    * **Format**: Docker
    * **Region**: Select a region (can be the same as the backend). **Note this down.**

### 2.4. Create Service Accounts

A service account is a special Google account that an application or a virtual machine (VM) instance, not a person, uses. Your GitLab CI/CD pipeline will use this to authenticate with GCP. It's best practice to have separate service accounts for different environments.

#### Staging Service Account

1.  Navigate to **IAM & Admin** > **Service Accounts**.
2.  Click **Create Service Account**:
    * **Name**: `gitlab-ci-staging`
    * **Service account ID**: This will be generated automatically.
    * Click **Create and Continue**.
3.  **Grant the following roles**:
    * `Artifact Registry Writer`: Allows pushing images to the registry.
    * `Cloud Run Admin`: Allows deploying and managing Cloud Run services.
    * `Service Account User`: Allows the service account to be used by other services.
4.  Click **Continue**, then **Done**.

#### Production Service Account

1.  Follow the same steps as above.
2.  **Name**: `gitlab-ci-production`
3.  **Grant the same roles**: `Artifact Registry Writer`, `Cloud Run Admin`, and `Service Account User`.

### 2.5. Generate JSON Keys for Service Accounts

For each service account, you need to generate a JSON key that GitLab will use for authentication.

1.  In the **Service Accounts** list, find your newly created service account (e.g., `gitlab-ci-staging`).
2.  Click the three-dot menu on the right and select **Manage keys**.
3.  Click **Add Key** > **Create new key**.
4.  Select **JSON** as the key type and click **Create**. A JSON file will be downloaded.
5.  **Rename the downloaded file** to `gcp-staging-key.json`.
6.  **Repeat these steps for the `gitlab-ci-production` service account**, and rename its key file to `gcp-production-key.json`.

**Important**: Treat these key files as highly sensitive credentials. Do not commit them to your Git repository.

---

## 3. GitLab Configuration

Now, configure your GitLab project to connect to your GCP account.

### 3.1. Set up CI/CD Variables

In your GitLab project, navigate to **Settings** > **CI/CD** and expand the **Variables** section. Add the following variables:

| Variable Name                       | Value                                                              | Protected | Masked | Environment Scope | Description                                                                                             |
| ----------------------------------- | ------------------------------------------------------------------ | --------- | ------ | ----------------- | ------------------------------------------------------------------------------------------------------- |
| `GCP_PROJECT_ID`                    | Your GCP Project ID                                                | Yes       | No     | All (default)     | The ID of your Google Cloud project.                                                                    |
| `GCP_REGION`                        | The GCP region for your backend (e.g., `europe-west3`)             | Yes       | No     | All (default)     | The GCP region for your backend services.                                                               |
| `GCP_REGION_FRONTEND`               | The GCP region for your frontend (e.g., `europe-west1`)            | Yes       | No     | All (default)     | The GCP region for your frontend services.                                                              |
| `GCP_BACKEND_REPO`                  | The name of your backend Artifact Registry repo                    | Yes       | No     | All (default)     | E.g., `parabella-backend`.                                                                              |
| `GCP_FRONTEND_REPO`                 | The name of your frontend Artifact Registry repo                   | Yes       | No     | All (default)     | E.g., `parabella-frontend`.                                                                             |
|                                     |                                                                    |           |        |                   |                                                                                                         |
| **Staging Variables** |                                                                    |           |        |                   |                                                                                                         |
| `GCP_SERVICE_KEY`                   | *Paste the content of `gcp-staging-key.json`* | Yes       | Yes    | `staging`         | The JSON key for the staging service account.                                                           |
| `BACKEND_SERVICE_NAME`              | `your-app-backend-staging`                                         | Yes       | No     | `staging`         | The name of the Cloud Run service for the staging backend.                                              |
| `FRONTEND_SERVICE_NAME`             | `your-app-frontend-staging`                                        | Yes       | No     | `staging`         | The name of the Cloud Run service for the staging frontend.                                             |
| `SPRING_DATASOURCE_CSRD_URL`        | `********************************`                                 | Yes       | Yes    | `staging`         | Staging database URL.                                                                                   |
| `...`                               | *...other staging secrets...* | Yes       | Yes    | `staging`         | All other secrets (`JWT_SECRET`, `OPEN_AI_API_KEY`, etc.) for the staging environment.                    |
|                                     |                                                                    |           |        |                   |                                                                                                         |
| **Production Variables** |                                                                    |           |        |                   |                                                                                                         |
| `GCP_SERVICE_KEY`                   | *Paste the content of `gcp-production-key.json`* | Yes       | Yes    | `production`      | The JSON key for the production service account.                                                        |
| `BACKEND_SERVICE_NAME`              | `your-app-backend-production`                                      | Yes       | No     | `production`      | The name of the Cloud Run service for the production backend.                                           |
| `FRONTEND_SERVICE_NAME`             | `your-app-frontend-production`                                     | Yes       | No     | `production`      | The name of the Cloud Run service for the production frontend.                                          |
| `SPRING_DATASOURCE_CSRD_URL`        | `***********************************`                              | Yes       | Yes    | `production`      | Production database URL.                                                                                |
| `...`                               | *...other production secrets...* | Yes       | Yes    | `production`      | All other secrets for the production environment.                                                       |

**Note**: To add a variable with an environment scope, fill in the key and value, then select the desired environment (e.g., `staging`, `production`) from the "Environment scope" dropdown.

---

## 4. Deployment Workflow

Your `gitlab-ci.yml` files are now configured to automate deployments based on your branch strategy.

### 4.1. Staging Deployment (Automatic)

* **Trigger**: Pushing commits or merging a merge request to the `staging` branch.
* **Action**:
    1.  The parent pipeline starts.
    2.  It checks for changes in the `parabella_frontend_nowa/parabella_elessar/` or backend-related directories (`src/`, `build.gradle`, etc.).
    3.  If changes are found, it triggers the corresponding child pipeline (`frontend.gitlab-ci.yml` or `backend.gitlab-ci.yml`).
    4.  The child pipeline builds a Docker image, tags it with the commit SHA, and pushes it to your Artifact Registry.
    5.  The new image is then automatically deployed to the corresponding Cloud Run service in the **staging** environment.

### 4.2. Production Deployment (Manual)

* **Trigger**: Pushing commits or merging a merge request to the `main` branch.
* **Action**:
    1.  The pipeline follows the same steps as the staging deployment for building and pushing the Docker image.
    2.  However, the `deploy_production_*` job **will not run automatically**. You must manually trigger it.
* **How to Deploy to Production**:
    1.  After a merge to `main`, go to your project's **CI/CD** > **Pipelines**.
    2.  Find the pipeline for your `main` branch commit. It will show the build/push stages as passed and the `deploy_production` stage as "blocked" or "manual".
    3.  Click the **play** icon (▶️) next to the `deploy_production_frontend` and `deploy_production_backend` jobs to start the deployment to production.

This manual step is a crucial safeguard to prevent accidental deployments to your live environment.
```