# Parabella CSRD Backend Technical Documentation

This documentation provides a comprehensive technical overview of the Parabella CSRD backend application.

## Table of Contents

1. [Architecture Overview](architecture-overview.md)
2. [Database Structure](database-structure.md)
3. [API Reference](api-reference.md)
4. [Authentication and Security](authentication-security.md)
5. [Service Layer](service-layer.md)
6. [Configuration Guide](configuration-guide.md)
7. [Deployment Guide](deployment-guide.md)
8. [Testing Strategy](testing-strategy.md)

## Introduction

The Parabella CSRD backend is a Spring Boot application designed to support the Corporate Sustainability Reporting Directive (CSRD) compliance platform. It provides a robust API for managing sustainability data, stakeholder engagement, and generating CSRD reports.

The backend is built using modern Java technologies and follows a multi-layered architecture with clear separation of concerns. It uses PostgreSQL for data persistence and implements security best practices for authentication and authorization.

## Getting Started

To get started with the backend development, please refer to the [Configuration Guide](configuration-guide.md) for setting up your local development environment.

For API documentation, see the [API Reference](api-reference.md) section.

## Support

For technical support or questions about the backend implementation, please contact the development team.
