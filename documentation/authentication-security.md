# Authentication and Security

This document describes the authentication and security mechanisms implemented in the Parabella CSRD backend.

## Authentication

The application uses JWT (JSON Web Token) based authentication. This stateless authentication mechanism is well-suited for RESTful APIs and provides a secure way to authenticate users.

### Authentication Flow

1. User submits credentials (username/password)
2. Server validates credentials against the database
3. If valid, server generates a JWT token and returns it to the client
4. Client includes the JWT token in the Authorization header for subsequent requests
5. Server validates the token for each protected request

### JWT Configuration

The JWT configuration is defined in the application properties:

```properties
parabella_csrd_db.jwtSecret=${JWT_SECRET}
parabella_csrd_db.jwtExpirationMs=86400000
```

The JWT secret is stored as an environment variable for security.

### JWT Token Structure

The JWT token consists of three parts:

1. **Header**: Contains the token type and signing algorithm
2. **Payload**: Contains claims about the user (subject, issued at, expiration)
3. **Signature**: Ensures the token hasn't been tampered with

### Authentication Controller

The `AuthController` class handles authentication requests:

```java
@RestController
@RequestMapping("/api/auth")
public class AuthController {

    @Autowired
    private AuthenticationManager authenticationManager;

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private RoleRepository roleRepository;

    @Autowired
    private PasswordEncoder encoder;

    @Autowired
    private JwtUtils jwtUtils;

    @PostMapping("/signin")
    public ResponseEntity<?> authenticateUser(@Valid @RequestBody LoginRequest loginRequest) {
        // Authenticate user and generate JWT token
    }

    @PostMapping("/signup")
    public ResponseEntity<?> registerUser(@Valid @RequestBody SignupRequest signupRequest) {
        // Register new user
    }

    @PostMapping("/refreshtoken")
    public ResponseEntity<?> refreshToken(@Valid @RequestBody TokenRefreshRequest request) {
        // Refresh JWT token
    }
}
```

### JWT Utilities

The `JwtUtils` class provides utilities for working with JWT tokens:

```java
@Component
public class JwtUtils {

    private String jwtSecret;
    private int jwtExpirationMs;

    public String generateJwtToken(Authentication authentication) {
        // Generate JWT token
    }

    public String getUserNameFromJwtToken(String token) {
        // Extract username from JWT token
    }

    public boolean validateJwtToken(String authToken) {
        // Validate JWT token
    }
}
```

### JWT Authentication Filter

The `AuthTokenFilter` class intercepts requests and validates JWT tokens:

```java
public class AuthTokenFilter extends OncePerRequestFilter {

    @Autowired
    private JwtUtils jwtUtils;

    @Autowired
    private UserDetailsServiceImpl userDetailsService;

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain)
            throws ServletException, IOException {
        // Extract and validate JWT token
        // Set authentication in security context if valid
    }
}
```

## Security Configuration

The security configuration is defined in the `WebSecurityConfig` class:

```java
@Configuration
@EnableWebSecurity
public class WebSecurityConfig {

    @Autowired
    private UserDetailsServiceImpl userDetailsService;

    @Autowired
    private AuthEntryPointJwt unauthorizedHandler;

    @Bean
    public AuthTokenFilter authenticationJwtTokenFilter() {
        return new AuthTokenFilter();
    }

    @Bean
    public SecurityFilterChain filterChain(HttpSecurity http) throws Exception {
        http.csrf(AbstractHttpConfigurer::disable)
                .exceptionHandling(exception -> exception.authenticationEntryPoint(unauthorizedHandler))
                .sessionManagement(session -> session.sessionCreationPolicy(SessionCreationPolicy.STATELESS))
                .authorizeHttpRequests(auth ->
                        auth.requestMatchers("/api/auth/**").permitAll()
                                .requestMatchers("/api/test/**").permitAll()
                                .requestMatchers("/api/csrd/**").permitAll()
                                .requestMatchers("/api/companies/**").authenticated()
                                .requestMatchers("/api/excel/data/**").authenticated()
                                .requestMatchers("/api/progress/**").authenticated()
                                .requestMatchers("/api/projects/**").authenticated()
                                .requestMatchers("/api/stakeholder/**").permitAll()
                                .anyRequest().authenticated()
                );

        http.authenticationProvider(authenticationProvider());
        http.addFilterBefore(authenticationJwtTokenFilter(), UsernamePasswordAuthenticationFilter.class);

        return http.build();
    }

    @Bean
    public DaoAuthenticationProvider authenticationProvider() {
        DaoAuthenticationProvider authProvider = new DaoAuthenticationProvider();
        authProvider.setUserDetailsService(userDetailsService);
        authProvider.setPasswordEncoder(passwordEncoder());
        return authProvider;
    }

    @Bean
    public AuthenticationManager authenticationManager(AuthenticationConfiguration authConfig) throws Exception {
        return authConfig.getAuthenticationManager();
    }

    @Bean
    public PasswordEncoder passwordEncoder() {
        return new BCryptPasswordEncoder();
    }
}
```

## CORS Configuration

Cross-Origin Resource Sharing (CORS) is configured to allow requests from specific origins:

```java
@Configuration
public class CorsConfig implements WebMvcConfigurer {

    @Override
    public void addCorsMappings(CorsRegistry registry) {
        registry.addMapping("/**")
                .allowedOrigins("https://storage.googleapis.com", "http://localhost:5173",
                        "https://parabella-elessar-1091242934000.europe-west3.run.app",
                        "https://gcr-parabella-staging-frontend-1091242934000.europe-west10.run.app",
                        "https://gcr-parabella-staging-frontend-1091242934000.europe-west4.run.app",
                        "https://parabella.app")
                .allowedMethods("GET", "POST", "PUT", "DELETE", "OPTIONS")
                .allowedHeaders("*")
                .allowCredentials(true)
                .maxAge(3600);
    }
}
```

## User Management

### User Entity

The `User` entity represents a user in the system:

```java
@Entity
@Table(name = "users")
public class User {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @NotBlank
    @Size(max = 50)
    private String username;

    @NotBlank
    @Size(max = 100)
    @Email
    private String email;

    @NotBlank
    @Size(max = 120)
    private String password;

    @ManyToMany(fetch = FetchType.LAZY)
    @JoinTable(name = "user_roles",
            joinColumns = @JoinColumn(name = "user_id"),
            inverseJoinColumns = @JoinColumn(name = "role_id"))
    private Set<Role> roles = new HashSet<>();

    private String totpSecret;

    // Constructors, getters, and setters
}
```

### Role Entity

The `Role` entity represents a role in the system:

```java
@Entity
@Table(name = "roles")
public class Role {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    @Enumerated(EnumType.STRING)
    @Column(length = 20)
    private ERole name;

    // Constructors, getters, and setters
}
```

### User Details Service

The `UserDetailsServiceImpl` class implements the Spring Security `UserDetailsService` interface:

```java
@Service
public class UserDetailsServiceImpl implements UserDetailsService {

    @Autowired
    private UserRepository userRepository;

    @Override
    @Transactional
    public UserDetails loadUserByUsername(String username) throws UsernameNotFoundException {
        User user = userRepository.findByUsername(username)
                .orElseThrow(() -> new UsernameNotFoundException("User Not Found with username: " + username));

        return UserDetailsImpl.build(user);
    }
}
```

## Password Management

### Password Encoding

Passwords are encoded using BCrypt:

```java
@Bean
public PasswordEncoder passwordEncoder() {
    return new BCryptPasswordEncoder();
}
```

### Password Reset

The application supports password reset functionality:

1. User requests a password reset by providing their email
2. A password reset token is generated and stored in the database
3. An email with a reset link is sent to the user
4. User clicks the link and provides a new password
5. The token is validated and the password is updated

## Two-Factor Authentication (2FA)

The application supports Time-based One-Time Password (TOTP) for two-factor authentication:

1. User enables 2FA in their account settings
2. A TOTP secret is generated and stored in the user's record
3. User configures their authenticator app with the secret
4. For subsequent logins, user must provide both password and TOTP code

## Security Best Practices

The application implements the following security best practices:

1. **Secure Password Storage**: Passwords are hashed using BCrypt
2. **JWT Token Security**: JWT tokens are signed with a secret key
3. **HTTPS**: All communication is encrypted using HTTPS
4. **CORS**: Cross-Origin Resource Sharing is configured to allow only specific origins
5. **Input Validation**: All input is validated using Bean Validation
6. **CSRF Protection**: CSRF protection is disabled for the API (stateless authentication)
7. **Role-Based Access Control**: Access to resources is controlled based on user roles
8. **Secure Headers**: Security headers are configured to prevent common attacks
9. **Rate Limiting**: API rate limiting is implemented to prevent abuse
10. **Audit Logging**: All security-related events are logged
