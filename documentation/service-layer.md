# Service Layer

The service layer in the Parabella CSRD backend implements the business logic of the application. This document provides an overview of the key services and their responsibilities.

## Service Organization

Services are organized by domain and follow a clear separation of concerns:

- **Authentication Services**: Handle user authentication and authorization
- **CSRD Services**: Handle CSRD data and reporting
- **Mithril Services**: Handle double materiality analysis
- **Elessar Services**: Handle project management and company information

## Key Services

### Authentication Services

#### UserDetailsServiceImpl

Implements the Spring Security `UserDetailsService` interface to load user details for authentication.

```java
@Service
public class UserDetailsServiceImpl implements UserDetailsService {

    @Autowired
    private UserRepository userRepository;

    @Override
    @Transactional
    public UserDetails loadUserByUsername(String username) throws UsernameNotFoundException {
        User user = userRepository.findByUsername(username)
                .orElseThrow(() -> new UsernameNotFoundException("User Not Found with username: " + username));

        return UserDetailsImpl.build(user);
    }
}
```

#### AuthService

Handles user authentication, registration, and token management.

```java
@Service
public class AuthService {

    @Autowired
    private AuthenticationManager authenticationManager;

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private RoleRepository roleRepository;

    @Autowired
    private PasswordEncoder encoder;

    @Autowired
    private JwtUtils jwtUtils;

    public JwtResponse authenticateUser(LoginRequest loginRequest) {
        // Authenticate user and generate JWT token
    }

    public MessageResponse registerUser(SignupRequest signupRequest) {
        // Register new user
    }

    public TokenRefreshResponse refreshToken(TokenRefreshRequest request) {
        // Refresh JWT token
    }
}
```

### CSRD Services

#### CsrdDataService

Handles CSRD data management.

```java
@Service
public class CsrdDataService {

    @Autowired
    private CsrdDataRepository csrdDataRepository;

    public List<CsrdDataDTO> getAllCsrdData() {
        // Get all CSRD data
    }

    public CsrdDataDTO save(CsrdDataDTO csrdDataDTO) {
        // Save CSRD data
    }

    public void delete(Long id) {
        // Delete CSRD data
    }
}
```

#### EmissionDataService

Handles emission data management.

```java
@Service
public class EmissionDataService {

    @Autowired
    private EmissionDataRepository emissionDataRepository;

    public void importDataFromExcel(String filePath) throws IOException {
        // Import emission data from Excel
    }
}
```

#### CsrdLoaderService

Handles loading CSRD topics and subtopics.

```java
@Service
public class CsrdLoaderService {

    @Autowired
    private CsrdTopicRepository topicRepository;

    @Autowired
    private CsrdSubtopicRepository subtopicRepository;

    @Autowired
    private CsrdSubtopicSectionRepository sectionRepository;

    @Autowired
    private CsrdFieldRepository fieldRepository;

    @Autowired
    private CsrdFieldOptionRepository optionRepository;

    @Autowired
    private CsrdConditionalFieldRepository conditionalFieldRepository;

    public void loadTopicsRoot(CsrdTopicsRootDTO topicsRoot) {
        // Load CSRD topics and subtopics
    }
}
```

### Mithril Services

#### EsrsTopicService

Handles ESRS topics for double materiality analysis.

```java
@Service
public class EsrsTopicService {

    @Autowired
    private EsrsTopicsRepository esrsTopicsRepository;

    public List<EsrsTopic> getAllSubtopics() {
        // Get all ESRS subtopics
    }

    public List<EsrsTopic> getSubtopicsByArea(String area) {
        // Get ESRS subtopics by area
    }

    public List<EsrsTopic> getSubtopicsByEsrsCode(String esrsCode) {
        // Get ESRS subtopics by ESRS code
    }
}
```

#### StakeholderService

Handles stakeholder management for double materiality analysis.

```java
@Service
public class StakeholderService {

    @Autowired
    private StakeholderRepository stakeholderRepository;

    @Autowired
    private ProjectRepository projectRepository;

    @Autowired
    private CompanyRepository companyRepository;

    @Autowired
    private EmailService emailService;

    public List<Stakeholder> getAllStakeholders() {
        // Get all stakeholders
    }

    public Stakeholder getStakeholderById(Long id) {
        // Get stakeholder by ID
    }

    public List<Stakeholder> getStakeholdersByProjectId(Long projectId) {
        // Get stakeholders by project ID
    }

    public Stakeholder createStakeholder(StakeholderDTO stakeholderDTO) {
        // Create new stakeholder
    }

    public Stakeholder updateStakeholder(Long id, StakeholderDTO stakeholderDTO) {
        // Update stakeholder
    }

    public void deleteStakeholder(Long id) {
        // Delete stakeholder
    }

    public void sendStakeholderEmails(List<Long> stakeholderIds) {
        // Send emails to stakeholders
    }
}
```

#### DashboardService

Handles dashboard data for double materiality analysis.

```java
@Service
public class DashboardService {

    @Autowired
    private StakeholderRepository stakeholderRepository;

    @Autowired
    private EsrsTopicsDetailsRepository esrsTopicsDetailsRepository;

    public List<StakeholderProgress> getStakeholderProgressByProjectId(Long projectId) {
        // Get stakeholder progress by project ID
    }

    public DatapointStatus getDatapointStatusByProjectId(Long projectId) {
        // Get datapoint status by project ID
    }
}
```

### Elessar Services

#### CsrdProjectService

Handles CSRD project management.

```java
@Service
public class CsrdProjectService {

    @Autowired
    private CsrdProjectRepository projectRepository;

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private CompanyInfoRepository companyInfoRepository;

    @Autowired
    private CsrdDtoMapper dtoMapper;

    public CsrdProjectDTO createCsrdProject(CsrdProjectDTO projectDTO) {
        // Create new CSRD project
    }

    public List<CsrdProjectDTO> getUserCsrdProjects(Long userId) {
        // Get user's CSRD projects
    }

    public CsrdProjectDTO getCsrdProjectById(Long id) {
        // Get CSRD project by ID
    }

    public CompanyInfoDTO saveOrUpdateCompanyInfo(Long projectId, CompanyInfoDTO companyInfoDTO) {
        // Save or update company information
    }

    public CompanyInfoDTO getCompanyInfoByProjectId(Long projectId) {
        // Get company information by project ID
    }
}
```

#### EmailService

Handles email sending.

```java
@Service
public class EmailService {

    @Autowired
    private JavaMailSender emailSender;

    @Value("${frontend.url}")
    private String frontendUrl;

    public void sendPlainOrHtmlEmail(String to, String subject, String text) {
        // Send plain or HTML email
    }

    public void sendHtmlEmail(String to, String token, String name) throws MessagingException, IOException {
        // Send HTML email with token
    }
}
```

## Service Design Patterns

The service layer implements several design patterns:

### Repository Pattern

Services use repositories to access data, providing a clean separation between business logic and data access.

```java
@Service
public class CsrdDataService {

    @Autowired
    private CsrdDataRepository csrdDataRepository;

    // Service methods
}
```

### DTO Pattern

Data Transfer Objects (DTOs) are used to transfer data between the service layer and the controller layer.

```java
public class CsrdDataDTO {
    private Long id;
    private String efrag_id;
    private String esrs;
    private String dr;
    // Other fields, getters, and setters
}
```

### Mapper Pattern

Mappers are used to convert between entities and DTOs.

```java
@Component
public class CsrdDtoMapper {

    public CsrdDataDTO toCsrdDataDTO(CsrdData csrdData) {
        // Convert CsrdData entity to CsrdDataDTO
    }

    public CsrdData toCsrdData(CsrdDataDTO csrdDataDTO) {
        // Convert CsrdDataDTO to CsrdData entity
    }
}
```

### Service Facade Pattern

Services provide a simplified interface to complex subsystems.

```java
@Service
public class CsrdLoaderService {
    // Service methods that coordinate multiple repositories
}
```

## Transaction Management

Services use Spring's declarative transaction management to ensure data consistency.

```java
@Service
public class CsrdDataService {

    @Transactional
    public CsrdDataDTO save(CsrdDataDTO csrdDataDTO) {
        // Save CSRD data in a transaction
    }
}
```

## Error Handling

Services use exceptions to handle errors and provide meaningful error messages.

```java
@Service
public class CsrdProjectService {

    public CsrdProjectDTO getCsrdProjectById(Long id) {
        CsrdProject project = projectRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Project not found with id: " + id));
        return dtoMapper.toCsrdProjectDTO(project);
    }
}
```

## Validation

Services validate input data to ensure data integrity.

```java
@Service
public class CsrdDataService {

    public CsrdDataDTO save(CsrdDataDTO csrdDataDTO) {
        // Validate input data
        if (csrdDataDTO.getEfrag_id() == null || csrdDataDTO.getEfrag_id().isEmpty()) {
            throw new IllegalArgumentException("EFRAG ID cannot be empty");
        }
        // Save CSRD data
    }
}
```

## Dependency Injection

Services use Spring's dependency injection to manage dependencies.

```java
@Service
public class CsrdDataService {

    private final CsrdDataRepository csrdDataRepository;
    private final CsrdDtoMapper dtoMapper;

    @Autowired
    public CsrdDataService(CsrdDataRepository csrdDataRepository, CsrdDtoMapper dtoMapper) {
        this.csrdDataRepository = csrdDataRepository;
        this.dtoMapper = dtoMapper;
    }

    // Service methods
}
```

## Service Testing

Services are tested using unit tests and integration tests.

```java
@ExtendWith(MockitoExtension.class)
class CsrdDataServiceTest {

    @Mock
    private CsrdDataRepository csrdDataRepository;

    @InjectMocks
    private CsrdDataService csrdDataService;

    @Test
    void getAllCsrdData_ShouldReturnAllData() {
        // Test getAllCsrdData method
    }

    @Test
    void save_ShouldSaveAndReturnData() {
        // Test save method
    }

    @Test
    void delete_ShouldDeleteData() {
        // Test delete method
    }
}
```
