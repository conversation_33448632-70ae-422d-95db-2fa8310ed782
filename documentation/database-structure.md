# Database Structure

The Parabella CSRD backend uses a multi-database architecture with two PostgreSQL databases:

1. **Main Database**: Stores the core application data
2. **Vector Database**: Stores vector embeddings for AI-powered search and analysis

## Database Configuration

The database configuration is defined in two main classes:

- `MainDbConfig.java`: Configures the main database connection
- `VectorDbConfig.java`: Configures the vector database connection

These configurations use Spring's `@EnableJpaRepositories` annotation to specify the repository packages and entity manager factories for each database.

## Entity Relationship Diagram

Below is a simplified entity relationship diagram showing the main entities and their relationships:

```
┌───────────────┐       ┌───────────────┐       ┌───────────────┐
│     User      │       │  CsrdProject  │       │ CompanyInfo   │
├───────────────┤       ├───────────────┤       ├───────────────┤
│ id            │       │ id            │       │ id            │
│ username      │◄──────┤ user_id       │       │ company_name  │
│ email         │       │ project_name  │◄──────┤ industry      │
│ password      │       │ project_type  │       │ size          │
└───────────────┘       │ created_at    │       │ revenue       │
        ▲                │ updated_at    │       │ employees     │
        │                └───────────────┘       └───────────────┘
        │                        
        │                        
┌───────────────┐       ┌───────────────┐       ┌───────────────┐
│     Role      │       │    Project    │       │   Company     │
├───────────────┤       ├───────────────┤       ├───────────────┤
│ id            │       │ id            │       │ id            │
│ name          │       │ user_id       │◄──────┤ project_id    │
└───────────────┘       │ project_name  │       │ company_name  │
                        │ created_at    │       │ industry      │
                        └───────────────┘       └───────────────┘
                                ▲                       ▲
                                │                       │
                                │                       │
┌───────────────┐       ┌───────────────┐       ┌───────────────┐
│  Stakeholder  │       │  CompanyGroup │       │ValueChainObject│
├───────────────┤       ├───────────────┤       ├───────────────┤
│ id            │       │ id            │       │ id            │
│ project_id    │◄──────┤ project_id    │       │ company_id    │
│ name          │       │ group_name    │◄──────┤ name          │
│ email         │       │ industry      │       │ industry      │
│ status        │       └───────────────┘       └───────────────┘
└───────────────┘
        ▲
        │
        │
┌───────────────┐       ┌───────────────┐       ┌───────────────┐
│  EsrsTopic    │       │EsrsTopicSelection     │     Iro       │
├───────────────┤       ├───────────────┤       ├───────────────┤
│ id            │◄──────┤ esrs_topic_id │◄──────┤ topic_selection_id│
│ area          │       │ stakeholder_id│       │ name          │
│ esrs_code     │       │ company_id    │       │ iro_type      │
│ topic         │       │ relevant      │       └───────────────┘
│ subtopic      │       └───────────────┘               │
└───────────────┘                                       ▼
                                               ┌───────────────┐
                                               │ IroEvaluation │
                                               ├───────────────┤
                                               │ id            │
                                               │ iro_id        │
                                               │ description   │
                                               │ magnitude     │
                                               │ likelihood    │
                                               └───────────────┘
```

## Main Database Entities

### Authentication Entities

- **User**: Represents a user of the system
- **Role**: Represents a role in the system (ADMIN, USER)
- **PasswordResetToken**: Stores tokens for password reset functionality

### CSRD Project Entities

- **CsrdProject**: Represents a CSRD reporting project
- **CompanyInfo**: Stores company information for CSRD reporting

### CSRD Dashboard Entities

- **CsrdTopic**: Represents a CSRD topic (e.g., "Climate Change")
- **CsrdSubtopic**: Represents a subtopic within a CSRD topic
- **CsrdSubtopicSection**: Represents a section within a CSRD subtopic
- **CsrdField**: Represents a field within a CSRD section
- **CsrdFieldOption**: Represents an option for a CSRD field

### Mithril Entities

- **Project**: Represents a double materiality analysis project
- **Company**: Represents a company in the system
- **CompanyGroup**: Represents a group of companies
- **Stakeholder**: Represents a stakeholder in the double materiality analysis
- **ValueChainObject**: Represents an object in the company's value chain
- **EsrsTopic**: Represents an ESRS topic for materiality analysis
- **EsrsTopicSelection**: Represents the selection of an ESRS topic for a company
- **Iro**: Represents an Impact, Risk, or Opportunity identified in the analysis
- **IroEvaluation**: Represents the evaluation of an IRO

### CSRD Data Entities

- **CsrdData**: Stores CSRD data points
- **EmissionData**: Stores emission data for CSRD reporting

## Vector Database Entities

- **EsrsDatapoint**: Stores ESRS datapoints with vector embeddings
- **Document**: Stores uploaded documents with vector embeddings

## Database Schema Details

### Main Database Schema

The main database schema includes the following key tables:

#### Authentication Tables

```sql
CREATE TABLE users (
    id BIGSERIAL PRIMARY KEY,
    username VARCHAR(50) NOT NULL UNIQUE,
    email VARCHAR(100) NOT NULL UNIQUE,
    password VARCHAR(120) NOT NULL,
    totp_secret VARCHAR(255)
);

CREATE TABLE roles (
    id SERIAL PRIMARY KEY,
    name VARCHAR(20) NOT NULL
);

CREATE TABLE user_roles (
    user_id BIGINT NOT NULL,
    role_id INT NOT NULL,
    PRIMARY KEY (user_id, role_id),
    FOREIGN KEY (user_id) REFERENCES users(id),
    FOREIGN KEY (role_id) REFERENCES roles(id)
);

CREATE TABLE password_reset_tokens (
    id BIGSERIAL PRIMARY KEY,
    token VARCHAR(255) NOT NULL,
    user_id BIGINT NOT NULL,
    expiry_date TIMESTAMP NOT NULL,
    FOREIGN KEY (user_id) REFERENCES users(id)
);
```

#### CSRD Project Tables

```sql
CREATE TABLE csrd_projects (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL,
    project_name VARCHAR(200) NOT NULL,
    project_description TEXT,
    project_type VARCHAR(50) NOT NULL,
    created_at TIMESTAMP NOT NULL,
    updated_at TIMESTAMP NOT NULL,
    company_info_id BIGINT,
    FOREIGN KEY (user_id) REFERENCES users(id),
    FOREIGN KEY (company_info_id) REFERENCES csrd_company_info(id)
);

CREATE TABLE csrd_company_info (
    id BIGSERIAL PRIMARY KEY,
    company_name VARCHAR(255),
    revenue VARCHAR(100),
    industry TEXT,
    size VARCHAR(50),
    number_of_employees VARCHAR(50)
);
```

#### Mithril Tables

```sql
CREATE TABLE projects (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL,
    project_name VARCHAR(255) NOT NULL,
    project_description TEXT,
    created_at TIMESTAMP,
    project_type VARCHAR(50),
    company_id BIGINT,
    company_group_id BIGINT,
    FOREIGN KEY (user_id) REFERENCES users(id)
);

CREATE TABLE company (
    id BIGSERIAL PRIMARY KEY,
    company_name VARCHAR(255),
    address VARCHAR(255),
    vat VARCHAR(100),
    num_employees VARCHAR(50),
    revenues DOUBLE PRECISION,
    industry VARCHAR(100),
    is_sub_company BOOLEAN,
    company_group_id BIGINT,
    project_id BIGINT,
    FOREIGN KEY (company_group_id) REFERENCES company_groups(id),
    FOREIGN KEY (project_id) REFERENCES projects(id)
);

CREATE TABLE stakeholders (
    id BIGSERIAL PRIMARY KEY,
    project_id BIGINT,
    stakeholder_name VARCHAR(255),
    stakeholder_email VARCHAR(255),
    role VARCHAR(100),
    stakeholder_type VARCHAR(50),
    company_id BIGINT,
    token VARCHAR(255),
    status VARCHAR(20),
    completed_datapoints INTEGER,
    total_datapoints INTEGER,
    is_responsible BOOLEAN,
    FOREIGN KEY (project_id) REFERENCES projects(id),
    FOREIGN KEY (company_id) REFERENCES company(id)
);
```

### Vector Database Schema

The vector database schema includes the following key tables:

```sql
CREATE TABLE esrs_datapoints (
    id BIGSERIAL PRIMARY KEY,
    disclosure_requirement VARCHAR(50),
    datapoint_text TEXT,
    source_id VARCHAR(100),
    embedding VECTOR(1536)
);

CREATE TABLE documents (
    id BIGSERIAL PRIMARY KEY,
    title VARCHAR(255),
    content TEXT,
    embedding VECTOR(1536),
    project_id BIGINT
);
```

## Database Migrations

Database migrations are managed using Hibernate's automatic schema generation (`spring.jpa.hibernate.ddl-auto=update`). In a production environment, it's recommended to use a dedicated migration tool like Flyway or Liquibase for more controlled schema evolution.
