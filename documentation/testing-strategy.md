# Testing Strategy

This document outlines the testing strategy for the Parabella CSRD backend application, including the types of tests, testing tools, and best practices.

## Testing Pyramid

The testing strategy follows the testing pyramid approach, with a focus on unit tests at the base, integration tests in the middle, and end-to-end tests at the top:

```
    /\
   /  \
  /    \
 / E2E  \
/--------\
/          \
/ Integration \
/----------------\
/                  \
/      Unit Tests     \
/------------------------\
```

## Test Types

### Unit Tests

Unit tests focus on testing individual components in isolation, typically at the method level. They are fast, reliable, and provide immediate feedback.

#### What to Test

- **Service Methods**: Test business logic in service classes
- **Repository Methods**: Test custom repository methods
- **Controller Methods**: Test request handling and response generation
- **Utility Methods**: Test helper methods and utilities

#### Example Unit Test

```java
@ExtendWith(MockitoExtension.class)
class CsrdDataServiceTest {

    @Mock
    private CsrdDataRepository csrdDataRepository;

    @InjectMocks
    private CsrdDataService csrdDataService;

    private CsrdData testCsrdData;
    private CsrdDataDTO testCsrdDataDTO;

    @BeforeEach
    void setUp() {
        // Setup test data
        testCsrdData = new CsrdData();
        testCsrdData.setId(1L);
        testCsrdData.setEfragID("EFRAG001");
        testCsrdData.setEsrs("ESRS E1");
        
        testCsrdDataDTO = new CsrdDataDTO();
        testCsrdDataDTO.setId(1L);
        testCsrdDataDTO.setEfrag_id("EFRAG001");
        testCsrdDataDTO.setEsrs("ESRS E1");
    }

    @Test
    void getAllCsrdData_ShouldReturnAllData() {
        // Arrange
        when(csrdDataRepository.findAll()).thenReturn(Arrays.asList(testCsrdData));

        // Act
        List<CsrdDataDTO> result = csrdDataService.getAllCsrdData();

        // Assert
        assertThat(result).hasSize(1);
        assertThat(result.get(0).getEfrag_id()).isEqualTo("EFRAG001");
        assertThat(result.get(0).getEsrs()).isEqualTo("ESRS E1");
        verify(csrdDataRepository, times(1)).findAll();
    }

    @Test
    void save_ShouldSaveAndReturnData() {
        // Arrange
        when(csrdDataRepository.save(any(CsrdData.class))).thenReturn(testCsrdData);

        // Act
        CsrdDataDTO result = csrdDataService.save(testCsrdDataDTO);

        // Assert
        assertThat(result).isNotNull();
        assertThat(result.getId()).isEqualTo(1L);
        assertThat(result.getEfrag_id()).isEqualTo("EFRAG001");
        verify(csrdDataRepository, times(1)).save(any(CsrdData.class));
    }
}
```

### Integration Tests

Integration tests focus on testing the interaction between components, such as the interaction between services and repositories, or between controllers and services.

#### What to Test

- **Repository Integration**: Test repository methods with a real database
- **Service Integration**: Test service methods with real repositories
- **Controller Integration**: Test controller endpoints with real services
- **Security Integration**: Test security configuration with real authentication

#### Example Integration Test

```java
@SpringBootTest
@AutoConfigureMockMvc
class CsrdDataControllerIntegrationTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private CsrdDataRepository csrdDataRepository;

    @BeforeEach
    void setUp() {
        csrdDataRepository.deleteAll();
        
        CsrdData csrdData = new CsrdData();
        csrdData.setEfragID("EFRAG001");
        csrdData.setEsrs("ESRS E1");
        csrdData.setDr("DR1");
        csrdData.setName("Test Data");
        
        csrdDataRepository.save(csrdData);
    }

    @Test
    void getAllCsrdData_ShouldReturnAllData() throws Exception {
        mockMvc.perform(get("/api/csrd/get"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$[0].efrag_id").value("EFRAG001"))
                .andExpect(jsonPath("$[0].esrs").value("ESRS E1"));
    }

    @Test
    @WithMockUser
    void createCsrdData_ShouldCreateAndReturnData() throws Exception {
        CsrdDataDTO csrdDataDTO = new CsrdDataDTO();
        csrdDataDTO.setEfrag_id("EFRAG002");
        csrdDataDTO.setEsrs("ESRS E2");
        csrdDataDTO.setDr("DR2");
        csrdDataDTO.setName("New Test Data");

        mockMvc.perform(post("/api/csrd/create")
                        .with(csrf())
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(new ObjectMapper().writeValueAsString(csrdDataDTO)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.efrag_id").value("EFRAG002"))
                .andExpect(jsonPath("$.esrs").value("ESRS E2"));

        assertThat(csrdDataRepository.findAll()).hasSize(2);
    }
}
```

### End-to-End Tests

End-to-end tests focus on testing the entire application stack, from the user interface to the database, simulating real user scenarios.

#### What to Test

- **User Flows**: Test complete user flows from start to finish
- **API Endpoints**: Test API endpoints with real data
- **Authentication**: Test authentication and authorization flows
- **Error Handling**: Test error handling and recovery

#### Example End-to-End Test

```java
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
class CsrdDataE2ETest {

    @Autowired
    private TestRestTemplate restTemplate;

    @Autowired
    private CsrdDataRepository csrdDataRepository;

    @BeforeEach
    void setUp() {
        csrdDataRepository.deleteAll();
        
        CsrdData csrdData = new CsrdData();
        csrdData.setEfragID("EFRAG001");
        csrdData.setEsrs("ESRS E1");
        csrdData.setDr("DR1");
        csrdData.setName("Test Data");
        
        csrdDataRepository.save(csrdData);
    }

    @Test
    void getAllCsrdData_ShouldReturnAllData() {
        ResponseEntity<CsrdDataDTO[]> response = restTemplate.getForEntity("/api/csrd/get", CsrdDataDTO[].class);
        
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody()).hasSize(1);
        assertThat(response.getBody()[0].getEfrag_id()).isEqualTo("EFRAG001");
        assertThat(response.getBody()[0].getEsrs()).isEqualTo("ESRS E1");
    }

    @Test
    void createCsrdData_ShouldCreateAndReturnData() {
        CsrdDataDTO csrdDataDTO = new CsrdDataDTO();
        csrdDataDTO.setEfrag_id("EFRAG002");
        csrdDataDTO.setEsrs("ESRS E2");
        csrdDataDTO.setDr("DR2");
        csrdDataDTO.setName("New Test Data");

        ResponseEntity<CsrdDataDTO> response = restTemplate
                .withBasicAuth("user", "password")
                .postForEntity("/api/csrd/create", csrdDataDTO, CsrdDataDTO.class);
        
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody().getEfrag_id()).isEqualTo("EFRAG002");
        assertThat(response.getBody().getEsrs()).isEqualTo("ESRS E2");
        
        assertThat(csrdDataRepository.findAll()).hasSize(2);
    }
}
```

## Testing Tools

### JUnit 5

JUnit 5 is the primary testing framework for the application. It provides a modern testing platform for Java applications.

```xml
<dependency>
    <groupId>org.junit.jupiter</groupId>
    <artifactId>junit-jupiter</artifactId>
    <scope>test</scope>
</dependency>
```

### Mockito

Mockito is used for mocking dependencies in unit tests.

```xml
<dependency>
    <groupId>org.mockito</groupId>
    <artifactId>mockito-junit-jupiter</artifactId>
    <scope>test</scope>
</dependency>
```

### AssertJ

AssertJ is used for fluent assertions in tests.

```xml
<dependency>
    <groupId>org.assertj</groupId>
    <artifactId>assertj-core</artifactId>
    <scope>test</scope>
</dependency>
```

### Spring Test

Spring Test provides testing support for Spring applications.

```xml
<dependency>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-test</artifactId>
    <scope>test</scope>
</dependency>
```

### Testcontainers

Testcontainers is used for integration tests that require a real database.

```xml
<dependency>
    <groupId>org.testcontainers</groupId>
    <artifactId>postgresql</artifactId>
    <scope>test</scope>
</dependency>
<dependency>
    <groupId>org.testcontainers</groupId>
    <artifactId>junit-jupiter</artifactId>
    <scope>test</scope>
</dependency>
```

## Test Configuration

### Test Properties

Test-specific properties are defined in `src/test/resources/application-test.properties`:

```properties
# Test database configuration
spring.datasource.csrd.url=jdbc:tc:postgresql:13:///parabella_csrd_db
spring.datasource.csrd.driver-class-name=org.testcontainers.jdbc.ContainerDatabaseDriver
spring.datasource.csrd.username=test
spring.datasource.csrd.password=test

spring.datasource.vector.url=jdbc:tc:postgresql:13:///parabella_vector_db
spring.datasource.vector.driver-class-name=org.testcontainers.jdbc.ContainerDatabaseDriver
spring.datasource.vector.username=test
spring.datasource.vector.password=test

# JPA configuration
spring.jpa.hibernate.ddl-auto=create-drop
spring.jpa.properties.hibernate.format_sql=true
spring.jpa.show-sql=true

# JWT configuration
parabella_csrd_db.jwtSecret=test-jwt-secret
parabella_csrd_db.jwtExpirationMs=60000
parabella_csrd_db.jwtRefreshExpirationMs=120000

# Email configuration
spring.mail.host=localhost
spring.mail.port=3025
spring.mail.username=test
spring.mail.password=test

# Frontend URL for email links
frontend.url=http://localhost:5173/
```

### Test Data

Test data is defined in `src/test/resources/data.sql`:

```sql
-- Insert test users
INSERT INTO users (id, username, email, password) VALUES
(1, 'testuser', '<EMAIL>', '$2a$10$eDhncK/4cNH2KE.Y51AWpeL8K9/IPPjA3FqKzw8QP.IGlkkAiUzZC');

-- Insert test roles
INSERT INTO roles (id, name) VALUES
(1, 'ROLE_USER'),
(2, 'ROLE_ADMIN');

-- Insert test user roles
INSERT INTO user_roles (user_id, role_id) VALUES
(1, 1);

-- Insert test CSRD data
INSERT INTO csrd_data (id, efrag_id, esrs, dr, name) VALUES
(1, 'EFRAG001', 'ESRS E1', 'DR1', 'Test Data');
```

## Test Execution

### Running Tests with Maven

```bash
# Run all tests
mvn test

# Run a specific test class
mvn test -Dtest=CsrdDataServiceTest

# Run a specific test method
mvn test -Dtest=CsrdDataServiceTest#getAllCsrdData_ShouldReturnAllData

# Run tests with a specific profile
mvn test -Dspring.profiles.active=test
```

### Running Tests with Gradle

```bash
# Run all tests
./gradlew test

# Run a specific test class
./gradlew test --tests CsrdDataServiceTest

# Run a specific test method
./gradlew test --tests CsrdDataServiceTest.getAllCsrdData_ShouldReturnAllData

# Run tests with a specific profile
./gradlew test -Dspring.profiles.active=test
```

### Running Tests in IntelliJ IDEA

1. Right-click on the test class or method
2. Select "Run" or "Debug"
3. To run all tests, right-click on the `src/test/java` directory and select "Run All Tests"

## Test Coverage

### JaCoCo

JaCoCo is used to measure test coverage.

```xml
<plugin>
    <groupId>org.jacoco</groupId>
    <artifactId>jacoco-maven-plugin</artifactId>
    <version>0.8.7</version>
    <executions>
        <execution>
            <goals>
                <goal>prepare-agent</goal>
            </goals>
        </execution>
        <execution>
            <id>report</id>
            <phase>prepare-package</phase>
            <goals>
                <goal>report</goal>
            </goals>
        </execution>
    </executions>
</plugin>
```

### Coverage Goals

- **Line Coverage**: At least 80%
- **Branch Coverage**: At least 70%
- **Method Coverage**: At least 80%
- **Class Coverage**: At least 90%

### Coverage Report

To generate a coverage report:

```bash
mvn jacoco:report
```

The report will be available at `target/site/jacoco/index.html`.

## Continuous Integration

### GitHub Actions

Tests are run automatically on GitHub Actions for every push and pull request.

```yaml
name: Java CI

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

jobs:
  build:
    runs-on: ubuntu-latest

    steps:
    - uses: actions/checkout@v2
    - name: Set up JDK 17
      uses: actions/setup-java@v2
      with:
        java-version: '17'
        distribution: 'temurin'
    - name: Build with Maven
      run: mvn -B package --file pom.xml
    - name: Test with Maven
      run: mvn test
    - name: Generate JaCoCo Report
      run: mvn jacoco:report
    - name: Upload JaCoCo Report
      uses: actions/upload-artifact@v2
      with:
        name: jacoco-report
        path: target/site/jacoco/
```

## Best Practices

### Test Naming

Tests should follow a consistent naming convention:

```
methodName_StateUnderTest_ExpectedBehavior
```

Examples:
- `getAllCsrdData_ShouldReturnAllData`
- `save_WithValidData_ShouldSaveAndReturnData`
- `delete_WithInvalidId_ShouldThrowException`

### Test Structure

Tests should follow the Arrange-Act-Assert (AAA) pattern:

```java
@Test
void methodName_StateUnderTest_ExpectedBehavior() {
    // Arrange - Set up the test data and conditions
    when(repository.findById(1L)).thenReturn(Optional.of(entity));

    // Act - Call the method being tested
    Result result = service.methodName(1L);

    // Assert - Verify the results
    assertThat(result).isNotNull();
    assertThat(result.getValue()).isEqualTo(expectedValue);
}
```

### Test Independence

Tests should be independent of each other:

- Each test should set up its own test data
- Tests should not depend on the order of execution
- Tests should clean up after themselves

### Test Data

Test data should be:

- Realistic but minimal
- Clearly defined and documented
- Consistent across tests
- Isolated from production data

### Mocking

Mocking should be used judiciously:

- Mock external dependencies
- Mock slow or unreliable components
- Don't mock the system under test
- Don't mock value objects or data structures

### Test Performance

Tests should be fast:

- Minimize database operations
- Use in-memory databases for tests
- Mock external services
- Parallelize test execution when possible

### Test Maintainability

Tests should be maintainable:

- Keep tests simple and focused
- Avoid test duplication
- Use helper methods for common setup
- Update tests when the code changes
