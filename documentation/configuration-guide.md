# Configuration Guide

This guide provides detailed instructions for configuring the Parabella CSRD backend application.

## Application Properties

The application configuration is defined in the `application.properties` file. This file contains settings for the database connection, security, logging, and other application-specific configurations.

### Core Properties

```properties
# Application name and version
spring.application.name=parabella-csrd-db
application.version=1.0.0

# Server configuration
server.port=8080
server.servlet.context-path=/
```

### Database Configuration

The application uses a multi-database configuration with two PostgreSQL databases:

```properties
# Main database configuration
spring.datasource.csrd.url=**************************************************
spring.datasource.csrd.username=parabella_user
spring.datasource.csrd.password=your_password
spring.datasource.csrd.driver-class-name=org.postgresql.Driver

# Vector database configuration
spring.datasource.vector.url=****************************************************
spring.datasource.vector.username=parabella_user
spring.datasource.vector.password=your_password
spring.datasource.vector.driver-class-name=org.postgresql.Driver

# JPA/Hibernate configuration
spring.jpa.hibernate.ddl-auto=update
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.PostgreSQLDialect
spring.jpa.properties.hibernate.format_sql=true
spring.jpa.show-sql=false
```

### Security Configuration

```properties
# JWT configuration
parabella_csrd_db.jwtSecret=${JWT_SECRET}
parabella_csrd_db.jwtExpirationMs=86400000
parabella_csrd_db.jwtRefreshExpirationMs=604800000

# CORS configuration
cors.allowed-origins=https://storage.googleapis.com,http://localhost:5173,https://parabella-elessar-1091242934000.europe-west3.run.app,https://gcr-parabella-staging-frontend-1091242934000.europe-west10.run.app,https://gcr-parabella-staging-frontend-1091242934000.europe-west4.run.app,https://parabella.app
```

### Email Configuration

```properties
# Email configuration
spring.mail.host=smtp.gmail.com
spring.mail.port=587
spring.mail.username=${EMAIL_USERNAME}
spring.mail.password=${EMAIL_PASSWORD}
spring.mail.properties.mail.smtp.auth=true
spring.mail.properties.mail.smtp.starttls.enable=true

# Frontend URL for email links
frontend.url=http://localhost:5173/
```

### Logging Configuration

```properties
# Logging configuration
logging.level.root=INFO
logging.level.com.example.parabella_csrd_db=DEBUG
logging.level.org.springframework.web=INFO
logging.level.org.hibernate=ERROR
```

### OpenAI Configuration

```properties
# OpenAI API configuration
openai.api.key=${OPENAI_API_KEY}
openai.model=gpt-4-1106-preview
openai.max-tokens=4000
openai.temperature=0.7
```

## Environment Variables

The application uses environment variables for sensitive configuration values. These should be set in the environment where the application runs:

- `JWT_SECRET`: Secret key for JWT token signing
- `EMAIL_USERNAME`: Username for the email service
- `EMAIL_PASSWORD`: Password for the email service
- `OPENAI_API_KEY`: API key for OpenAI services

### Setting Environment Variables

#### Local Development

For local development, you can set environment variables in your IDE or using a `.env` file with a library like `dotenv`.

**IntelliJ IDEA:**

1. Go to Run > Edit Configurations
2. Select your Spring Boot configuration
3. Add environment variables in the "Environment variables" field:
   ```
   JWT_SECRET=your_jwt_secret;EMAIL_USERNAME=your_email;EMAIL_PASSWORD=your_password;OPENAI_API_KEY=your_openai_key
   ```

**Command Line:**

```bash
export JWT_SECRET=your_jwt_secret
export EMAIL_USERNAME=your_email
export EMAIL_PASSWORD=your_password
export OPENAI_API_KEY=your_openai_key
```

#### Production Deployment

For production deployment, set environment variables according to your deployment platform:

**Docker:**

```dockerfile
ENV JWT_SECRET=your_jwt_secret
ENV EMAIL_USERNAME=your_email
ENV EMAIL_PASSWORD=your_password
ENV OPENAI_API_KEY=your_openai_key
```

**Kubernetes:**

```yaml
env:
  - name: JWT_SECRET
    valueFrom:
      secretKeyRef:
        name: parabella-secrets
        key: jwt-secret
  - name: EMAIL_USERNAME
    valueFrom:
      secretKeyRef:
        name: parabella-secrets
        key: email-username
  - name: EMAIL_PASSWORD
    valueFrom:
      secretKeyRef:
        name: parabella-secrets
        key: email-password
  - name: OPENAI_API_KEY
    valueFrom:
      secretKeyRef:
        name: parabella-secrets
        key: openai-api-key
```

## Database Configuration

### PostgreSQL Setup

1. Install PostgreSQL:

   ```bash
   # Ubuntu/Debian
   sudo apt update
   sudo apt install postgresql postgresql-contrib

   # macOS (using Homebrew)
   brew install postgresql
   ```

2. Start PostgreSQL service:

   ```bash
   # Ubuntu/Debian
   sudo service postgresql start

   # macOS
   brew services start postgresql
   ```

3. Create databases and user:

   ```bash
   # Connect to PostgreSQL
   sudo -u postgres psql

   # Create user
   CREATE USER parabella_user WITH PASSWORD 'your_password';

   # Create main database
   CREATE DATABASE parabella_csrd_db OWNER parabella_user;

   # Create vector database
   CREATE DATABASE parabella_vector_db OWNER parabella_user;

   # Grant privileges
   GRANT ALL PRIVILEGES ON DATABASE parabella_csrd_db TO parabella_user;
   GRANT ALL PRIVILEGES ON DATABASE parabella_vector_db TO parabella_user;
   ```

### Database Migration

The application uses Hibernate's automatic schema generation (`spring.jpa.hibernate.ddl-auto=update`) to create and update the database schema. For production environments, it's recommended to use a dedicated migration tool like Flyway or Liquibase.

To enable Flyway migrations:

1. Add Flyway dependency to `pom.xml`:

   ```xml
   <dependency>
       <groupId>org.flywaydb</groupId>
       <artifactId>flyway-core</artifactId>
   </dependency>
   ```

2. Configure Flyway in `application.properties`:

   ```properties
   spring.flyway.enabled=true
   spring.flyway.locations=classpath:db/migration
   spring.flyway.baseline-on-migrate=true
   spring.jpa.hibernate.ddl-auto=validate
   ```

3. Create migration scripts in `src/main/resources/db/migration`:

   ```sql
   -- V1__init.sql
   CREATE TABLE users (
       id BIGSERIAL PRIMARY KEY,
       username VARCHAR(50) NOT NULL UNIQUE,
       email VARCHAR(100) NOT NULL UNIQUE,
       password VARCHAR(120) NOT NULL,
       totp_secret VARCHAR(255)
   );

   -- Add more tables as needed
   ```

## Security Configuration

### Web Security Configuration

The web security configuration is defined in the `WebSecurityConfig` class:

```java
@Configuration
@EnableWebSecurity
public class WebSecurityConfig {

    @Autowired
    private UserDetailsServiceImpl userDetailsService;

    @Autowired
    private AuthEntryPointJwt unauthorizedHandler;

    @Bean
    public AuthTokenFilter authenticationJwtTokenFilter() {
        return new AuthTokenFilter();
    }

    @Bean
    public SecurityFilterChain filterChain(HttpSecurity http) throws Exception {
        http.csrf(AbstractHttpConfigurer::disable)
                .exceptionHandling(exception -> exception.authenticationEntryPoint(unauthorizedHandler))
                .sessionManagement(session -> session.sessionCreationPolicy(SessionCreationPolicy.STATELESS))
                .authorizeHttpRequests(auth ->
                        auth.requestMatchers("/api/auth/**").permitAll()
                                .requestMatchers("/api/test/**").permitAll()
                                .requestMatchers("/api/csrd/**").permitAll()
                                .requestMatchers("/api/companies/**").authenticated()
                                .requestMatchers("/api/excel/data/**").authenticated()
                                .requestMatchers("/api/progress/**").authenticated()
                                .requestMatchers("/api/projects/**").authenticated()
                                .requestMatchers("/api/stakeholder/**").permitAll()
                                .anyRequest().authenticated()
                );

        http.authenticationProvider(authenticationProvider());
        http.addFilterBefore(authenticationJwtTokenFilter(), UsernamePasswordAuthenticationFilter.class);

        return http.build();
    }

    @Bean
    public DaoAuthenticationProvider authenticationProvider() {
        DaoAuthenticationProvider authProvider = new DaoAuthenticationProvider();
        authProvider.setUserDetailsService(userDetailsService);
        authProvider.setPasswordEncoder(passwordEncoder());
        return authProvider;
    }

    @Bean
    public AuthenticationManager authenticationManager(AuthenticationConfiguration authConfig) throws Exception {
        return authConfig.getAuthenticationManager();
    }

    @Bean
    public PasswordEncoder passwordEncoder() {
        return new BCryptPasswordEncoder();
    }
}
```

### CORS Configuration

The CORS configuration is defined in the `CorsConfig` class:

```java
@Configuration
public class CorsConfig implements WebMvcConfigurer {

    @Value("${cors.allowed-origins}")
    private String[] allowedOrigins;

    @Override
    public void addCorsMappings(CorsRegistry registry) {
        registry.addMapping("/**")
                .allowedOrigins(allowedOrigins)
                .allowedMethods("GET", "POST", "PUT", "DELETE", "OPTIONS")
                .allowedHeaders("*")
                .allowCredentials(true)
                .maxAge(3600);
    }
}
```

## Email Configuration

The email configuration is defined in the `application.properties` file and used by the `EmailService` class:

```java
@Service
public class EmailService {

    @Autowired
    private JavaMailSender emailSender;

    @Value("${frontend.url}")
    private String frontendUrl;

    public void sendPlainOrHtmlEmail(String to, String subject, String text) {
        // Send plain or HTML email
    }

    public void sendHtmlEmail(String to, String token, String name) throws MessagingException, IOException {
        // Send HTML email with token
    }
}
```

## OpenAI Configuration

The OpenAI configuration is defined in the `application.properties` file and used by the `OpenAIService` class:

```java
@Service
public class OpenAIService {

    @Value("${openai.api.key}")
    private String apiKey;

    @Value("${openai.model}")
    private String model;

    @Value("${openai.max-tokens}")
    private int maxTokens;

    @Value("${openai.temperature}")
    private double temperature;

    public String generateCompletion(String prompt) {
        // Generate completion using OpenAI API
    }
}
```

## Logging Configuration

The logging configuration is defined in the `application.properties` file and can be customized using a `logback.xml` file:

```xml
<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n</pattern>
        </encoder>
    </appender>

    <appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>logs/parabella-csrd-db.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>logs/parabella-csrd-db.%d{yyyy-MM-dd}.log</fileNamePattern>
            <maxHistory>30</maxHistory>
        </rollingPolicy>
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n</pattern>
        </encoder>
    </appender>

    <root level="INFO">
        <appender-ref ref="CONSOLE" />
        <appender-ref ref="FILE" />
    </root>

    <logger name="com.example.parabella_csrd_db" level="DEBUG" />
    <logger name="org.springframework.web" level="INFO" />
    <logger name="org.hibernate" level="ERROR" />
</configuration>
```

## Profiles

The application supports different profiles for different environments:

```properties
# application.properties
spring.profiles.active=dev
```

```properties
# application-dev.properties
spring.datasource.csrd.url=**************************************************
spring.jpa.show-sql=true
```

```properties
# application-prod.properties
spring.datasource.csrd.url=***********************************************************
spring.jpa.show-sql=false
```

To activate a specific profile:

```bash
# Command line
java -jar parabella-csrd-db.jar --spring.profiles.active=prod

# Environment variable
export SPRING_PROFILES_ACTIVE=prod
```

## Actuator Configuration

Spring Boot Actuator provides production-ready features for monitoring and managing the application:

```xml
<dependency>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-actuator</artifactId>
</dependency>
```

```properties
# Actuator configuration
management.endpoints.web.exposure.include=health,info,metrics,prometheus
management.endpoint.health.show-details=when_authorized
management.info.env.enabled=true
```

## Swagger/OpenAPI Configuration

Swagger/OpenAPI provides API documentation:

```xml
<dependency>
    <groupId>org.springdoc</groupId>
    <artifactId>springdoc-openapi-starter-webmvc-ui</artifactId>
    <version>2.0.2</version>
</dependency>
```

```java
@Configuration
public class OpenApiConfig {

    @Bean
    public OpenAPI customOpenAPI() {
        return new OpenAPI()
                .info(new Info()
                        .title("Parabella CSRD API")
                        .version("1.0.0")
                        .description("API for Parabella CSRD application")
                        .contact(new Contact()
                                .name("Parabella Analytics")
                                .url("https://parabella.app")
                                .email("<EMAIL>")));
    }
}
```

Access the API documentation at: `http://localhost:8080/swagger-ui.html`
