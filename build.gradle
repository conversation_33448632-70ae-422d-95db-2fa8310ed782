plugins {
    id 'java'
    id 'org.springframework.boot' version '3.3.0'
    id 'io.spring.dependency-management' version '1.1.5'
}

group = 'com.example'
version = '0.0.1-SNAPSHOT'


java {
    toolchain {
        languageVersion = JavaLanguageVersion.of(21)
    }
}

configurations {
    compileOnly {
        extendsFrom annotationProcessor
    }
}


ext {
    set('testcontainersVersion', "1.19.0")
}

repositories {
    mavenCentral()
}

dependencies {
    implementation 'com.google.cloud.sql:postgres-socket-factory:1.20.0'
    implementation 'org.springframework.boot:spring-boot-starter-data-jpa'
    implementation 'org.springframework.boot:spring-boot-starter-jdbc'
    implementation 'org.springframework.boot:spring-boot-starter-web'
    implementation 'org.springframework.boot:spring-boot-starter-webflux'
    compileOnly 'org.projectlombok:lombok'
    developmentOnly 'org.springframework.boot:spring-boot-devtools'
    runtimeOnly 'org.postgresql:postgresql'
    annotationProcessor 'org.springframework.boot:spring-boot-configuration-processor'
    annotationProcessor 'org.projectlombok:lombok'
    testImplementation 'io.projectreactor:reactor-test'
    testRuntimeOnly 'org.junit.platform:junit-platform-launcher'
    implementation 'org.apache.poi:poi:'
    implementation 'org.apache.poi:poi-ooxml:5.2.5'
    implementation 'org.springframework.boot:spring-boot-starter-web'
    implementation 'org.springframework.boot:spring-boot-starter-security'
    testImplementation 'org.springframework.security:spring-security-test'
    testImplementation 'org.springframework.boot:spring-boot-starter-test'
    implementation 'io.jsonwebtoken:jjwt-api:0.12.5'
    runtimeOnly 'io.jsonwebtoken:jjwt-jackson:0.12.5'
    runtimeOnly 'io.jsonwebtoken:jjwt-impl:0.12.5'
    implementation 'org.springframework.boot:spring-boot-starter-validation'
    implementation 'org.apache.commons:commons-lang3:3.0'
    implementation 'org.apache.commons:commons-csv:1.11.0'
    implementation 'org.springframework.boot:spring-boot-starter-mail:3.3.1'
    implementation 'com.google.cloud:spring-cloud-gcp-dependencies:5.6.0'
    implementation 'com.zaxxer:HikariCP:5.1.0'
    implementation 'com.google.cloud.sql:postgres-socket-factory:1.20.1'
    testImplementation 'com.h2database:h2'
    testImplementation 'org.junit.jupiter:junit-jupiter:5.11.0'
    testImplementation 'org.springframework.boot:spring-boot-testcontainers'
    testImplementation 'org.testcontainers:junit-jupiter'
    testImplementation 'io.rest-assured:rest-assured'
    testImplementation "org.testcontainers:spock:1.19.0"
    testImplementation 'org.testcontainers:testcontainers:1.19.0'
    testImplementation 'org.testcontainers:postgresql:1.19.0'
    implementation 'com.opencsv:opencsv:5.9'
    implementation 'com.warrenstrange:googleauth:1.5.0'
    implementation 'org.hibernate.orm:hibernate-envers:6.6.4.Final'
    implementation("com.vladmihalcea:hibernate-types-60:2.21.1")




}

dependencyManagement {
    imports {
        mavenBom "org.testcontainers:testcontainers-bom:${testcontainersVersion}"
    }
}
//tasks.named('test') {
//    useJUnitPlatform()
//}
