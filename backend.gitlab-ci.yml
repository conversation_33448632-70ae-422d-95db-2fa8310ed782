
# backend.gitlab-ci.yml
stages:
  - build
  - docker_build_push # Combined build and push
  - deploy_staging
  - deploy_production

variables:
  # Common variables derived from GitLab CI/CD Variables or predefined ones
  # Note: GCP_SERVICE_KEY_* variables are automatically available if defined in settings
  #       Variables scoped to environments will be automatically selected based on the job's environment setting.
  IMAGE_TAG: $CI_COMMIT_SHORT_SHA
  ARTIFACT_REGISTRY_PATH: $GCP_REGION-docker.pkg.dev/$GCP_PROJECT_ID/$GCP_BACKEND_REPO
  FULL_IMAGE_NAME_BACKEND: $ARTIFACT_REGISTRY_PATH/$CI_COMMIT_BRANCH-$IMAGE_TAG


.gcloud_auth: &gcloud_auth
  # Activate service account based on environment (staging/production)

  # Git<PERSON><PERSON> automatically selects the correct scoped variable GCP_SERVICE_KEY_STAGING or GCP_SERVICE_KEY_PRODUCTION
  - echo "$GCP_SERVICE_KEY" > /tmp/gcp-key.json
  - gcloud auth activate-service-account --key-file=/tmp/gcp-key.json
  - gcloud config set project "$GCP_PROJECT_ID"
  - gcloud auth configure-docker $GCP_REGION-docker.pkg.dev --quiet

build-backend:
  stage: build
  image: gradle:jdk21
  script:
    - gradle build --no-daemon # Use --no-daemon for CI environments
  artifacts:
    paths:
      - build/libs/*.jar
    expire_in: 1 hour # Keep artifacts only as long as needed

docker_build_push_backend:
  stage: docker_build_push
  image: google/cloud-sdk:latest
  services:
    - name: docker:20.10-dind # Use specific version
      alias: docker # Ensure alias matches DOCKER_HOST if needed
  variables:
    # Explicitly set DOCKER_HOST if needed, though often works without with dind service alias
    DOCKER_HOST: tcp://docker:2375
    DOCKER_TLS_CERTDIR: ""
    DOCKER_DRIVER: overlay2 # Recommended driver for dind
  dependencies:
    - build-backend
  script:
    - *gcloud_auth # Use the anchor for auth
    - docker build --platform linux/amd64 -t "$FULL_IMAGE_NAME_BACKEND" .
    - docker push "$FULL_IMAGE_NAME_BACKEND"
  needs: [build-backend] # Explicit dependency

deploy_staging_backend:
  stage: deploy_staging
  image: google/cloud-sdk:latest
  environment:
    name: staging # Links to GitLab Environment 'staging'
    url: https://$BACKEND_SERVICE_NAME-xxxxxx-ew.a.run.app # Set dynamically or manually if needed
  script:
    - *gcloud_auth

    # Pass required env vars; GitLab picks the 'staging' scoped values automatically
    - |
      gcloud run deploy "$BACKEND_SERVICE_NAME" \
        --image "$FULL_IMAGE_NAME_BACKEND" \
        --region "$GCP_REGION" \
        --platform managed \
        --allow-unauthenticated \
        --set-env-vars="SPRING_PROFILES_ACTIVE=staging,SPRING_DATASOURCE_CSRD_URL=$SPRING_DATASOURCE_URL,SPRING_DATASOURCE_CSRD_USERNAME=$SPRING_DATASOURCE_USERNAME,SPRING_DATASOURCE_CSRD_PASSWORD=$SPRING_DATASOURCE_PASSWORD,JWT_SECRET=$JWT_SECRET,SPRING_MAIL_USERNAME=$SPRING_MAIL_USERNAME,SPRING_MAIL_PASSWORD=$SPRING_MAIL_PASSWORD,OPENAI_API_KEY=$OPENAI_API_KEY,APP_FRONTEND_URL=$APP_FRONTEND_URL" \        --quiet
  rules:
    # Run only on commits/merges to the 'staging' branch
    - if: '$CI_COMMIT_BRANCH == "staging"'
  needs: [docker_build_push_backend]

deploy_production_backend:
  stage: deploy_production
  image: google/cloud-sdk:latest
  environment:
    name: production # Links to GitLab Environment 'production'
    url: https://$BACKEND_SERVICE_NAME-xxxxxx-ew.a.run.app
  script:
    - *gcloud_auth

    # Pass required env vars; GitLab picks the 'production' scoped values automatically
    - |
      gcloud run deploy "$BACKEND_SERVICE_NAME" \
        --image "$FULL_IMAGE_NAME_BACKEND" \
        --region "$GCP_REGION" \
        --platform managed \
        --allow-unauthenticated \
        --set-env-vars="\
      SPRING_PROFILES_ACTIVE=staging,\
      SPRING_DATASOURCE_CSRD_URL=$SPRING_DATASOURCE_CSRD_URL,\
      SPRING_DATASOURCE_CSRD_USERNAME=$SPRING_DATASOURCE_CSRD_USERNAME,\
      SPRING_DATASOURCE_CSRD_PASSWORD=$SPRING_DATASOURCE_CSRD_PASSWORD,\
      SPRING_DATASOURCE_VECTORDB_URL=$SPRING_DATASOURCE_VECTORDB_URL,\
      SPRING_DATASOURCE_VECTORDB_USERNAME=$SPRING_DATASOURCE_VECTORDB_USERNAME,\
      SPRING_DATASOURCE_VECTORDB_PASSWORD=$SPRING_DATASOURCE_VECTORDB_PASSWORD,\
      JWT_SECRET=$JWT_SECRET,\
      SPRING_MAIL_USERNAME=$SPRING_MAIL_USERNAME,\
      SPRING_MAIL_PASSWORD=$SPRING_MAIL_PASSWORD,\
      OPENAI_API_KEY=$OPENAI_API_KEY,\
      APP_FRONTEND_URL=$APP_FRONTEND_URL" \
      --quiet
  rules:
    # Run only on commits/merges to the 'main' branch, AND requires manual trigger
    - if: '$CI_COMMIT_BRANCH == "main"'
      when: manual # IMPORTANT: Manual trigger for production deployment
      allow_failure: false # Ensure production deploy failure stops the pipeline
  needs: [docker_build_push_backend]

## backend.gitlab-ci.yml
##stages:
##  - build
##  - docker_build
##  - deploy
##
##variables:
##  REGION: europe-west10
##  BACKEND_REPO: staging-frontend
##  IMAGE_NAME_BACKEND: gcr-parabella-staging-0-1
##  IMAGE_TAG_BACKEND: $CI_COMMIT_SHORT_SHA
##  FULL_IMAGE_NAME_BACKEND: $REGION-docker.pkg.dev/$PROJECT_ID/$BACKEND_REPO/$IMAGE_NAME_BACKEND:$IMAGE_TAG_BACKEND
##  DOCKER_HOST: tcp://docker:2375
##  DOCKER_TLS_CERTDIR: ""
##
##build-backend:
##  stage: build
##  image: gradle:jdk21
##  script:
##    - gradle build
##  artifacts:
##    paths:
##      - build/libs/*.jar
##
##docker-build-backend:
##  stage: docker_build
##  image: google/cloud-sdk:latest
##  services:
##    - docker:dind
##  dependencies:
##    - build-backend
##  script:
##    - set -x
##    - apt-get update && apt-get install -y docker.io
##    - echo "$GCP_SERVICE_KEY" > service-account-key.json
##    - gcloud auth activate-service-account --key-file=service-account-key.json
##    - gcloud config set project "$PROJECT_ID"
##    - gcloud auth configure-docker $REGION-docker.pkg.dev
##    - docker build --platform linux/amd64 -t "$FULL_IMAGE_NAME_BACKEND" .
##    - docker push "$FULL_IMAGE_NAME_BACKEND"
##  environment:
##    name: "build/$CI_COMMIT_REF_NAME"
##
##deploy-backend:
##  stage: deploy
##  image: google/cloud-sdk:latest
##  script:
##    - set -x
##    - echo "$GCP_SERVICE_KEY" > service-account-key.json
##    - gcloud auth activate-service-account --key-file=service-account-key.json
##    - gcloud config set project "$PROJECT_ID"
##    - |
##      gcloud run deploy "$IMAGE_NAME_BACKEND" \
##        --image "$FULL_IMAGE_NAME_BACKEND" \
##        --region "$REGION" \
##        --platform managed \
##        --allow-unauthenticated \
##        --set-env-vars "SPRING_DATASOURCE_URL=$SPRING_DATASOURCE_URL,SPRING_DATASOURCE_USERNAME=$SPRING_DATASOURCE_USERNAME,SPRING_DATASOURCE_PASSWORD=$SPRING_DATASOURCE_PASSWORD,JWT_SECRET=$JWT_SECRET,SPRING_MAIL_USERNAME=$SPRING_MAIL_USERNAME,SPRING_MAIL_PASSWORD=$SPRING_MAIL_PASSWORD,OPEN_AI_API_KEY=$OPEN_AI_API_KEY"
##  environment:
##    name: "deploy/$CI_COMMIT_REF_NAME"
#
#stages:
#  - build
#  - docker_build
#  - deploy
#
#variables:
#  REGION: europe-west10
#  BACKEND_REPO: staging-frontend
#  IMAGE_NAME_BACKEND: gcr-parabella-testing-0-1
#  IMAGE_TAG_BACKEND: $CI_COMMIT_SHORT_SHA
#  FULL_IMAGE_NAME_BACKEND: $REGION-docker.pkg.dev/$PROJECT_ID/$BACKEND_REPO/$IMAGE_NAME_BACKEND:$IMAGE_TAG_BACKEND
#  DOCKER_HOST: tcp://docker:2375
#  DOCKER_TLS_CERTDIR: ""
#
#build-backend:
#  stage: build
#  image: gradle:jdk21
#  script:
#    - gradle build
#  artifacts:
#    paths:
#      - build/libs/*.jar
#  environment:
#    name: "build/testing"
#
#docker-build-backend:
#  stage: docker_build
#  image: google/cloud-sdk:latest
#  services:
#    - docker:dind
#  dependencies:
#    - build-backend
#  script:
#    - set -x
#    - apt-get update && apt-get install -y docker.io
#    - echo "$GCP_SERVICE_KEY" > service-account-key.json
#    - gcloud auth activate-service-account --key-file=service-account-key.json
#    - gcloud config set project "$PROJECT_ID"
#    - gcloud auth configure-docker $REGION-docker.pkg.dev
#    - docker build --platform linux/amd64 -t "$FULL_IMAGE_NAME_BACKEND" .
#    - docker push "$FULL_IMAGE_NAME_BACKEND"
#  environment:
#    name: "build/testing"
#
#deploy-backend:
#  stage: deploy
#  image: google/cloud-sdk:latest
#  script:
#    - set -x
#    - echo "$GCP_SERVICE_KEY" > service-account-key.json
#    - gcloud auth activate-service-account --key-file=service-account-key.json
#    - gcloud config set project "$PROJECT_ID"
#    - |
#      gcloud run deploy "$IMAGE_NAME_BACKEND" \
#        --image "$FULL_IMAGE_NAME_BACKEND" \
#        --region "$REGION" \
#        --platform managed \
#        --allow-unauthenticated \
#        --set-env-vars "SPRING_DATASOURCE_URL=$SPRING_DATASOURCE_URL,SPRING_DATASOURCE_USERNAME=$SPRING_DATASOURCE_USERNAME,SPRING_DATASOURCE_PASSWORD=$SPRING_DATASOURCE_PASSWORD,JWT_SECRET=$JWT_SECRET,SPRING_MAIL_USERNAME=$SPRING_MAIL_USERNAME,SPRING_MAIL_PASSWORD=$SPRING_MAIL_PASSWORD,OPEN_AI_API_KEY=$OPEN_AI_API_KEY"
#  environment:
#    name: "deploy/testing"
#
