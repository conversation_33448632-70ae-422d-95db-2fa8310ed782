# Parabella Ellesár

Parabella Ellesár is an enterprise software designed to analyze the sustainability degree of a company, generate the CSRD report, and provide possibilities for prognostic assumptions regarding sustainability. The platform helps companies comply with Corporate Sustainability Reporting Directive (CSRD) requirements through a comprehensive set of tools for data collection, analysis, and reporting.

## Table of Contents

- [System Architecture](#system-architecture)
- [Modules](#modules)
- [Prerequisites](#prerequisites)
- [Installation](#installation)
- [Running the Application](#running-the-application)
- [Development Workflow](#development-workflow)
- [API Documentation](#api-documentation)
- [Database Schema](#database-schema)
- [Troubleshooting](#troubleshooting)
- [Contributing](#contributing)
- [License](#license)
- [Contact](#contact)

## System Architecture

Parabella Ellesár follows a modern microservice-oriented architecture with a clear separation of concerns:

### Backend Architecture

- **Spring Boot Application**: Java-based backend using Spring Boot 3.x
- **Multi-Database Configuration**: Separate databases for main application data and vector storage
- **Security Layer**: JWT-based authentication and role-based authorization
- **Service Layer**: Business logic encapsulated in service classes
- **Repository Layer**: Data access through Spring Data JPA repositories
- **Controller Layer**: RESTful API endpoints for frontend communication
- **AI Integration**: OpenAI API integration for intelligent data processing

### Frontend Architecture

- **React Application**: Built with React 18 and TypeScript
- **State Management**: Context API for global state management
- **Routing**: React Router for navigation
- **UI Framework**: Material UI components with custom styling
- **API Integration**: Axios for HTTP requests to backend services
- **Form Handling**: React Hook Form for form validation and submission

### Data Flow

1. User interacts with React frontend
2. Frontend makes API calls to Spring Boot backend
3. Backend processes requests through controllers
4. Service layer applies business logic
5. Repository layer handles data persistence
6. Data is stored in PostgreSQL databases
7. Responses flow back to the frontend for rendering

## Modules

Parabella Ellesár is composed of three main modules:

1. **Arkenstein**:
   - Parabella Academy/Wikipedia module which encompasses the entire CSRD regulatory framework with examples
   - Provides educational content and reference materials for CSRD compliance
   - Includes searchable knowledge base of sustainability reporting requirements

2. **Mithril**:
   - A tool for double materiality analysis
   - Helps companies identify and assess sustainability impacts
   - Supports stakeholder engagement and data collection
   - Generates materiality matrices and impact assessments

3. **Vilya**:
   - A product carbon footprint calculator
   - Tracks emissions across scope 1, 2, and 3
   - Provides data visualization and reporting tools
   - Supports scenario planning and reduction target setting

## Prerequisites

Before you begin, ensure you have the following installed on your machine:

- **Java Development Kit (JDK)** - Version 17 or higher
- **Maven** - Version 3.8 or higher (for backend build)
- **Node.js** - Version 16 or higher
- **npm** - Version 8 or higher
- **Git** - Latest version
- **PostgreSQL** - Version 13 or higher
- **Docker** - Latest version (optional, for containerized deployment)
- **IDE** - IntelliJ IDEA (recommended for backend) and VS Code (recommended for frontend)

## Installation

### 1. Clone the Repository

```bash
git clone https://github.com/your-organization/ParabellaCSRDFaqTool.git
cd ParabellaCSRDFaqTool
```

### 2. Backend Setup

#### Configure the Database

1. Create a PostgreSQL database:

```sql
-- 1) Create a new user
CREATE ROLE parabella_user WITH LOGIN PASSWORD 'your_password';

-- 2) Create database and assign ownership to the new user
CREATE DATABASE parabella_csrd_db OWNER parabella_user;

-- (Optional) If the database already exists:
-- GRANT ALL PRIVILEGES ON DATABASE parabella_csrd_db TO parabella_user;
```

2. Configure the database connection in `src/main/resources/application.properties`:

```properties
spring.datasource.csrd.url=**************************************************
spring.datasource.csrd.username=parabella_user
spring.datasource.csrd.password=your_password
spring.jpa.hibernate.ddl-auto=update
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.PostgreSQLDialect
```

3. Build and Run the Backend

Using Maven:

```bash
mvn clean install
mvn spring-boot:run
```

Or using your IDE:
- Open the project in IntelliJ IDEA
- Import Maven dependencies
- Run the main application class: `com.example.parabella_csrd_db.ParabellaCsrdDbApplication`

4. Verify Backend Setup

The backend should be running at `http://localhost:8080`. You can test it by accessing:
- `http://localhost:8080/api/csrd/topics` - Should return a list of CSRD topics

### 3. Frontend Setup

1. Navigate to the Frontend Directory

```bash
cd parabella_frontend_nowa/parabella_elessar
```

2. Install Dependencies

```bash
npm install
```

3. Configure Environment Variables

Create a `.env` file in the frontend root directory:

```
VITE_API_BASE_URL=http://localhost:8080
```

4. Run the Frontend Development Server

```bash
npm run dev
```

5. Verify Frontend Setup

The frontend should be running at `http://localhost:5173`. Open this URL in your browser to see the application.

## Running the Application

### Backend

- **Development mode**: `mvn spring-boot:run`
- **Production build**: `mvn clean package`
- **Run tests**: `mvn test`

### Frontend

- **Development mode**: `npm run dev`
- **Production build**: `npm run build`
- **Run tests**: `npm test`

## Development Workflow

### Backend Development

1. **Database Changes**:
   - Entity changes are automatically applied to the database with `spring.jpa.hibernate.ddl-auto=update`
   - For complex schema changes, create SQL migration scripts

2. **API Development**:
   - Controllers are in `src/main/java/com/example/parabella_csrd_db/controller`
   - Services are in `src/main/java/com/example/parabella_csrd_db/service`
   - Repositories are in `src/main/java/com/example/parabella_csrd_db/repository`

3. **Testing**:
   - Write unit tests for entities, services, and controllers
   - Run tests with `mvn test`

### Frontend Development

1. **Component Development**:
   - Components are in `src/ui_components/components`
   - CSRD Module components are in `src/ui_components/components/CSRD_Module`

2. **API Integration**:
   - API functions are in `src/ui_components/components/CSRD_Module/api`
   - Context providers are in `src/ui_components/components/CSRD_Module/context`

3. **Testing**:
   - Write tests for components and API functions
   - Run tests with `npm test`

## API Documentation

The Parabella Ellesár API follows RESTful principles and is organized by domain:

### Authentication Endpoints

- `POST /api/auth/signin` - User login
- `POST /api/auth/signup` - User registration
- `POST /api/auth/refreshtoken` - Refresh JWT token
- `POST /api/auth/reset-password` - Request password reset

### CSRD Project Endpoints

- `GET /api/csrd/projects/user/{userId}` - Get user's CSRD projects
- `GET /api/csrd/projects/{id}` - Get project details
- `POST /api/csrd/projects` - Create new project
- `PUT /api/csrd/projects/{id}` - Update project
- `DELETE /api/csrd/projects/{id}` - Delete project

### Company Information Endpoints

- `GET /api/csrd/projects/{id}/company-info` - Get company information
- `POST /api/csrd/projects/{id}/company-info` - Save company information

### CSRD Topics Endpoints

- `GET /api/csrd/topics` - Get all CSRD topics
- `GET /api/csrd/topics/{id}/subtopics` - Get subtopics for a topic

### Stakeholder Endpoints

- `GET /api/stakeholder/dashboard/{projectId}` - Get stakeholder dashboard
- `POST /api/stakeholders/send-stakeholder-emails` - Send invitations

### AI Integration Endpoints

- `POST /api/chat/completion` - Get AI-generated content
- `POST /api/chat/upload` - Upload documents for AI processing

## Database Schema

The application uses a PostgreSQL database with the following key tables:

### Authentication Tables

- `users` - User accounts and credentials
- `roles` - User roles (ADMIN, USER)
- `user_roles` - Many-to-many relationship between users and roles
- `password_reset_tokens` - Tokens for password reset functionality

### CSRD Project Tables

- `csrd_projects` - Main project information
- `csrd_company_info` - Company details for CSRD reporting

### Materiality Analysis Tables

- `projects` - Double materiality analysis projects
- `stakeholders` - Project stakeholders and their status
- `esrs_topic` - ESRS topics from the CSRD framework
- `esrs_topic_selection` - Topics selected for materiality assessment
- `iro` - Impacts, Risks, and Opportunities identified
- `iro_evaluation` - Detailed assessment of IROs

### Vector Database Tables

- `embeddings` - Vector embeddings for AI-powered search
- `documents` - Uploaded and processed documents

## Troubleshooting

### Common Backend Issues

1. **Database Connection Issues**:
   - Verify PostgreSQL is running: `sudo service postgresql status`
   - Check database credentials in `application.properties`
   - Ensure the database exists: `\l` (in psql) or `SELECT datname FROM pg_database;`
   - Check PostgreSQL logs: `tail -f /var/log/postgresql/postgresql-13-main.log`

2. **Build Failures**:
   - Clear Maven cache: `mvn clean`
   - Update Maven dependencies: `mvn dependency:resolve`
   - Check for Java version compatibility issues

3. **Runtime Errors**:
   - Check application logs in the console
   - Verify Java version: `java -version`
   - Check for environment variable issues

### Common Frontend Issues

1. **Dependency Issues**:
   - Delete `node_modules` and reinstall: `rm -rf node_modules && npm install`
   - Clear npm cache: `npm cache clean --force`
   - Check for Node.js version compatibility

2. **API Connection Issues**:
   - Verify backend is running
   - Check CORS configuration in the backend
   - Verify API base URL in `.env` file
   - Use browser developer tools to inspect network requests

3. **Build Issues**:
   - Check for TypeScript errors
   - Verify Vite configuration in `vite.config.ts`
   - Try building with verbose output: `npm run build -- --debug`

## Contributing

We welcome contributions to the Parabella Ellesár project! Please follow these steps:

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

Please ensure your code follows our coding standards and includes appropriate tests.

## License

The software is provided under a Service Level Agreement. Please contact Parabella Analytics for licensing details.

## Contact

For support or any inquiries, please contact:

**Parabella Analytics**

---

© 2023 Parabella Analytics. All rights reserved.
